I don’t think that the sheer amount of jobs would be an issue. We just need to make sure that we don’t spawn too many workers which would overwhelm the database.

The problem is the background jobs (1 per recipient) are queued synchronously without any user feedback. A few issues with this:
- That's a very long time to wait for that many recipients. I just tested locally with 15k recipients and it took around 10 minutes (the environment is not quite comparable, but still I don't expect it to be faster in production).
- Cloudfront will in any case time out after 3 minutes (even if the request is still being processed by the back-end).
- If the thread that is processing the request dies somewhere in the middle, it will be difficult to recover from that without resending the mail to all recipients. (Pretty sure a release could interrupt the thread, but not 100% sure).

I don’t think the sheer number of jobs will be an issue. We just need to ensure we don’t spawn too many workers, as that could overwhelm the database.

The real problem is that background jobs (one per recipient) are queued synchronously without any user feedback. A few issues with this:
- That's a very long time to wait for that many recipients. I tested locally with 15k recipients, and it took around 10 minutes. The environment isn’t exactly comparable, but still, I don't expect it to be faster in production, quite the opposite.
- In any case, CloudFront will time out after 3 minutes, even if the backend is still processing the request. So the user will be tempted to re-send the campaign.
- If the thread processing the request dies somewhere in the middle, recovering without resending the email to all recipients will be tricky. (Pretty sure a release could interrupt the thread, but not 100% sure.)

On the other hand, it does not seem reasonable to ask them to send the campaign by batches of 1000 recipients.

Ideally, they would do it with the assistance of someone on our side.

Errattum: they won’t be able to resend the campaign and send it multiple times to some users. But they could be tempted to create a copy and try again. They should not.