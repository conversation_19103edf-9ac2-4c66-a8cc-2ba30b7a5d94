It seems to emerge from discussions with customers that they all have slightly different needs when it comes to what an project moderator should be able to do or not.

- Some customers welcomed the idea that PMs could create projects, while others would still rather have them ask the admin to do it (this touchpoint gives the admin a chance to brief and discuss the project with the PM).
- In some cases, it seems to be acceptable for PMs to publish projects once they are approved, while in other cases, customers prefer that this responsibility remains with the admin.
- Some customers trust certain PMs enough to let them publish projects without approval, while others treat all PMs the same.

**Example of actions:**

- approve_project
- create_project
- publish_project
- publish_approved_project

I see two main approaches here. Either we try to identify the most common needs and add some flexibility through simple global settings (e.g. `enable approval step`), or we can build a more advanced and flexible permission system to support additional roles, including ideally custom roles. Of course, this is not as clear-cut, but this can give a rough sense of the two extremes.