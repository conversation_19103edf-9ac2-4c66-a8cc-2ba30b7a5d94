
      ~ ordered_cache_behavior {
          ~ allowed_methods        = [
              - "DELETE",
                "GET",
                "HEAD",
              - "OPTIONS",
              - "PATCH",
              - "POST",
              - "PUT",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
          ~ compress               = false -> true
            default_ttl            = 0
            max_ttl                = 0
            min_ttl                = 0
          ~ path_pattern           = "/workshops*" -> "/uploads/*"
            smooth_streaming       = false
          ~ target_origin_id       = "ELB-cl2-prd-benelux-load-balancer-47306379" -> "S3-cl2-tenants"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

          ~ forwarded_values {
                headers                 = [
                    "*",
                ]
              ~ query_string            = true -> false
                query_string_cache_keys = []

              ~ cookies {
                  ~ forward           = "all" -> "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
          ~ allowed_methods        = [
                "GET",
                "HEAD",
              + "DELETE",
              + "OPTIONS",
              + "PATCH",
              + "POST",
              + "PUT",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
          ~ compress               = true -> false
          ~ default_ttl            = 86400 -> 0
          ~ max_ttl                = 86400 -> 0
            min_ttl                = 0
          ~ path_pattern           = "/fonts/*" -> "/workshops*"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-cms" -> "LB-cluster"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

          ~ forwarded_values {
              ~ headers                 = [
                  + "*",
                ]
              ~ query_string            = false -> true
                query_string_cache_keys = []

              ~ cookies {
                  ~ forward           = "none" -> "all"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
            path_pattern           = "*.js"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-front-production-benelux" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
          ~ path_pattern           = "/uploads/*" -> "*.svg"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-tenants-production-benelux" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
          ~ path_pattern           = "*.svg" -> "*.woff2"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-front-production-benelux" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
          ~ path_pattern           = "*.woff2" -> "*.woff"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-front-production-benelux" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
          ~ path_pattern           = "*.woff" -> "*.ttf"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-front-production-benelux" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
          ~ path_pattern           = "*.ttf" -> "*.jpg"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-front-production-benelux" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
          ~ path_pattern           = "*.jpg" -> "*.png"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-front-production-benelux" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
          ~ path_pattern           = "*.png" -> "*.css"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-front-production-benelux" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
          ~ compress               = false -> true
            default_ttl            = 86400
            max_ttl                = 31536000
            min_ttl                = 0
          ~ path_pattern           = "/fragments/*" -> "/.well-known/security.txt"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-cms" -> "S3-cl2-front"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
            default_ttl            = 86400
          ~ max_ttl                = 31536000 -> 86400
            min_ttl                = 0
          ~ path_pattern           = "*.css" -> "/fragments/*"
            smooth_streaming       = false
          ~ target_origin_id       = "S3-cl2-front-production-benelux" -> "S3-cl2-cms"
            trusted_key_groups     = []
            trusted_signers        = []
          ~ viewer_protocol_policy = "allow-all" -> "redirect-to-https"

            forwarded_values {
                headers                 = []
                query_string            = false
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
          ~ default_ttl            = 86400 -> 60
          ~ max_ttl                = 31536000 -> 60
          ~ min_ttl                = 0 -> 60
            path_pattern           = "/sitemap.xml"
            smooth_streaming       = false
          ~ target_origin_id       = "ELB-cl2-prd-benelux-load-balancer-47306379" -> "LB-cluster"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

            forwarded_values {
                headers                 = [
                    "Host",
                ]
                query_string            = true
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
            allowed_methods        = [
                "GET",
                "HEAD",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
            compress               = true
          ~ default_ttl            = 120 -> 60
          ~ max_ttl                = 120 -> 60
          ~ min_ttl                = 120 -> 60
          ~ path_pattern           = "/widgets/*" -> "/robots.txt"
            smooth_streaming       = false
          ~ target_origin_id       = "ELB-cl2-prd-benelux-load-balancer-47306379" -> "LB-cluster"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

          ~ forwarded_values {
              ~ headers                 = [
                  - "Accept-Language",
                    "Host",
                ]
                query_string            = true
                query_string_cache_keys = []

                cookies {
                    forward           = "none"
                    whitelisted_names = []
                }
            }
        }
      ~ ordered_cache_behavior {
          ~ allowed_methods        = [
              - "DELETE",
                "GET",
                "HEAD",
              - "OPTIONS",
              - "PATCH",
              - "POST",
              - "PUT",
            ]
            cached_methods         = [
                "GET",
                "HEAD",
            ]
          ~ compress               = false -> true
          ~ default_ttl            = 86400 -> 120
          ~ max_ttl                = 31536000 -> 120
          ~ min_ttl                = 0 -> 120
          ~ path_pattern           = "/admin_templates_api/*" -> "/widgets/*"
            smooth_streaming       = false
          ~ target_origin_id       = "GlobalLoadBalancer" -> "LB-cluster"
            trusted_key_groups     = []
            trusted_signers        = []
            viewer_protocol_policy = "redirect-to-https"

          ~ forwarded_values {
              ~ headers                 = [
                  - "*",
                  + "Accept-Language",
                  + "Host",
                ]
                query_string            = true
                query_string_cache_keys = []

              ~ cookies {
                  ~ forward           = "all" -> "none"
                    whitelisted_names = []
                }
            }
        }
      + ordered_cache_behavior {
          + allowed_methods        = [
              + "DELETE",
              + "GET",
              + "HEAD",
              + "OPTIONS",
              + "PATCH",
              + "POST",
              + "PUT",
            ]
          + cached_methods         = [
              + "GET",
              + "HEAD",
            ]
          + compress               = true
          + min_ttl                = 0
          + path_pattern           = "/admin_templates_api/*"
          + target_origin_id       = "LB-global"
          + viewer_protocol_policy = "redirect-to-https"

          + forwarded_values {
              + headers                 = [
                  + "*",
                ]
              + query_string            = true
              + query_string_cache_keys = (known after apply)

              + cookies {
                  + forward = "all"
                }
            }
        }

      - origin {
          - connection_attempts = 3 -> null
          - connection_timeout  = 10 -> null
          - domain_name         = "cl2-prd-benelux-load-balancer-47306379.eu-central-1.elb.amazonaws.com" -> null
          - origin_id           = "ELB-cl2-prd-benelux-load-balancer-47306379" -> null

          - custom_origin_config {
              - http_port                = 80 -> null
              - https_port               = 443 -> null
              - origin_keepalive_timeout = 10 -> null
              - origin_protocol_policy   = "http-only" -> null
              - origin_read_timeout      = 60 -> null
              - origin_ssl_protocols     = [
                  - "TLSv1",
                  - "TLSv1.1",
                  - "TLSv1.2",
                ] -> null
            }
        }
      - origin {
          - connection_attempts = 3 -> null
          - connection_timeout  = 10 -> null
          - domain_name         = "templates.hq.citizenlab.co" -> null
          - origin_id           = "GlobalLoadBalancer" -> null

          - custom_origin_config {
              - http_port                = 80 -> null
              - https_port               = 443 -> null
              - origin_keepalive_timeout = 5 -> null
              - origin_protocol_policy   = "https-only" -> null
              - origin_read_timeout      = 60 -> null
              - origin_ssl_protocols     = [
                  - "TLSv1",
                  - "TLSv1.1",
                  - "TLSv1.2",
                ] -> null
            }
        }
      - origin {
          - connection_attempts = 3 -> null
          - connection_timeout  = 10 -> null
          - domain_name         = "cl2-cms.s3.amazonaws.com" -> null
          - origin_id           = "S3-cl2-cms" -> null
        }
      - origin {
          - connection_attempts = 3 -> null
          - connection_timeout  = 10 -> null
          - domain_name         = "cl2-front-production-benelux.s3.amazonaws.com" -> null
          - origin_id           = "S3-cl2-front-production-benelux" -> null
        }
      - origin {
          - connection_attempts = 3 -> null
          - connection_timeout  = 10 -> null
          - domain_name         = "cl2-tenants-production-benelux.s3.amazonaws.com" -> null
          - origin_id           = "S3-cl2-tenants-production-benelux" -> null
        }
      + origin {
          + connection_attempts = 3
          + connection_timeout  = 10
          + domain_name         = "cl2-prd-benelux-load-balancer-47306379.eu-central-1.elb.amazonaws.com"
          + origin_id           = "LB-cluster"

          + custom_origin_config {
              + http_port                = 80
              + https_port               = 443
              + origin_keepalive_timeout = 5
              + origin_protocol_policy   = "http-only"
              + origin_read_timeout      = 60
              + origin_ssl_protocols     = [
                  + "TLSv1.2",
                ]
            }
        }
      + origin {
          + connection_attempts = 3
          + connection_timeout  = 10
          + domain_name         = "templates.hq.citizenlab.co"
          + origin_id           = "LB-global"

          + custom_origin_config {
              + http_port                = 80
              + https_port               = 443
              + origin_keepalive_timeout = 5
              + origin_protocol_policy   = "https-only"
              + origin_read_timeout      = 30
              + origin_ssl_protocols     = [
                  + "TLSv1.2",
                ]
            }
        }
      + origin {
          + connection_attempts = 3
          + connection_timeout  = 10
          + domain_name         = "cl2-cms.s3.amazonaws.com"
          + origin_id           = "S3-cl2-cms"
        }
      + origin {
          + connection_attempts = 3
          + connection_timeout  = 10
          + domain_name         = "cl2-front-production-benelux.s3.amazonaws.com"
          + origin_id           = "S3-cl2-front"
        }
      + origin {
          + connection_attempts = 3
          + connection_timeout  = 10
          + domain_name         = "cl2-tenants-production-benelux.s3.amazonaws.com"
          + origin_id           = "S3-cl2-tenants"
        }

        restrictions {
            geo_restriction {
                locations        = []
                restriction_type = "none"
            }
        }

        viewer_certificate {
            acm_certificate_arn            = "arn:aws:acm:us-east-1:389841910643:certificate/148c91cf-bef3-4ec7-afd2-8d38cb834b0d"
            cloudfront_default_certificate = false
            minimum_protocol_version       = "TLSv1.2_2021"
            ssl_support_method             = "sni-only"
        }
    }
