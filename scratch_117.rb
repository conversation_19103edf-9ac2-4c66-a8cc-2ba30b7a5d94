task add_project_id_to_activities_rails: :environment do
  Rails.application.eager_load!

  Tenant.switch_each do |tenant|
    models_with_project_id = ActiveRecord::Base.descendants.select do |klass|
      klass.new.respond_to?(:project_id)
    rescue Exception
      false
    end

    total_nb_records = 0
    models_with_project_id.each do |model_class|
      Activity.includes(:item)
              .where(item_type: model_class.name, project_id: nil)
              .find_each do |activity|
        next unless (project_id = activity.item.project_id)

        activity.update!(project_id: project_id)
        total_nb_records += 1
      end
    end
  rescue => e
    puts({ tenant_id: tenant.id, message: 'failure', nb_records: total_nb_records, error: e, backtrace: e.backtrace }.to_json)
  else
    puts({ tenant_id: tenant.id, message: 'success', nb_records: total_nb_records }.to_json)
  end
end
