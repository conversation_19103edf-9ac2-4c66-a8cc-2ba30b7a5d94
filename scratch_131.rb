tenant = Tenant.find_by(name: 'FES')

tenants = Tenant.all.filter do |tenant|
  tenant.switch do
    config = AppConfiguration.instance
    style = config.style
    style.keys.to_set.intersection(['signedOutHeaderOverlayColor', 'signedOutHeaderOverlayOpacity', 'signedInHeaderOverlayOpacity'].to_set).any?
  end
end


tenants.map do |tenant|
  tenant.switch do
    config = AppConfiguration.instance
    style = config.style
    # remove keys from style
    style.except!('signedOutHeaderOverlayColor', 'signedOutHeaderOverlayOpacity', 'signedInHeaderOverlayOpacity')
    config.cleanup_settings
    config.style = style
    config.save!
  end
end

# signedOutHeaderOverlayColor
