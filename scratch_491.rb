folder.text_images.map do |ti|
  temp = TextImage.create!(imageable: folder, imageable_field: 'description_multiloc', image: ti.image.url)
  temp.destroy!
end

TextImage.create!(imageable_field: :description_multiloc, imageable: folder, remote_image_url: "http://shaping.salford.gov.uk/uploads/ec693d91-2e2c-43d7-915f-84f9f3e34786/text_image/image/44a6fe8c-7e76-4b9f-b679-e8041dbfb54b/c78232e3-a538-4a1d-819d-3b49e6b7faf4.jpeg")
TextImage.create!(imageable_field: :description_multiloc, imageable: folder, remote_image_url: "https://shaping.salford.gov.uk/uploads/ec693d91-2e2c-43d7-915f-84f9f3e34786/text_image/image/1cfcadc5-4a51-494e-be1b-9401efc5700b/1e4040f1-c6e0-427a-a594-c69bc0fc3901.jpeg")