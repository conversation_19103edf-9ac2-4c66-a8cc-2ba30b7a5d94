hosts = [
  'arlon.citizenlab.co',
  'bois-guillaume.citizenlab.co',
  'borgerlab.horsholm.dk',
  'borgernet.odsherred.dk',
  'demain.villedavray.fr',
  'denkmee.amstelveen.nl',
  'jeparticipe.rungis.fr',
  'jeparticipe.ville-bron.fr',
  'jeparticipe-cusset.com',
  'ikdenkmee.gavere.be',
  'iedereenmee.koksijde.be',
  'hallo.harelbeke.be'
]

counts = hosts.map do |host|
  Tenant.find_by(host: host).switch do
    values = User.pluck(:custom_field_values)

    {
      tenant: Tenant.current.id,
      tenant_id: host,
      gender: values.pluck("gender").tally,
      birthyear: values.pluck("birthyear").tally
    }
  end
end
