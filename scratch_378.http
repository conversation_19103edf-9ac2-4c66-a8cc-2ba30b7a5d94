### GET request to example server
GET https://examples.http-client.intellij.net/get$END$
    ?generated-in=RubyMine

###
# curl 'https://hello.saanich.ca/web_api/v1/projects/3d42a8e5-674a-4fa1-bb3e-ea8591f9416f' -X PATCH -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; rv:128.0) Gecko/20100101 Firefox/128.0' -H 'Accept: */*' -H 'Accept-Language: en-US,en;q=0.5' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'Origin: https://hello.saanich.ca' -H 'DNT: 1' -H 'Connection: keep-alive' -H 'Cookie: cl2_jwt=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' -H 'Sec-Fetch-Dest: empty' -H 'Sec-Fetch-Mode: no-cors' -H 'Sec-Fetch-Site: same-origin' -H 'TE: trailers' -H 'Content-Type: application/json' -H 'Authorization: Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' -H 'Priority: u=4' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' --data-raw '{"project":{"folder_id":null}}'
PATCH https://hello.saanich.ca/web_api/v1/projects/3d42a8e5-674a-4fa1-bb3e-ea8591f9416f
User-Agent: Mozilla/5.0 (Windows NT 10.0; rv:128.0) Gecko/20100101 Firefox/128.0
Accept: */*
Accept-Language: en-US,en;q=0.5
Accept-Encoding: gzip, deflate, br, zstd
Origin: https://hello.saanich.ca
DNT: 1
Connection: keep-alive
Cookie: cl2_jwt=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: no-cors
Sec-Fetch-Site: same-origin
TE: trailers
Authorization: Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Priority: u=4
Pragma: no-cache
Cache-Control: no-cache
Content-Type: application/json

{
  "project": {
    "folder_id": null
  }
}

<> 2024-08-05T154858.500.json

###

