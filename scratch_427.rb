# "paliers/tranches de taxation" in English:
def taxes(amount, brackets, rates)
  brackets = [0, *brackets, Float::INFINITY]
  tax = 0
  brackets.each_cons(2).with_index do |(lower, upper), i|
    if amount <= upper
      tax += (amount - lower) * rates[i]
      break
    else
      tax += (upper - lower) * rates[i]
    end
  end
  tax
end

# Test cases:


brackets = [12500, 25000, 50000, 100000, 150000, 200000, 250000, 500000]
rates =     [0.03, 0.04, 0.05, 0.07, 0.10, 0.14, 0.18, 0.24, 0.30]
new_rates = [0.03, 0.04, 0.05, 0.05, 0.05, 0.07, 0.09, 0.12, 0.15]

puts taxes(1000000, brackets, rates), taxes(1000000, brackets, new_rates)
puts

puts taxes(500000, brackets, rates), taxes(500000, brackets, new_rates)
puts

puts taxes(250000, brackets, rates), taxes(250000, brackets, new_rates)
puts

puts taxes(100000, brackets, rates), taxes(100000, brackets, new_rates)
puts

puts taxes(50000, brackets, rates), taxes(50000, brackets, new_rates)
puts

puts taxes(25000, brackets, rates), taxes(25000, brackets, new_rates)
puts




