** Invoke db:reset (first_time)
** Invoke db:drop (first_time)
** Invoke db:load_config (first_time)
** Invoke environment (first_time)
** Execute environment
** Execute db:load_config
** Invoke db:check_protected_environments (first_time)
** Invoke db:load_config
** Execute db:check_protected_environments
** Invoke apartment:drop (first_time)
** Execute apartment:drop
Dropping localhost tenant
** Execute db:drop
** Invoke db:drop:_unsafe (first_time)
** Invoke db:load_config
** Execute db:drop:_unsafe
Dropped database 'cl2_back_test'
** Invoke db:setup (first_time)
** Invoke db:create (first_time)
** Invoke db:load_config
** Execute db:create
Created database 'cl2_back_test'
** Invoke db:extensions (first_time)
** Invoke environment
** Execute db:extensions
** Invoke environment
** Invoke db:schema:load (first_time)
** Invoke db:load_config
** Invoke db:check_protected_environments
** Execute db:schema:load
** Invoke db:seed (first_time)
** Invoke db:load_config
** Execute db:seed
** Invoke db:abort_if_pending_migrations (first_time)
** Invoke db:load_config
** Execute db:abort_if_pending_migrations
** Invoke apartment:seed (first_time)
** Execute apartment:seed
Creating localhost tenant
Tried to create already existing tenant: Error while creating tenant localhost: PG::DuplicateSchema: ERROR:  schema "localhost" already exists
Seeding localhost tenant
** Execute db:setup
** Execute db:reset
