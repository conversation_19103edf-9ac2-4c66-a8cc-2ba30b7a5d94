[TAN-2315] Fix Bug in Posting Limits

The posting limits, which dictate the number of `Idea` records a user can create within a participation context, were previously encoded using two attributes in the `Phase` model:
- `posting_method` ('limited' or 'unlimited') indicating if the limit should be enforced
- `posting_limited_max` (integer) specifying the actual limit

This was a bit over-engineered since, today, all phases have a `posting_limit` of 1, and the limit is (in principle) never enforced except for native surveys.

However, these attributes were not updated when the participation method changed, leading to inconsistencies:
- Some native surveys allowed unlimited submissions
- Other participation types, like ideation, could enforce a limit of 1 idea per user

To resolve this issue and given the underuse of these attributes, we've decided to remove them and hardcode the limit (if any) at the participation method level.

<EMAIL>


