<!-- saved from url=(0091)https://mail.google.com/mail/u/0/?ik=9c7a941e05&view=om&permmsgid=msg-f:1834617208175324936 -->
<html lang="en-US"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><style data-emotion="mui-global" data-s=""></style><style data-emotion="mui" data-s=""></style><title>Original Message</title><link rel="shortcut icon" href="https://www.google.com/a/cpanel/govocal.com/images/favicon.ico" type="image/x-icon"><link rel="stylesheet" href="chrome-extension://emnoomldgleagdjapdeckpmebokijail/font/roboto-fonts.css" class="wtd-font"><link rel="stylesheet" href="chrome-extension://emnoomldgleagdjapdeckpmebokijail/font/unbounded-fonts.css" class="wtd-font"><script src="chrome-extension://chmpifjjfpeodjljjadlobceoiflhdid/gmail-event-proxy.js"></script><script src="chrome-extension://chmpifjjfpeodjljjadlobceoiflhdid/gmail-globals.js"></script><style type="text/css">
// v4 font rules
// cyrillic-ext
@font-face {
	font-family: 'Atlassian Sans Ext';
	font-style: normal;
	font-weight: 100 900;
	font-display: swap;
	src: url('chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-cyrillic-ext.woff2') format('woff2');
	unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

// cyrillic
@font-face {
	font-family: 'Atlassian Sans Ext';
	font-style: normal;
	font-weight: 100 900;
	font-display: swap;
	src: url('chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-cyrillic.woff2') format('woff2');
	unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

// vietnamese
@font-face {
	font-family: 'Atlassian Sans Ext';
	font-style: normal;
	font-weight: 100 900;
	font-display: swap;
	src: url('chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-vietnamese.woff2') format('woff2');
	unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0,
		U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}

// latin
@font-face {
	font-family: 'Atlassian Sans Ext';
	font-style: normal;
	font-weight: 100 900;
	font-display: swap;
	src: url('chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-latin.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304,
		U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

// latin-ext
@font-face {
	font-family: 'Atlassian Sans Ext';
	font-style: normal;
	font-weight: 100 900;
	font-display: swap;
	src: url('chrome-extension://liecbddmkiiihnedobmlmillhodjkdmb/fonts/AtlassianSans-latin-ext.woff2') format('woff2');
	unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308,
		U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113,
		U+2C60-2C7F, U+A720-A7FF;
}</style><style type="text/css">

  .lo-gmail-compose-button-dropdown-menu {
    margin: 0;
    box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.07), 0px 1px 4px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 8px;

    background: #fff;

    font-size: 14px;
    line-height: 22px;
    font-family: circular, Helvetica, sans-serif;
  }

  .lo-gmail-compose-button-dropdown-menu li {
    align-items: center;
    flex-direction: row;
    justify-content: flex-start;

    display: flex;

    border-radius: 4px;
    padding: 5px 8px 5px 10px;

    background-color: transparent;
    cursor: pointer;

    color: var(--lns-color-grey8);
  }

  .lo-gmail-compose-button-dropdown-menu li:hover {
    background-color: var(--lns-color-blurple);

    color: #fff;
  }

  .lo-gmail-compose-button-dropdown-menu li:hover svg path {
    fill: #fff;
  }

  .lo-gmail-compose-button-dropdown-menu-insert-latest
    .lo-universal-tooltip
    .lo-universal-tooltip-content {
    box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.07), 0px 1px 4px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 16px;

    background-color: #fff;

    position: relative;
  }

  .lo-gmail-compose-button-dropdown-menu-insert-latest
    .lo-universal-tooltip
    .lo-universal-tooltip-content:before {
    position: absolute;
    content: '';
    top: -16px;
    left: -16px;
    bottom: -16px;
    right: -16px;
  }

  .lo-gmail-compose-button-dropdown-menu-insert-latest .lo-universal-tooltip {
    display: none;
    opacity: 0;

    transition: opacity 0.2s ease-in-out;
  }

  .lo-gmail-compose-button-dropdown-menu-insert-latest:hover
    .lo-universal-tooltip {

    top: calc(100% - 240px);
    right: 100%;

    display: flex;
    opacity: 1;

  }

  .lo-gmail-compose-button-dropdown-menu
    li
    .lo-gmail-compose-dropdown-menu-label {
    margin-left: 10px;
  }

  .lo-gmail-compose-dropdown-item-tooltip {
    position: relative;

    width: 392px;
    height: 304px;
  }

  .lo-embed-loader-placeholder {
    position: absolute;
    top: calc(50% - 24px);
    left: calc(50% - 24px);
  }

  .lo-gmail-recorded-video-preview-embed-container {
    position: relative;

    width: 100%;
    min-height: 260px;
  }

  .lo-gmail-recorded-video-preview-content {
    flex-direction: column;

    display: flex;
  }

  .lo-gmail-recorded-video-preview-content
    .lo-gmail-recorded-video-preview-title {
    box-sizing: border-box;
  }

  .loom-surround-div .lo-gmail-compose-button {
    opacity: 0.8;
  }

  .loom-embedded-button {
    position: relative;
    z-index: 1;
    align-items: center;
    justify-content: center;

    display: flex;

    border-radius: 5px;
    width: 100%;
    height: 100%;

    cursor: default;

    text-align: center;
  }

  .loom-embedded-button:hover {
    background-color: var(--lns-color-grey1);
    cursor: pointer;
  }

  .loom-drop-down {
    position: absolute;
    bottom: 100%;

    display: none;

    min-width: 160px;

    background-color: var(--lns-color-white);
  }

  .loom-surround-div {
    align-items: center;
    justify-content: center;

    display: flex;

    width: 28px;
    height: 28px;
    padding: 4px;
    padding-right: 0px;
    padding-left: 8px;
  }
  .loom-icon {
    max-width: 70%;
    max-height: 70%;
    padding: 2px;
  }
  .loom-button-show {
    display: block;
  }
  .loom-button-td + td {
    padding-left: 5px;
  }
  .loom-button-td + td.inboxsdk__compose_actionToolbar {
    padding-left: 2px;
  }
  .loom-remove-modal {
    padding-right: 5%;
  }

  // following value is related to GMAIL_DIV_ALWAYS_HIDDEN in constants/gmail.js
  #loom-always-hidden-div {
    display: none;
  }

  .lo-universal-tooltip .arrow-right {
    flex-direction: column;
    height: 100%;
    right: calc(~'100% + 8px');
  }

  .lo-universal-tooltip .arrow-right .lo-universal-tooltip-content {
    content: ' ';

    position: absolute;

    border: solid transparent;
    border-width: 8px;
    width: 0;
    height: 0;

    pointer-events: none;
  }

  .lo-universal-tooltip {
    position: absolute;
    justify-content: center;

    display: flex;

    &.arrow-right,
    &.arrow-left {
      flex-direction: column;

      height: 100%;
    }

    &.arrow-top,
    &.arrow-bottom {
      flex-direction: row;

      width: 100%;
    }

    &.arrow-right {
      right: calc(~'100% + 8px');
    }

    &.arrow-bottom {
      bottom: calc(~'100% + 8px');
    }

    &.arrow-left {
      left: calc(~'100% + 8px');
    }

    &.arrow-top {
      top: calc(~'100% + 8px');
    }

    &.sticky-on-hover {
      pointer-events: all;
    }

    .lo-universal-tooltip-content {
      border-radius: var(--lns-radius-large);
      padding: 8px 12px;

      background-color: var(--lns-color-grey1);

      // originally font-size: @font-size-h5;
      font-size: var(--lns-fontSize-large);
      font-family: circular, Helvetica, sans-serif;
      color: var(--lns-color-white);

      .arrow-right .arrow-top .arrow-left .arrow-bottom &:after {
        content: ' ';

        position: absolute;

        border: solid transparent;
        border-width: 8px;
        width: 0;
        height: 0;

        pointer-events: none;
      }
      &:after {
        top: 50%;
        left: 100%;

        margin-top: 8px;
        border-left-color: var(--lns-color-grey1);
      }
      &:after {
        bottom: 100%;
        left: 50%;

        margin-left: 8px;
        border-bottom-color: var(--lns-color-grey1);
      }
      &:after {
        top: 50%;
        right: 100%;

        margin-top: 8px;
        border-right-color: var(--lns-color-grey1);
      }
      &:after {
        top: 100%;
        left: 50%;

        margin-left: 8px;
        border-top-color: var(--lns-color-grey1);
      }
    }
  }



</style><style data-emotion="css-global" data-s=""></style></head><body class="inboxsdk_hack_disableComposeSizeFixer" data-gptw="" cz-shortcut-listen="true"><div class="page-wrapper"><div class="top-area"><h3 class="page_title">Original Message</h3><div id="butterbar-container"><div id="butterbar-wrap"><div class="jfk-butterBar jfk-butterBar-info" aria-live="assertive" aria-atomic="true"><div id="status-messages"><div id="copy-error" style="display:none;">There was a problem copying the raw header and body.</div><div id="copy-success" style="display:none;"><span class="clipboard-icon"></span>Copied to clipboard!</div></div></div></div></div><table><tbody><tr><th>Message ID</th><td class="message_id">&lt;<EMAIL>&gt;</td></tr><tr><th>Created at:</th><td>Wed, Jun 11, 2025 at 9:35 AM (Delivered after 5 seconds)</td></tr><tr><th>From:</th><td>Crowdin Enterprise &lt;<EMAIL>&gt;</td></tr><tr><th>To:</th><td class="toRecipientsSelector"><EMAIL></td></tr><tr><th>Subject:</th><td>Verification code for Crowdin Enterprise account</td></tr><tr><th>SPF:</th><td><span class="authresult">SOFTFAIL</span> with IP 2600:1901:101:0:0:0:0:15 <a href="https://support.google.com/a?p=show_original&amp;hl=en" target="_blank" class="learn-more-link">Learn more</a></td></tr><tr><th>DKIM:</th><td><span class="authresult">'PASS'</span> with domain crowdin.com <a href="https://support.google.com/a?p=show_original&amp;hl=en" target="_blank" class="learn-more-link">Learn more</a></td></tr><tr><th>DMARC:</th><td><span class="authresult">'PASS'</span> <a href="https://support.google.com/a?p=show_original&amp;hl=en" target="_blank" class="learn-more-link">Learn more</a></td></tr></tbody></table><style type="text/css" nonce="">
body{font-family:"Arial","Helvetica",sans-serif!important;font-size:14px;margin:0}.page-wrapper{display:table;height:auto;width:100%}.page_title{font-size:20px;font-weight:normal;padding:20px 11% 0 11%}.message_id{color:#2e7d32}.top-area{background-color:#fff;display:table-row}table{border-collapse:collapse;margin:0 auto;width:80%}td,th{border-bottom:solid thin #e0e0e0;border-top:solid thin #e0e0e0;font-weight:normal}td{border-right:solid thin #e0e0e0;padding:8px}th{border-left:solid thin #e0e0e0;padding:12px 20px;text-align:left}.download-buttons,.learn-more-link{color:#15c;cursor:pointer;padding-left:5px;text-decoration:none}.authresult{text-transform:uppercase}.download-buttons{display:inline-block;font-size:14px;padding:20px}.more-actions{display:table;padding-bottom:25px;width:100%}.download-links{display:table-cell;width:39%}.header-body-button{display:table-cell;text-align:right;width:39%}#butterbar-wrap{display:inline-block}#butterbar-container{left:auto;position:absolute;text-align:center;top:18px;width:100%}.clipboard-icon{background-image:url(//ssl.gstatic.com/images/icons/material/system/1x/content_paste_black_24dp.png);background-position:center;background-repeat:no-repeat;-webkit-background-size:15px 15px;background-size:15px;padding:8px 20px 8px 8px}.copy-header-body-button.jfk-button.jfk-button-action{display:inline-block;padding:4px 6px;text-decoration:none}#copy-header-body-button{margin:10px}.spacing{display:table-cell;width:11%}.bottom-area{background-color:#eee;display:table-row;height:100%}.raw_message{border-top:solid thin #bdbdbd;display:table-cell;height:100%}.raw_message_text{background-color:#fff;border:solid thin #e0e0e0;border-radius:5px;color:#616161;height:100%;margin:25px 10%;padding:40px 30px;white-space:pre-wrap;word-break:break-word;word-wrap:break-word}.truncate_message{color:#d32f2f;padding:10px;text-align:center}.jfk-butterBar{-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;-webkit-box-shadow:0px 2px 4px rgba(0,0,0,.2);-moz-box-shadow:0px 2px 4px rgba(0,0,0,.2);box-shadow:0px 2px 4px rgba(0,0,0,.2);-webkit-transition:all 0s linear 1s,opacity 1s;-moz-transition:all 0s linear 1s,opacity 1s;-o-transition:all 0s linear 1s,opacity 1s;transition:all 0s linear 1s,opacity 1s;border-style:solid;border-width:0;font-size:11px;height:0;opacity:0;visibility:hidden;overflow:hidden;padding:0;text-align:center}.jfk-butterBar-info{background-color:#f9edbe;border-color:#f0c36d;color:#333}.jfk-butterBar-error{background-color:#484848;border-color:#202020;color:#fff}.jfk-butterBar-promo{background-color:#d6e9f8;border-color:#4d90f0;color:#333}.jfk-butterBar-warning{background-color:#dd4b39;border-color:#602019;color:#fff}.jfk-butterBar-shown{-webkit-transition:opacity 0.218s;-moz-transition:opacity 0.218s;-o-transition:opacity 0.218s;transition:opacity 0.218s;border-width:1px;min-height:14px;height:auto;opacity:1;visibility:visible;padding:6px 16px}.jfk-butterBar-mini.jfk-butterBar-shown{padding:2px 16px}.goog-inline-block{position:relative;display:-moz-inline-box;display:inline-block}* html .goog-inline-block{display:inline}*:first-child+html .goog-inline-block{display:inline}.jfk-button{-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;cursor:default;font-size:11px;font-weight:bold;text-align:center;white-space:nowrap;margin-right:16px;height:27px;line-height:27px;min-width:54px;outline:0;padding:0 8px}.jfk-button-hover{-webkit-box-shadow:0 1px 1px rgba(0,0,0,.1);-moz-box-shadow:0 1px 1px rgba(0,0,0,.1);box-shadow:0 1px 1px rgba(0,0,0,.1)}.jfk-button-selected{-webkit-box-shadow:inset 0 1px 2px rgba(0,0,0,.1);-moz-box-shadow:inset 0 1px 2px rgba(0,0,0,.1);box-shadow:inset 0 1px 2px rgba(0,0,0,.1)}.jfk-button .jfk-button-img{margin-top:-3px;vertical-align:middle}.jfk-button-label{margin-left:5px}.jfk-button-narrow{min-width:34px;padding:0}.jfk-button-collapse-left,.jfk-button-collapse-right{z-index:1}.jfk-button-collapse-left.jfk-button-disabled{z-index:0}.jfk-button-checked.jfk-button-collapse-left,.jfk-button-checked.jfk-button-collapse-right{z-index:2}.jfk-button-collapse-left:focus,.jfk-button-collapse-right:focus,.jfk-button-hover.jfk-button-collapse-left,.jfk-button-hover.jfk-button-collapse-right{z-index:3}.jfk-button-collapse-left{margin-left:-1px;-moz-border-radius-bottomleft:0;-moz-border-radius-topleft:0;-webkit-border-bottom-left-radius:0;-webkit-border-top-left-radius:0;border-bottom-left-radius:0;border-top-left-radius:0}.jfk-button-collapse-right{margin-right:0;-moz-border-radius-topright:0;-moz-border-radius-bottomright:0;-webkit-border-top-right-radius:0;-webkit-border-bottom-right-radius:0;border-top-right-radius:0;border-bottom-right-radius:0}.jfk-button.jfk-button-disabled:active{-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none}.jfk-button-action{-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none;background-color:#4d90fe;background-image:-webkit-linear-gradient(top,#4d90fe,#4787ed);background-image:-moz-linear-gradient(top,#4d90fe,#4787ed);background-image:-ms-linear-gradient(top,#4d90fe,#4787ed);background-image:-o-linear-gradient(top,#4d90fe,#4787ed);background-image:linear-gradient(top,#4d90fe,#4787ed);border:1px solid #3079ed;color:#fff}.jfk-button-action.jfk-button-hover{-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none;background-color:#357ae8;background-image:-webkit-linear-gradient(top,#4d90fe,#357ae8);background-image:-moz-linear-gradient(top,#4d90fe,#357ae8);background-image:-ms-linear-gradient(top,#4d90fe,#357ae8);background-image:-o-linear-gradient(top,#4d90fe,#357ae8);background-image:linear-gradient(top,#4d90fe,#357ae8);border:1px solid #2f5bb7;border-bottom-color:#2f5bb7}.jfk-button-action:focus{-webkit-box-shadow:inset 0 0 0 1px #fff;-moz-box-shadow:inset 0 0 0 1px #fff;box-shadow:inset 0 0 0 1px #fff;border:1px solid #fff;border:rgba(0,0,0,0) solid 1px;outline:1px solid #4d90fe;outline:rgba(0,0,0,0) 0}.jfk-button-action.jfk-button-clear-outline{-webkit-box-shadow:none;-moz-box-shadow:none;box-shadow:none;outline:none}.jfk-button-action:active{-webkit-box-shadow:inset 0 1px 2px rgba(0,0,0,.3);-moz-box-shadow:inset 0 1px 2px rgba(0,0,0,.3);box-shadow:inset 0 1px 2px rgba(0,0,0,.3);background:#357ae8;border:1px solid #2f5bb7;border-top:1px solid #2f5bb7}.jfk-button-action.jfk-button-disabled{background:#4d90fe;filter:alpha(opacity=50);opacity:.5}sentinel{}
</style><br><br><div class="more-actions"><div class="spacing"></div><div class="download-links"><a class="download-buttons" href="https://mail.google.com/mail/u/0?view=att&amp;th=1975dea1701bef08&amp;attid=0&amp;disp=comp&amp;safe=1&amp;zw">Download Original</a></div><div class="header-body-button"><div class="copy-header-body-button jfk-button jfk-button-action" id="copy-header-body-button" style="" tabindex="0">Copy to clipboard</div></div><div class="spacing"></div></div></div><script type="text/javascript" nonce="">
(function(){var aa=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ba=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},ca=ba(this),da=function(a,b){if(b)a:{var c=ca;a=a.split(".");for(var d=
0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&aa(c,a,{configurable:!0,writable:!0,value:b})}};da("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;aa(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};
return e});var ea=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},k;if(typeof Object.setPrototypeOf=="function")k=Object.setPrototypeOf;else{var l;a:{var fa={a:!0},ha={};try{ha.__proto__=fa;l=ha.a;break a}catch(a){}l=!1}k=l?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var ia=k,ja=function(a,b){a.prototype=ea(b.prototype);a.prototype.constructor=a;if(ia)ia(a,b);else for(var c in b)if(c!=
"prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.s=b.prototype};da("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});var m=this||self,ka=function(a){var b=typeof a;b=b!="object"?b:a?Array.isArray(a)?"array":b:"null";return b=="array"||b=="object"&&typeof a.length=="number"},n=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"},la=function(a,b,c){return a.call.apply(a.bind,arguments)},
ma=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},p=function(a,b,c){p=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?la:ma;return p.apply(null,arguments)},q=function(a,b){function c(){}c.prototype=b.prototype;a.s=b.prototype;a.prototype=
new c;a.prototype.constructor=a;a.R=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var na;var oa=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},pa=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,
b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)};function qa(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]}var ra=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?\x3d[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");var sa;sa=function(a){if(!a)return a;var b=(typeof a==="object"?a.href:a).match(ra);a=b[1];if(a!=="http"&&a!=="https")a=a||"";else{a=
b[1];var c=b[3],d=b[4],e=b[5];b=b[6];var f="";a&&(f+=a+":");c&&(f=f+"//"+c,d&&(f+=":"+d));e&&(f+=e);b&&(f+="?"+b);a=f}return a};var r=function(){this.o=this.o;this.D=this.D};r.prototype.o=!1;r.prototype.dispose=function(){this.o||(this.o=!0,this.h())};r.prototype[Symbol.dispose]=function(){this.dispose()};r.prototype.h=function(){if(this.D)for(;this.D.length;)this.D.shift()()};a:for(var ta=["CLOSURE_FLAGS"],t=m,u=0;u<ta.length;u++)if(t=t[ta[u]],t==null)break a;function v(){var a=m.navigator;return a&&
(a=a.userAgent)?a:""}var w=function(){r.call(this);this.i=0;this.g=null};ja(w,r);w.prototype.init=function(){this.g=[]};var x=new w,ua=function(a){this.e=a};var y=function(a,b){this.type=a;this.target=b;this.defaultPrevented=!1};y.prototype.g=function(){this.defaultPrevented=!0};var va=function(){if(!m.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};m.addEventListener("test",c,b);m.removeEventListener("test",
c,b)}catch(d){}return a}();var wa=v().indexOf("Gecko")!=-1&&!(v().toLowerCase().indexOf("webkit")!=-1&&v().indexOf("Edge")==-1)&&!(v().indexOf("Trident")!=-1||v().indexOf("MSIE")!=-1)&&v().indexOf("Edge")==-1,xa=v().toLowerCase().indexOf("webkit")!=-1&&v().indexOf("Edge")==-1;var z=function(a,b){y.call(this,a?a.type:"");this.relatedTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=
!1;this.state=null;this.pointerId=0;this.pointerType="";this.h=null;a&&this.init(a,b)};q(z,y);z.prototype.init=function(a){var b=this.type=a.type,c=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;var d=a.relatedTarget;d||(b=="mouseover"?d=a.fromElement:b=="mouseout"&&(d=a.toElement));this.relatedTarget=d;c?(this.clientX=c.clientX!==void 0?c.clientX:c.pageX,this.clientY=c.clientY!==void 0?c.clientY:c.pageY,this.screenX=c.screenX||0,this.screenY=
c.screenY||0):(this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.h=a;a.defaultPrevented&&z.s.g.call(this)};z.prototype.g=function(){z.s.g.call(this);var a=
this.h;a.preventDefault?a.preventDefault():a.returnValue=!1};var A="closure_listenable_"+(Math.random()*1E6|0);var ya=0;var za=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.h=e;this.key=++ya;this.g=this.F=!1},B=function(a){a.g=!0;a.listener=null;a.proxy=null;a.src=null;a.h=null};function Aa(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function C(a){this.src=a;this.g={};this.h=0}C.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=
this.g[f]=[],this.h++);var g=D(a,b,d,e);g>-1?(b=a[g],c||(b.F=!1)):(b=new za(b,this.src,f,!!d,e),b.F=c,a.push(b));return b};var Ba=function(a,b){var c=b.type;if(c in a.g){var d=a.g[c],e=oa(d,b),f;(f=e>=0)&&Array.prototype.splice.call(d,e,1);f&&(B(b),a.g[c].length==0&&(delete a.g[c],a.h--))}},D=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.g&&f.listener==b&&f.capture==!!c&&f.h==d)return e}return-1};var E="closure_lm_"+(Math.random()*1E6|0),F={},Ca=0,G=function(a,b,c,d,e){if(d&&d.once)return Da(a,
b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)G(a,b[f],c,d,e);return null}c=H(c);return a&&a[A]?a.l.add(String(b),c,!1,n(d)?!!d.capture:!!d,e):Ea(a,b,c,!1,d,e)},Ea=function(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=n(e)?!!e.capture:!!e,h=I(a);h||(a[E]=h=new C(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Fa();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)va||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Ga(b.toString()),
d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Ca++;return c},Fa=function(){var a=Ha,b=function(c){return a.call(b.src,b.listener,c)};return b},Da=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)Da(a,b[f],c,d,e);return null}c=H(c);return a&&a[A]?a.l.add(String(b),c,!0,n(d)?!!d.capture:!!d,e):Ea(a,b,c,!0,d,e)},Ia=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Ia(a,b[f],c,d,e);
else(d=n(d)?!!d.capture:!!d,c=H(c),a&&a[A])?(a=a.l,b=String(b).toString(),b in a.g&&(f=a.g[b],c=D(f,c,d,e),c>-1&&(B(f[c]),Array.prototype.splice.call(f,c,1),f.length==0&&(delete a.g[b],a.h--)))):a&&(a=I(a))&&(b=a.g[b.toString()],a=-1,b&&(a=D(b,c,d,e)),(c=a>-1?b[a]:null)&&J(c))},J=function(a){if(typeof a!=="number"&&a&&!a.g){var b=a.src;if(b&&b[A])Ba(b.l,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Ga(c),d):b.addListener&&b.removeListener&&
b.removeListener(d);Ca--;(c=I(b))?(Ba(c,a),c.h==0&&(c.src=null,b[E]=null)):B(a)}}},Ga=function(a){return a in F?F[a]:F[a]="on"+a},Ha=function(a,b){if(a.g)a=!0;else{b=new z(b,this);var c=a.listener,d=a.h||a.src;a.F&&J(a);a=c.call(d,b)}return a},I=function(a){a=a[E];return a instanceof C?a:null},K="__closure_events_fn_"+(Math.random()*1E9>>>0),H=function(a){if(typeof a==="function")return a;a[K]||(a[K]=function(b){return a.handleEvent(b)});return a[K]};var L=function(){r.call(this);this.l=new C(this)};
q(L,r);L.prototype[A]=!0;L.prototype.removeEventListener=function(a,b,c,d){Ia(this,a,b,c,d)};L.prototype.h=function(){L.s.h.call(this);if(this.l){var a=this.l,b=0,c;for(c in a.g){for(var d=a.g[c],e=0;e<d.length;e++)++b,B(d[e]);delete a.g[c];a.h--}}};function M(a,b,c){r.call(this);this.i=a;this.C=b||0;this.j=c;this.l=p(this.B,this)}q(M,r);M.prototype.g=0;M.prototype.h=function(){M.s.h.call(this);this.isActive()&&m.clearTimeout(this.g);this.g=0;delete this.i;delete this.j};M.prototype.start=function(a){this.isActive()&&
m.clearTimeout(this.g);this.g=0;var b=this.l;a=a!==void 0?a:this.C;if(typeof b!=="function")if(b&&typeof b.handleEvent=="function")b=p(b.handleEvent,b);else throw Error("Invalid listener argument");this.g=Number(a)>2147483647?-1:m.setTimeout(b,a||0)};M.prototype.isActive=function(){return this.g!=0};M.prototype.B=function(){this.g=0;this.i&&this.i.call(this.j)};var N=function(a,b){return typeof b==="string"?a.getElementById(b):b},Ka=function(a,b){Aa(b,function(c,d){d=="style"?a.style.cssText=c:d==
"class"?a.className=c:d=="for"?a.htmlFor=c:Ja.hasOwnProperty(d)?a.setAttribute(Ja[d],c):d.lastIndexOf("aria-",0)==0||d.lastIndexOf("data-",0)==0?a.setAttribute(d,c):a[d]=c})},Ja={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"},La=function(a,b,c,d){function e(h){h&&b.appendChild(typeof h==="string"?a.createTextNode(h):
h)}for(;d<c.length;d++){var f=c[d];if(!ka(f)||n(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(n(f)){var g=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){g=typeof f.item=="function";break a}}g=!1}pa(g?qa(f):f,e)}}},Ma=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)},Na=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0},
Oa=function(a){a&&a.parentNode&&a.parentNode.removeChild(a)},O=function(a){return a.nodeType==9?a:a.ownerDocument||a.document},P=function(){this.g=m.document||document};P.prototype.m=function(){return N(this.g)};P.prototype.i=function(a,b,c){var d=this.g,e=arguments,f=e[1],g=Ma(d,String(e[0]));f&&(typeof f==="string"?g.className=f:Array.isArray(f)?g.className=f.join(" "):Ka(g,f));e.length>2&&La(d,g,e,2);return g};P.prototype.h=function(a,b){La(O(a),a,arguments,1)};var Pa=function(){};var Qa=function(){};
var Q=function(a){this.g=a};q(Q,Qa);var Ra=function(a){var b=O(a).createRange();if(a.nodeType==3)b.setStart(a,0),b.setEnd(a,a.length);else if(Na(a)||a.nodeType==3){for(var c,d=a;(c=d.firstChild)&&(Na(c)||c.nodeType==3);)d=c;b.setStart(d,0);for(d=a;(c=d.lastChild)&&(Na(c)||c.nodeType==3);)d=c;b.setEnd(d,d.nodeType==1?d.childNodes.length:d.length)}else c=a.parentNode,a=Array.prototype.indexOf.call(c.childNodes,a),b.setStart(c,a),b.setEnd(c,a+1);return b},Sa=function(a,b,c,d){var e=O(a).createRange();
e.setStart(a,b);e.setEnd(c,d);return e};Q.prototype.h=function(a){a.removeAllRanges();a.addRange(this.g)};function R(a){this.g=a}q(R,Q);R.prototype.h=function(a,b){!b||this.g.collapsed?R.s.h.call(this,a,b):(a.collapse(this.g.endContainer,this.g.endOffset),a.extend(this.g.startContainer,this.g.startOffset))};function S(a){this.g=a}q(S,Q);S.prototype.h=function(a,b){b?a.setBaseAndExtent(this.g.endContainer,this.g.endOffset,this.g.startContainer,this.g.startOffset):a.setBaseAndExtent(this.g.startContainer,
this.g.startOffset,this.g.endContainer,this.g.endOffset)};var Ta=function(){this.h=this.j=this.i=this.o=this.g=null;this.l=!1};q(Ta,Pa);var T=function(a){var b;if(!(b=a.g)){b=a.o||(a.o=T(a).g.startContainer);var c=a.i!=null?a.i:a.i=T(a).g.startOffset,d=a.j||(a.j=T(a).g.endContainer),e=a.h!=null?a.h:a.h=T(a).g.endOffset;b=xa?new S(Sa(b,c,d,e)):wa?new R(Sa(b,c,d,e)):new Q(Sa(b,c,d,e));b=a.g=b}return b};var U=function(a){r.call(this);this.i=a;this.g={}};q(U,r);var Ua=[],Va=function(a,b,c,d){Array.isArray(c)||
(c&&(Ua[0]=c.toString()),c=Ua);for(var e=0;e<c.length;e++){var f=G(b,c[e],d||a.handleEvent,!1,a.i||a);if(!f)break;a.g[f.key]=f}},Wa=function(a){Aa(a.g,function(b,c){this.g.hasOwnProperty(c)&&J(b)},a);a.g={}};U.prototype.h=function(){U.s.h.call(this);Wa(this)};U.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};var V=function(a,b){a.style.display=b?"":"none"};var W=function(){};W.g=void 0;W.h=function(){W.g||(W.g=new W)};var X=function(a){L.call(this);a||(a=
na||(na=new P));this.B=a;this.v=!1;this.g=null;this.i=void 0;this.G=this.C=null};q(X,L);W.h();X.prototype.m=function(){return this.g};var Xa=function(a){a.i||(a.i=new U(a));return a.i};X.prototype.H=function(){this.g=Ma(this.B.g,"DIV")};var Za=function(a){a.v=!0;Ya(a,function(b){!b.v&&b.m()&&Za(b)})},$a=function(a){Ya(a,function(b){b.v&&$a(b)});a.i&&Wa(a.i);a.v=!1};X.prototype.h=function(){this.v&&$a(this);this.i&&(this.i.dispose(),delete this.i);Ya(this,function(a){a.dispose()});this.g&&Oa(this.g);
this.C=this.g=this.G=null;X.s.h.call(this)};var Ya=function(a,b){a.G&&a.G.forEach(b,void 0)};var ab;var bb=function(a,b,c){Array.isArray(c)&&(c=c.join(" "));var d="aria-"+b;c===""||c==void 0?(ab||(c={},ab=(c.atomic=!1,c.autocomplete="none",c.dropeffect="none",c.haspopup=!1,c.live="off",c.multiline=!1,c.multiselectable=!1,c.orientation="vertical",c.readonly=!1,c.relevant="additions text",c.required=!1,c.sort="none",c.busy=!1,c.disabled=!1,c.hidden=!1,c.invalid="false",c)),c=ab,b in c?a.setAttribute(d,
c[b]):a.removeAttribute(d)):a.setAttribute(d,c)};var cb=function(a){return typeof a.className=="string"?a.className:a.getAttribute&&a.getAttribute("class")||""},db=function(a,b){typeof a.className=="string"?a.className=b:a.setAttribute&&a.setAttribute("class",b)},eb=function(a,b){a.classList?b=a.classList.contains(b):(a=a.classList?a.classList:cb(a).match(/\S+/g)||[],b=oa(a,b)>=0);return b},fb=function(a,b){if(a.classList)a.classList.add(b);else if(!eb(a,b)){var c=cb(a);db(a,c+(c.length>0?" "+b:b))}},
gb=function(a,b){a.classList?a.classList.remove(b):eb(a,b)&&db(a,Array.prototype.filter.call(a.classList?a.classList:cb(a).match(/\S+/g)||[],function(c){return c!=b}).join(" "))};var Y=function(a,b){X.call(this,b);this.j=a};q(Y,X);Y.prototype.A="info";Y.prototype.u=!1;var hb={info:"jfk-butterBar-info",error:"jfk-butterBar-error",warning:"jfk-butterBar-warning",promo:"jfk-butterBar-promo"},ib=function(a,b){if(a.g){var c=a.m(),d=hb[b];gb(c,hb[a.A]);fb(c,d)}a.A=b};Y.prototype.isVisible=function(){var a=
this.m();return a!=null&&eb(a,"jfk-butterBar-shown")};Y.prototype.H=function(){this.g=this.B.i("DIV","jfk-butterBar");var a=this.m();a&&(bb(a,"live","assertive"),bb(a,"atomic","true"));this.j=this.j;if(a=this.m()){for(var b=this.B,c;c=a.firstChild;)a.removeChild(c);b.h(a,this.j)}this.u=this.u;(a=this.m())&&(this.u?fb(a,"jfk-butterBar-mini"):gb(a,"jfk-butterBar-mini"));ib(this,this.A)};var Z=function(a){X.call(this,a);this.u=N(document,"copy-header-body-button");this.j=N(document,"raw_message_text");
this.J=N(document,"copy-success");this.I=N(document,"copy-error");N(document,"bottom-area");a=new Y(N(document,"status-messages"));var b=N(document,"butterbar-wrap");if(a.v)throw Error("Component already rendered");a.g||a.H();b?b.insertBefore(a.g,null):a.B.g.body.appendChild(a.g);a.C&&!a.C.v||Za(a);ib(a,"info");this.A=a;this.O=new M(this.P,1E4,this);a=document;var c;a.getElementsByClassName?c=a.getElementsByClassName("raw_message")[0]:c=document.querySelector(".raw_message");this.M=c||null;this.L=
this.j.getAttribute("class")};ja(Z,X);Z.prototype.K=function(){var a=!1;this.j.removeAttribute("class");Oa(this.j);document.body.appendChild(this.j);try{var b=this.j;var c=xa?new S(Ra(b)):wa?new R(Ra(b)):new Q(Ra(b));var d=new Ta;d.g=c;d.l=!1;var e=T(d),f=O(e.g.startContainer);e.h((f?f.defaultView:window).getSelection(),d.l);a=document.execCommand("copy")}catch(jb){var g=Error("Could not copy raw header and body to clipboard.");if(g instanceof Object&&!Object.isFrozen(g)){var h=sa(g.fileName||g.filename||
g.sourceURL||m.$googDebugFname||location.href);try{g.fileName=h}catch(kb){}}if(x.i>=3)throw Error("Recursive loop detected while trying to report exception. Message: null");x.i++;try{x.o||x.g&&x.g.length<10&&x.g.push(new ua(g))}finally{x.i--}}finally{Oa(this.j),this.M.appendChild(this.j),this.j.setAttribute("class",this.L)}c=this.A.m();fb(c,"jfk-butterBar-shown");this.O.start(1E4);a?(V(this.J,!0),V(this.I,!1)):(V(this.J,!1),V(this.I,!0))};Z.prototype.P=function(){var a=this.A.m();gb(a,"jfk-butterBar-shown")};
Z.prototype.N=function(a){a.keyCode==13&&this.K()};G(window,"load",function(){var a=new Z;if(document.queryCommandSupported("copy")){V(a.u,!0);var b=Xa(a),c=a.u,d=p(a.K,a);Va(b,c,"click",d);b=Xa(a);c=a.u;a=p(a.N,a);Va(b,c,"keypress",a)}})}).call(this);
</script><div id="bottom-area" class="bottom-area"><div class="raw_message"><pre class="raw_message_text" id="raw_message_text">Delivered-To: <EMAIL>
Received: by 2002:a05:7108:b187:b0:45e:228d:14e1 with SMTP id oa7csp2749269gdb;
        Wed, 11 Jun 2025 00:35:12 -0700 (PDT)
X-Forwarded-Encrypted: i=6; AJvYcCWDac2I+lgbeeNLo+ZAybVyeLYd+OTZp+hd1OLCDh74xmiAGZJn5eqVTURdLg19ycJ3yG1cGsRjpTQ=@govocal.com
X-Google-Smtp-Source: AGHT+IGs2IK3P24iT6Ph8FDdqC7X4Mjpqs4UkpOh9sAq5FuO6qhwU62hFr+dFPyP7oLvic6vZ9LD
X-Received: by 2002:a17:907:94d2:b0:ad8:8cd8:a3b7 with SMTP id a640c23a62f3a-ade8953c1c9mr202610566b.23.1749627311938;
        Wed, 11 Jun 2025 00:35:11 -0700 (PDT)
ARC-Seal: i=5; a=rsa-sha256; t=1749627311; cv=pass;
        d=google.com; s=arc-20240605;
        b=U2CYxTFwkssUZ0wxzc5Fs+hJtBjVIOT/r+JMwOrlf6qUHgIXQf3kBhp1WScDEnIy6Y
         Qan0fiX7xzqUZmNYeA/0zmmDZbn/zZxfNIHMamDC8UCLf+nA6JB1fMg4xZTB2ErV9Zke
         fDr/UfrepOdKqzxDV0yJ2fzyjUgkmgR+e+1HR+eR4wEVowY+4CaZhH511LDuegZ+LpVJ
         sKEi/kr53q/YW6irbVpPNRaA2yxVktvH2UakBH835XV5SrTsJ0M2tHFwNwmtYl7wYlpn
         1v6unWdV24jet3TO+suIhx30UZoi5vVhFmXvzmapdfHARsoO124DTS72xBofQS+plEVh
         UXAQ==
ARC-Message-Signature: i=5; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=feedback-id:date:mime-version:message-id:subject:to:from
         :dkim-signature:dkim-signature:delivered-to;
        bh=0QH9SMRKJAqgWvNWmnnnsQ4pvddJ0EMjObzMb7Pog4c=;
        fh=045a09nkcwf/CCThEPT0cheCJFQWHIY79QvYUv9Hb6w=;
        b=fI1NCfiyfTn2K/A8GXA30R4viQtoX+CzOI7OFJR8CA1FH9/FOdxMdEznIOLhQ/VCF0
         4x8oqgruenzYRCIjjw33IORemvHuLU3n6env8DZUt5Cz3KPDuKZ/CGPZ2XKGy2arDwhO
         cwMdNPSyNSB50qmwN4RFz/Ht9ojLf7Is77ueO1KaB6rl5ML4AxMaE6vzGaCTWM6kitW4
         vyekCvb9mHrrFTnk/wX7bQe9g80/3D6oylGt8/SAhOH0rZ7bCtp6VX0iaYXZWEiR09au
         g8M8+/AurmaEX4uGOIpFjH4RvXjyKJ0HRO3WolCD4uq/+AwErtyw2mEPJ7zqJsM1nuqX
         AY1Q==;
        dara=google.com
ARC-Authentication-Results: i=5; mx.google.com;
       dkim=pass header.i=@crowdin.com header.s=ouu4s5v3twnxtv2736ybwjp3a4lrvwuf header.b=hv2hItXN;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=Do2H+Rfs;
       arc=pass (i=4 spf=pass spfdomain=govocal.com dkim=pass dkdomain=crowdin.com dkim=pass dkdomain=amazonses.com dmarc=pass fromdomain=crowdin.com);
       spf=softfail (google.com: domain of transitioning adrien+caf_=guillaume=<EMAIL> does not designate 2600:1901:101::15 as permitted sender) smtp.mailfrom="adrien+caf_=guillaume=<EMAIL>";
       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=crowdin.com;
       dara=pass header.i=@govocal.com
Return-Path: &lt;adrien+caf_=guillaume=<EMAIL>&gt;
Received: from 15.v6.unverified-forwarding.1e100.net (15.v6.unverified-forwarding.1e100.net. [2600:1901:101::15])
        by mx.google.com with ESMTPS id 4fb4d7f45d1cf-60803c488bfsi4174245a12.379.2025.***********.11
        for &lt;<EMAIL>&gt;
        (version=TLS1_3 cipher=TLS_AES_128_GCM_SHA256 bits=128/128);
        Wed, 11 Jun 2025 00:35:11 -0700 (PDT)
Received-SPF: softfail (google.com: domain of transitioning adrien+caf_=guillaume=<EMAIL> does not designate 2600:1901:101::15 as permitted sender) client-ip=2600:1901:101::15;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@crowdin.com header.s=ouu4s5v3twnxtv2736ybwjp3a4lrvwuf header.b=hv2hItXN;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=Do2H+Rfs;
       arc=pass (i=4 spf=pass spfdomain=govocal.com dkim=pass dkdomain=crowdin.com dkim=pass dkdomain=amazonses.com dmarc=pass fromdomain=crowdin.com);
       spf=softfail (google.com: domain of transitioning adrien+caf_=guillaume=<EMAIL> does not designate 2600:1901:101::15 as permitted sender) smtp.mailfrom="adrien+caf_=guillaume=<EMAIL>";
       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=crowdin.com;
       dara=pass header.i=@govocal.com
Received: by 15.v6.unverified-forwarding.1e100.net with SMTP id 5b1f17b1804b1-442ea786e26so11251615e9.3
        for &lt;<EMAIL>&gt;; Wed, 11 Jun 2025 00:35:11 -0700 (PDT)
ARC-Seal: i=4; a=rsa-sha256; t=1749627311; cv=pass;
        d=google.com; s=arc-20240605;
        b=eiYIMwtHQWnJGI42ysGLRBFhFhCaF0sA/jiN0A6CgRGhgjdwXdl3LDjE02TexcBftn
         fBQ0OYHBrOAewjgqq3405Gbv2vsX3WvlLUtiSn0aCr/Rr/0BlQnsSz4C5QLwdMT6pWUH
         zfdNqJ4BQkIl4n2UgwDR+lNUxDfj5LhmPpWItT85uIOz70FLT0G8pBjpa6AoX2SfXwFr
         8oOxzJApLdCfwJM4ml/AJj9nDosdBVCRiuLJXdxpAECiVru8VJe0RpUx9PGwY+U5cA+s
         948XeXgStY/hP/RJoXPQLU4PdNNEyl8KToEc7AAIg6VYW+bAR26uLxEMqVofiRw0wOvO
         hM7A==
ARC-Message-Signature: i=4; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=feedback-id:date:mime-version:message-id:subject:to:from
         :dkim-signature:dkim-signature:delivered-to;
        bh=0QH9SMRKJAqgWvNWmnnnsQ4pvddJ0EMjObzMb7Pog4c=;
        fh=045a09nkcwf/CCThEPT0cheCJFQWHIY79QvYUv9Hb6w=;
        b=RZfW021azgi8CgONZJC41DIjP+6uNwlGHaNjkUnJ2Jqg/U1zMHlK9dJ2e3Y1OA2BUR
         gcPUM1FrrC3xq8T7nYCgN6NvNLsYjBMQTZ11WwIgxfz6Wcn6X1X3dUlQHfif1Xc8mxED
         05LzY2BfbydfdsMpWLqZTuByfmR+yOMb2W8VLLU42XicMo7n66W5KEaZVim9/Pf/7G0e
         HuRtdo3JPs9QIomag43dGMo9ws3ViYsVjk1JH2T74ouSrMenPJaAwDjU0f1MwP1JWgC0
         SRrPPLJjGVbG9TkEsyLCFSgBgfcvo2IGYuxh65FVIGDEEX5D/h+WHpF4kwW55vDzSZ0p
         rN+A==;
        dara=google.com
ARC-Authentication-Results: i=4; mx.google.com;
       dkim=pass header.i=@crowdin.com header.s=ouu4s5v3twnxtv2736ybwjp3a4lrvwuf header.b=hv2hItXN;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=Do2H+Rfs;
       arc=pass (i=2 spf=pass spfdomain=mail.crowdin.com dkim=pass dkdomain=crowdin.com dkim=pass dkdomain=amazonses.com dmarc=pass fromdomain=crowdin.com);
       spf=pass (google.com: domain of adrien+caf_=guillaume=<EMAIL> designates ************* as permitted sender) smtp.mailfrom="adrien+caf_=guillaume=<EMAIL>";
       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=crowdin.com;
       dara=pass header.i=@govocal.com
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=1e100.net; s=20230601; t=1749627311; x=1750232111;
        h=feedback-id:date:mime-version:message-id:subject:to:from
         :dkim-signature:dkim-signature:delivered-to:x-forwarded-for
         :x-forwarded-to:x-gm-message-state:from:to:cc:subject:date
         :message-id:reply-to;
        bh=0QH9SMRKJAqgWvNWmnnnsQ4pvddJ0EMjObzMb7Pog4c=;
        b=GkfdoNpRfcvZpOpTheKlmiCu8PhokYNlniTAqSz8ERYDl1fJlTBD0qGk2Td2O/5u20
         cUjb/TyTvr9eoSK5rx9+Hc1dYyQww+jwRdQlXGpFiz9BP7vDTINy+blaMEXPCuDNFYpu
         568YZHG8daoKHKbAh98+AgBI1t62xoyuJXozZoC/kQpGFdvZ8YrO3r5XzM6GVrV0x37W
         hepGUKtamhCkOzjcmtew9dkwbqLYPHw7XEiDQ2/n57ev4TKbFlDBO8euzDZYBw1ZkIR8
         4FTN2eJEqM4XrO9fBYGUdBvce5dox61VeJafEPmVcjpPc99FRs7imymhaS+NUeSp5ZRz
         639A==
X-Forwarded-Encrypted: i=4; AJvYcCW2q+CH07Es9cSyYrFdnFsBItE3XyXdkfyoNGQ2emQ2Qo8ZTKDMWV+NEhwofoBtKJ2UgojBv6tx4rA=@govocal.com
X-Gm-Message-State: AOJu0YyZssH4UIN3BVsBdGu5NlT+ofimgeViC8V567h2/53+l2oU+r0x KQIG5Xi2AnGQHOzKQw+fVEVp3Qzi5/+PMbuvNL56aSWbYpp3RuS3v9+ICvQdqQ0JFlZ/lY07r7x ko0C/Q0ZF7CZVHVHQEPd6kfOiENmvVLVvabzDZohwzVROAVDgEGfRIcEY0G6VotkU0++R/WXWNv lU/+29eL2a0p8Ot0RRokLT+Q==
X-Gm-Gg: ASbGnctpgQp58kuXj4ve7ozoVwwvGl6JdpWwjchOxOPOAHHLnqw3V2HnOFKKvthsRc+ 8bPFCK2memtMr6Q8FLEGtw1LS+qDyHX7RBBcf9qRxlCQKzdpLOSd69K+PspVEAf6N76w/BIurzo BrlUzRwzy/HTuRpUf84/4pMuhxNn6N7iXMxKwxIpfeJvkEiveR8CWxFyOWZmVCW8r5shk6Aglnf fvWYR4oUg9mNzm7LrLpp9fSk332BblFwM/CiTHEVS/B1go=
X-Received: by 2002:a05:6000:250f:b0:3a4:d6ed:8e00 with SMTP id ffacd0b85a97d-3a55881db16mr1529895f8f.33.1749627311309;
        Wed, 11 Jun 2025 00:35:11 -0700 (PDT)
X-Received: by 2002:a05:6000:250f:b0:3a4:d6ed:8e00 with SMTP id ffacd0b85a97d-3a55881db16mr1529820f8f.33.1749627309951;
        Wed, 11 Jun 2025 00:35:09 -0700 (PDT)
ARC-Seal: i=3; a=rsa-sha256; t=1749627309; cv=pass;
        d=google.com; s=arc-20240605;
        b=HNCbUsDVaaq46V1ouBbMIEm2fLVtofiWa9nAG7FsiNLuHV6g/1oaKF6Km4Pa7xnz/j
         kzReNljJ0VIHybfWOj6kLvEhNcEnjjibUd+/je3EWVIILBfo4+z1YHPr8qlBNMKMJAQv
         9Aiq87YVkKK3051TRaq5vhvwC9Zb0fEaUNbIBqonWTt+FZbnxOSIcRumRiwHQipjlRSK
         QTDtlwUBnO3feVTktgwQKtrhcAklDGsETiaq0RQO3EbL57OEBsT3Q/iTDvp54fsUsBra
         iZgjx30MuBVFNal/cUt7KalY6XQkh8XOhS88Yzq8l/KqXpZTu0g41WeGL1tUyZMLOTNo
         qGCQ==
ARC-Message-Signature: i=3; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=feedback-id:date:mime-version:message-id:subject:to:from
         :dkim-signature:dkim-signature:delivered-to;
        bh=0QH9SMRKJAqgWvNWmnnnsQ4pvddJ0EMjObzMb7Pog4c=;
        fh=g7ISeqaNGkN7xZ5Vl7ImT1hHr8pVd4CpPaEtJFDtdMQ=;
        b=bn8+2RlyEH9GCdML5QQlja1TQUny/X2omqjw2jxa+xsrWeB9N/e/LXf5qycAmO2fQU
         6SxGrHuG6OrtxBVN/xP3EyA2MX/1tbcJqGGvlLVTDcvhcymP8hbVjhXGnKh3CtgZ1YIj
         7h8NHscxRHWeHyt0UmkWWyvqv7/MOIHZXqP79ryMrAqHjB7kwOxPvpJIuwfO5nA5Eenr
         91dpWQ8E8Hydx6hHIxlUprGadtbpRCoftf7LyA62T8G1pZeyuu/pmEleJw5blKAR3GtN
         f1fcoiJe8DE6Piz9CNl/HxXO/sthU8l4sjI8CbnVw4SxxefvTnwjKLKVpOZN4sL9HeNx
         f0bQ==;
        dara=google.com
ARC-Authentication-Results: i=3; mx.google.com;
       dkim=pass header.i=@crowdin.com header.s=ouu4s5v3twnxtv2736ybwjp3a4lrvwuf header.b=hv2hItXN;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=Do2H+Rfs;
       arc=pass (i=2 spf=pass spfdomain=mail.crowdin.com dkim=pass dkdomain=crowdin.com dkim=pass dkdomain=amazonses.com dmarc=pass fromdomain=crowdin.com);
       spf=pass (google.com: domain of adrien+caf_=guillaume=<EMAIL> designates ************* as permitted sender) smtp.mailfrom="adrien+caf_=guillaume=<EMAIL>";
       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=crowdin.com;
       dara=pass header.i=@govocal.com
Return-Path: &lt;adrien+caf_=guillaume=<EMAIL>&gt;
Received: from mail-sor-f41.google.com (mail-sor-f41.google.com. [*************])
        by mx.google.com with SMTPS id 5b1f17b1804b1-452730bd356sor52134885e9.7.2025.***********.09
        for &lt;<EMAIL>&gt;
        (Google Transport Security);
        Wed, 11 Jun 2025 00:35:09 -0700 (PDT)
Received-SPF: pass (google.com: domain of adrien+caf_=guillaume=<EMAIL> designates ************* as permitted sender) client-ip=*************;
ARC-Seal: i=2; a=rsa-sha256; t=1749627309; cv=pass;
        d=google.com; s=arc-20240605;
        b=ZE/RrrD2vtyz8VpLmlR3pr0cgVClNaZUFbeZfGDwNBjCZdRRva0JmSbri28LShzYX4
         7O01fJRwCPxkkvaxUPLzpsiIWvm+5u/hPvuR3PTnVtIrlr3Q/C1rSFZEHUkjfD40Q09u
         3P7jQYDgzlHwY5Y0b9R023xrlORFeLH5JIzo2+yY5D7xtDR+/JdKCTvg7wPNpHFzx6mF
         SZT2jIt/5srRAxdotma/IFKdmAKe2zsIbhdW650E7q6MwiCf8QNoRva3WGibwFdm0HAk
         0T0OzjTS3uaqWIj1GU6GIcHtjkV4zJwfWnq5+XjnzLvVBquay8E1XRtE0QCHTXZfaHKK
         Xxjg==
ARC-Message-Signature: i=2; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=feedback-id:date:mime-version:message-id:subject:to:from
         :dkim-signature:dkim-signature:delivered-to;
        bh=0QH9SMRKJAqgWvNWmnnnsQ4pvddJ0EMjObzMb7Pog4c=;
        fh=g7ISeqaNGkN7xZ5Vl7ImT1hHr8pVd4CpPaEtJFDtdMQ=;
        b=JP2o7C6WO2fBzz5N7nl7egMlsbvG6Axgdx2xg1QXaW0BhQuZDlE2/unPHTIaFPvbtE
         szhCE8d4kLYk81udzz2sxHqWdZK11vWN7hCj8eqV0QhvzGws6kpdcEdjIt6uSZgMju3l
         W05fg7llP6m7YvZGIV/Vx44a7EPdeKywbtgRuUT9mJWU8xdylaSsT4cit0cyPwApNJKa
         RgkC8KJDdyvsR178PDchSOFgZUKyG3BpZ3GnG8fKJfFIwYxlqbfEdPPwkC5yiCZ9+0Nh
         ydI5f8PuR1aPcx7To5U7xHVgKJVTAinhpOXd3Dbc+IgTDwQpdnPoOeMRSU1be86kbwvD
         +ijQ==;
        darn=govocal.com
ARC-Authentication-Results: i=2; mx.google.com;
       dkim=pass header.i=@crowdin.com header.s=ouu4s5v3twnxtv2736ybwjp3a4lrvwuf header.b=hv2hItXN;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=Do2H+Rfs;
       spf=pass (google.com: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=crowdin.com
X-Forwarded-Encrypted: i=2; AJvYcCVwztnBzAhFWgh7ERuWEkXeoa/llhZ3RUniYUqT86W/TQxhUlBaVIQiBFtVRXqBAGS7yADRgK07gd8=@govocal.com
X-Received: by 2002:a05:600c:1910:b0:43d:fa59:a685 with SMTP id 5b1f17b1804b1-453248fb58amr16993445e9.33.1749627309023;
        Wed, 11 Jun 2025 00:35:09 -0700 (PDT)
X-Forwarded-To: <EMAIL>
X-Forwarded-For: <EMAIL> <EMAIL>
Delivered-To: <EMAIL>
Received: by 2002:a5d:4d86:0:b0:3a4:f03a:accc with SMTP id b6csp2569152wru;
        Wed, 11 Jun 2025 00:35:07 -0700 (PDT)
X-Received: by 2002:a05:622a:1456:b0:4a7:1417:5127 with SMTP id d75a77b69052e-4a71417534amr32078491cf.36.**********449;
        Wed, 11 Jun 2025 00:35:07 -0700 (PDT)
ARC-Seal: i=1; a=rsa-sha256; t=**********; cv=none;
        d=google.com; s=arc-20240605;
        b=RvVCnU8Y0HrLLpr7rlkJ97/s1nJN8zToyAIRMPfScNnErm5z4rwzFj2aTnB1ZdRjFi
         Py3OIUS50GzpkiXos/NeYNx+HZovOZTB2+EAAADjco2LZeNYJwF4rWNLJFt9cIZR93RY
         9twBZWAZ0jgBc20Ultz6jpBGlzJnxN4OBx6NEUvZMvdhVu9oXX5ngu44UJZZQLrYIhJ3
         4EZ2ZG2LMryiF1uOAvbkaqvSt/j/VEtyC+j+N9E/OFqqUSvtgAIlaLu5LzKcGCnBObeb
         1w5wI+t4S2R7Pcd96ppobIdd/49mP+6A4968CqJ3UAkqVG93+OT6tlbnFT3g0r9hTu6r
         87Ow==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;
        h=feedback-id:date:mime-version:message-id:subject:to:from
         :dkim-signature:dkim-signature;
        bh=0QH9SMRKJAqgWvNWmnnnsQ4pvddJ0EMjObzMb7Pog4c=;
        fh=gxtoD9JCzbGdq483Q0HRnqLZY7m8UfUlpX7Z8jg3evg=;
        b=enBvdYxB7mkqGSQJTH5b/RKIlvp28p9PzL9qLVwV+O21nCvcZvIgzXdtibbwfrRk23
         WKE6mi6RDto+U7bErF2xR1kIKKeBUx1Gg2bZh/rQfE2UB8/Tx3+Ju54yHnDYi5OA5RdY
         6ueNU5dmN0x4SxcYYZasquLjslZl1rInxww8791lZeFxTxK4tnlZ05dy6nKYrN354Jzc
         jJvuVs0jRaDBEw232sOOXJS/zVRu2Q7sNS4g4mJqUh5zgatsKbpjC7niakRezt6erBXy
         zj3l9uHUCTmfi0/3+lciiDFSo/jeKHFxaQGDRLxmXIBFlh5HtZ9bWmG2rb7iJfYfF/wj
         FLWQ==;
        dara=google.com
ARC-Authentication-Results: i=1; mx.google.com;
       dkim=pass header.i=@crowdin.com header.s=ouu4s5v3twnxtv2736ybwjp3a4lrvwuf header.b=hv2hItXN;
       dkim=pass header.i=@amazonses.com header.s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw header.b=Do2H+Rfs;
       spf=pass (google.com: <NAME_EMAIL> designates ************** as permitted sender) smtp.mailfrom=<EMAIL>;
       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=crowdin.com
Return-Path: &lt;<EMAIL>&gt;
Received: from a126-207.smtp-out.amazonses.com (a126-207.smtp-out.amazonses.com. [**************])
        by mx.google.com with ESMTPS id d75a77b69052e-4a6198b3b19si119662631cf.553.2025.***********.07
        for &lt;<EMAIL>&gt;
        (version=TLS1_3 cipher=TLS_AES_128_GCM_SHA256 bits=128/128);
        Wed, 11 Jun 2025 00:35:07 -0700 (PDT)
Received-SPF: pass (google.com: <NAME_EMAIL> designates ************** as permitted sender) client-ip=**************;
DKIM-Signature: v=1; a=rsa-sha256; q=dns/txt; c=relaxed/simple; s=ouu4s5v3twnxtv2736ybwjp3a4lrvwuf; d=crowdin.com; t=**********; h=From:To:Subject:Message-ID:MIME-Version:Date:Content-Type; bh=h0vDQ5oJgub841Fi4Ql66W1OTFprzQfxh2o9A4A8p0o=; b=hv2hItXNUh/zcdemr5JFtleE/DuL3ZVneERpA/BNKqaVU04UxyKnD1KH9MZsWnGR zN7e8xiybJoIYeHwvkgt9RSlicWwduMtHaGnQA+JhjWwwknkOGiCa3+r1VzMOqDI8SW p1ia0pXqjNUl7oBmey5TqllkCWGR76zWWpxfislA=
DKIM-Signature: v=1; a=rsa-sha256; q=dns/txt; c=relaxed/simple; s=6gbrjpgwjskckoa6a5zn6fwqkn67xbtw; d=amazonses.com; t=**********; h=From:To:Subject:Message-ID:MIME-Version:Date:Content-Type:Feedback-ID; bh=h0vDQ5oJgub841Fi4Ql66W1OTFprzQfxh2o9A4A8p0o=; b=Do2H+Rfs0GJqpiJM7Nsp4cVbP8YBqBEyyRnlaK/iqdFhXpUy9mMas0WkGjmoF7LR CHdb7jj4HBPK5SOpM2+92WbMHIDLfx3NXqikY1Gcodb2teuXnn23qsoDwlWaJpFSl3L ughNBe48X5TaWbqtV92CJ1+1xgdTIT6VupjKrvjE=
From: Crowdin Enterprise &lt;<EMAIL>&gt;
To: <EMAIL>
Subject: Verification code for Crowdin Enterprise account
X-Crowdin-Domain: citizenlab
Message-ID: &lt;<EMAIL>&gt;
MIME-Version: 1.0
Date: Wed, 11 Jun 2025 07:35:06 +0000
Content-Type: multipart/alternative; boundary=KsKZmLSs
Feedback-ID: ::1.us-east-1.eDPhX1ubx8CYC8kVDkbhSmyYPdXv68lkM7LCSgWL0ec=:AmazonSES
X-SES-Outgoing: 2025.06.11-**************

--KsKZmLSs
Content-Type: text/plain; charset=utf-8
Content-Transfer-Encoding: quoted-printable

Hello adessy,

You=E2=80=99re trying to log in to your Crowdin account from a new device.

Country: Belgium (*************)
Browser: Firefox 139
Operating system: Mac

To complete the login, use the following verification code: 145780

If you didn't initiate this login, we recommend securing your account by re=
setting your password.
https://citizenlab.crowdin.com/u/user_settings/security

Kind regards,
Crowdin Enterprise Team

--KsKZmLSs
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

&lt;!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org=
/TR/xhtml1/DTD/xhtml1-strict.dtd"&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;meta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=
=3D1.0"/&gt;
    &lt;style&gt;
        #retina_logo {
            display: inline-block;
            width: 250px;
            height: 42px;
            background-image: url(https://d2gma3rgtloi6d.cloudfront.net/aut=
h/e87f295a/images/emails/enterprise/logo.png);
            background-position: center center;
            background-size: 250px auto;
            vertical-align: middle;
        }
        @import url('https://fonts.googleapis.com/css?family=3DRoboto:400,5=
00');

        @media all and (-webkit-min-device-pixel-ratio: 1.1), all and (min-=
device-pixel-ratio: 1.1) {

            #retina_logo {
                background-image: url(https://d2gma3rgtloi6d.cloudfront.net=
/auth/e87f295a/images/emails/enterprise/<EMAIL>);
            }
        }
        @media all and (-webkit-min-device-pixel-ratio: 2.1), all and (min-=
device-pixel-ratio: 2.1) {
            #retina_logo {
                background-image: url(https://d2gma3rgtloi6d.cloudfront.net=
/auth/e87f295a/images/emails/enterprise/<EMAIL>);
            }
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body style=3D"margin:0; padding: 0; -webkit-text-size-adjust:none; -ms-tex=
t-size-adjust: 100%;"&gt;
    &lt;table width=3D"100%" cellspacing=3D"0" cellpadding=3D"0" border=3D"0" =
align=3D"center"&gt;
        &lt;tbody&gt;
            &lt;tr&gt;
                &lt;td&gt;
                    &lt;table align=3D"center" bgcolor=3D"#F91D00" width=3D"10=
0%" cellspacing=3D"0" cellpadding=3D"0" border=3D"0"
                           style=3D"border-radius: 4px; border-collapse:col=
lapse; background-color: #ffffff; min-width: 320px; max-width: 600px"&gt;
                        &lt;tbody&gt;
                                                            &lt;tr&gt;
                                    &lt;td style=3D"padding: 40px 20px; text-a=
lign: center;" align=3D"center" valign=3D"top"&gt;
                                        &lt;table align=3D"left" cellspacing=
=3D"0" cellpadding=3D"0" border=3D"0"
                                               style=3D"float: none; margin=
: 0 auto;"&gt;
                                            &lt;tbody&gt;
                                            &lt;tr&gt;
                                                &lt;td valign=3D"center" width=
=3D"250"
                                                    style=3D"min-width: 250=
px; height: 42px;"&gt;
                                                    &lt;a id=3D"retina_logo" t=
arget=3D"_blank"
                                                       style=3D"border-widt=
h: 0px; border: 0px; text-decoration: none; height: 42px; display: inline-b=
lock; margin: 0;"
                                                       href=3D"https://citi=
zenlab.crowdin.com"&gt;
                                                        &lt;img id=3D"default_=
logo" width=3D"250" height=3D"42" id=3D"default_logo" alt=3D""
                                                             border=3D"0" s=
tyle=3D"border-width:0px; border:0px; vertical-align: top;"
                                                             src=3D"https:/=
/d2gma3rgtloi6d.cloudfront.net/auth/e87f295a/images/emails/enterprise/logo.=
png"/&gt;
                                                    &lt;/a&gt;
                                                &lt;/td&gt;
                                            &lt;/tr&gt;
                                            &lt;/tbody&gt;
                                        &lt;/table&gt;
                                    &lt;/td&gt;
                                &lt;/tr&gt;
                                                        &lt;tr&gt;
                                &lt;td align=3D"left" style=3D"padding: 0;"&gt;
                                   =20
                                                                    &lt;/td&gt;
                            &lt;/tr&gt;
                            &lt;tr&gt;
                                &lt;td valign=3D"top" style=3D"padding: 0px 20=
px 40px;"&gt;
                                    &lt;table width=3D"100%"&gt;
                                        &lt;tbody&gt;
                                        &lt;tr style=3D"width:100%"&gt;
                                            &lt;td style=3D"width:100%; font-f=
amily: &amp;quot;Open Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, Arial, sans-serif=
; color: #333333; font-size: 14px; line-height: 1.5em;"&gt;
                                                            &lt;p style=3D"pad=
ding: 0; margin: 0; margin-bottom: 20px"&gt;
    Hello adessy,
&lt;/p&gt;

&lt;p style=3D"padding: 0; margin: 0; margin-bottom: 20px"&gt;
    You=E2=80=99re trying to log in to your Crowdin account from a new devi=
ce.
&lt;/p&gt;

&lt;p style=3D"padding: 0; margin: 0px"&gt;Country: &lt;b&gt;Belgium (*************)&lt;/b=
&gt;&lt;/p&gt;

&lt;p style=3D"padding: 0; margin: 0px"&gt;Browser: &lt;b&gt;Firefox 139&lt;/b&gt;&lt;/p&gt;

&lt;p style=3D"padding: 0; margin: 0; margin-bottom: 20px"&gt;Operating system: &lt;=
b&gt;Mac&lt;/b&gt;&lt;/p&gt;

&lt;p style=3D"padding: 0; margin: 0; margin-bottom: 20px"&gt;
    To complete the login, use the following verification code:
&lt;/p&gt;

&lt;p style=3D"padding: 0; margin: 0; margin-bottom: 20px"&gt;
    &lt;b&gt;145780&lt;/b&gt;
&lt;/p&gt;

&lt;p style=3D"padding: 0; margin: 0; margin-bottom: 20px"&gt;
    If you didn't initiate this login, we recommend securing your account b=
y resetting your &lt;a href=3D"https://citizenlab.crowdin.com/u/user_settings/=
security"&gt;password&lt;/a&gt;.
&lt;/p&gt;
&lt;br&gt;

&lt;p style=3D"padding: 0; margin: 0px"&gt;
    Kind regards,
    &lt;br&gt;
    Crowdin Enterprise Team
&lt;/p&gt;
                                                &lt;/td&gt;
                                        &lt;/tr&gt;
                                        &lt;/tbody&gt;
                                    &lt;/table&gt;
                                &lt;/td&gt;
                            &lt;/tr&gt;
                            &lt;tr&gt;
                                &lt;td valign=3D"top" align=3D"center" style=
=3D"font-family: &amp;quot;Open Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, Arial, =
sans-serif; color: #333333; font-size: 14px; line-height: 1.5em; padding: 0=
px 20px 40px; text-align: center; color: #999999; font-size: 12px; line-hei=
ght: 1.5em"&gt;
                                    &lt;hr style=3D"border-bottom: 1px solid r=
gba(46, 51, 64, 0.10); border-top: 0px; margin-bottom: 24px;"&gt;
                                   =20
                                   =20
                                    &lt;p style=3D"padding: 0; margin: 0px"&gt;
                                        &amp;copy; 2025 Crowdin Enterprise
                                    &lt;/p&gt;
                                &lt;/td&gt;
                            &lt;/tr&gt;
                        &lt;/tbody&gt;
                    &lt;/table&gt;
                &lt;/td&gt;
            &lt;/tr&gt;
        &lt;/tbody&gt;
    &lt;/table&gt;
&lt;/body&gt;
&lt;/html&gt;

--KsKZmLSs--</pre></div></div></div><script id="define-custom-element-wtd-root" src="chrome-extension://emnoomldgleagdjapdeckpmebokijail/src/scripts/injected/defineCustomElementsInjected.js" data-params="{&quot;elementName&quot;:&quot;wtd-root&quot;,&quot;eventName&quot;:&quot;customElements.defined&quot;,&quot;isShadowRoot&quot;:true}"></script><div id="loom-companion-mv3" ext-id="liecbddmkiiihnedobmlmillhodjkdmb"><div id="shadow-host-companion"><template shadowrootmode="open"><div id="inner-shadow-companion"><div id="tooltip-mount-layer-companion" class="css-0"></div><style data-emotion="companion-global" data-s=""></style><style data-emotion="companion" data-s=""></style><style>

    #inner-shadow-companion {
      font-size: 100%;
    }
    #inner-shadow-companion {
      --lns-fontFamily-body: "Atlassian Sans", ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, system-ui, "Helvetica Neue", sans-serif;
      --lns-fontFamily-heading: "Atlassian Sans", ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, system-ui, "Helvetica Neue", sans-serif;
      --lns-fontFamily-code: "Atlassian Mono", ui-monospace, Menlo, "Segoe UI Mono", "Ubuntu Mono", monospace;

      font-family: var(--lns-fontFamily-body);
      color: var(--ds-text, var(--lns-color-body));

  font-size: var(--lns-fontSize-body-md);
  line-height: var(--lns-lineHeight-body-md);
  letter-spacing: var(--lns-letterSpacing-body-md);
;
    }

    #inner-shadow-companion *,
    #inner-shadow-companion *:before,
    #inner-shadow-companion *:after {
      box-sizing: border-box;
    }

    #inner-shadow-companion * {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }


    #inner-shadow-companion,
    .theme-light,
    [data-lens-theme="light"] {
      --lns-color-primary: var(--lns-themeLight-color-primary);--lns-color-primaryHover: var(--lns-themeLight-color-primaryHover);--lns-color-primaryActive: var(--lns-themeLight-color-primaryActive);--lns-color-body: var(--lns-themeLight-color-body);--lns-color-bodyDimmed: var(--lns-themeLight-color-bodyDimmed);--lns-color-bodyInverse: var(--lns-themeLight-color-bodyInverse);--lns-color-background: var(--lns-themeLight-color-background);--lns-color-backgroundHover: var(--lns-themeLight-color-backgroundHover);--lns-color-backgroundActive: var(--lns-themeLight-color-backgroundActive);--lns-color-backgroundSecondary: var(--lns-themeLight-color-backgroundSecondary);--lns-color-backgroundSecondary2: var(--lns-themeLight-color-backgroundSecondary2);--lns-color-backgroundInverse: var(--lns-themeLight-color-backgroundInverse);--lns-color-overlay: var(--lns-themeLight-color-overlay);--lns-color-border: var(--lns-themeLight-color-border);--lns-color-focusRing: var(--lns-themeLight-color-focusRing);--lns-color-record: var(--lns-themeLight-color-record);--lns-color-recordHover: var(--lns-themeLight-color-recordHover);--lns-color-recordActive: var(--lns-themeLight-color-recordActive);--lns-color-info: var(--lns-themeLight-color-info);--lns-color-success: var(--lns-themeLight-color-success);--lns-color-warning: var(--lns-themeLight-color-warning);--lns-color-danger: var(--lns-themeLight-color-danger);--lns-color-dangerHover: var(--lns-themeLight-color-dangerHover);--lns-color-dangerActive: var(--lns-themeLight-color-dangerActive);--lns-color-backdrop: var(--lns-themeLight-color-backdrop);--lns-color-backdropDark: var(--lns-themeLight-color-backdropDark);--lns-color-backdropTwilight: var(--lns-themeLight-color-backdropTwilight);--lns-color-disabledContent: var(--lns-themeLight-color-disabledContent);--lns-color-highlight: var(--lns-themeLight-color-highlight);--lns-color-disabledBackground: var(--lns-themeLight-color-disabledBackground);--lns-color-formFieldBorder: var(--lns-themeLight-color-formFieldBorder);--lns-color-formFieldBackground: var(--lns-themeLight-color-formFieldBackground);--lns-color-buttonBorder: var(--lns-themeLight-color-buttonBorder);--lns-color-upgrade: var(--lns-themeLight-color-upgrade);--lns-color-upgradeHover: var(--lns-themeLight-color-upgradeHover);--lns-color-upgradeActive: var(--lns-themeLight-color-upgradeActive);--lns-color-tabBackground: var(--lns-themeLight-color-tabBackground);--lns-color-discoveryBackground: var(--lns-themeLight-color-discoveryBackground);--lns-color-discoveryLightBackground: var(--lns-themeLight-color-discoveryLightBackground);--lns-color-discoveryTitle: var(--lns-themeLight-color-discoveryTitle);--lns-color-discoveryHighlight: var(--lns-themeLight-color-discoveryHighlight);
    }

    .theme-dark,
    [data-lens-theme="dark"] {
      --lns-color-primary: var(--lns-themeDark-color-primary);--lns-color-primaryHover: var(--lns-themeDark-color-primaryHover);--lns-color-primaryActive: var(--lns-themeDark-color-primaryActive);--lns-color-body: var(--lns-themeDark-color-body);--lns-color-bodyDimmed: var(--lns-themeDark-color-bodyDimmed);--lns-color-bodyInverse: var(--lns-themeDark-color-bodyInverse);--lns-color-background: var(--lns-themeDark-color-background);--lns-color-backgroundHover: var(--lns-themeDark-color-backgroundHover);--lns-color-backgroundActive: var(--lns-themeDark-color-backgroundActive);--lns-color-backgroundSecondary: var(--lns-themeDark-color-backgroundSecondary);--lns-color-backgroundSecondary2: var(--lns-themeDark-color-backgroundSecondary2);--lns-color-backgroundInverse: var(--lns-themeDark-color-backgroundInverse);--lns-color-overlay: var(--lns-themeDark-color-overlay);--lns-color-border: var(--lns-themeDark-color-border);--lns-color-focusRing: var(--lns-themeDark-color-focusRing);--lns-color-record: var(--lns-themeDark-color-record);--lns-color-recordHover: var(--lns-themeDark-color-recordHover);--lns-color-recordActive: var(--lns-themeDark-color-recordActive);--lns-color-info: var(--lns-themeDark-color-info);--lns-color-success: var(--lns-themeDark-color-success);--lns-color-warning: var(--lns-themeDark-color-warning);--lns-color-danger: var(--lns-themeDark-color-danger);--lns-color-dangerHover: var(--lns-themeDark-color-dangerHover);--lns-color-dangerActive: var(--lns-themeDark-color-dangerActive);--lns-color-backdrop: var(--lns-themeDark-color-backdrop);--lns-color-backdropDark: var(--lns-themeDark-color-backdropDark);--lns-color-backdropTwilight: var(--lns-themeDark-color-backdropTwilight);--lns-color-disabledContent: var(--lns-themeDark-color-disabledContent);--lns-color-highlight: var(--lns-themeDark-color-highlight);--lns-color-disabledBackground: var(--lns-themeDark-color-disabledBackground);--lns-color-formFieldBorder: var(--lns-themeDark-color-formFieldBorder);--lns-color-formFieldBackground: var(--lns-themeDark-color-formFieldBackground);--lns-color-buttonBorder: var(--lns-themeDark-color-buttonBorder);--lns-color-upgrade: var(--lns-themeDark-color-upgrade);--lns-color-upgradeHover: var(--lns-themeDark-color-upgradeHover);--lns-color-upgradeActive: var(--lns-themeDark-color-upgradeActive);--lns-color-tabBackground: var(--lns-themeDark-color-tabBackground);--lns-color-discoveryBackground: var(--lns-themeDark-color-discoveryBackground);--lns-color-discoveryLightBackground: var(--lns-themeDark-color-discoveryLightBackground);--lns-color-discoveryTitle: var(--lns-themeDark-color-discoveryTitle);--lns-color-discoveryHighlight: var(--lns-themeDark-color-discoveryHighlight);
    }



    #inner-shadow-companion {
      --lns-fontWeight-book:400;--lns-fontWeight-regular:400;--lns-fontWeight-medium:500;--lns-fontWeight-bold:653;--lns-unit:0.5rem;--lns-fontSize-small:calc(1.5 * var(--lns-unit, 8px));--lns-lineHeight-small:1.5;--lns-letterSpacing-small:normal;--lns-fontSize-body-sm:calc(1.5 * var(--lns-unit, 8px));--lns-lineHeight-body-sm:1.5;--lns-letterSpacing-body-sm:normal;--lns-fontSize-medium:calc(1.75 * var(--lns-unit, 8px));--lns-lineHeight-medium:1.57;--lns-letterSpacing-medium:normal;--lns-fontSize-body-md:calc(1.75 * var(--lns-unit, 8px));--lns-lineHeight-body-md:1.57;--lns-letterSpacing-body-md:normal;--lns-fontSize-large:calc(2.25 * var(--lns-unit, 8px));--lns-lineHeight-large:1.44;--lns-letterSpacing-large:-0.2px;--lns-fontSize-body-lg:calc(2.25 * var(--lns-unit, 8px));--lns-lineHeight-body-lg:1.44;--lns-letterSpacing-body-lg:-0.2px;--lns-fontSize-xlarge:calc(3 * var(--lns-unit, 8px));--lns-lineHeight-xlarge:1.16;--lns-letterSpacing-xlarge:-0.2px;--lns-fontSize-heading-sm:calc(3 * var(--lns-unit, 8px));--lns-lineHeight-heading-sm:1.16;--lns-letterSpacing-heading-sm:-0.2px;--lns-fontSize-xxlarge:calc(4 * var(--lns-unit, 8px));--lns-lineHeight-xxlarge:1.125;--lns-letterSpacing-xxlarge:-0.5px;--lns-fontSize-heading-md:calc(4 * var(--lns-unit, 8px));--lns-lineHeight-heading-md:1.125;--lns-letterSpacing-heading-md:-0.5px;--lns-fontSize-xxxlarge:calc(6 * var(--lns-unit, 8px));--lns-lineHeight-xxxlarge:1.16;--lns-letterSpacing-xxxlarge:-1.2px;--lns-fontSize-heading-lg:calc(6 * var(--lns-unit, 8px));--lns-lineHeight-heading-lg:1.16;--lns-letterSpacing-heading-lg:-1.2px;--lns-radius-50:calc(0.5 * var(--lns-unit, 8px));--lns-radius-100:calc(1 * var(--lns-unit, 8px));--lns-radius-150:calc(1.5 * var(--lns-unit, 8px));--lns-radius-175:calc(1.75 * var(--lns-unit, 8px));--lns-radius-200:calc(2 * var(--lns-unit, 8px));--lns-radius-250:calc(2.5 * var(--lns-unit, 8px));--lns-radius-300:calc(3 * var(--lns-unit, 8px));--lns-radius-none:0;--lns-radius-medium:calc(1 * var(--lns-unit, 8px));--lns-radius-large:calc(2 * var(--lns-unit, 8px));--lns-radius-xlarge:calc(3 * var(--lns-unit, 8px));--lns-radius-round:calc(999 * var(--lns-unit, 8px));--lns-radius-full:calc(999 * var(--lns-unit, 8px));--lns-shadow-small:0 calc(0.5 * var(--lns-unit, 8px)) calc(1.25 * var(--lns-unit, 8px)) hsla(0, 0%, 0%, 0.05);--lns-shadow-medium:0 calc(0.5 * var(--lns-unit, 8px)) calc(1.25 * var(--lns-unit, 8px)) hsla(0, 0%, 0%, 0.1);--lns-shadow-large:0 calc(0.75 * var(--lns-unit, 8px)) calc(3 * var(--lns-unit, 8px)) hsla(0, 0%, 0%, 0.1);--lns-space-xsmall:calc(0.5 * var(--lns-unit, 8px));--lns-space-small:calc(1 * var(--lns-unit, 8px));--lns-space-medium:calc(2 * var(--lns-unit, 8px));--lns-space-large:calc(3 * var(--lns-unit, 8px));--lns-space-xlarge:calc(5 * var(--lns-unit, 8px));--lns-space-xxlarge:calc(8 * var(--lns-unit, 8px));--lns-formFieldBorderWidth:1px;--lns-formFieldBorderWidthFocus:2px;--lns-formFieldHeight:calc(4.5 * var(--lns-unit, 8px));--lns-formFieldRadius:var(--lns-radius-175);--lns-formFieldHorizontalPadding:calc(2 * var(--lns-unit, 8px));--lns-formFieldBorderShadow:
    inset 0 0 0 var(--lns-formFieldBorderWidth) var(--lns-color-formFieldBorder)
  ;--lns-formFieldBorderShadowFocus:
    inset 0 0 0 var(--lns-formFieldBorderWidthFocus) var(--lns-color-blurple),
    0 0 0 var(--lns-formFieldBorderWidthFocus) var(--lns-color-focusRing)
  ;--lns-formFieldBorderShadowError:
    inset 0 0 0 var(--lns-formFieldBorderWidthFocus) var(--lns-color-danger),
    0 0 0 var(--lns-formFieldBorderWidthFocus) var(--lns-color-orangeLight)
  ;--lns-color-red:hsla(11,80%,45%,1);--lns-color-blurpleLight:hsla(240,83.3%,95.3%,1);--lns-color-blurpleMedium:hsla(242,81%,87.6%,1);--lns-color-blurple:hsla(242,88.4%,66.3%,1);--lns-color-blurpleDark:hsla(242,87.6%,62%,1);--lns-color-blurpleStrong:hsla(252,46%,33%,1);--lns-color-offWhite:hsla(45,36.4%,95.7%,1);--lns-color-blueLight:hsla(206,58.3%,85.9%,1);--lns-color-blue:hsla(206,100%,73.3%,1);--lns-color-blueDark:hsla(206,29.5%,33.9%,1);--lns-color-orangeLight:hsla(6,100%,89.6%,1);--lns-color-orange:hsla(11,100%,62.2%,1);--lns-color-orangeDark:hsla(11,79.9%,64.9%,1);--lns-color-tealLight:hsla(180,20%,67.6%,1);--lns-color-teal:hsla(180,51.4%,51.6%,1);--lns-color-tealDark:hsla(180,16.2%,22.9%,1);--lns-color-yellowLight:hsla(39,100%,87.8%,1);--lns-color-yellow:hsla(50,100%,57.3%,1);--lns-color-yellowDark:hsla(39,100%,68%,1);--lns-color-grey8:hsla(0,0%,13%,1);--lns-color-grey7:hsla(246,16%,26%,1);--lns-color-grey6:hsla(252,13%,46%,1);--lns-color-grey5:hsla(240,7%,62%,1);--lns-color-grey4:hsla(259,12%,75%,1);--lns-color-grey3:hsla(260,11%,85%,1);--lns-color-grey2:hsla(260,11%,95%,1);--lns-color-grey1:hsla(240,7%,97%,1);--lns-color-white:hsla(0,0%,100%,1);--lns-themeLight-color-primary:hsla(242,88.4%,66.3%,1);--lns-themeLight-color-primaryHover:hsla(242,88.4%,56.3%,1);--lns-themeLight-color-primaryActive:hsla(242,88.4%,45.3%,1);--lns-themeLight-color-body:var(--ds-text, hsla(0,0%,13%,1));--lns-themeLight-color-bodyDimmed:var(--ds-text-subtlest, hsla(252,13%,46%,1));--lns-themeLight-color-bodyInverse:var(--ds-text-inverse, hsla(0,0%,100%,1));--lns-themeLight-color-background:var(--ds-surface, hsla(0,0%,100%,1));--lns-themeLight-color-backgroundHover:var(--ds-background-neutral-subtle-hovered, hsla(246,16%,26%,0.1));--lns-themeLight-color-backgroundActive:var(--ds-background-neutral-subtle-pressed, hsla(246,16%,26%,0.3));--lns-themeLight-color-backgroundSecondary:var(--ds-surface-sunken, hsla(246,16%,26%,0.04));--lns-themeLight-color-backgroundSecondary2:var(--ds-surface-sunken, hsla(45,34%,78%,0.2));--lns-themeLight-color-backgroundInverse:var(--ds-background-neutral-bold, hsla(228,6%,17%,1));--lns-themeLight-color-overlay:var(--ds-surface-overlay, hsla(0,0%,100%,1));--lns-themeLight-color-border:var(--ds-border, hsla(252,13%,46%,0.2));--lns-themeLight-color-focusRing:hsla(242,88.4%,66.3%,0.8);--lns-themeLight-color-record:hsla(11,100%,62.2%,1);--lns-themeLight-color-recordHover:hsla(11,100%,52.2%,1);--lns-themeLight-color-recordActive:hsla(11,100%,42.2%,1);--lns-themeLight-color-info:var(--ds-background-information-bold, hsla(206,100%,73.3%,1));--lns-themeLight-color-success:var(--ds-background-accent-green-bolder, hsla(180,51.4%,51.6%,1));--lns-themeLight-color-warning:var(--ds-background-warning-bold, hsla(39,100%,68%,1));--lns-themeLight-color-danger:var(--ds-background-danger-bold, hsla(11,80%,45%,1));--lns-themeLight-color-dangerHover:var(--ds-background-danger-bold-hovered, hsla(11,80%,38%,1));--lns-themeLight-color-dangerActive:var(--ds-background-danger-bold-pressed, hsla(11,80%,31%,1));--lns-themeLight-color-backdrop:var(--ds-blanket, hsla(0,0%,13%,0.5));--lns-themeLight-color-backdropDark:hsla(0,0%,13%,0.9);--lns-themeLight-color-backdropTwilight:hsla(245,44.8%,46.9%,0.8);--lns-themeLight-color-disabledContent:var(--ds-text-disabled, hsla(240,7%,62%,1));--lns-themeLight-color-highlight:hsla(240,83.3%,66.3%,0.15);--lns-themeLight-color-disabledBackground:var(--ds-background-disabled, hsla(260,11%,95%,1));--lns-themeLight-color-formFieldBorder:var(--ds-border-input, hsla(260,11%,85%,1));--lns-themeLight-color-formFieldBackground:var(--ds-background-input, hsla(0,0%,100%,1));--lns-themeLight-color-buttonBorder:var(--ds-border, hsla(252,13%,46%,0.25));--lns-themeLight-color-upgrade:var(--ds-background-discovery, hsla(206,100%,93%,1));--lns-themeLight-color-upgradeHover:var(--ds-background-discovery-hovered, hsla(206,100%,85%,1));--lns-themeLight-color-upgradeActive:var(--ds-background-discovery-pressed, hsla(206,100%,77%,1));--lns-themeLight-color-tabBackground:var(--ds-background-neutral, hsla(252,13%,46%,0.15));--lns-themeLight-color-discoveryBackground:var(--ds-background-discovery, hsla(206,100%,93%,1));--lns-themeLight-color-discoveryLightBackground:var(--ds-background-discovery, hsla(206,100%,97%,1));--lns-themeLight-color-discoveryTitle:var(--ds-text, hsla(0,0%,13%,1));--lns-themeLight-color-discoveryHighlight:var(--ds-background-discovery, hsla(206,100%,77%,0.3));--lns-themeDark-color-primary:hsla(242,87%,73%,1);--lns-themeDark-color-primaryHover:hsla(242,88.4%,56.3%,1);--lns-themeDark-color-primaryActive:hsla(242,88.4%,45.3%,1);--lns-themeDark-color-body:var(--ds-text, hsla(240,7%,97%,1));--lns-themeDark-color-bodyDimmed:var(--ds-text-subtlest, hsla(240,7%,62%,1));--lns-themeDark-color-bodyInverse:var(--ds-text-inverse, hsla(240,3%,13%,1));--lns-themeDark-color-background:var(--ds-surface, hsla(0,0%,13%,1));--lns-themeDark-color-backgroundHover:var(--ds-background-neutral-subtle-hovered, hsla(0,0%,100%,0.1));--lns-themeDark-color-backgroundActive:var(--ds-background-neutral-subtle-pressed, hsla(0,0%,100%,0.2));--lns-themeDark-color-backgroundSecondary:var(--ds-surface-sunken, hsla(0,0%,100%,0.04));--lns-themeDark-color-backgroundSecondary2:var(--ds-surface-sunken, hsla(45,13%,44%,0.2));--lns-themeDark-color-backgroundInverse:var(--ds-background-neutral-bold, hsla(225,4%,82%,1));--lns-themeDark-color-overlay:var(--ds-surface-overlay, hsla(0,0%,20%,1));--lns-themeDark-color-border:var(--ds-border, hsla(259,12%,75%,0.2));--lns-themeDark-color-focusRing:hsla(242,88.4%,66.3%,0.8);--lns-themeDark-color-record:hsla(11,100%,62.2%,1);--lns-themeDark-color-recordHover:hsla(11,100%,52.2%,1);--lns-themeDark-color-recordActive:hsla(11,100%,42.2%,1);--lns-themeDark-color-info:var(--ds-background-information-bold, hsla(206,100%,73.3%,1));--lns-themeDark-color-success:var(--ds-background-accent-green-bolder, hsla(180,51.4%,51.6%,1));--lns-themeDark-color-warning:var(--ds-background-warning-bold, hsla(39,100%,68%,1));--lns-themeDark-color-danger:var(--ds-background-danger-bold, hsla(11,80%,45%,1));--lns-themeDark-color-dangerHover:var(--ds-background-danger-bold-hovered, hsla(11,80%,38%,1));--lns-themeDark-color-dangerActive:var(--ds-background-danger-bold-pressed, hsla(11,80%,31%,1));--lns-themeDark-color-backdrop:var(--ds-blanket, hsla(0,0%,13%,0.5));--lns-themeDark-color-backdropDark:hsla(0,0%,13%,0.9);--lns-themeDark-color-backdropTwilight:hsla(245,44.8%,46.9%,0.8);--lns-themeDark-color-disabledContent:var(--ds-text-disabled, hsla(240,7%,62%,1));--lns-themeDark-color-highlight:hsla(240,83.3%,66.3%,0.15);--lns-themeDark-color-disabledBackground:var(--ds-background-disabled, hsla(252,13%,23%,1));--lns-themeDark-color-formFieldBorder:var(--ds-border-input, hsla(252,13%,46%,1));--lns-themeDark-color-formFieldBackground:var(--ds-background-input, hsla(0,0%,13%,1));--lns-themeDark-color-buttonBorder:var(--ds-border, hsla(0,0%,100%,0.25));--lns-themeDark-color-upgrade:var(--ds-background-discovery, hsla(206,92%,81%,1));--lns-themeDark-color-upgradeHover:var(--ds-background-discovery-hovered, hsla(206,92%,74%,1));--lns-themeDark-color-upgradeActive:var(--ds-background-discovery-pressed, hsla(206,92%,67%,1));--lns-themeDark-color-tabBackground:var(--ds-background-neutral, hsla(0,0%,100%,0.15));--lns-themeDark-color-discoveryBackground:var(--ds-background-discovery, hsla(206,92%,81%,1));--lns-themeDark-color-discoveryLightBackground:var(--ds-background-discovery, hsla(0,0%,13%,1));--lns-themeDark-color-discoveryTitle:var(--ds-text, hsla(206,100%,73.3%,1));--lns-themeDark-color-discoveryHighlight:var(--ds-background-discovery, hsla(206,100%,77%,0.3));--lns-gradient-ai-primary:radial-gradient(134.96% 884.49% at 119.29% 112.58%, #DC43BE 0%, #565ADD 70%);--lns-gradient-ai-secondary:radial-gradient(100% 138.41% at 100% 100%, #EFF0FF 0%, #FFFFFF 100%);
    }


    .c\:red{color:var(--lns-color-red)}.c\:blurpleLight{color:var(--lns-color-blurpleLight)}.c\:blurpleMedium{color:var(--lns-color-blurpleMedium)}.c\:blurple{color:var(--lns-color-blurple)}.c\:blurpleDark{color:var(--lns-color-blurpleDark)}.c\:blurpleStrong{color:var(--lns-color-blurpleStrong)}.c\:offWhite{color:var(--lns-color-offWhite)}.c\:blueLight{color:var(--lns-color-blueLight)}.c\:blue{color:var(--lns-color-blue)}.c\:blueDark{color:var(--lns-color-blueDark)}.c\:orangeLight{color:var(--lns-color-orangeLight)}.c\:orange{color:var(--lns-color-orange)}.c\:orangeDark{color:var(--lns-color-orangeDark)}.c\:tealLight{color:var(--lns-color-tealLight)}.c\:teal{color:var(--lns-color-teal)}.c\:tealDark{color:var(--lns-color-tealDark)}.c\:yellowLight{color:var(--lns-color-yellowLight)}.c\:yellow{color:var(--lns-color-yellow)}.c\:yellowDark{color:var(--lns-color-yellowDark)}.c\:grey8{color:var(--lns-color-grey8)}.c\:grey7{color:var(--lns-color-grey7)}.c\:grey6{color:var(--lns-color-grey6)}.c\:grey5{color:var(--lns-color-grey5)}.c\:grey4{color:var(--lns-color-grey4)}.c\:grey3{color:var(--lns-color-grey3)}.c\:grey2{color:var(--lns-color-grey2)}.c\:grey1{color:var(--lns-color-grey1)}.c\:white{color:var(--lns-color-white)}.c\:primary{color:var(--lns-color-primary)}.c\:primaryHover{color:var(--lns-color-primaryHover)}.c\:primaryActive{color:var(--lns-color-primaryActive)}.c\:body{color:var(--lns-color-body)}.c\:bodyDimmed{color:var(--lns-color-bodyDimmed)}.c\:bodyInverse{color:var(--lns-color-bodyInverse)}.c\:background{color:var(--lns-color-background)}.c\:backgroundHover{color:var(--lns-color-backgroundHover)}.c\:backgroundActive{color:var(--lns-color-backgroundActive)}.c\:backgroundSecondary{color:var(--lns-color-backgroundSecondary)}.c\:backgroundSecondary2{color:var(--lns-color-backgroundSecondary2)}.c\:backgroundInverse{color:var(--lns-color-backgroundInverse)}.c\:overlay{color:var(--lns-color-overlay)}.c\:border{color:var(--lns-color-border)}.c\:focusRing{color:var(--lns-color-focusRing)}.c\:record{color:var(--lns-color-record)}.c\:recordHover{color:var(--lns-color-recordHover)}.c\:recordActive{color:var(--lns-color-recordActive)}.c\:info{color:var(--lns-color-info)}.c\:success{color:var(--lns-color-success)}.c\:warning{color:var(--lns-color-warning)}.c\:danger{color:var(--lns-color-danger)}.c\:dangerHover{color:var(--lns-color-dangerHover)}.c\:dangerActive{color:var(--lns-color-dangerActive)}.c\:backdrop{color:var(--lns-color-backdrop)}.c\:backdropDark{color:var(--lns-color-backdropDark)}.c\:backdropTwilight{color:var(--lns-color-backdropTwilight)}.c\:disabledContent{color:var(--lns-color-disabledContent)}.c\:highlight{color:var(--lns-color-highlight)}.c\:disabledBackground{color:var(--lns-color-disabledBackground)}.c\:formFieldBorder{color:var(--lns-color-formFieldBorder)}.c\:formFieldBackground{color:var(--lns-color-formFieldBackground)}.c\:buttonBorder{color:var(--lns-color-buttonBorder)}.c\:upgrade{color:var(--lns-color-upgrade)}.c\:upgradeHover{color:var(--lns-color-upgradeHover)}.c\:upgradeActive{color:var(--lns-color-upgradeActive)}.c\:tabBackground{color:var(--lns-color-tabBackground)}.c\:discoveryBackground{color:var(--lns-color-discoveryBackground)}.c\:discoveryLightBackground{color:var(--lns-color-discoveryLightBackground)}.c\:discoveryTitle{color:var(--lns-color-discoveryTitle)}.c\:discoveryHighlight{color:var(--lns-color-discoveryHighlight)}.shadow\:small{box-shadow:var(--lns-shadow-small)}.shadow\:medium{box-shadow:var(--lns-shadow-medium)}.shadow\:large{box-shadow:var(--lns-shadow-large)}.radius\:50{border-radius:var(--lns-radius-50)}.radius\:100{border-radius:var(--lns-radius-100)}.radius\:150{border-radius:var(--lns-radius-150)}.radius\:175{border-radius:var(--lns-radius-175)}.radius\:200{border-radius:var(--lns-radius-200)}.radius\:250{border-radius:var(--lns-radius-250)}.radius\:300{border-radius:var(--lns-radius-300)}.radius\:none{border-radius:var(--lns-radius-none)}.radius\:medium{border-radius:var(--lns-radius-medium)}.radius\:large{border-radius:var(--lns-radius-large)}.radius\:xlarge{border-radius:var(--lns-radius-xlarge)}.radius\:round{border-radius:var(--lns-radius-round)}.radius\:full{border-radius:var(--lns-radius-full)}.bgc\:red{background-color:var(--lns-color-red)}.bgc\:blurpleLight{background-color:var(--lns-color-blurpleLight)}.bgc\:blurpleMedium{background-color:var(--lns-color-blurpleMedium)}.bgc\:blurple{background-color:var(--lns-color-blurple)}.bgc\:blurpleDark{background-color:var(--lns-color-blurpleDark)}.bgc\:blurpleStrong{background-color:var(--lns-color-blurpleStrong)}.bgc\:offWhite{background-color:var(--lns-color-offWhite)}.bgc\:blueLight{background-color:var(--lns-color-blueLight)}.bgc\:blue{background-color:var(--lns-color-blue)}.bgc\:blueDark{background-color:var(--lns-color-blueDark)}.bgc\:orangeLight{background-color:var(--lns-color-orangeLight)}.bgc\:orange{background-color:var(--lns-color-orange)}.bgc\:orangeDark{background-color:var(--lns-color-orangeDark)}.bgc\:tealLight{background-color:var(--lns-color-tealLight)}.bgc\:teal{background-color:var(--lns-color-teal)}.bgc\:tealDark{background-color:var(--lns-color-tealDark)}.bgc\:yellowLight{background-color:var(--lns-color-yellowLight)}.bgc\:yellow{background-color:var(--lns-color-yellow)}.bgc\:yellowDark{background-color:var(--lns-color-yellowDark)}.bgc\:grey8{background-color:var(--lns-color-grey8)}.bgc\:grey7{background-color:var(--lns-color-grey7)}.bgc\:grey6{background-color:var(--lns-color-grey6)}.bgc\:grey5{background-color:var(--lns-color-grey5)}.bgc\:grey4{background-color:var(--lns-color-grey4)}.bgc\:grey3{background-color:var(--lns-color-grey3)}.bgc\:grey2{background-color:var(--lns-color-grey2)}.bgc\:grey1{background-color:var(--lns-color-grey1)}.bgc\:white{background-color:var(--lns-color-white)}.bgc\:primary{background-color:var(--lns-color-primary)}.bgc\:primaryHover{background-color:var(--lns-color-primaryHover)}.bgc\:primaryActive{background-color:var(--lns-color-primaryActive)}.bgc\:body{background-color:var(--lns-color-body)}.bgc\:bodyDimmed{background-color:var(--lns-color-bodyDimmed)}.bgc\:bodyInverse{background-color:var(--lns-color-bodyInverse)}.bgc\:background{background-color:var(--lns-color-background)}.bgc\:backgroundHover{background-color:var(--lns-color-backgroundHover)}.bgc\:backgroundActive{background-color:var(--lns-color-backgroundActive)}.bgc\:backgroundSecondary{background-color:var(--lns-color-backgroundSecondary)}.bgc\:backgroundSecondary2{background-color:var(--lns-color-backgroundSecondary2)}.bgc\:backgroundInverse{background-color:var(--lns-color-backgroundInverse)}.bgc\:overlay{background-color:var(--lns-color-overlay)}.bgc\:border{background-color:var(--lns-color-border)}.bgc\:focusRing{background-color:var(--lns-color-focusRing)}.bgc\:record{background-color:var(--lns-color-record)}.bgc\:recordHover{background-color:var(--lns-color-recordHover)}.bgc\:recordActive{background-color:var(--lns-color-recordActive)}.bgc\:info{background-color:var(--lns-color-info)}.bgc\:success{background-color:var(--lns-color-success)}.bgc\:warning{background-color:var(--lns-color-warning)}.bgc\:danger{background-color:var(--lns-color-danger)}.bgc\:dangerHover{background-color:var(--lns-color-dangerHover)}.bgc\:dangerActive{background-color:var(--lns-color-dangerActive)}.bgc\:backdrop{background-color:var(--lns-color-backdrop)}.bgc\:backdropDark{background-color:var(--lns-color-backdropDark)}.bgc\:backdropTwilight{background-color:var(--lns-color-backdropTwilight)}.bgc\:disabledContent{background-color:var(--lns-color-disabledContent)}.bgc\:highlight{background-color:var(--lns-color-highlight)}.bgc\:disabledBackground{background-color:var(--lns-color-disabledBackground)}.bgc\:formFieldBorder{background-color:var(--lns-color-formFieldBorder)}.bgc\:formFieldBackground{background-color:var(--lns-color-formFieldBackground)}.bgc\:buttonBorder{background-color:var(--lns-color-buttonBorder)}.bgc\:upgrade{background-color:var(--lns-color-upgrade)}.bgc\:upgradeHover{background-color:var(--lns-color-upgradeHover)}.bgc\:upgradeActive{background-color:var(--lns-color-upgradeActive)}.bgc\:tabBackground{background-color:var(--lns-color-tabBackground)}.bgc\:discoveryBackground{background-color:var(--lns-color-discoveryBackground)}.bgc\:discoveryLightBackground{background-color:var(--lns-color-discoveryLightBackground)}.bgc\:discoveryTitle{background-color:var(--lns-color-discoveryTitle)}.bgc\:discoveryHighlight{background-color:var(--lns-color-discoveryHighlight)}.m\:0{margin:0}.m\:auto{margin:auto}.m\:xsmall{margin:var(--lns-space-xsmall)}.m\:small{margin:var(--lns-space-small)}.m\:medium{margin:var(--lns-space-medium)}.m\:large{margin:var(--lns-space-large)}.m\:xlarge{margin:var(--lns-space-xlarge)}.m\:xxlarge{margin:var(--lns-space-xxlarge)}.mt\:0{margin-top:0}.mt\:auto{margin-top:auto}.mt\:xsmall{margin-top:var(--lns-space-xsmall)}.mt\:small{margin-top:var(--lns-space-small)}.mt\:medium{margin-top:var(--lns-space-medium)}.mt\:large{margin-top:var(--lns-space-large)}.mt\:xlarge{margin-top:var(--lns-space-xlarge)}.mt\:xxlarge{margin-top:var(--lns-space-xxlarge)}.mb\:0{margin-bottom:0}.mb\:auto{margin-bottom:auto}.mb\:xsmall{margin-bottom:var(--lns-space-xsmall)}.mb\:small{margin-bottom:var(--lns-space-small)}.mb\:medium{margin-bottom:var(--lns-space-medium)}.mb\:large{margin-bottom:var(--lns-space-large)}.mb\:xlarge{margin-bottom:var(--lns-space-xlarge)}.mb\:xxlarge{margin-bottom:var(--lns-space-xxlarge)}.ml\:0{margin-left:0}.ml\:auto{margin-left:auto}.ml\:xsmall{margin-left:var(--lns-space-xsmall)}.ml\:small{margin-left:var(--lns-space-small)}.ml\:medium{margin-left:var(--lns-space-medium)}.ml\:large{margin-left:var(--lns-space-large)}.ml\:xlarge{margin-left:var(--lns-space-xlarge)}.ml\:xxlarge{margin-left:var(--lns-space-xxlarge)}.mr\:0{margin-right:0}.mr\:auto{margin-right:auto}.mr\:xsmall{margin-right:var(--lns-space-xsmall)}.mr\:small{margin-right:var(--lns-space-small)}.mr\:medium{margin-right:var(--lns-space-medium)}.mr\:large{margin-right:var(--lns-space-large)}.mr\:xlarge{margin-right:var(--lns-space-xlarge)}.mr\:xxlarge{margin-right:var(--lns-space-xxlarge)}.mx\:0{margin-left:0;margin-right:0}.mx\:auto{margin-left:auto;margin-right:auto}.mx\:xsmall{margin-left:var(--lns-space-xsmall);margin-right:var(--lns-space-xsmall)}.mx\:small{margin-left:var(--lns-space-small);margin-right:var(--lns-space-small)}.mx\:medium{margin-left:var(--lns-space-medium);margin-right:var(--lns-space-medium)}.mx\:large{margin-left:var(--lns-space-large);margin-right:var(--lns-space-large)}.mx\:xlarge{margin-left:var(--lns-space-xlarge);margin-right:var(--lns-space-xlarge)}.mx\:xxlarge{margin-left:var(--lns-space-xxlarge);margin-right:var(--lns-space-xxlarge)}.my\:0{margin-top:0;margin-bottom:0}.my\:auto{margin-top:auto;margin-bottom:auto}.my\:xsmall{margin-top:var(--lns-space-xsmall);margin-bottom:var(--lns-space-xsmall)}.my\:small{margin-top:var(--lns-space-small);margin-bottom:var(--lns-space-small)}.my\:medium{margin-top:var(--lns-space-medium);margin-bottom:var(--lns-space-medium)}.my\:large{margin-top:var(--lns-space-large);margin-bottom:var(--lns-space-large)}.my\:xlarge{margin-top:var(--lns-space-xlarge);margin-bottom:var(--lns-space-xlarge)}.my\:xxlarge{margin-top:var(--lns-space-xxlarge);margin-bottom:var(--lns-space-xxlarge)}.p\:0{padding:0}.p\:xsmall{padding:var(--lns-space-xsmall)}.p\:small{padding:var(--lns-space-small)}.p\:medium{padding:var(--lns-space-medium)}.p\:large{padding:var(--lns-space-large)}.p\:xlarge{padding:var(--lns-space-xlarge)}.p\:xxlarge{padding:var(--lns-space-xxlarge)}.pt\:0{padding-top:0}.pt\:xsmall{padding-top:var(--lns-space-xsmall)}.pt\:small{padding-top:var(--lns-space-small)}.pt\:medium{padding-top:var(--lns-space-medium)}.pt\:large{padding-top:var(--lns-space-large)}.pt\:xlarge{padding-top:var(--lns-space-xlarge)}.pt\:xxlarge{padding-top:var(--lns-space-xxlarge)}.pb\:0{padding-bottom:0}.pb\:xsmall{padding-bottom:var(--lns-space-xsmall)}.pb\:small{padding-bottom:var(--lns-space-small)}.pb\:medium{padding-bottom:var(--lns-space-medium)}.pb\:large{padding-bottom:var(--lns-space-large)}.pb\:xlarge{padding-bottom:var(--lns-space-xlarge)}.pb\:xxlarge{padding-bottom:var(--lns-space-xxlarge)}.pl\:0{padding-left:0}.pl\:xsmall{padding-left:var(--lns-space-xsmall)}.pl\:small{padding-left:var(--lns-space-small)}.pl\:medium{padding-left:var(--lns-space-medium)}.pl\:large{padding-left:var(--lns-space-large)}.pl\:xlarge{padding-left:var(--lns-space-xlarge)}.pl\:xxlarge{padding-left:var(--lns-space-xxlarge)}.pr\:0{padding-right:0}.pr\:xsmall{padding-right:var(--lns-space-xsmall)}.pr\:small{padding-right:var(--lns-space-small)}.pr\:medium{padding-right:var(--lns-space-medium)}.pr\:large{padding-right:var(--lns-space-large)}.pr\:xlarge{padding-right:var(--lns-space-xlarge)}.pr\:xxlarge{padding-right:var(--lns-space-xxlarge)}.px\:0{padding-left:0;padding-right:0}.px\:xsmall{padding-left:var(--lns-space-xsmall);padding-right:var(--lns-space-xsmall)}.px\:small{padding-left:var(--lns-space-small);padding-right:var(--lns-space-small)}.px\:medium{padding-left:var(--lns-space-medium);padding-right:var(--lns-space-medium)}.px\:large{padding-left:var(--lns-space-large);padding-right:var(--lns-space-large)}.px\:xlarge{padding-left:var(--lns-space-xlarge);padding-right:var(--lns-space-xlarge)}.px\:xxlarge{padding-left:var(--lns-space-xxlarge);padding-right:var(--lns-space-xxlarge)}.py\:0{padding-top:0;padding-bottom:0}.py\:xsmall{padding-top:var(--lns-space-xsmall);padding-bottom:var(--lns-space-xsmall)}.py\:small{padding-top:var(--lns-space-small);padding-bottom:var(--lns-space-small)}.py\:medium{padding-top:var(--lns-space-medium);padding-bottom:var(--lns-space-medium)}.py\:large{padding-top:var(--lns-space-large);padding-bottom:var(--lns-space-large)}.py\:xlarge{padding-top:var(--lns-space-xlarge);padding-bottom:var(--lns-space-xlarge)}.py\:xxlarge{padding-top:var(--lns-space-xxlarge);padding-bottom:var(--lns-space-xxlarge)}.text\:small{font-size:var(--lns-fontSize-small);line-height:var(--lns-lineHeight-small);letter-spacing:var(--lns-letterSpacing-small);font-weight:var(--lns-fontWeight-regular)}.text\:body-sm{font-size:var(--lns-fontSize-body-sm);line-height:var(--lns-lineHeight-body-sm);letter-spacing:var(--lns-letterSpacing-body-sm);font-weight:var(--lns-fontWeight-regular)}.text\:medium{font-size:var(--lns-fontSize-medium);line-height:var(--lns-lineHeight-medium);letter-spacing:var(--lns-letterSpacing-medium);font-weight:var(--lns-fontWeight-regular)}.text\:body-md{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);letter-spacing:var(--lns-letterSpacing-body-md);font-weight:var(--lns-fontWeight-regular)}.text\:large{font-size:var(--lns-fontSize-large);line-height:var(--lns-lineHeight-large);letter-spacing:var(--lns-letterSpacing-large);font-weight:var(--lns-fontWeight-regular)}.text\:body-lg{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);letter-spacing:var(--lns-letterSpacing-body-lg);font-weight:var(--lns-fontWeight-regular)}.text\:xlarge{font-size:var(--lns-fontSize-xlarge);line-height:var(--lns-lineHeight-xlarge);letter-spacing:var(--lns-letterSpacing-xlarge);font-weight:var(--lns-fontWeight-bold)}.text\:heading-sm{font-size:var(--lns-fontSize-heading-sm);line-height:var(--lns-lineHeight-heading-sm);letter-spacing:var(--lns-letterSpacing-heading-sm);font-weight:var(--lns-fontWeight-bold)}.text\:xxlarge{font-size:var(--lns-fontSize-xxlarge);line-height:var(--lns-lineHeight-xxlarge);letter-spacing:var(--lns-letterSpacing-xxlarge);font-weight:var(--lns-fontWeight-bold)}.text\:heading-md{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);letter-spacing:var(--lns-letterSpacing-heading-md);font-weight:var(--lns-fontWeight-bold)}.text\:xxxlarge{font-size:var(--lns-fontSize-xxxlarge);line-height:var(--lns-lineHeight-xxxlarge);letter-spacing:var(--lns-letterSpacing-xxxlarge);font-weight:var(--lns-fontWeight-bold)}.text\:heading-lg{font-size:var(--lns-fontSize-heading-lg);line-height:var(--lns-lineHeight-heading-lg);letter-spacing:var(--lns-letterSpacing-heading-lg);font-weight:var(--lns-fontWeight-bold)}.weight\:book{font-weight:var(--lns-fontWeight-book)}.weight\:regular{font-weight:var(--lns-fontWeight-regular)}.weight\:medium{font-weight:var(--lns-fontWeight-medium)}.weight\:bold{font-weight:var(--lns-fontWeight-bold)}.text\:body{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);font-weight:var(--lns-fontWeight-regular)}.text\:title{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);font-weight:var(--lns-fontWeight-bold)}.text\:mainTitle{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);font-weight:var(--lns-fontWeight-bold)}.text\:left{text-align:left}.text\:right{text-align:right}.text\:center{text-align:center}.border{border:1px solid var(--lns-color-border)}.borderTop{border-top:1px solid var(--lns-color-border)}.borderBottom{border-bottom:1px solid var(--lns-color-border)}.borderLeft{border-left:1px solid var(--lns-color-border)}.borderRight{border-right:1px solid var(--lns-color-border)}.inline{display:inline}.block{display:block}.flex{display:flex}.inlineBlock{display:inline-block}.inlineFlex{display:inline-flex}.none{display:none}.flexWrap{flex-wrap:wrap}.flexDirection\:column{flex-direction:column}.flexDirection\:row{flex-direction:row}.items\:stretch{align-items:stretch}.items\:center{align-items:center}.items\:baseline{align-items:baseline}.items\:flexStart{align-items:flex-start}.items\:flexEnd{align-items:flex-end}.items\:selfStart{align-items:self-start}.items\:selfEnd{align-items:self-end}.justify\:flexStart{justify-content:flex-start}.justify\:flexEnd{justify-content:flex-end}.justify\:center{justify-content:center}.justify\:spaceBetween{justify-content:space-between}.justify\:spaceAround{justify-content:space-around}.justify\:spaceEvenly{justify-content:space-evenly}.grow\:0{flex-grow:0}.grow\:1{flex-grow:1}.shrink\:0{flex-shrink:0}.shrink\:1{flex-shrink:1}.self\:auto{align-self:auto}.self\:flexStart{align-self:flex-start}.self\:flexEnd{align-self:flex-end}.self\:center{align-self:center}.self\:baseline{align-self:baseline}.self\:stretch{align-self:stretch}.overflow\:hidden{overflow:hidden}.overflow\:auto{overflow:auto}.relative{position:relative}.absolute{position:absolute}.sticky{position:sticky}.fixed{position:fixed}.top\:0{top:0}.top\:auto{top:auto}.top\:xsmall{top:var(--lns-space-xsmall)}.top\:small{top:var(--lns-space-small)}.top\:medium{top:var(--lns-space-medium)}.top\:large{top:var(--lns-space-large)}.top\:xlarge{top:var(--lns-space-xlarge)}.top\:xxlarge{top:var(--lns-space-xxlarge)}.bottom\:0{bottom:0}.bottom\:auto{bottom:auto}.bottom\:xsmall{bottom:var(--lns-space-xsmall)}.bottom\:small{bottom:var(--lns-space-small)}.bottom\:medium{bottom:var(--lns-space-medium)}.bottom\:large{bottom:var(--lns-space-large)}.bottom\:xlarge{bottom:var(--lns-space-xlarge)}.bottom\:xxlarge{bottom:var(--lns-space-xxlarge)}.left\:0{left:0}.left\:auto{left:auto}.left\:xsmall{left:var(--lns-space-xsmall)}.left\:small{left:var(--lns-space-small)}.left\:medium{left:var(--lns-space-medium)}.left\:large{left:var(--lns-space-large)}.left\:xlarge{left:var(--lns-space-xlarge)}.left\:xxlarge{left:var(--lns-space-xxlarge)}.right\:0{right:0}.right\:auto{right:auto}.right\:xsmall{right:var(--lns-space-xsmall)}.right\:small{right:var(--lns-space-small)}.right\:medium{right:var(--lns-space-medium)}.right\:large{right:var(--lns-space-large)}.right\:xlarge{right:var(--lns-space-xlarge)}.right\:xxlarge{right:var(--lns-space-xxlarge)}.width\:auto{width:auto}.width\:full{width:100%}.width\:0{width:0}.minWidth\:0{min-width:0}.height\:auto{height:auto}.height\:full{height:100%}.height\:0{height:0}.ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.srOnly{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0}@media(min-width:31em){.xs-c\:red{color:var(--lns-color-red)}.xs-c\:blurpleLight{color:var(--lns-color-blurpleLight)}.xs-c\:blurpleMedium{color:var(--lns-color-blurpleMedium)}.xs-c\:blurple{color:var(--lns-color-blurple)}.xs-c\:blurpleDark{color:var(--lns-color-blurpleDark)}.xs-c\:blurpleStrong{color:var(--lns-color-blurpleStrong)}.xs-c\:offWhite{color:var(--lns-color-offWhite)}.xs-c\:blueLight{color:var(--lns-color-blueLight)}.xs-c\:blue{color:var(--lns-color-blue)}.xs-c\:blueDark{color:var(--lns-color-blueDark)}.xs-c\:orangeLight{color:var(--lns-color-orangeLight)}.xs-c\:orange{color:var(--lns-color-orange)}.xs-c\:orangeDark{color:var(--lns-color-orangeDark)}.xs-c\:tealLight{color:var(--lns-color-tealLight)}.xs-c\:teal{color:var(--lns-color-teal)}.xs-c\:tealDark{color:var(--lns-color-tealDark)}.xs-c\:yellowLight{color:var(--lns-color-yellowLight)}.xs-c\:yellow{color:var(--lns-color-yellow)}.xs-c\:yellowDark{color:var(--lns-color-yellowDark)}.xs-c\:grey8{color:var(--lns-color-grey8)}.xs-c\:grey7{color:var(--lns-color-grey7)}.xs-c\:grey6{color:var(--lns-color-grey6)}.xs-c\:grey5{color:var(--lns-color-grey5)}.xs-c\:grey4{color:var(--lns-color-grey4)}.xs-c\:grey3{color:var(--lns-color-grey3)}.xs-c\:grey2{color:var(--lns-color-grey2)}.xs-c\:grey1{color:var(--lns-color-grey1)}.xs-c\:white{color:var(--lns-color-white)}.xs-c\:primary{color:var(--lns-color-primary)}.xs-c\:primaryHover{color:var(--lns-color-primaryHover)}.xs-c\:primaryActive{color:var(--lns-color-primaryActive)}.xs-c\:body{color:var(--lns-color-body)}.xs-c\:bodyDimmed{color:var(--lns-color-bodyDimmed)}.xs-c\:bodyInverse{color:var(--lns-color-bodyInverse)}.xs-c\:background{color:var(--lns-color-background)}.xs-c\:backgroundHover{color:var(--lns-color-backgroundHover)}.xs-c\:backgroundActive{color:var(--lns-color-backgroundActive)}.xs-c\:backgroundSecondary{color:var(--lns-color-backgroundSecondary)}.xs-c\:backgroundSecondary2{color:var(--lns-color-backgroundSecondary2)}.xs-c\:backgroundInverse{color:var(--lns-color-backgroundInverse)}.xs-c\:overlay{color:var(--lns-color-overlay)}.xs-c\:border{color:var(--lns-color-border)}.xs-c\:focusRing{color:var(--lns-color-focusRing)}.xs-c\:record{color:var(--lns-color-record)}.xs-c\:recordHover{color:var(--lns-color-recordHover)}.xs-c\:recordActive{color:var(--lns-color-recordActive)}.xs-c\:info{color:var(--lns-color-info)}.xs-c\:success{color:var(--lns-color-success)}.xs-c\:warning{color:var(--lns-color-warning)}.xs-c\:danger{color:var(--lns-color-danger)}.xs-c\:dangerHover{color:var(--lns-color-dangerHover)}.xs-c\:dangerActive{color:var(--lns-color-dangerActive)}.xs-c\:backdrop{color:var(--lns-color-backdrop)}.xs-c\:backdropDark{color:var(--lns-color-backdropDark)}.xs-c\:backdropTwilight{color:var(--lns-color-backdropTwilight)}.xs-c\:disabledContent{color:var(--lns-color-disabledContent)}.xs-c\:highlight{color:var(--lns-color-highlight)}.xs-c\:disabledBackground{color:var(--lns-color-disabledBackground)}.xs-c\:formFieldBorder{color:var(--lns-color-formFieldBorder)}.xs-c\:formFieldBackground{color:var(--lns-color-formFieldBackground)}.xs-c\:buttonBorder{color:var(--lns-color-buttonBorder)}.xs-c\:upgrade{color:var(--lns-color-upgrade)}.xs-c\:upgradeHover{color:var(--lns-color-upgradeHover)}.xs-c\:upgradeActive{color:var(--lns-color-upgradeActive)}.xs-c\:tabBackground{color:var(--lns-color-tabBackground)}.xs-c\:discoveryBackground{color:var(--lns-color-discoveryBackground)}.xs-c\:discoveryLightBackground{color:var(--lns-color-discoveryLightBackground)}.xs-c\:discoveryTitle{color:var(--lns-color-discoveryTitle)}.xs-c\:discoveryHighlight{color:var(--lns-color-discoveryHighlight)}.xs-shadow\:small{box-shadow:var(--lns-shadow-small)}.xs-shadow\:medium{box-shadow:var(--lns-shadow-medium)}.xs-shadow\:large{box-shadow:var(--lns-shadow-large)}.xs-radius\:50{border-radius:var(--lns-radius-50)}.xs-radius\:100{border-radius:var(--lns-radius-100)}.xs-radius\:150{border-radius:var(--lns-radius-150)}.xs-radius\:175{border-radius:var(--lns-radius-175)}.xs-radius\:200{border-radius:var(--lns-radius-200)}.xs-radius\:250{border-radius:var(--lns-radius-250)}.xs-radius\:300{border-radius:var(--lns-radius-300)}.xs-radius\:none{border-radius:var(--lns-radius-none)}.xs-radius\:medium{border-radius:var(--lns-radius-medium)}.xs-radius\:large{border-radius:var(--lns-radius-large)}.xs-radius\:xlarge{border-radius:var(--lns-radius-xlarge)}.xs-radius\:round{border-radius:var(--lns-radius-round)}.xs-radius\:full{border-radius:var(--lns-radius-full)}.xs-bgc\:red{background-color:var(--lns-color-red)}.xs-bgc\:blurpleLight{background-color:var(--lns-color-blurpleLight)}.xs-bgc\:blurpleMedium{background-color:var(--lns-color-blurpleMedium)}.xs-bgc\:blurple{background-color:var(--lns-color-blurple)}.xs-bgc\:blurpleDark{background-color:var(--lns-color-blurpleDark)}.xs-bgc\:blurpleStrong{background-color:var(--lns-color-blurpleStrong)}.xs-bgc\:offWhite{background-color:var(--lns-color-offWhite)}.xs-bgc\:blueLight{background-color:var(--lns-color-blueLight)}.xs-bgc\:blue{background-color:var(--lns-color-blue)}.xs-bgc\:blueDark{background-color:var(--lns-color-blueDark)}.xs-bgc\:orangeLight{background-color:var(--lns-color-orangeLight)}.xs-bgc\:orange{background-color:var(--lns-color-orange)}.xs-bgc\:orangeDark{background-color:var(--lns-color-orangeDark)}.xs-bgc\:tealLight{background-color:var(--lns-color-tealLight)}.xs-bgc\:teal{background-color:var(--lns-color-teal)}.xs-bgc\:tealDark{background-color:var(--lns-color-tealDark)}.xs-bgc\:yellowLight{background-color:var(--lns-color-yellowLight)}.xs-bgc\:yellow{background-color:var(--lns-color-yellow)}.xs-bgc\:yellowDark{background-color:var(--lns-color-yellowDark)}.xs-bgc\:grey8{background-color:var(--lns-color-grey8)}.xs-bgc\:grey7{background-color:var(--lns-color-grey7)}.xs-bgc\:grey6{background-color:var(--lns-color-grey6)}.xs-bgc\:grey5{background-color:var(--lns-color-grey5)}.xs-bgc\:grey4{background-color:var(--lns-color-grey4)}.xs-bgc\:grey3{background-color:var(--lns-color-grey3)}.xs-bgc\:grey2{background-color:var(--lns-color-grey2)}.xs-bgc\:grey1{background-color:var(--lns-color-grey1)}.xs-bgc\:white{background-color:var(--lns-color-white)}.xs-bgc\:primary{background-color:var(--lns-color-primary)}.xs-bgc\:primaryHover{background-color:var(--lns-color-primaryHover)}.xs-bgc\:primaryActive{background-color:var(--lns-color-primaryActive)}.xs-bgc\:body{background-color:var(--lns-color-body)}.xs-bgc\:bodyDimmed{background-color:var(--lns-color-bodyDimmed)}.xs-bgc\:bodyInverse{background-color:var(--lns-color-bodyInverse)}.xs-bgc\:background{background-color:var(--lns-color-background)}.xs-bgc\:backgroundHover{background-color:var(--lns-color-backgroundHover)}.xs-bgc\:backgroundActive{background-color:var(--lns-color-backgroundActive)}.xs-bgc\:backgroundSecondary{background-color:var(--lns-color-backgroundSecondary)}.xs-bgc\:backgroundSecondary2{background-color:var(--lns-color-backgroundSecondary2)}.xs-bgc\:backgroundInverse{background-color:var(--lns-color-backgroundInverse)}.xs-bgc\:overlay{background-color:var(--lns-color-overlay)}.xs-bgc\:border{background-color:var(--lns-color-border)}.xs-bgc\:focusRing{background-color:var(--lns-color-focusRing)}.xs-bgc\:record{background-color:var(--lns-color-record)}.xs-bgc\:recordHover{background-color:var(--lns-color-recordHover)}.xs-bgc\:recordActive{background-color:var(--lns-color-recordActive)}.xs-bgc\:info{background-color:var(--lns-color-info)}.xs-bgc\:success{background-color:var(--lns-color-success)}.xs-bgc\:warning{background-color:var(--lns-color-warning)}.xs-bgc\:danger{background-color:var(--lns-color-danger)}.xs-bgc\:dangerHover{background-color:var(--lns-color-dangerHover)}.xs-bgc\:dangerActive{background-color:var(--lns-color-dangerActive)}.xs-bgc\:backdrop{background-color:var(--lns-color-backdrop)}.xs-bgc\:backdropDark{background-color:var(--lns-color-backdropDark)}.xs-bgc\:backdropTwilight{background-color:var(--lns-color-backdropTwilight)}.xs-bgc\:disabledContent{background-color:var(--lns-color-disabledContent)}.xs-bgc\:highlight{background-color:var(--lns-color-highlight)}.xs-bgc\:disabledBackground{background-color:var(--lns-color-disabledBackground)}.xs-bgc\:formFieldBorder{background-color:var(--lns-color-formFieldBorder)}.xs-bgc\:formFieldBackground{background-color:var(--lns-color-formFieldBackground)}.xs-bgc\:buttonBorder{background-color:var(--lns-color-buttonBorder)}.xs-bgc\:upgrade{background-color:var(--lns-color-upgrade)}.xs-bgc\:upgradeHover{background-color:var(--lns-color-upgradeHover)}.xs-bgc\:upgradeActive{background-color:var(--lns-color-upgradeActive)}.xs-bgc\:tabBackground{background-color:var(--lns-color-tabBackground)}.xs-bgc\:discoveryBackground{background-color:var(--lns-color-discoveryBackground)}.xs-bgc\:discoveryLightBackground{background-color:var(--lns-color-discoveryLightBackground)}.xs-bgc\:discoveryTitle{background-color:var(--lns-color-discoveryTitle)}.xs-bgc\:discoveryHighlight{background-color:var(--lns-color-discoveryHighlight)}.xs-m\:0{margin:0}.xs-m\:auto{margin:auto}.xs-m\:xsmall{margin:var(--lns-space-xsmall)}.xs-m\:small{margin:var(--lns-space-small)}.xs-m\:medium{margin:var(--lns-space-medium)}.xs-m\:large{margin:var(--lns-space-large)}.xs-m\:xlarge{margin:var(--lns-space-xlarge)}.xs-m\:xxlarge{margin:var(--lns-space-xxlarge)}.xs-mt\:0{margin-top:0}.xs-mt\:auto{margin-top:auto}.xs-mt\:xsmall{margin-top:var(--lns-space-xsmall)}.xs-mt\:small{margin-top:var(--lns-space-small)}.xs-mt\:medium{margin-top:var(--lns-space-medium)}.xs-mt\:large{margin-top:var(--lns-space-large)}.xs-mt\:xlarge{margin-top:var(--lns-space-xlarge)}.xs-mt\:xxlarge{margin-top:var(--lns-space-xxlarge)}.xs-mb\:0{margin-bottom:0}.xs-mb\:auto{margin-bottom:auto}.xs-mb\:xsmall{margin-bottom:var(--lns-space-xsmall)}.xs-mb\:small{margin-bottom:var(--lns-space-small)}.xs-mb\:medium{margin-bottom:var(--lns-space-medium)}.xs-mb\:large{margin-bottom:var(--lns-space-large)}.xs-mb\:xlarge{margin-bottom:var(--lns-space-xlarge)}.xs-mb\:xxlarge{margin-bottom:var(--lns-space-xxlarge)}.xs-ml\:0{margin-left:0}.xs-ml\:auto{margin-left:auto}.xs-ml\:xsmall{margin-left:var(--lns-space-xsmall)}.xs-ml\:small{margin-left:var(--lns-space-small)}.xs-ml\:medium{margin-left:var(--lns-space-medium)}.xs-ml\:large{margin-left:var(--lns-space-large)}.xs-ml\:xlarge{margin-left:var(--lns-space-xlarge)}.xs-ml\:xxlarge{margin-left:var(--lns-space-xxlarge)}.xs-mr\:0{margin-right:0}.xs-mr\:auto{margin-right:auto}.xs-mr\:xsmall{margin-right:var(--lns-space-xsmall)}.xs-mr\:small{margin-right:var(--lns-space-small)}.xs-mr\:medium{margin-right:var(--lns-space-medium)}.xs-mr\:large{margin-right:var(--lns-space-large)}.xs-mr\:xlarge{margin-right:var(--lns-space-xlarge)}.xs-mr\:xxlarge{margin-right:var(--lns-space-xxlarge)}.xs-mx\:0{margin-left:0;margin-right:0}.xs-mx\:auto{margin-left:auto;margin-right:auto}.xs-mx\:xsmall{margin-left:var(--lns-space-xsmall);margin-right:var(--lns-space-xsmall)}.xs-mx\:small{margin-left:var(--lns-space-small);margin-right:var(--lns-space-small)}.xs-mx\:medium{margin-left:var(--lns-space-medium);margin-right:var(--lns-space-medium)}.xs-mx\:large{margin-left:var(--lns-space-large);margin-right:var(--lns-space-large)}.xs-mx\:xlarge{margin-left:var(--lns-space-xlarge);margin-right:var(--lns-space-xlarge)}.xs-mx\:xxlarge{margin-left:var(--lns-space-xxlarge);margin-right:var(--lns-space-xxlarge)}.xs-my\:0{margin-top:0;margin-bottom:0}.xs-my\:auto{margin-top:auto;margin-bottom:auto}.xs-my\:xsmall{margin-top:var(--lns-space-xsmall);margin-bottom:var(--lns-space-xsmall)}.xs-my\:small{margin-top:var(--lns-space-small);margin-bottom:var(--lns-space-small)}.xs-my\:medium{margin-top:var(--lns-space-medium);margin-bottom:var(--lns-space-medium)}.xs-my\:large{margin-top:var(--lns-space-large);margin-bottom:var(--lns-space-large)}.xs-my\:xlarge{margin-top:var(--lns-space-xlarge);margin-bottom:var(--lns-space-xlarge)}.xs-my\:xxlarge{margin-top:var(--lns-space-xxlarge);margin-bottom:var(--lns-space-xxlarge)}.xs-p\:0{padding:0}.xs-p\:xsmall{padding:var(--lns-space-xsmall)}.xs-p\:small{padding:var(--lns-space-small)}.xs-p\:medium{padding:var(--lns-space-medium)}.xs-p\:large{padding:var(--lns-space-large)}.xs-p\:xlarge{padding:var(--lns-space-xlarge)}.xs-p\:xxlarge{padding:var(--lns-space-xxlarge)}.xs-pt\:0{padding-top:0}.xs-pt\:xsmall{padding-top:var(--lns-space-xsmall)}.xs-pt\:small{padding-top:var(--lns-space-small)}.xs-pt\:medium{padding-top:var(--lns-space-medium)}.xs-pt\:large{padding-top:var(--lns-space-large)}.xs-pt\:xlarge{padding-top:var(--lns-space-xlarge)}.xs-pt\:xxlarge{padding-top:var(--lns-space-xxlarge)}.xs-pb\:0{padding-bottom:0}.xs-pb\:xsmall{padding-bottom:var(--lns-space-xsmall)}.xs-pb\:small{padding-bottom:var(--lns-space-small)}.xs-pb\:medium{padding-bottom:var(--lns-space-medium)}.xs-pb\:large{padding-bottom:var(--lns-space-large)}.xs-pb\:xlarge{padding-bottom:var(--lns-space-xlarge)}.xs-pb\:xxlarge{padding-bottom:var(--lns-space-xxlarge)}.xs-pl\:0{padding-left:0}.xs-pl\:xsmall{padding-left:var(--lns-space-xsmall)}.xs-pl\:small{padding-left:var(--lns-space-small)}.xs-pl\:medium{padding-left:var(--lns-space-medium)}.xs-pl\:large{padding-left:var(--lns-space-large)}.xs-pl\:xlarge{padding-left:var(--lns-space-xlarge)}.xs-pl\:xxlarge{padding-left:var(--lns-space-xxlarge)}.xs-pr\:0{padding-right:0}.xs-pr\:xsmall{padding-right:var(--lns-space-xsmall)}.xs-pr\:small{padding-right:var(--lns-space-small)}.xs-pr\:medium{padding-right:var(--lns-space-medium)}.xs-pr\:large{padding-right:var(--lns-space-large)}.xs-pr\:xlarge{padding-right:var(--lns-space-xlarge)}.xs-pr\:xxlarge{padding-right:var(--lns-space-xxlarge)}.xs-px\:0{padding-left:0;padding-right:0}.xs-px\:xsmall{padding-left:var(--lns-space-xsmall);padding-right:var(--lns-space-xsmall)}.xs-px\:small{padding-left:var(--lns-space-small);padding-right:var(--lns-space-small)}.xs-px\:medium{padding-left:var(--lns-space-medium);padding-right:var(--lns-space-medium)}.xs-px\:large{padding-left:var(--lns-space-large);padding-right:var(--lns-space-large)}.xs-px\:xlarge{padding-left:var(--lns-space-xlarge);padding-right:var(--lns-space-xlarge)}.xs-px\:xxlarge{padding-left:var(--lns-space-xxlarge);padding-right:var(--lns-space-xxlarge)}.xs-py\:0{padding-top:0;padding-bottom:0}.xs-py\:xsmall{padding-top:var(--lns-space-xsmall);padding-bottom:var(--lns-space-xsmall)}.xs-py\:small{padding-top:var(--lns-space-small);padding-bottom:var(--lns-space-small)}.xs-py\:medium{padding-top:var(--lns-space-medium);padding-bottom:var(--lns-space-medium)}.xs-py\:large{padding-top:var(--lns-space-large);padding-bottom:var(--lns-space-large)}.xs-py\:xlarge{padding-top:var(--lns-space-xlarge);padding-bottom:var(--lns-space-xlarge)}.xs-py\:xxlarge{padding-top:var(--lns-space-xxlarge);padding-bottom:var(--lns-space-xxlarge)}.xs-text\:small{font-size:var(--lns-fontSize-small);line-height:var(--lns-lineHeight-small);letter-spacing:var(--lns-letterSpacing-small);font-weight:var(--lns-fontWeight-regular)}.xs-text\:body-sm{font-size:var(--lns-fontSize-body-sm);line-height:var(--lns-lineHeight-body-sm);letter-spacing:var(--lns-letterSpacing-body-sm);font-weight:var(--lns-fontWeight-regular)}.xs-text\:medium{font-size:var(--lns-fontSize-medium);line-height:var(--lns-lineHeight-medium);letter-spacing:var(--lns-letterSpacing-medium);font-weight:var(--lns-fontWeight-regular)}.xs-text\:body-md{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);letter-spacing:var(--lns-letterSpacing-body-md);font-weight:var(--lns-fontWeight-regular)}.xs-text\:large{font-size:var(--lns-fontSize-large);line-height:var(--lns-lineHeight-large);letter-spacing:var(--lns-letterSpacing-large);font-weight:var(--lns-fontWeight-regular)}.xs-text\:body-lg{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);letter-spacing:var(--lns-letterSpacing-body-lg);font-weight:var(--lns-fontWeight-regular)}.xs-text\:xlarge{font-size:var(--lns-fontSize-xlarge);line-height:var(--lns-lineHeight-xlarge);letter-spacing:var(--lns-letterSpacing-xlarge);font-weight:var(--lns-fontWeight-bold)}.xs-text\:heading-sm{font-size:var(--lns-fontSize-heading-sm);line-height:var(--lns-lineHeight-heading-sm);letter-spacing:var(--lns-letterSpacing-heading-sm);font-weight:var(--lns-fontWeight-bold)}.xs-text\:xxlarge{font-size:var(--lns-fontSize-xxlarge);line-height:var(--lns-lineHeight-xxlarge);letter-spacing:var(--lns-letterSpacing-xxlarge);font-weight:var(--lns-fontWeight-bold)}.xs-text\:heading-md{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);letter-spacing:var(--lns-letterSpacing-heading-md);font-weight:var(--lns-fontWeight-bold)}.xs-text\:xxxlarge{font-size:var(--lns-fontSize-xxxlarge);line-height:var(--lns-lineHeight-xxxlarge);letter-spacing:var(--lns-letterSpacing-xxxlarge);font-weight:var(--lns-fontWeight-bold)}.xs-text\:heading-lg{font-size:var(--lns-fontSize-heading-lg);line-height:var(--lns-lineHeight-heading-lg);letter-spacing:var(--lns-letterSpacing-heading-lg);font-weight:var(--lns-fontWeight-bold)}.xs-weight\:book{font-weight:var(--lns-fontWeight-book)}.xs-weight\:regular{font-weight:var(--lns-fontWeight-regular)}.xs-weight\:medium{font-weight:var(--lns-fontWeight-medium)}.xs-weight\:bold{font-weight:var(--lns-fontWeight-bold)}.xs-text\:body{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);font-weight:var(--lns-fontWeight-regular)}.xs-text\:title{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);font-weight:var(--lns-fontWeight-bold)}.xs-text\:mainTitle{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);font-weight:var(--lns-fontWeight-bold)}.xs-text\:left{text-align:left}.xs-text\:right{text-align:right}.xs-text\:center{text-align:center}.xs-border{border:1px solid var(--lns-color-border)}.xs-borderTop{border-top:1px solid var(--lns-color-border)}.xs-borderBottom{border-bottom:1px solid var(--lns-color-border)}.xs-borderLeft{border-left:1px solid var(--lns-color-border)}.xs-borderRight{border-right:1px solid var(--lns-color-border)}.xs-inline{display:inline}.xs-block{display:block}.xs-flex{display:flex}.xs-inlineBlock{display:inline-block}.xs-inlineFlex{display:inline-flex}.xs-none{display:none}.xs-flexWrap{flex-wrap:wrap}.xs-flexDirection\:column{flex-direction:column}.xs-flexDirection\:row{flex-direction:row}.xs-items\:stretch{align-items:stretch}.xs-items\:center{align-items:center}.xs-items\:baseline{align-items:baseline}.xs-items\:flexStart{align-items:flex-start}.xs-items\:flexEnd{align-items:flex-end}.xs-items\:selfStart{align-items:self-start}.xs-items\:selfEnd{align-items:self-end}.xs-justify\:flexStart{justify-content:flex-start}.xs-justify\:flexEnd{justify-content:flex-end}.xs-justify\:center{justify-content:center}.xs-justify\:spaceBetween{justify-content:space-between}.xs-justify\:spaceAround{justify-content:space-around}.xs-justify\:spaceEvenly{justify-content:space-evenly}.xs-grow\:0{flex-grow:0}.xs-grow\:1{flex-grow:1}.xs-shrink\:0{flex-shrink:0}.xs-shrink\:1{flex-shrink:1}.xs-self\:auto{align-self:auto}.xs-self\:flexStart{align-self:flex-start}.xs-self\:flexEnd{align-self:flex-end}.xs-self\:center{align-self:center}.xs-self\:baseline{align-self:baseline}.xs-self\:stretch{align-self:stretch}.xs-overflow\:hidden{overflow:hidden}.xs-overflow\:auto{overflow:auto}.xs-relative{position:relative}.xs-absolute{position:absolute}.xs-sticky{position:sticky}.xs-fixed{position:fixed}.xs-top\:0{top:0}.xs-top\:auto{top:auto}.xs-top\:xsmall{top:var(--lns-space-xsmall)}.xs-top\:small{top:var(--lns-space-small)}.xs-top\:medium{top:var(--lns-space-medium)}.xs-top\:large{top:var(--lns-space-large)}.xs-top\:xlarge{top:var(--lns-space-xlarge)}.xs-top\:xxlarge{top:var(--lns-space-xxlarge)}.xs-bottom\:0{bottom:0}.xs-bottom\:auto{bottom:auto}.xs-bottom\:xsmall{bottom:var(--lns-space-xsmall)}.xs-bottom\:small{bottom:var(--lns-space-small)}.xs-bottom\:medium{bottom:var(--lns-space-medium)}.xs-bottom\:large{bottom:var(--lns-space-large)}.xs-bottom\:xlarge{bottom:var(--lns-space-xlarge)}.xs-bottom\:xxlarge{bottom:var(--lns-space-xxlarge)}.xs-left\:0{left:0}.xs-left\:auto{left:auto}.xs-left\:xsmall{left:var(--lns-space-xsmall)}.xs-left\:small{left:var(--lns-space-small)}.xs-left\:medium{left:var(--lns-space-medium)}.xs-left\:large{left:var(--lns-space-large)}.xs-left\:xlarge{left:var(--lns-space-xlarge)}.xs-left\:xxlarge{left:var(--lns-space-xxlarge)}.xs-right\:0{right:0}.xs-right\:auto{right:auto}.xs-right\:xsmall{right:var(--lns-space-xsmall)}.xs-right\:small{right:var(--lns-space-small)}.xs-right\:medium{right:var(--lns-space-medium)}.xs-right\:large{right:var(--lns-space-large)}.xs-right\:xlarge{right:var(--lns-space-xlarge)}.xs-right\:xxlarge{right:var(--lns-space-xxlarge)}.xs-width\:auto{width:auto}.xs-width\:full{width:100%}.xs-width\:0{width:0}.xs-minWidth\:0{min-width:0}.xs-height\:auto{height:auto}.xs-height\:full{height:100%}.xs-height\:0{height:0}.xs-ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.xs-srOnly{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0}}@media(min-width:48em){.sm-c\:red{color:var(--lns-color-red)}.sm-c\:blurpleLight{color:var(--lns-color-blurpleLight)}.sm-c\:blurpleMedium{color:var(--lns-color-blurpleMedium)}.sm-c\:blurple{color:var(--lns-color-blurple)}.sm-c\:blurpleDark{color:var(--lns-color-blurpleDark)}.sm-c\:blurpleStrong{color:var(--lns-color-blurpleStrong)}.sm-c\:offWhite{color:var(--lns-color-offWhite)}.sm-c\:blueLight{color:var(--lns-color-blueLight)}.sm-c\:blue{color:var(--lns-color-blue)}.sm-c\:blueDark{color:var(--lns-color-blueDark)}.sm-c\:orangeLight{color:var(--lns-color-orangeLight)}.sm-c\:orange{color:var(--lns-color-orange)}.sm-c\:orangeDark{color:var(--lns-color-orangeDark)}.sm-c\:tealLight{color:var(--lns-color-tealLight)}.sm-c\:teal{color:var(--lns-color-teal)}.sm-c\:tealDark{color:var(--lns-color-tealDark)}.sm-c\:yellowLight{color:var(--lns-color-yellowLight)}.sm-c\:yellow{color:var(--lns-color-yellow)}.sm-c\:yellowDark{color:var(--lns-color-yellowDark)}.sm-c\:grey8{color:var(--lns-color-grey8)}.sm-c\:grey7{color:var(--lns-color-grey7)}.sm-c\:grey6{color:var(--lns-color-grey6)}.sm-c\:grey5{color:var(--lns-color-grey5)}.sm-c\:grey4{color:var(--lns-color-grey4)}.sm-c\:grey3{color:var(--lns-color-grey3)}.sm-c\:grey2{color:var(--lns-color-grey2)}.sm-c\:grey1{color:var(--lns-color-grey1)}.sm-c\:white{color:var(--lns-color-white)}.sm-c\:primary{color:var(--lns-color-primary)}.sm-c\:primaryHover{color:var(--lns-color-primaryHover)}.sm-c\:primaryActive{color:var(--lns-color-primaryActive)}.sm-c\:body{color:var(--lns-color-body)}.sm-c\:bodyDimmed{color:var(--lns-color-bodyDimmed)}.sm-c\:bodyInverse{color:var(--lns-color-bodyInverse)}.sm-c\:background{color:var(--lns-color-background)}.sm-c\:backgroundHover{color:var(--lns-color-backgroundHover)}.sm-c\:backgroundActive{color:var(--lns-color-backgroundActive)}.sm-c\:backgroundSecondary{color:var(--lns-color-backgroundSecondary)}.sm-c\:backgroundSecondary2{color:var(--lns-color-backgroundSecondary2)}.sm-c\:backgroundInverse{color:var(--lns-color-backgroundInverse)}.sm-c\:overlay{color:var(--lns-color-overlay)}.sm-c\:border{color:var(--lns-color-border)}.sm-c\:focusRing{color:var(--lns-color-focusRing)}.sm-c\:record{color:var(--lns-color-record)}.sm-c\:recordHover{color:var(--lns-color-recordHover)}.sm-c\:recordActive{color:var(--lns-color-recordActive)}.sm-c\:info{color:var(--lns-color-info)}.sm-c\:success{color:var(--lns-color-success)}.sm-c\:warning{color:var(--lns-color-warning)}.sm-c\:danger{color:var(--lns-color-danger)}.sm-c\:dangerHover{color:var(--lns-color-dangerHover)}.sm-c\:dangerActive{color:var(--lns-color-dangerActive)}.sm-c\:backdrop{color:var(--lns-color-backdrop)}.sm-c\:backdropDark{color:var(--lns-color-backdropDark)}.sm-c\:backdropTwilight{color:var(--lns-color-backdropTwilight)}.sm-c\:disabledContent{color:var(--lns-color-disabledContent)}.sm-c\:highlight{color:var(--lns-color-highlight)}.sm-c\:disabledBackground{color:var(--lns-color-disabledBackground)}.sm-c\:formFieldBorder{color:var(--lns-color-formFieldBorder)}.sm-c\:formFieldBackground{color:var(--lns-color-formFieldBackground)}.sm-c\:buttonBorder{color:var(--lns-color-buttonBorder)}.sm-c\:upgrade{color:var(--lns-color-upgrade)}.sm-c\:upgradeHover{color:var(--lns-color-upgradeHover)}.sm-c\:upgradeActive{color:var(--lns-color-upgradeActive)}.sm-c\:tabBackground{color:var(--lns-color-tabBackground)}.sm-c\:discoveryBackground{color:var(--lns-color-discoveryBackground)}.sm-c\:discoveryLightBackground{color:var(--lns-color-discoveryLightBackground)}.sm-c\:discoveryTitle{color:var(--lns-color-discoveryTitle)}.sm-c\:discoveryHighlight{color:var(--lns-color-discoveryHighlight)}.sm-shadow\:small{box-shadow:var(--lns-shadow-small)}.sm-shadow\:medium{box-shadow:var(--lns-shadow-medium)}.sm-shadow\:large{box-shadow:var(--lns-shadow-large)}.sm-radius\:50{border-radius:var(--lns-radius-50)}.sm-radius\:100{border-radius:var(--lns-radius-100)}.sm-radius\:150{border-radius:var(--lns-radius-150)}.sm-radius\:175{border-radius:var(--lns-radius-175)}.sm-radius\:200{border-radius:var(--lns-radius-200)}.sm-radius\:250{border-radius:var(--lns-radius-250)}.sm-radius\:300{border-radius:var(--lns-radius-300)}.sm-radius\:none{border-radius:var(--lns-radius-none)}.sm-radius\:medium{border-radius:var(--lns-radius-medium)}.sm-radius\:large{border-radius:var(--lns-radius-large)}.sm-radius\:xlarge{border-radius:var(--lns-radius-xlarge)}.sm-radius\:round{border-radius:var(--lns-radius-round)}.sm-radius\:full{border-radius:var(--lns-radius-full)}.sm-bgc\:red{background-color:var(--lns-color-red)}.sm-bgc\:blurpleLight{background-color:var(--lns-color-blurpleLight)}.sm-bgc\:blurpleMedium{background-color:var(--lns-color-blurpleMedium)}.sm-bgc\:blurple{background-color:var(--lns-color-blurple)}.sm-bgc\:blurpleDark{background-color:var(--lns-color-blurpleDark)}.sm-bgc\:blurpleStrong{background-color:var(--lns-color-blurpleStrong)}.sm-bgc\:offWhite{background-color:var(--lns-color-offWhite)}.sm-bgc\:blueLight{background-color:var(--lns-color-blueLight)}.sm-bgc\:blue{background-color:var(--lns-color-blue)}.sm-bgc\:blueDark{background-color:var(--lns-color-blueDark)}.sm-bgc\:orangeLight{background-color:var(--lns-color-orangeLight)}.sm-bgc\:orange{background-color:var(--lns-color-orange)}.sm-bgc\:orangeDark{background-color:var(--lns-color-orangeDark)}.sm-bgc\:tealLight{background-color:var(--lns-color-tealLight)}.sm-bgc\:teal{background-color:var(--lns-color-teal)}.sm-bgc\:tealDark{background-color:var(--lns-color-tealDark)}.sm-bgc\:yellowLight{background-color:var(--lns-color-yellowLight)}.sm-bgc\:yellow{background-color:var(--lns-color-yellow)}.sm-bgc\:yellowDark{background-color:var(--lns-color-yellowDark)}.sm-bgc\:grey8{background-color:var(--lns-color-grey8)}.sm-bgc\:grey7{background-color:var(--lns-color-grey7)}.sm-bgc\:grey6{background-color:var(--lns-color-grey6)}.sm-bgc\:grey5{background-color:var(--lns-color-grey5)}.sm-bgc\:grey4{background-color:var(--lns-color-grey4)}.sm-bgc\:grey3{background-color:var(--lns-color-grey3)}.sm-bgc\:grey2{background-color:var(--lns-color-grey2)}.sm-bgc\:grey1{background-color:var(--lns-color-grey1)}.sm-bgc\:white{background-color:var(--lns-color-white)}.sm-bgc\:primary{background-color:var(--lns-color-primary)}.sm-bgc\:primaryHover{background-color:var(--lns-color-primaryHover)}.sm-bgc\:primaryActive{background-color:var(--lns-color-primaryActive)}.sm-bgc\:body{background-color:var(--lns-color-body)}.sm-bgc\:bodyDimmed{background-color:var(--lns-color-bodyDimmed)}.sm-bgc\:bodyInverse{background-color:var(--lns-color-bodyInverse)}.sm-bgc\:background{background-color:var(--lns-color-background)}.sm-bgc\:backgroundHover{background-color:var(--lns-color-backgroundHover)}.sm-bgc\:backgroundActive{background-color:var(--lns-color-backgroundActive)}.sm-bgc\:backgroundSecondary{background-color:var(--lns-color-backgroundSecondary)}.sm-bgc\:backgroundSecondary2{background-color:var(--lns-color-backgroundSecondary2)}.sm-bgc\:backgroundInverse{background-color:var(--lns-color-backgroundInverse)}.sm-bgc\:overlay{background-color:var(--lns-color-overlay)}.sm-bgc\:border{background-color:var(--lns-color-border)}.sm-bgc\:focusRing{background-color:var(--lns-color-focusRing)}.sm-bgc\:record{background-color:var(--lns-color-record)}.sm-bgc\:recordHover{background-color:var(--lns-color-recordHover)}.sm-bgc\:recordActive{background-color:var(--lns-color-recordActive)}.sm-bgc\:info{background-color:var(--lns-color-info)}.sm-bgc\:success{background-color:var(--lns-color-success)}.sm-bgc\:warning{background-color:var(--lns-color-warning)}.sm-bgc\:danger{background-color:var(--lns-color-danger)}.sm-bgc\:dangerHover{background-color:var(--lns-color-dangerHover)}.sm-bgc\:dangerActive{background-color:var(--lns-color-dangerActive)}.sm-bgc\:backdrop{background-color:var(--lns-color-backdrop)}.sm-bgc\:backdropDark{background-color:var(--lns-color-backdropDark)}.sm-bgc\:backdropTwilight{background-color:var(--lns-color-backdropTwilight)}.sm-bgc\:disabledContent{background-color:var(--lns-color-disabledContent)}.sm-bgc\:highlight{background-color:var(--lns-color-highlight)}.sm-bgc\:disabledBackground{background-color:var(--lns-color-disabledBackground)}.sm-bgc\:formFieldBorder{background-color:var(--lns-color-formFieldBorder)}.sm-bgc\:formFieldBackground{background-color:var(--lns-color-formFieldBackground)}.sm-bgc\:buttonBorder{background-color:var(--lns-color-buttonBorder)}.sm-bgc\:upgrade{background-color:var(--lns-color-upgrade)}.sm-bgc\:upgradeHover{background-color:var(--lns-color-upgradeHover)}.sm-bgc\:upgradeActive{background-color:var(--lns-color-upgradeActive)}.sm-bgc\:tabBackground{background-color:var(--lns-color-tabBackground)}.sm-bgc\:discoveryBackground{background-color:var(--lns-color-discoveryBackground)}.sm-bgc\:discoveryLightBackground{background-color:var(--lns-color-discoveryLightBackground)}.sm-bgc\:discoveryTitle{background-color:var(--lns-color-discoveryTitle)}.sm-bgc\:discoveryHighlight{background-color:var(--lns-color-discoveryHighlight)}.sm-m\:0{margin:0}.sm-m\:auto{margin:auto}.sm-m\:xsmall{margin:var(--lns-space-xsmall)}.sm-m\:small{margin:var(--lns-space-small)}.sm-m\:medium{margin:var(--lns-space-medium)}.sm-m\:large{margin:var(--lns-space-large)}.sm-m\:xlarge{margin:var(--lns-space-xlarge)}.sm-m\:xxlarge{margin:var(--lns-space-xxlarge)}.sm-mt\:0{margin-top:0}.sm-mt\:auto{margin-top:auto}.sm-mt\:xsmall{margin-top:var(--lns-space-xsmall)}.sm-mt\:small{margin-top:var(--lns-space-small)}.sm-mt\:medium{margin-top:var(--lns-space-medium)}.sm-mt\:large{margin-top:var(--lns-space-large)}.sm-mt\:xlarge{margin-top:var(--lns-space-xlarge)}.sm-mt\:xxlarge{margin-top:var(--lns-space-xxlarge)}.sm-mb\:0{margin-bottom:0}.sm-mb\:auto{margin-bottom:auto}.sm-mb\:xsmall{margin-bottom:var(--lns-space-xsmall)}.sm-mb\:small{margin-bottom:var(--lns-space-small)}.sm-mb\:medium{margin-bottom:var(--lns-space-medium)}.sm-mb\:large{margin-bottom:var(--lns-space-large)}.sm-mb\:xlarge{margin-bottom:var(--lns-space-xlarge)}.sm-mb\:xxlarge{margin-bottom:var(--lns-space-xxlarge)}.sm-ml\:0{margin-left:0}.sm-ml\:auto{margin-left:auto}.sm-ml\:xsmall{margin-left:var(--lns-space-xsmall)}.sm-ml\:small{margin-left:var(--lns-space-small)}.sm-ml\:medium{margin-left:var(--lns-space-medium)}.sm-ml\:large{margin-left:var(--lns-space-large)}.sm-ml\:xlarge{margin-left:var(--lns-space-xlarge)}.sm-ml\:xxlarge{margin-left:var(--lns-space-xxlarge)}.sm-mr\:0{margin-right:0}.sm-mr\:auto{margin-right:auto}.sm-mr\:xsmall{margin-right:var(--lns-space-xsmall)}.sm-mr\:small{margin-right:var(--lns-space-small)}.sm-mr\:medium{margin-right:var(--lns-space-medium)}.sm-mr\:large{margin-right:var(--lns-space-large)}.sm-mr\:xlarge{margin-right:var(--lns-space-xlarge)}.sm-mr\:xxlarge{margin-right:var(--lns-space-xxlarge)}.sm-mx\:0{margin-left:0;margin-right:0}.sm-mx\:auto{margin-left:auto;margin-right:auto}.sm-mx\:xsmall{margin-left:var(--lns-space-xsmall);margin-right:var(--lns-space-xsmall)}.sm-mx\:small{margin-left:var(--lns-space-small);margin-right:var(--lns-space-small)}.sm-mx\:medium{margin-left:var(--lns-space-medium);margin-right:var(--lns-space-medium)}.sm-mx\:large{margin-left:var(--lns-space-large);margin-right:var(--lns-space-large)}.sm-mx\:xlarge{margin-left:var(--lns-space-xlarge);margin-right:var(--lns-space-xlarge)}.sm-mx\:xxlarge{margin-left:var(--lns-space-xxlarge);margin-right:var(--lns-space-xxlarge)}.sm-my\:0{margin-top:0;margin-bottom:0}.sm-my\:auto{margin-top:auto;margin-bottom:auto}.sm-my\:xsmall{margin-top:var(--lns-space-xsmall);margin-bottom:var(--lns-space-xsmall)}.sm-my\:small{margin-top:var(--lns-space-small);margin-bottom:var(--lns-space-small)}.sm-my\:medium{margin-top:var(--lns-space-medium);margin-bottom:var(--lns-space-medium)}.sm-my\:large{margin-top:var(--lns-space-large);margin-bottom:var(--lns-space-large)}.sm-my\:xlarge{margin-top:var(--lns-space-xlarge);margin-bottom:var(--lns-space-xlarge)}.sm-my\:xxlarge{margin-top:var(--lns-space-xxlarge);margin-bottom:var(--lns-space-xxlarge)}.sm-p\:0{padding:0}.sm-p\:xsmall{padding:var(--lns-space-xsmall)}.sm-p\:small{padding:var(--lns-space-small)}.sm-p\:medium{padding:var(--lns-space-medium)}.sm-p\:large{padding:var(--lns-space-large)}.sm-p\:xlarge{padding:var(--lns-space-xlarge)}.sm-p\:xxlarge{padding:var(--lns-space-xxlarge)}.sm-pt\:0{padding-top:0}.sm-pt\:xsmall{padding-top:var(--lns-space-xsmall)}.sm-pt\:small{padding-top:var(--lns-space-small)}.sm-pt\:medium{padding-top:var(--lns-space-medium)}.sm-pt\:large{padding-top:var(--lns-space-large)}.sm-pt\:xlarge{padding-top:var(--lns-space-xlarge)}.sm-pt\:xxlarge{padding-top:var(--lns-space-xxlarge)}.sm-pb\:0{padding-bottom:0}.sm-pb\:xsmall{padding-bottom:var(--lns-space-xsmall)}.sm-pb\:small{padding-bottom:var(--lns-space-small)}.sm-pb\:medium{padding-bottom:var(--lns-space-medium)}.sm-pb\:large{padding-bottom:var(--lns-space-large)}.sm-pb\:xlarge{padding-bottom:var(--lns-space-xlarge)}.sm-pb\:xxlarge{padding-bottom:var(--lns-space-xxlarge)}.sm-pl\:0{padding-left:0}.sm-pl\:xsmall{padding-left:var(--lns-space-xsmall)}.sm-pl\:small{padding-left:var(--lns-space-small)}.sm-pl\:medium{padding-left:var(--lns-space-medium)}.sm-pl\:large{padding-left:var(--lns-space-large)}.sm-pl\:xlarge{padding-left:var(--lns-space-xlarge)}.sm-pl\:xxlarge{padding-left:var(--lns-space-xxlarge)}.sm-pr\:0{padding-right:0}.sm-pr\:xsmall{padding-right:var(--lns-space-xsmall)}.sm-pr\:small{padding-right:var(--lns-space-small)}.sm-pr\:medium{padding-right:var(--lns-space-medium)}.sm-pr\:large{padding-right:var(--lns-space-large)}.sm-pr\:xlarge{padding-right:var(--lns-space-xlarge)}.sm-pr\:xxlarge{padding-right:var(--lns-space-xxlarge)}.sm-px\:0{padding-left:0;padding-right:0}.sm-px\:xsmall{padding-left:var(--lns-space-xsmall);padding-right:var(--lns-space-xsmall)}.sm-px\:small{padding-left:var(--lns-space-small);padding-right:var(--lns-space-small)}.sm-px\:medium{padding-left:var(--lns-space-medium);padding-right:var(--lns-space-medium)}.sm-px\:large{padding-left:var(--lns-space-large);padding-right:var(--lns-space-large)}.sm-px\:xlarge{padding-left:var(--lns-space-xlarge);padding-right:var(--lns-space-xlarge)}.sm-px\:xxlarge{padding-left:var(--lns-space-xxlarge);padding-right:var(--lns-space-xxlarge)}.sm-py\:0{padding-top:0;padding-bottom:0}.sm-py\:xsmall{padding-top:var(--lns-space-xsmall);padding-bottom:var(--lns-space-xsmall)}.sm-py\:small{padding-top:var(--lns-space-small);padding-bottom:var(--lns-space-small)}.sm-py\:medium{padding-top:var(--lns-space-medium);padding-bottom:var(--lns-space-medium)}.sm-py\:large{padding-top:var(--lns-space-large);padding-bottom:var(--lns-space-large)}.sm-py\:xlarge{padding-top:var(--lns-space-xlarge);padding-bottom:var(--lns-space-xlarge)}.sm-py\:xxlarge{padding-top:var(--lns-space-xxlarge);padding-bottom:var(--lns-space-xxlarge)}.sm-text\:small{font-size:var(--lns-fontSize-small);line-height:var(--lns-lineHeight-small);letter-spacing:var(--lns-letterSpacing-small);font-weight:var(--lns-fontWeight-regular)}.sm-text\:body-sm{font-size:var(--lns-fontSize-body-sm);line-height:var(--lns-lineHeight-body-sm);letter-spacing:var(--lns-letterSpacing-body-sm);font-weight:var(--lns-fontWeight-regular)}.sm-text\:medium{font-size:var(--lns-fontSize-medium);line-height:var(--lns-lineHeight-medium);letter-spacing:var(--lns-letterSpacing-medium);font-weight:var(--lns-fontWeight-regular)}.sm-text\:body-md{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);letter-spacing:var(--lns-letterSpacing-body-md);font-weight:var(--lns-fontWeight-regular)}.sm-text\:large{font-size:var(--lns-fontSize-large);line-height:var(--lns-lineHeight-large);letter-spacing:var(--lns-letterSpacing-large);font-weight:var(--lns-fontWeight-regular)}.sm-text\:body-lg{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);letter-spacing:var(--lns-letterSpacing-body-lg);font-weight:var(--lns-fontWeight-regular)}.sm-text\:xlarge{font-size:var(--lns-fontSize-xlarge);line-height:var(--lns-lineHeight-xlarge);letter-spacing:var(--lns-letterSpacing-xlarge);font-weight:var(--lns-fontWeight-bold)}.sm-text\:heading-sm{font-size:var(--lns-fontSize-heading-sm);line-height:var(--lns-lineHeight-heading-sm);letter-spacing:var(--lns-letterSpacing-heading-sm);font-weight:var(--lns-fontWeight-bold)}.sm-text\:xxlarge{font-size:var(--lns-fontSize-xxlarge);line-height:var(--lns-lineHeight-xxlarge);letter-spacing:var(--lns-letterSpacing-xxlarge);font-weight:var(--lns-fontWeight-bold)}.sm-text\:heading-md{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);letter-spacing:var(--lns-letterSpacing-heading-md);font-weight:var(--lns-fontWeight-bold)}.sm-text\:xxxlarge{font-size:var(--lns-fontSize-xxxlarge);line-height:var(--lns-lineHeight-xxxlarge);letter-spacing:var(--lns-letterSpacing-xxxlarge);font-weight:var(--lns-fontWeight-bold)}.sm-text\:heading-lg{font-size:var(--lns-fontSize-heading-lg);line-height:var(--lns-lineHeight-heading-lg);letter-spacing:var(--lns-letterSpacing-heading-lg);font-weight:var(--lns-fontWeight-bold)}.sm-weight\:book{font-weight:var(--lns-fontWeight-book)}.sm-weight\:regular{font-weight:var(--lns-fontWeight-regular)}.sm-weight\:medium{font-weight:var(--lns-fontWeight-medium)}.sm-weight\:bold{font-weight:var(--lns-fontWeight-bold)}.sm-text\:body{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);font-weight:var(--lns-fontWeight-regular)}.sm-text\:title{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);font-weight:var(--lns-fontWeight-bold)}.sm-text\:mainTitle{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);font-weight:var(--lns-fontWeight-bold)}.sm-text\:left{text-align:left}.sm-text\:right{text-align:right}.sm-text\:center{text-align:center}.sm-border{border:1px solid var(--lns-color-border)}.sm-borderTop{border-top:1px solid var(--lns-color-border)}.sm-borderBottom{border-bottom:1px solid var(--lns-color-border)}.sm-borderLeft{border-left:1px solid var(--lns-color-border)}.sm-borderRight{border-right:1px solid var(--lns-color-border)}.sm-inline{display:inline}.sm-block{display:block}.sm-flex{display:flex}.sm-inlineBlock{display:inline-block}.sm-inlineFlex{display:inline-flex}.sm-none{display:none}.sm-flexWrap{flex-wrap:wrap}.sm-flexDirection\:column{flex-direction:column}.sm-flexDirection\:row{flex-direction:row}.sm-items\:stretch{align-items:stretch}.sm-items\:center{align-items:center}.sm-items\:baseline{align-items:baseline}.sm-items\:flexStart{align-items:flex-start}.sm-items\:flexEnd{align-items:flex-end}.sm-items\:selfStart{align-items:self-start}.sm-items\:selfEnd{align-items:self-end}.sm-justify\:flexStart{justify-content:flex-start}.sm-justify\:flexEnd{justify-content:flex-end}.sm-justify\:center{justify-content:center}.sm-justify\:spaceBetween{justify-content:space-between}.sm-justify\:spaceAround{justify-content:space-around}.sm-justify\:spaceEvenly{justify-content:space-evenly}.sm-grow\:0{flex-grow:0}.sm-grow\:1{flex-grow:1}.sm-shrink\:0{flex-shrink:0}.sm-shrink\:1{flex-shrink:1}.sm-self\:auto{align-self:auto}.sm-self\:flexStart{align-self:flex-start}.sm-self\:flexEnd{align-self:flex-end}.sm-self\:center{align-self:center}.sm-self\:baseline{align-self:baseline}.sm-self\:stretch{align-self:stretch}.sm-overflow\:hidden{overflow:hidden}.sm-overflow\:auto{overflow:auto}.sm-relative{position:relative}.sm-absolute{position:absolute}.sm-sticky{position:sticky}.sm-fixed{position:fixed}.sm-top\:0{top:0}.sm-top\:auto{top:auto}.sm-top\:xsmall{top:var(--lns-space-xsmall)}.sm-top\:small{top:var(--lns-space-small)}.sm-top\:medium{top:var(--lns-space-medium)}.sm-top\:large{top:var(--lns-space-large)}.sm-top\:xlarge{top:var(--lns-space-xlarge)}.sm-top\:xxlarge{top:var(--lns-space-xxlarge)}.sm-bottom\:0{bottom:0}.sm-bottom\:auto{bottom:auto}.sm-bottom\:xsmall{bottom:var(--lns-space-xsmall)}.sm-bottom\:small{bottom:var(--lns-space-small)}.sm-bottom\:medium{bottom:var(--lns-space-medium)}.sm-bottom\:large{bottom:var(--lns-space-large)}.sm-bottom\:xlarge{bottom:var(--lns-space-xlarge)}.sm-bottom\:xxlarge{bottom:var(--lns-space-xxlarge)}.sm-left\:0{left:0}.sm-left\:auto{left:auto}.sm-left\:xsmall{left:var(--lns-space-xsmall)}.sm-left\:small{left:var(--lns-space-small)}.sm-left\:medium{left:var(--lns-space-medium)}.sm-left\:large{left:var(--lns-space-large)}.sm-left\:xlarge{left:var(--lns-space-xlarge)}.sm-left\:xxlarge{left:var(--lns-space-xxlarge)}.sm-right\:0{right:0}.sm-right\:auto{right:auto}.sm-right\:xsmall{right:var(--lns-space-xsmall)}.sm-right\:small{right:var(--lns-space-small)}.sm-right\:medium{right:var(--lns-space-medium)}.sm-right\:large{right:var(--lns-space-large)}.sm-right\:xlarge{right:var(--lns-space-xlarge)}.sm-right\:xxlarge{right:var(--lns-space-xxlarge)}.sm-width\:auto{width:auto}.sm-width\:full{width:100%}.sm-width\:0{width:0}.sm-minWidth\:0{min-width:0}.sm-height\:auto{height:auto}.sm-height\:full{height:100%}.sm-height\:0{height:0}.sm-ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.sm-srOnly{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0}}@media(min-width:64em){.md-c\:red{color:var(--lns-color-red)}.md-c\:blurpleLight{color:var(--lns-color-blurpleLight)}.md-c\:blurpleMedium{color:var(--lns-color-blurpleMedium)}.md-c\:blurple{color:var(--lns-color-blurple)}.md-c\:blurpleDark{color:var(--lns-color-blurpleDark)}.md-c\:blurpleStrong{color:var(--lns-color-blurpleStrong)}.md-c\:offWhite{color:var(--lns-color-offWhite)}.md-c\:blueLight{color:var(--lns-color-blueLight)}.md-c\:blue{color:var(--lns-color-blue)}.md-c\:blueDark{color:var(--lns-color-blueDark)}.md-c\:orangeLight{color:var(--lns-color-orangeLight)}.md-c\:orange{color:var(--lns-color-orange)}.md-c\:orangeDark{color:var(--lns-color-orangeDark)}.md-c\:tealLight{color:var(--lns-color-tealLight)}.md-c\:teal{color:var(--lns-color-teal)}.md-c\:tealDark{color:var(--lns-color-tealDark)}.md-c\:yellowLight{color:var(--lns-color-yellowLight)}.md-c\:yellow{color:var(--lns-color-yellow)}.md-c\:yellowDark{color:var(--lns-color-yellowDark)}.md-c\:grey8{color:var(--lns-color-grey8)}.md-c\:grey7{color:var(--lns-color-grey7)}.md-c\:grey6{color:var(--lns-color-grey6)}.md-c\:grey5{color:var(--lns-color-grey5)}.md-c\:grey4{color:var(--lns-color-grey4)}.md-c\:grey3{color:var(--lns-color-grey3)}.md-c\:grey2{color:var(--lns-color-grey2)}.md-c\:grey1{color:var(--lns-color-grey1)}.md-c\:white{color:var(--lns-color-white)}.md-c\:primary{color:var(--lns-color-primary)}.md-c\:primaryHover{color:var(--lns-color-primaryHover)}.md-c\:primaryActive{color:var(--lns-color-primaryActive)}.md-c\:body{color:var(--lns-color-body)}.md-c\:bodyDimmed{color:var(--lns-color-bodyDimmed)}.md-c\:bodyInverse{color:var(--lns-color-bodyInverse)}.md-c\:background{color:var(--lns-color-background)}.md-c\:backgroundHover{color:var(--lns-color-backgroundHover)}.md-c\:backgroundActive{color:var(--lns-color-backgroundActive)}.md-c\:backgroundSecondary{color:var(--lns-color-backgroundSecondary)}.md-c\:backgroundSecondary2{color:var(--lns-color-backgroundSecondary2)}.md-c\:backgroundInverse{color:var(--lns-color-backgroundInverse)}.md-c\:overlay{color:var(--lns-color-overlay)}.md-c\:border{color:var(--lns-color-border)}.md-c\:focusRing{color:var(--lns-color-focusRing)}.md-c\:record{color:var(--lns-color-record)}.md-c\:recordHover{color:var(--lns-color-recordHover)}.md-c\:recordActive{color:var(--lns-color-recordActive)}.md-c\:info{color:var(--lns-color-info)}.md-c\:success{color:var(--lns-color-success)}.md-c\:warning{color:var(--lns-color-warning)}.md-c\:danger{color:var(--lns-color-danger)}.md-c\:dangerHover{color:var(--lns-color-dangerHover)}.md-c\:dangerActive{color:var(--lns-color-dangerActive)}.md-c\:backdrop{color:var(--lns-color-backdrop)}.md-c\:backdropDark{color:var(--lns-color-backdropDark)}.md-c\:backdropTwilight{color:var(--lns-color-backdropTwilight)}.md-c\:disabledContent{color:var(--lns-color-disabledContent)}.md-c\:highlight{color:var(--lns-color-highlight)}.md-c\:disabledBackground{color:var(--lns-color-disabledBackground)}.md-c\:formFieldBorder{color:var(--lns-color-formFieldBorder)}.md-c\:formFieldBackground{color:var(--lns-color-formFieldBackground)}.md-c\:buttonBorder{color:var(--lns-color-buttonBorder)}.md-c\:upgrade{color:var(--lns-color-upgrade)}.md-c\:upgradeHover{color:var(--lns-color-upgradeHover)}.md-c\:upgradeActive{color:var(--lns-color-upgradeActive)}.md-c\:tabBackground{color:var(--lns-color-tabBackground)}.md-c\:discoveryBackground{color:var(--lns-color-discoveryBackground)}.md-c\:discoveryLightBackground{color:var(--lns-color-discoveryLightBackground)}.md-c\:discoveryTitle{color:var(--lns-color-discoveryTitle)}.md-c\:discoveryHighlight{color:var(--lns-color-discoveryHighlight)}.md-shadow\:small{box-shadow:var(--lns-shadow-small)}.md-shadow\:medium{box-shadow:var(--lns-shadow-medium)}.md-shadow\:large{box-shadow:var(--lns-shadow-large)}.md-radius\:50{border-radius:var(--lns-radius-50)}.md-radius\:100{border-radius:var(--lns-radius-100)}.md-radius\:150{border-radius:var(--lns-radius-150)}.md-radius\:175{border-radius:var(--lns-radius-175)}.md-radius\:200{border-radius:var(--lns-radius-200)}.md-radius\:250{border-radius:var(--lns-radius-250)}.md-radius\:300{border-radius:var(--lns-radius-300)}.md-radius\:none{border-radius:var(--lns-radius-none)}.md-radius\:medium{border-radius:var(--lns-radius-medium)}.md-radius\:large{border-radius:var(--lns-radius-large)}.md-radius\:xlarge{border-radius:var(--lns-radius-xlarge)}.md-radius\:round{border-radius:var(--lns-radius-round)}.md-radius\:full{border-radius:var(--lns-radius-full)}.md-bgc\:red{background-color:var(--lns-color-red)}.md-bgc\:blurpleLight{background-color:var(--lns-color-blurpleLight)}.md-bgc\:blurpleMedium{background-color:var(--lns-color-blurpleMedium)}.md-bgc\:blurple{background-color:var(--lns-color-blurple)}.md-bgc\:blurpleDark{background-color:var(--lns-color-blurpleDark)}.md-bgc\:blurpleStrong{background-color:var(--lns-color-blurpleStrong)}.md-bgc\:offWhite{background-color:var(--lns-color-offWhite)}.md-bgc\:blueLight{background-color:var(--lns-color-blueLight)}.md-bgc\:blue{background-color:var(--lns-color-blue)}.md-bgc\:blueDark{background-color:var(--lns-color-blueDark)}.md-bgc\:orangeLight{background-color:var(--lns-color-orangeLight)}.md-bgc\:orange{background-color:var(--lns-color-orange)}.md-bgc\:orangeDark{background-color:var(--lns-color-orangeDark)}.md-bgc\:tealLight{background-color:var(--lns-color-tealLight)}.md-bgc\:teal{background-color:var(--lns-color-teal)}.md-bgc\:tealDark{background-color:var(--lns-color-tealDark)}.md-bgc\:yellowLight{background-color:var(--lns-color-yellowLight)}.md-bgc\:yellow{background-color:var(--lns-color-yellow)}.md-bgc\:yellowDark{background-color:var(--lns-color-yellowDark)}.md-bgc\:grey8{background-color:var(--lns-color-grey8)}.md-bgc\:grey7{background-color:var(--lns-color-grey7)}.md-bgc\:grey6{background-color:var(--lns-color-grey6)}.md-bgc\:grey5{background-color:var(--lns-color-grey5)}.md-bgc\:grey4{background-color:var(--lns-color-grey4)}.md-bgc\:grey3{background-color:var(--lns-color-grey3)}.md-bgc\:grey2{background-color:var(--lns-color-grey2)}.md-bgc\:grey1{background-color:var(--lns-color-grey1)}.md-bgc\:white{background-color:var(--lns-color-white)}.md-bgc\:primary{background-color:var(--lns-color-primary)}.md-bgc\:primaryHover{background-color:var(--lns-color-primaryHover)}.md-bgc\:primaryActive{background-color:var(--lns-color-primaryActive)}.md-bgc\:body{background-color:var(--lns-color-body)}.md-bgc\:bodyDimmed{background-color:var(--lns-color-bodyDimmed)}.md-bgc\:bodyInverse{background-color:var(--lns-color-bodyInverse)}.md-bgc\:background{background-color:var(--lns-color-background)}.md-bgc\:backgroundHover{background-color:var(--lns-color-backgroundHover)}.md-bgc\:backgroundActive{background-color:var(--lns-color-backgroundActive)}.md-bgc\:backgroundSecondary{background-color:var(--lns-color-backgroundSecondary)}.md-bgc\:backgroundSecondary2{background-color:var(--lns-color-backgroundSecondary2)}.md-bgc\:backgroundInverse{background-color:var(--lns-color-backgroundInverse)}.md-bgc\:overlay{background-color:var(--lns-color-overlay)}.md-bgc\:border{background-color:var(--lns-color-border)}.md-bgc\:focusRing{background-color:var(--lns-color-focusRing)}.md-bgc\:record{background-color:var(--lns-color-record)}.md-bgc\:recordHover{background-color:var(--lns-color-recordHover)}.md-bgc\:recordActive{background-color:var(--lns-color-recordActive)}.md-bgc\:info{background-color:var(--lns-color-info)}.md-bgc\:success{background-color:var(--lns-color-success)}.md-bgc\:warning{background-color:var(--lns-color-warning)}.md-bgc\:danger{background-color:var(--lns-color-danger)}.md-bgc\:dangerHover{background-color:var(--lns-color-dangerHover)}.md-bgc\:dangerActive{background-color:var(--lns-color-dangerActive)}.md-bgc\:backdrop{background-color:var(--lns-color-backdrop)}.md-bgc\:backdropDark{background-color:var(--lns-color-backdropDark)}.md-bgc\:backdropTwilight{background-color:var(--lns-color-backdropTwilight)}.md-bgc\:disabledContent{background-color:var(--lns-color-disabledContent)}.md-bgc\:highlight{background-color:var(--lns-color-highlight)}.md-bgc\:disabledBackground{background-color:var(--lns-color-disabledBackground)}.md-bgc\:formFieldBorder{background-color:var(--lns-color-formFieldBorder)}.md-bgc\:formFieldBackground{background-color:var(--lns-color-formFieldBackground)}.md-bgc\:buttonBorder{background-color:var(--lns-color-buttonBorder)}.md-bgc\:upgrade{background-color:var(--lns-color-upgrade)}.md-bgc\:upgradeHover{background-color:var(--lns-color-upgradeHover)}.md-bgc\:upgradeActive{background-color:var(--lns-color-upgradeActive)}.md-bgc\:tabBackground{background-color:var(--lns-color-tabBackground)}.md-bgc\:discoveryBackground{background-color:var(--lns-color-discoveryBackground)}.md-bgc\:discoveryLightBackground{background-color:var(--lns-color-discoveryLightBackground)}.md-bgc\:discoveryTitle{background-color:var(--lns-color-discoveryTitle)}.md-bgc\:discoveryHighlight{background-color:var(--lns-color-discoveryHighlight)}.md-m\:0{margin:0}.md-m\:auto{margin:auto}.md-m\:xsmall{margin:var(--lns-space-xsmall)}.md-m\:small{margin:var(--lns-space-small)}.md-m\:medium{margin:var(--lns-space-medium)}.md-m\:large{margin:var(--lns-space-large)}.md-m\:xlarge{margin:var(--lns-space-xlarge)}.md-m\:xxlarge{margin:var(--lns-space-xxlarge)}.md-mt\:0{margin-top:0}.md-mt\:auto{margin-top:auto}.md-mt\:xsmall{margin-top:var(--lns-space-xsmall)}.md-mt\:small{margin-top:var(--lns-space-small)}.md-mt\:medium{margin-top:var(--lns-space-medium)}.md-mt\:large{margin-top:var(--lns-space-large)}.md-mt\:xlarge{margin-top:var(--lns-space-xlarge)}.md-mt\:xxlarge{margin-top:var(--lns-space-xxlarge)}.md-mb\:0{margin-bottom:0}.md-mb\:auto{margin-bottom:auto}.md-mb\:xsmall{margin-bottom:var(--lns-space-xsmall)}.md-mb\:small{margin-bottom:var(--lns-space-small)}.md-mb\:medium{margin-bottom:var(--lns-space-medium)}.md-mb\:large{margin-bottom:var(--lns-space-large)}.md-mb\:xlarge{margin-bottom:var(--lns-space-xlarge)}.md-mb\:xxlarge{margin-bottom:var(--lns-space-xxlarge)}.md-ml\:0{margin-left:0}.md-ml\:auto{margin-left:auto}.md-ml\:xsmall{margin-left:var(--lns-space-xsmall)}.md-ml\:small{margin-left:var(--lns-space-small)}.md-ml\:medium{margin-left:var(--lns-space-medium)}.md-ml\:large{margin-left:var(--lns-space-large)}.md-ml\:xlarge{margin-left:var(--lns-space-xlarge)}.md-ml\:xxlarge{margin-left:var(--lns-space-xxlarge)}.md-mr\:0{margin-right:0}.md-mr\:auto{margin-right:auto}.md-mr\:xsmall{margin-right:var(--lns-space-xsmall)}.md-mr\:small{margin-right:var(--lns-space-small)}.md-mr\:medium{margin-right:var(--lns-space-medium)}.md-mr\:large{margin-right:var(--lns-space-large)}.md-mr\:xlarge{margin-right:var(--lns-space-xlarge)}.md-mr\:xxlarge{margin-right:var(--lns-space-xxlarge)}.md-mx\:0{margin-left:0;margin-right:0}.md-mx\:auto{margin-left:auto;margin-right:auto}.md-mx\:xsmall{margin-left:var(--lns-space-xsmall);margin-right:var(--lns-space-xsmall)}.md-mx\:small{margin-left:var(--lns-space-small);margin-right:var(--lns-space-small)}.md-mx\:medium{margin-left:var(--lns-space-medium);margin-right:var(--lns-space-medium)}.md-mx\:large{margin-left:var(--lns-space-large);margin-right:var(--lns-space-large)}.md-mx\:xlarge{margin-left:var(--lns-space-xlarge);margin-right:var(--lns-space-xlarge)}.md-mx\:xxlarge{margin-left:var(--lns-space-xxlarge);margin-right:var(--lns-space-xxlarge)}.md-my\:0{margin-top:0;margin-bottom:0}.md-my\:auto{margin-top:auto;margin-bottom:auto}.md-my\:xsmall{margin-top:var(--lns-space-xsmall);margin-bottom:var(--lns-space-xsmall)}.md-my\:small{margin-top:var(--lns-space-small);margin-bottom:var(--lns-space-small)}.md-my\:medium{margin-top:var(--lns-space-medium);margin-bottom:var(--lns-space-medium)}.md-my\:large{margin-top:var(--lns-space-large);margin-bottom:var(--lns-space-large)}.md-my\:xlarge{margin-top:var(--lns-space-xlarge);margin-bottom:var(--lns-space-xlarge)}.md-my\:xxlarge{margin-top:var(--lns-space-xxlarge);margin-bottom:var(--lns-space-xxlarge)}.md-p\:0{padding:0}.md-p\:xsmall{padding:var(--lns-space-xsmall)}.md-p\:small{padding:var(--lns-space-small)}.md-p\:medium{padding:var(--lns-space-medium)}.md-p\:large{padding:var(--lns-space-large)}.md-p\:xlarge{padding:var(--lns-space-xlarge)}.md-p\:xxlarge{padding:var(--lns-space-xxlarge)}.md-pt\:0{padding-top:0}.md-pt\:xsmall{padding-top:var(--lns-space-xsmall)}.md-pt\:small{padding-top:var(--lns-space-small)}.md-pt\:medium{padding-top:var(--lns-space-medium)}.md-pt\:large{padding-top:var(--lns-space-large)}.md-pt\:xlarge{padding-top:var(--lns-space-xlarge)}.md-pt\:xxlarge{padding-top:var(--lns-space-xxlarge)}.md-pb\:0{padding-bottom:0}.md-pb\:xsmall{padding-bottom:var(--lns-space-xsmall)}.md-pb\:small{padding-bottom:var(--lns-space-small)}.md-pb\:medium{padding-bottom:var(--lns-space-medium)}.md-pb\:large{padding-bottom:var(--lns-space-large)}.md-pb\:xlarge{padding-bottom:var(--lns-space-xlarge)}.md-pb\:xxlarge{padding-bottom:var(--lns-space-xxlarge)}.md-pl\:0{padding-left:0}.md-pl\:xsmall{padding-left:var(--lns-space-xsmall)}.md-pl\:small{padding-left:var(--lns-space-small)}.md-pl\:medium{padding-left:var(--lns-space-medium)}.md-pl\:large{padding-left:var(--lns-space-large)}.md-pl\:xlarge{padding-left:var(--lns-space-xlarge)}.md-pl\:xxlarge{padding-left:var(--lns-space-xxlarge)}.md-pr\:0{padding-right:0}.md-pr\:xsmall{padding-right:var(--lns-space-xsmall)}.md-pr\:small{padding-right:var(--lns-space-small)}.md-pr\:medium{padding-right:var(--lns-space-medium)}.md-pr\:large{padding-right:var(--lns-space-large)}.md-pr\:xlarge{padding-right:var(--lns-space-xlarge)}.md-pr\:xxlarge{padding-right:var(--lns-space-xxlarge)}.md-px\:0{padding-left:0;padding-right:0}.md-px\:xsmall{padding-left:var(--lns-space-xsmall);padding-right:var(--lns-space-xsmall)}.md-px\:small{padding-left:var(--lns-space-small);padding-right:var(--lns-space-small)}.md-px\:medium{padding-left:var(--lns-space-medium);padding-right:var(--lns-space-medium)}.md-px\:large{padding-left:var(--lns-space-large);padding-right:var(--lns-space-large)}.md-px\:xlarge{padding-left:var(--lns-space-xlarge);padding-right:var(--lns-space-xlarge)}.md-px\:xxlarge{padding-left:var(--lns-space-xxlarge);padding-right:var(--lns-space-xxlarge)}.md-py\:0{padding-top:0;padding-bottom:0}.md-py\:xsmall{padding-top:var(--lns-space-xsmall);padding-bottom:var(--lns-space-xsmall)}.md-py\:small{padding-top:var(--lns-space-small);padding-bottom:var(--lns-space-small)}.md-py\:medium{padding-top:var(--lns-space-medium);padding-bottom:var(--lns-space-medium)}.md-py\:large{padding-top:var(--lns-space-large);padding-bottom:var(--lns-space-large)}.md-py\:xlarge{padding-top:var(--lns-space-xlarge);padding-bottom:var(--lns-space-xlarge)}.md-py\:xxlarge{padding-top:var(--lns-space-xxlarge);padding-bottom:var(--lns-space-xxlarge)}.md-text\:small{font-size:var(--lns-fontSize-small);line-height:var(--lns-lineHeight-small);letter-spacing:var(--lns-letterSpacing-small);font-weight:var(--lns-fontWeight-regular)}.md-text\:body-sm{font-size:var(--lns-fontSize-body-sm);line-height:var(--lns-lineHeight-body-sm);letter-spacing:var(--lns-letterSpacing-body-sm);font-weight:var(--lns-fontWeight-regular)}.md-text\:medium{font-size:var(--lns-fontSize-medium);line-height:var(--lns-lineHeight-medium);letter-spacing:var(--lns-letterSpacing-medium);font-weight:var(--lns-fontWeight-regular)}.md-text\:body-md{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);letter-spacing:var(--lns-letterSpacing-body-md);font-weight:var(--lns-fontWeight-regular)}.md-text\:large{font-size:var(--lns-fontSize-large);line-height:var(--lns-lineHeight-large);letter-spacing:var(--lns-letterSpacing-large);font-weight:var(--lns-fontWeight-regular)}.md-text\:body-lg{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);letter-spacing:var(--lns-letterSpacing-body-lg);font-weight:var(--lns-fontWeight-regular)}.md-text\:xlarge{font-size:var(--lns-fontSize-xlarge);line-height:var(--lns-lineHeight-xlarge);letter-spacing:var(--lns-letterSpacing-xlarge);font-weight:var(--lns-fontWeight-bold)}.md-text\:heading-sm{font-size:var(--lns-fontSize-heading-sm);line-height:var(--lns-lineHeight-heading-sm);letter-spacing:var(--lns-letterSpacing-heading-sm);font-weight:var(--lns-fontWeight-bold)}.md-text\:xxlarge{font-size:var(--lns-fontSize-xxlarge);line-height:var(--lns-lineHeight-xxlarge);letter-spacing:var(--lns-letterSpacing-xxlarge);font-weight:var(--lns-fontWeight-bold)}.md-text\:heading-md{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);letter-spacing:var(--lns-letterSpacing-heading-md);font-weight:var(--lns-fontWeight-bold)}.md-text\:xxxlarge{font-size:var(--lns-fontSize-xxxlarge);line-height:var(--lns-lineHeight-xxxlarge);letter-spacing:var(--lns-letterSpacing-xxxlarge);font-weight:var(--lns-fontWeight-bold)}.md-text\:heading-lg{font-size:var(--lns-fontSize-heading-lg);line-height:var(--lns-lineHeight-heading-lg);letter-spacing:var(--lns-letterSpacing-heading-lg);font-weight:var(--lns-fontWeight-bold)}.md-weight\:book{font-weight:var(--lns-fontWeight-book)}.md-weight\:regular{font-weight:var(--lns-fontWeight-regular)}.md-weight\:medium{font-weight:var(--lns-fontWeight-medium)}.md-weight\:bold{font-weight:var(--lns-fontWeight-bold)}.md-text\:body{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);font-weight:var(--lns-fontWeight-regular)}.md-text\:title{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);font-weight:var(--lns-fontWeight-bold)}.md-text\:mainTitle{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);font-weight:var(--lns-fontWeight-bold)}.md-text\:left{text-align:left}.md-text\:right{text-align:right}.md-text\:center{text-align:center}.md-border{border:1px solid var(--lns-color-border)}.md-borderTop{border-top:1px solid var(--lns-color-border)}.md-borderBottom{border-bottom:1px solid var(--lns-color-border)}.md-borderLeft{border-left:1px solid var(--lns-color-border)}.md-borderRight{border-right:1px solid var(--lns-color-border)}.md-inline{display:inline}.md-block{display:block}.md-flex{display:flex}.md-inlineBlock{display:inline-block}.md-inlineFlex{display:inline-flex}.md-none{display:none}.md-flexWrap{flex-wrap:wrap}.md-flexDirection\:column{flex-direction:column}.md-flexDirection\:row{flex-direction:row}.md-items\:stretch{align-items:stretch}.md-items\:center{align-items:center}.md-items\:baseline{align-items:baseline}.md-items\:flexStart{align-items:flex-start}.md-items\:flexEnd{align-items:flex-end}.md-items\:selfStart{align-items:self-start}.md-items\:selfEnd{align-items:self-end}.md-justify\:flexStart{justify-content:flex-start}.md-justify\:flexEnd{justify-content:flex-end}.md-justify\:center{justify-content:center}.md-justify\:spaceBetween{justify-content:space-between}.md-justify\:spaceAround{justify-content:space-around}.md-justify\:spaceEvenly{justify-content:space-evenly}.md-grow\:0{flex-grow:0}.md-grow\:1{flex-grow:1}.md-shrink\:0{flex-shrink:0}.md-shrink\:1{flex-shrink:1}.md-self\:auto{align-self:auto}.md-self\:flexStart{align-self:flex-start}.md-self\:flexEnd{align-self:flex-end}.md-self\:center{align-self:center}.md-self\:baseline{align-self:baseline}.md-self\:stretch{align-self:stretch}.md-overflow\:hidden{overflow:hidden}.md-overflow\:auto{overflow:auto}.md-relative{position:relative}.md-absolute{position:absolute}.md-sticky{position:sticky}.md-fixed{position:fixed}.md-top\:0{top:0}.md-top\:auto{top:auto}.md-top\:xsmall{top:var(--lns-space-xsmall)}.md-top\:small{top:var(--lns-space-small)}.md-top\:medium{top:var(--lns-space-medium)}.md-top\:large{top:var(--lns-space-large)}.md-top\:xlarge{top:var(--lns-space-xlarge)}.md-top\:xxlarge{top:var(--lns-space-xxlarge)}.md-bottom\:0{bottom:0}.md-bottom\:auto{bottom:auto}.md-bottom\:xsmall{bottom:var(--lns-space-xsmall)}.md-bottom\:small{bottom:var(--lns-space-small)}.md-bottom\:medium{bottom:var(--lns-space-medium)}.md-bottom\:large{bottom:var(--lns-space-large)}.md-bottom\:xlarge{bottom:var(--lns-space-xlarge)}.md-bottom\:xxlarge{bottom:var(--lns-space-xxlarge)}.md-left\:0{left:0}.md-left\:auto{left:auto}.md-left\:xsmall{left:var(--lns-space-xsmall)}.md-left\:small{left:var(--lns-space-small)}.md-left\:medium{left:var(--lns-space-medium)}.md-left\:large{left:var(--lns-space-large)}.md-left\:xlarge{left:var(--lns-space-xlarge)}.md-left\:xxlarge{left:var(--lns-space-xxlarge)}.md-right\:0{right:0}.md-right\:auto{right:auto}.md-right\:xsmall{right:var(--lns-space-xsmall)}.md-right\:small{right:var(--lns-space-small)}.md-right\:medium{right:var(--lns-space-medium)}.md-right\:large{right:var(--lns-space-large)}.md-right\:xlarge{right:var(--lns-space-xlarge)}.md-right\:xxlarge{right:var(--lns-space-xxlarge)}.md-width\:auto{width:auto}.md-width\:full{width:100%}.md-width\:0{width:0}.md-minWidth\:0{min-width:0}.md-height\:auto{height:auto}.md-height\:full{height:100%}.md-height\:0{height:0}.md-ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.md-srOnly{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0}}@media(min-width:75em){.lg-c\:red{color:var(--lns-color-red)}.lg-c\:blurpleLight{color:var(--lns-color-blurpleLight)}.lg-c\:blurpleMedium{color:var(--lns-color-blurpleMedium)}.lg-c\:blurple{color:var(--lns-color-blurple)}.lg-c\:blurpleDark{color:var(--lns-color-blurpleDark)}.lg-c\:blurpleStrong{color:var(--lns-color-blurpleStrong)}.lg-c\:offWhite{color:var(--lns-color-offWhite)}.lg-c\:blueLight{color:var(--lns-color-blueLight)}.lg-c\:blue{color:var(--lns-color-blue)}.lg-c\:blueDark{color:var(--lns-color-blueDark)}.lg-c\:orangeLight{color:var(--lns-color-orangeLight)}.lg-c\:orange{color:var(--lns-color-orange)}.lg-c\:orangeDark{color:var(--lns-color-orangeDark)}.lg-c\:tealLight{color:var(--lns-color-tealLight)}.lg-c\:teal{color:var(--lns-color-teal)}.lg-c\:tealDark{color:var(--lns-color-tealDark)}.lg-c\:yellowLight{color:var(--lns-color-yellowLight)}.lg-c\:yellow{color:var(--lns-color-yellow)}.lg-c\:yellowDark{color:var(--lns-color-yellowDark)}.lg-c\:grey8{color:var(--lns-color-grey8)}.lg-c\:grey7{color:var(--lns-color-grey7)}.lg-c\:grey6{color:var(--lns-color-grey6)}.lg-c\:grey5{color:var(--lns-color-grey5)}.lg-c\:grey4{color:var(--lns-color-grey4)}.lg-c\:grey3{color:var(--lns-color-grey3)}.lg-c\:grey2{color:var(--lns-color-grey2)}.lg-c\:grey1{color:var(--lns-color-grey1)}.lg-c\:white{color:var(--lns-color-white)}.lg-c\:primary{color:var(--lns-color-primary)}.lg-c\:primaryHover{color:var(--lns-color-primaryHover)}.lg-c\:primaryActive{color:var(--lns-color-primaryActive)}.lg-c\:body{color:var(--lns-color-body)}.lg-c\:bodyDimmed{color:var(--lns-color-bodyDimmed)}.lg-c\:bodyInverse{color:var(--lns-color-bodyInverse)}.lg-c\:background{color:var(--lns-color-background)}.lg-c\:backgroundHover{color:var(--lns-color-backgroundHover)}.lg-c\:backgroundActive{color:var(--lns-color-backgroundActive)}.lg-c\:backgroundSecondary{color:var(--lns-color-backgroundSecondary)}.lg-c\:backgroundSecondary2{color:var(--lns-color-backgroundSecondary2)}.lg-c\:backgroundInverse{color:var(--lns-color-backgroundInverse)}.lg-c\:overlay{color:var(--lns-color-overlay)}.lg-c\:border{color:var(--lns-color-border)}.lg-c\:focusRing{color:var(--lns-color-focusRing)}.lg-c\:record{color:var(--lns-color-record)}.lg-c\:recordHover{color:var(--lns-color-recordHover)}.lg-c\:recordActive{color:var(--lns-color-recordActive)}.lg-c\:info{color:var(--lns-color-info)}.lg-c\:success{color:var(--lns-color-success)}.lg-c\:warning{color:var(--lns-color-warning)}.lg-c\:danger{color:var(--lns-color-danger)}.lg-c\:dangerHover{color:var(--lns-color-dangerHover)}.lg-c\:dangerActive{color:var(--lns-color-dangerActive)}.lg-c\:backdrop{color:var(--lns-color-backdrop)}.lg-c\:backdropDark{color:var(--lns-color-backdropDark)}.lg-c\:backdropTwilight{color:var(--lns-color-backdropTwilight)}.lg-c\:disabledContent{color:var(--lns-color-disabledContent)}.lg-c\:highlight{color:var(--lns-color-highlight)}.lg-c\:disabledBackground{color:var(--lns-color-disabledBackground)}.lg-c\:formFieldBorder{color:var(--lns-color-formFieldBorder)}.lg-c\:formFieldBackground{color:var(--lns-color-formFieldBackground)}.lg-c\:buttonBorder{color:var(--lns-color-buttonBorder)}.lg-c\:upgrade{color:var(--lns-color-upgrade)}.lg-c\:upgradeHover{color:var(--lns-color-upgradeHover)}.lg-c\:upgradeActive{color:var(--lns-color-upgradeActive)}.lg-c\:tabBackground{color:var(--lns-color-tabBackground)}.lg-c\:discoveryBackground{color:var(--lns-color-discoveryBackground)}.lg-c\:discoveryLightBackground{color:var(--lns-color-discoveryLightBackground)}.lg-c\:discoveryTitle{color:var(--lns-color-discoveryTitle)}.lg-c\:discoveryHighlight{color:var(--lns-color-discoveryHighlight)}.lg-shadow\:small{box-shadow:var(--lns-shadow-small)}.lg-shadow\:medium{box-shadow:var(--lns-shadow-medium)}.lg-shadow\:large{box-shadow:var(--lns-shadow-large)}.lg-radius\:50{border-radius:var(--lns-radius-50)}.lg-radius\:100{border-radius:var(--lns-radius-100)}.lg-radius\:150{border-radius:var(--lns-radius-150)}.lg-radius\:175{border-radius:var(--lns-radius-175)}.lg-radius\:200{border-radius:var(--lns-radius-200)}.lg-radius\:250{border-radius:var(--lns-radius-250)}.lg-radius\:300{border-radius:var(--lns-radius-300)}.lg-radius\:none{border-radius:var(--lns-radius-none)}.lg-radius\:medium{border-radius:var(--lns-radius-medium)}.lg-radius\:large{border-radius:var(--lns-radius-large)}.lg-radius\:xlarge{border-radius:var(--lns-radius-xlarge)}.lg-radius\:round{border-radius:var(--lns-radius-round)}.lg-radius\:full{border-radius:var(--lns-radius-full)}.lg-bgc\:red{background-color:var(--lns-color-red)}.lg-bgc\:blurpleLight{background-color:var(--lns-color-blurpleLight)}.lg-bgc\:blurpleMedium{background-color:var(--lns-color-blurpleMedium)}.lg-bgc\:blurple{background-color:var(--lns-color-blurple)}.lg-bgc\:blurpleDark{background-color:var(--lns-color-blurpleDark)}.lg-bgc\:blurpleStrong{background-color:var(--lns-color-blurpleStrong)}.lg-bgc\:offWhite{background-color:var(--lns-color-offWhite)}.lg-bgc\:blueLight{background-color:var(--lns-color-blueLight)}.lg-bgc\:blue{background-color:var(--lns-color-blue)}.lg-bgc\:blueDark{background-color:var(--lns-color-blueDark)}.lg-bgc\:orangeLight{background-color:var(--lns-color-orangeLight)}.lg-bgc\:orange{background-color:var(--lns-color-orange)}.lg-bgc\:orangeDark{background-color:var(--lns-color-orangeDark)}.lg-bgc\:tealLight{background-color:var(--lns-color-tealLight)}.lg-bgc\:teal{background-color:var(--lns-color-teal)}.lg-bgc\:tealDark{background-color:var(--lns-color-tealDark)}.lg-bgc\:yellowLight{background-color:var(--lns-color-yellowLight)}.lg-bgc\:yellow{background-color:var(--lns-color-yellow)}.lg-bgc\:yellowDark{background-color:var(--lns-color-yellowDark)}.lg-bgc\:grey8{background-color:var(--lns-color-grey8)}.lg-bgc\:grey7{background-color:var(--lns-color-grey7)}.lg-bgc\:grey6{background-color:var(--lns-color-grey6)}.lg-bgc\:grey5{background-color:var(--lns-color-grey5)}.lg-bgc\:grey4{background-color:var(--lns-color-grey4)}.lg-bgc\:grey3{background-color:var(--lns-color-grey3)}.lg-bgc\:grey2{background-color:var(--lns-color-grey2)}.lg-bgc\:grey1{background-color:var(--lns-color-grey1)}.lg-bgc\:white{background-color:var(--lns-color-white)}.lg-bgc\:primary{background-color:var(--lns-color-primary)}.lg-bgc\:primaryHover{background-color:var(--lns-color-primaryHover)}.lg-bgc\:primaryActive{background-color:var(--lns-color-primaryActive)}.lg-bgc\:body{background-color:var(--lns-color-body)}.lg-bgc\:bodyDimmed{background-color:var(--lns-color-bodyDimmed)}.lg-bgc\:bodyInverse{background-color:var(--lns-color-bodyInverse)}.lg-bgc\:background{background-color:var(--lns-color-background)}.lg-bgc\:backgroundHover{background-color:var(--lns-color-backgroundHover)}.lg-bgc\:backgroundActive{background-color:var(--lns-color-backgroundActive)}.lg-bgc\:backgroundSecondary{background-color:var(--lns-color-backgroundSecondary)}.lg-bgc\:backgroundSecondary2{background-color:var(--lns-color-backgroundSecondary2)}.lg-bgc\:backgroundInverse{background-color:var(--lns-color-backgroundInverse)}.lg-bgc\:overlay{background-color:var(--lns-color-overlay)}.lg-bgc\:border{background-color:var(--lns-color-border)}.lg-bgc\:focusRing{background-color:var(--lns-color-focusRing)}.lg-bgc\:record{background-color:var(--lns-color-record)}.lg-bgc\:recordHover{background-color:var(--lns-color-recordHover)}.lg-bgc\:recordActive{background-color:var(--lns-color-recordActive)}.lg-bgc\:info{background-color:var(--lns-color-info)}.lg-bgc\:success{background-color:var(--lns-color-success)}.lg-bgc\:warning{background-color:var(--lns-color-warning)}.lg-bgc\:danger{background-color:var(--lns-color-danger)}.lg-bgc\:dangerHover{background-color:var(--lns-color-dangerHover)}.lg-bgc\:dangerActive{background-color:var(--lns-color-dangerActive)}.lg-bgc\:backdrop{background-color:var(--lns-color-backdrop)}.lg-bgc\:backdropDark{background-color:var(--lns-color-backdropDark)}.lg-bgc\:backdropTwilight{background-color:var(--lns-color-backdropTwilight)}.lg-bgc\:disabledContent{background-color:var(--lns-color-disabledContent)}.lg-bgc\:highlight{background-color:var(--lns-color-highlight)}.lg-bgc\:disabledBackground{background-color:var(--lns-color-disabledBackground)}.lg-bgc\:formFieldBorder{background-color:var(--lns-color-formFieldBorder)}.lg-bgc\:formFieldBackground{background-color:var(--lns-color-formFieldBackground)}.lg-bgc\:buttonBorder{background-color:var(--lns-color-buttonBorder)}.lg-bgc\:upgrade{background-color:var(--lns-color-upgrade)}.lg-bgc\:upgradeHover{background-color:var(--lns-color-upgradeHover)}.lg-bgc\:upgradeActive{background-color:var(--lns-color-upgradeActive)}.lg-bgc\:tabBackground{background-color:var(--lns-color-tabBackground)}.lg-bgc\:discoveryBackground{background-color:var(--lns-color-discoveryBackground)}.lg-bgc\:discoveryLightBackground{background-color:var(--lns-color-discoveryLightBackground)}.lg-bgc\:discoveryTitle{background-color:var(--lns-color-discoveryTitle)}.lg-bgc\:discoveryHighlight{background-color:var(--lns-color-discoveryHighlight)}.lg-m\:0{margin:0}.lg-m\:auto{margin:auto}.lg-m\:xsmall{margin:var(--lns-space-xsmall)}.lg-m\:small{margin:var(--lns-space-small)}.lg-m\:medium{margin:var(--lns-space-medium)}.lg-m\:large{margin:var(--lns-space-large)}.lg-m\:xlarge{margin:var(--lns-space-xlarge)}.lg-m\:xxlarge{margin:var(--lns-space-xxlarge)}.lg-mt\:0{margin-top:0}.lg-mt\:auto{margin-top:auto}.lg-mt\:xsmall{margin-top:var(--lns-space-xsmall)}.lg-mt\:small{margin-top:var(--lns-space-small)}.lg-mt\:medium{margin-top:var(--lns-space-medium)}.lg-mt\:large{margin-top:var(--lns-space-large)}.lg-mt\:xlarge{margin-top:var(--lns-space-xlarge)}.lg-mt\:xxlarge{margin-top:var(--lns-space-xxlarge)}.lg-mb\:0{margin-bottom:0}.lg-mb\:auto{margin-bottom:auto}.lg-mb\:xsmall{margin-bottom:var(--lns-space-xsmall)}.lg-mb\:small{margin-bottom:var(--lns-space-small)}.lg-mb\:medium{margin-bottom:var(--lns-space-medium)}.lg-mb\:large{margin-bottom:var(--lns-space-large)}.lg-mb\:xlarge{margin-bottom:var(--lns-space-xlarge)}.lg-mb\:xxlarge{margin-bottom:var(--lns-space-xxlarge)}.lg-ml\:0{margin-left:0}.lg-ml\:auto{margin-left:auto}.lg-ml\:xsmall{margin-left:var(--lns-space-xsmall)}.lg-ml\:small{margin-left:var(--lns-space-small)}.lg-ml\:medium{margin-left:var(--lns-space-medium)}.lg-ml\:large{margin-left:var(--lns-space-large)}.lg-ml\:xlarge{margin-left:var(--lns-space-xlarge)}.lg-ml\:xxlarge{margin-left:var(--lns-space-xxlarge)}.lg-mr\:0{margin-right:0}.lg-mr\:auto{margin-right:auto}.lg-mr\:xsmall{margin-right:var(--lns-space-xsmall)}.lg-mr\:small{margin-right:var(--lns-space-small)}.lg-mr\:medium{margin-right:var(--lns-space-medium)}.lg-mr\:large{margin-right:var(--lns-space-large)}.lg-mr\:xlarge{margin-right:var(--lns-space-xlarge)}.lg-mr\:xxlarge{margin-right:var(--lns-space-xxlarge)}.lg-mx\:0{margin-left:0;margin-right:0}.lg-mx\:auto{margin-left:auto;margin-right:auto}.lg-mx\:xsmall{margin-left:var(--lns-space-xsmall);margin-right:var(--lns-space-xsmall)}.lg-mx\:small{margin-left:var(--lns-space-small);margin-right:var(--lns-space-small)}.lg-mx\:medium{margin-left:var(--lns-space-medium);margin-right:var(--lns-space-medium)}.lg-mx\:large{margin-left:var(--lns-space-large);margin-right:var(--lns-space-large)}.lg-mx\:xlarge{margin-left:var(--lns-space-xlarge);margin-right:var(--lns-space-xlarge)}.lg-mx\:xxlarge{margin-left:var(--lns-space-xxlarge);margin-right:var(--lns-space-xxlarge)}.lg-my\:0{margin-top:0;margin-bottom:0}.lg-my\:auto{margin-top:auto;margin-bottom:auto}.lg-my\:xsmall{margin-top:var(--lns-space-xsmall);margin-bottom:var(--lns-space-xsmall)}.lg-my\:small{margin-top:var(--lns-space-small);margin-bottom:var(--lns-space-small)}.lg-my\:medium{margin-top:var(--lns-space-medium);margin-bottom:var(--lns-space-medium)}.lg-my\:large{margin-top:var(--lns-space-large);margin-bottom:var(--lns-space-large)}.lg-my\:xlarge{margin-top:var(--lns-space-xlarge);margin-bottom:var(--lns-space-xlarge)}.lg-my\:xxlarge{margin-top:var(--lns-space-xxlarge);margin-bottom:var(--lns-space-xxlarge)}.lg-p\:0{padding:0}.lg-p\:xsmall{padding:var(--lns-space-xsmall)}.lg-p\:small{padding:var(--lns-space-small)}.lg-p\:medium{padding:var(--lns-space-medium)}.lg-p\:large{padding:var(--lns-space-large)}.lg-p\:xlarge{padding:var(--lns-space-xlarge)}.lg-p\:xxlarge{padding:var(--lns-space-xxlarge)}.lg-pt\:0{padding-top:0}.lg-pt\:xsmall{padding-top:var(--lns-space-xsmall)}.lg-pt\:small{padding-top:var(--lns-space-small)}.lg-pt\:medium{padding-top:var(--lns-space-medium)}.lg-pt\:large{padding-top:var(--lns-space-large)}.lg-pt\:xlarge{padding-top:var(--lns-space-xlarge)}.lg-pt\:xxlarge{padding-top:var(--lns-space-xxlarge)}.lg-pb\:0{padding-bottom:0}.lg-pb\:xsmall{padding-bottom:var(--lns-space-xsmall)}.lg-pb\:small{padding-bottom:var(--lns-space-small)}.lg-pb\:medium{padding-bottom:var(--lns-space-medium)}.lg-pb\:large{padding-bottom:var(--lns-space-large)}.lg-pb\:xlarge{padding-bottom:var(--lns-space-xlarge)}.lg-pb\:xxlarge{padding-bottom:var(--lns-space-xxlarge)}.lg-pl\:0{padding-left:0}.lg-pl\:xsmall{padding-left:var(--lns-space-xsmall)}.lg-pl\:small{padding-left:var(--lns-space-small)}.lg-pl\:medium{padding-left:var(--lns-space-medium)}.lg-pl\:large{padding-left:var(--lns-space-large)}.lg-pl\:xlarge{padding-left:var(--lns-space-xlarge)}.lg-pl\:xxlarge{padding-left:var(--lns-space-xxlarge)}.lg-pr\:0{padding-right:0}.lg-pr\:xsmall{padding-right:var(--lns-space-xsmall)}.lg-pr\:small{padding-right:var(--lns-space-small)}.lg-pr\:medium{padding-right:var(--lns-space-medium)}.lg-pr\:large{padding-right:var(--lns-space-large)}.lg-pr\:xlarge{padding-right:var(--lns-space-xlarge)}.lg-pr\:xxlarge{padding-right:var(--lns-space-xxlarge)}.lg-px\:0{padding-left:0;padding-right:0}.lg-px\:xsmall{padding-left:var(--lns-space-xsmall);padding-right:var(--lns-space-xsmall)}.lg-px\:small{padding-left:var(--lns-space-small);padding-right:var(--lns-space-small)}.lg-px\:medium{padding-left:var(--lns-space-medium);padding-right:var(--lns-space-medium)}.lg-px\:large{padding-left:var(--lns-space-large);padding-right:var(--lns-space-large)}.lg-px\:xlarge{padding-left:var(--lns-space-xlarge);padding-right:var(--lns-space-xlarge)}.lg-px\:xxlarge{padding-left:var(--lns-space-xxlarge);padding-right:var(--lns-space-xxlarge)}.lg-py\:0{padding-top:0;padding-bottom:0}.lg-py\:xsmall{padding-top:var(--lns-space-xsmall);padding-bottom:var(--lns-space-xsmall)}.lg-py\:small{padding-top:var(--lns-space-small);padding-bottom:var(--lns-space-small)}.lg-py\:medium{padding-top:var(--lns-space-medium);padding-bottom:var(--lns-space-medium)}.lg-py\:large{padding-top:var(--lns-space-large);padding-bottom:var(--lns-space-large)}.lg-py\:xlarge{padding-top:var(--lns-space-xlarge);padding-bottom:var(--lns-space-xlarge)}.lg-py\:xxlarge{padding-top:var(--lns-space-xxlarge);padding-bottom:var(--lns-space-xxlarge)}.lg-text\:small{font-size:var(--lns-fontSize-small);line-height:var(--lns-lineHeight-small);letter-spacing:var(--lns-letterSpacing-small);font-weight:var(--lns-fontWeight-regular)}.lg-text\:body-sm{font-size:var(--lns-fontSize-body-sm);line-height:var(--lns-lineHeight-body-sm);letter-spacing:var(--lns-letterSpacing-body-sm);font-weight:var(--lns-fontWeight-regular)}.lg-text\:medium{font-size:var(--lns-fontSize-medium);line-height:var(--lns-lineHeight-medium);letter-spacing:var(--lns-letterSpacing-medium);font-weight:var(--lns-fontWeight-regular)}.lg-text\:body-md{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);letter-spacing:var(--lns-letterSpacing-body-md);font-weight:var(--lns-fontWeight-regular)}.lg-text\:large{font-size:var(--lns-fontSize-large);line-height:var(--lns-lineHeight-large);letter-spacing:var(--lns-letterSpacing-large);font-weight:var(--lns-fontWeight-regular)}.lg-text\:body-lg{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);letter-spacing:var(--lns-letterSpacing-body-lg);font-weight:var(--lns-fontWeight-regular)}.lg-text\:xlarge{font-size:var(--lns-fontSize-xlarge);line-height:var(--lns-lineHeight-xlarge);letter-spacing:var(--lns-letterSpacing-xlarge);font-weight:var(--lns-fontWeight-bold)}.lg-text\:heading-sm{font-size:var(--lns-fontSize-heading-sm);line-height:var(--lns-lineHeight-heading-sm);letter-spacing:var(--lns-letterSpacing-heading-sm);font-weight:var(--lns-fontWeight-bold)}.lg-text\:xxlarge{font-size:var(--lns-fontSize-xxlarge);line-height:var(--lns-lineHeight-xxlarge);letter-spacing:var(--lns-letterSpacing-xxlarge);font-weight:var(--lns-fontWeight-bold)}.lg-text\:heading-md{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);letter-spacing:var(--lns-letterSpacing-heading-md);font-weight:var(--lns-fontWeight-bold)}.lg-text\:xxxlarge{font-size:var(--lns-fontSize-xxxlarge);line-height:var(--lns-lineHeight-xxxlarge);letter-spacing:var(--lns-letterSpacing-xxxlarge);font-weight:var(--lns-fontWeight-bold)}.lg-text\:heading-lg{font-size:var(--lns-fontSize-heading-lg);line-height:var(--lns-lineHeight-heading-lg);letter-spacing:var(--lns-letterSpacing-heading-lg);font-weight:var(--lns-fontWeight-bold)}.lg-weight\:book{font-weight:var(--lns-fontWeight-book)}.lg-weight\:regular{font-weight:var(--lns-fontWeight-regular)}.lg-weight\:medium{font-weight:var(--lns-fontWeight-medium)}.lg-weight\:bold{font-weight:var(--lns-fontWeight-bold)}.lg-text\:body{font-size:var(--lns-fontSize-body-md);line-height:var(--lns-lineHeight-body-md);font-weight:var(--lns-fontWeight-regular)}.lg-text\:title{font-size:var(--lns-fontSize-body-lg);line-height:var(--lns-lineHeight-body-lg);font-weight:var(--lns-fontWeight-bold)}.lg-text\:mainTitle{font-size:var(--lns-fontSize-heading-md);line-height:var(--lns-lineHeight-heading-md);font-weight:var(--lns-fontWeight-bold)}.lg-text\:left{text-align:left}.lg-text\:right{text-align:right}.lg-text\:center{text-align:center}.lg-border{border:1px solid var(--lns-color-border)}.lg-borderTop{border-top:1px solid var(--lns-color-border)}.lg-borderBottom{border-bottom:1px solid var(--lns-color-border)}.lg-borderLeft{border-left:1px solid var(--lns-color-border)}.lg-borderRight{border-right:1px solid var(--lns-color-border)}.lg-inline{display:inline}.lg-block{display:block}.lg-flex{display:flex}.lg-inlineBlock{display:inline-block}.lg-inlineFlex{display:inline-flex}.lg-none{display:none}.lg-flexWrap{flex-wrap:wrap}.lg-flexDirection\:column{flex-direction:column}.lg-flexDirection\:row{flex-direction:row}.lg-items\:stretch{align-items:stretch}.lg-items\:center{align-items:center}.lg-items\:baseline{align-items:baseline}.lg-items\:flexStart{align-items:flex-start}.lg-items\:flexEnd{align-items:flex-end}.lg-items\:selfStart{align-items:self-start}.lg-items\:selfEnd{align-items:self-end}.lg-justify\:flexStart{justify-content:flex-start}.lg-justify\:flexEnd{justify-content:flex-end}.lg-justify\:center{justify-content:center}.lg-justify\:spaceBetween{justify-content:space-between}.lg-justify\:spaceAround{justify-content:space-around}.lg-justify\:spaceEvenly{justify-content:space-evenly}.lg-grow\:0{flex-grow:0}.lg-grow\:1{flex-grow:1}.lg-shrink\:0{flex-shrink:0}.lg-shrink\:1{flex-shrink:1}.lg-self\:auto{align-self:auto}.lg-self\:flexStart{align-self:flex-start}.lg-self\:flexEnd{align-self:flex-end}.lg-self\:center{align-self:center}.lg-self\:baseline{align-self:baseline}.lg-self\:stretch{align-self:stretch}.lg-overflow\:hidden{overflow:hidden}.lg-overflow\:auto{overflow:auto}.lg-relative{position:relative}.lg-absolute{position:absolute}.lg-sticky{position:sticky}.lg-fixed{position:fixed}.lg-top\:0{top:0}.lg-top\:auto{top:auto}.lg-top\:xsmall{top:var(--lns-space-xsmall)}.lg-top\:small{top:var(--lns-space-small)}.lg-top\:medium{top:var(--lns-space-medium)}.lg-top\:large{top:var(--lns-space-large)}.lg-top\:xlarge{top:var(--lns-space-xlarge)}.lg-top\:xxlarge{top:var(--lns-space-xxlarge)}.lg-bottom\:0{bottom:0}.lg-bottom\:auto{bottom:auto}.lg-bottom\:xsmall{bottom:var(--lns-space-xsmall)}.lg-bottom\:small{bottom:var(--lns-space-small)}.lg-bottom\:medium{bottom:var(--lns-space-medium)}.lg-bottom\:large{bottom:var(--lns-space-large)}.lg-bottom\:xlarge{bottom:var(--lns-space-xlarge)}.lg-bottom\:xxlarge{bottom:var(--lns-space-xxlarge)}.lg-left\:0{left:0}.lg-left\:auto{left:auto}.lg-left\:xsmall{left:var(--lns-space-xsmall)}.lg-left\:small{left:var(--lns-space-small)}.lg-left\:medium{left:var(--lns-space-medium)}.lg-left\:large{left:var(--lns-space-large)}.lg-left\:xlarge{left:var(--lns-space-xlarge)}.lg-left\:xxlarge{left:var(--lns-space-xxlarge)}.lg-right\:0{right:0}.lg-right\:auto{right:auto}.lg-right\:xsmall{right:var(--lns-space-xsmall)}.lg-right\:small{right:var(--lns-space-small)}.lg-right\:medium{right:var(--lns-space-medium)}.lg-right\:large{right:var(--lns-space-large)}.lg-right\:xlarge{right:var(--lns-space-xlarge)}.lg-right\:xxlarge{right:var(--lns-space-xxlarge)}.lg-width\:auto{width:auto}.lg-width\:full{width:100%}.lg-width\:0{width:0}.lg-minWidth\:0{min-width:0}.lg-height\:auto{height:auto}.lg-height\:full{height:100%}.lg-height\:0{height:0}.lg-ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.lg-srOnly{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0}}

    html[data-theme="light:light dark:dark"] {
      --lns-color-red: #C9372C;
      --lns-color-blurpleStrong: #2D298E;
      --lns-color-offWhite: #F8F8F8;
      --lns-color-blueLight: #CFE1FD;
      --lns-color-blue: #1868DB;
      --lns-color-blueDark: #123263;
      --lns-color-orangeLight: #FFD5D2;
      --lns-color-orangeDark: #D72700;
      --lns-color-tealLight: #BAF3DB;
      --lns-color-teal: #1F845A;
      --lns-color-tealDark: #164B35;
      --lns-color-yellowLight: #FCE4A6;
      --lns-color-yellow: #FBC828;
      --lns-color-yellowDark: #FCA700;

      --lns-color-grey1: #F8F8F8;
      --lns-color-grey2: #F0F1F2;
      --lns-color-grey3: #DDDEE1;
      --lns-color-grey4: #B7B9BE;
      --lns-color-grey5: #8C8F97;
      --lns-color-grey6: #6B6E76;
      --lns-color-grey7: #3B3D42;
      --lns-color-grey8: #292A2E;

      --lns-color-primaryHover: #4F49F3;
      --lns-color-primaryActive: #2D298E;
      --lns-color-backdropDark: #292A2EE5;
      --lns-color-backdropTwilight: #2D298ECC;
      --lns-color-highlight: #625DF526;
      --lns-color-focusRing: #625DF5CC;
    }

    html[data-color-mode="light"][data-theme="light:light dark:dark"],
    html[data-theme="light:light dark:dark"] [data-lens-theme="light"],
    html[data-theme="light:light dark:dark"] .theme-light {
      --ds-text: #292A2E;
      --ds-text-subtlest: #6B6E76;
      --ds-text-disabled: #B7B9BE;
      --ds-text-inverse: #FFFFFF;
      --ds-background-information-bold: #1868DB;
      --ds-background-accent-green-bolder: #1F845A;
      --ds-background-warning-bold: #FBC828;
      --ds-background-danger-bold: #C9372C;
      --ds-background-danger-bold-hovered: #AE2E24;
      --ds-background-danger-bold-pressed: #5D1F1A;
      --ds-background-discovery: #F8EEFE;
      --ds-background-discovery-hovered: #EED7FC;
      --ds-background-discovery-pressed: #D8A0F7;
      --ds-surface: #FFFFFF;
      --ds-background-neutral-subtle-hovered: #0515240F;
      --ds-background-neutral-subtle-pressed: #0B122824;
      --ds-surface-sunken: #F8F8F8;
      --ds-background-disabled: #17171708;
      --ds-background-neutral-bold: #292A2E;
      --ds-surface-overlay: #FFFFFF;
      --ds-background-input: #FFFFFF;
      --ds-background-neutral: #0515240F;
      --ds-background-discovery-bold: #964AC0;
      --ds-blanket: #050C1F75;
      --ds-border: #0B122824;
      --ds-border-input: #8C8F97;

      --lns-themeLight-color-body: var(--ds-text);
      --lns-themeLight-color-bodyDimmed: var(--ds-text-subtlest);
      --lns-themeLight-color-disabledContent: var(--ds-text-disabled);
      --lns-themeLight-color-discoveryTitle: var(--ds-text);
      --lns-themeLight-color-bodyInverse: var(--ds-text-inverse);
      --lns-themeLight-color-info: var(--ds-background-information-bold);
      --lns-themeLight-color-success: var(--ds-background-accent-green-bolder);
      --lns-themeLight-color-warning: var(--ds-background-warning-bold);
      --lns-themeLight-color-danger: var(--ds-background-danger-bold);
      --lns-themeLight-color-dangerHover: var(--ds-background-danger-bold-hovered);
      --lns-themeLight-color-dangerActive: var(--ds-background-danger-bold-pressed);
      --lns-themeLight-color-upgrade: var(--ds-background-discovery);
      --lns-themeLight-color-upgradeHover: var(--ds-background-discovery-hovered);
      --lns-themeLight-color-upgradeActive: var(--ds-background-discovery-pressed);
      --lns-themeLight-color-background: var(--ds-surface);
      --lns-themeLight-color-backgroundHover: var(--ds-background-neutral-subtle-hovered);
      --lns-themeLight-color-backgroundActive: var(--ds-background-neutral-subtle-pressed);
      --lns-themeLight-color-backgroundSecondary: var(--ds-surface-sunken);
      --lns-themeLight-color-backgroundSecondary2: var(--ds-surface-sunken);
      --lns-themeLight-color-disabledBackground: var(--ds-background-disabled);
      --lns-themeLight-color-backgroundInverse: var(--ds-background-neutral-bold);
      --lns-themeLight-color-overlay: var(--ds-surface-overlay);
      --lns-themeLight-color-formFieldBackground: var(--ds-background-input);
      --lns-themeLight-color-tabBackground: var(--ds-background-neutral);
      --lns-themeLight-color-discoveryBackground: var(--ds-background-discovery-bold);
      --lns-themeLight-color-discoveryLightBackground: var(--ds-background-discovery);
      --lns-themeLight-color-backdrop: var(--ds-blanket);
      --lns-themeLight-color-discoveryHighlight: var(--ds-background-discovery);
      --lns-themeLight-color-border: var(--ds-border);
      --lns-themeLight-color-formFieldBorder: var(--ds-border-input);
      --lns-themeLight-color-buttonBorder: var(--ds-border);

      --lns-themeLight-color-primaryHover: #4F49F3;
      --lns-themeLight-color-primaryActive: #2D298E;
      --lns-themeLight-color-backdropDark: #292A2EE5;
      --lns-themeLight-color-backdropTwilight: #2D298ECC;
      --lns-themeLight-color-highlight: #625DF526;
      --lns-themeLight-color-focusRing: #625DF5CC;
    }

    html[data-color-mode="dark"][data-theme="light:light dark:dark"],
    html[data-theme="light:light dark:dark"] [data-lens-theme="dark"],
    html[data-theme="light:light dark:dark"] .theme-dark {
      --ds-text: #CECFD2;
      --ds-text-subtlest: #96999E;
      --ds-text-disabled: #505258;
      --ds-text-inverse: #1F1F21;
      --ds-background-information-bold: #669DF1;
      --ds-background-accent-green-bolder: #4BCE97;
      --ds-background-warning-bold: #FBC828;
      --ds-background-danger-bold: #F87168;
      --ds-background-danger-bold-hovered: #FD9891;
      --ds-background-danger-bold-pressed: #FFD5D2;
      --ds-background-discovery: #35243F;
      --ds-background-discovery-hovered: #48245D;
      --ds-background-discovery-pressed: #803FA5;
      --ds-surface: #1F1F21;
      --ds-background-neutral-subtle-hovered: #CECED912;
      --ds-background-neutral-subtle-pressed: #E3E4F21F;
      --ds-surface-sunken: #18191A;
      --ds-background-disabled: #01040475;
      --ds-background-neutral-bold: #CECFD2;
      --ds-surface-overlay: #2B2C2F;
      --ds-background-input: #242528;
      --ds-background-neutral: #CECED912;
      --ds-background-discovery-bold: #C97CF4;
      --ds-blanket: #10121499;
      --ds-border: #E3E4F21F;
      --ds-border-input: #7E8188;

      --lns-themeDark-color-body: var(--ds-text);
      --lns-themeDark-color-bodyDimmed: var(--ds-text-subtlest);
      --lns-themeDark-color-disabledContent: var(--ds-text-disabled);
      --lns-themeDark-color-discoveryTitle: var(--ds-text);
      --lns-themeDark-color-bodyInverse: var(--ds-text-inverse);
      --lns-themeDark-color-info: var(--ds-background-information-bold);
      --lns-themeDark-color-success: var(--ds-background-accent-green-bolder);
      --lns-themeDark-color-warning: var(--ds-background-warning-bold);
      --lns-themeDark-color-danger: var(--ds-background-danger-bold);
      --lns-themeDark-color-dangerHover: var(--ds-background-danger-bold-hovered);
      --lns-themeDark-color-dangerActive: var(--ds-background-danger-bold-pressed);
      --lns-themeDark-color-upgrade: var(--ds-background-discovery);
      --lns-themeDark-color-upgradeHover: var(--ds-background-discovery-hovered);
      --lns-themeDark-color-upgradeActive: var(--ds-background-discovery-pressed);
      --lns-themeDark-color-background: var(--ds-surface);
      --lns-themeDark-color-backgroundHover: var(--ds-background-neutral-subtle-hovered);
      --lns-themeDark-color-backgroundActive: var(--ds-background-neutral-subtle-pressed);
      --lns-themeDark-color-backgroundSecondary: var(--ds-surface-sunken);
      --lns-themeDark-color-backgroundSecondary2: var(--ds-surface-sunken);
      --lns-themeDark-color-disabledBackground: var(--ds-background-disabled);
      --lns-themeDark-color-backgroundInverse: var(--ds-background-neutral-bold);
      --lns-themeDark-color-overlay: var(--ds-surface-overlay);
      --lns-themeDark-color-formFieldBackground: var(--ds-background-input);
      --lns-themeDark-color-tabBackground: var(--ds-background-neutral);
      --lns-themeDark-color-discoveryBackground: var(--ds-background-discovery-bold);
      --lns-themeDark-color-discoveryLightBackground: var(--ds-background-discovery);
      --lns-themeDark-color-backdrop: var(--ds-blanket);
      --lns-themeDark-color-discoveryHighlight: var(--ds-background-discovery);
      --lns-themeDark-color-border: var(--ds-border);
      --lns-themeDark-color-formFieldBorder: var(--ds-border-input);
      --lns-themeDark-color-buttonBorder: var(--ds-border);

      --lns-themeDark-color-primaryHover: #4F49F3;
      --lns-themeDark-color-primaryActive: #2D298E;
      --lns-themeDark-color-backdropDark: #292A2EE5;
      --lns-themeDark-color-backdropTwilight: #2D298ECC;
      --lns-themeDark-color-highlight: #625DF526;
      --lns-themeDark-color-focusRing: #625DF5CC;
    }

            #inner-shadow-companion {
              --lns-unit: 8px;

              --lns-color-red: #C9372C;
              --lns-color-blurpleStrong: #2D298E;
              --lns-color-offWhite: #F8F8F8;
              --lns-color-blueLight: #CFE1FD;
              --lns-color-blue: #1868DB;
              --lns-color-blueDark: #123263;
              --lns-color-orangeLight: #FFD5D2;
              --lns-color-orangeDark: #D72700;
              --lns-color-tealLight: #BAF3DB;
              --lns-color-teal: #1F845A;
              --lns-color-tealDark: #164B35;
              --lns-color-yellowLight: #FCE4A6;
              --lns-color-yellow: #FBC828;
              --lns-color-yellowDark: #FCA700;

              --lns-color-grey1: #F8F8F8;
              --lns-color-grey2: #F0F1F2;
              --lns-color-grey3: #DDDEE1;
              --lns-color-grey4: #B7B9BE;
              --lns-color-grey5: #8C8F97;
              --lns-color-grey6: #6B6E76;
              --lns-color-grey7: #3B3D42;
              --lns-color-grey8: #292A2E;

              --lns-color-primaryHover: #4F49F3;
              --lns-color-primaryActive: #2D298E;
              --lns-color-backdropDark: #292A2EE5;
              --lns-color-backdropTwilight: #2D298ECC;
              --lns-color-highlight: #625DF526;
              --lns-color-focusRing: #625DF5CC;

              --ds-text: #292A2E;
              --ds-text-subtlest: #6B6E76;
              --ds-text-disabled: #B7B9BE;
              --ds-text-inverse: #FFFFFF;
              --ds-background-information-bold: #1868DB;
              --ds-background-accent-green-bolder: #1F845A;
              --ds-background-warning-bold: #FBC828;
              --ds-background-danger-bold: #C9372C;
              --ds-background-danger-bold-hovered: #AE2E24;
              --ds-background-danger-bold-pressed: #5D1F1A;
              --ds-background-discovery: #F8EEFE;
              --ds-background-discovery-hovered: #EED7FC;
              --ds-background-discovery-pressed: #D8A0F7;
              --ds-surface: #FFFFFF;
              --ds-background-neutral-subtle-hovered: #0515240F;
              --ds-background-neutral-subtle-pressed: #0B122824;
              --ds-surface-sunken: #F8F8F8;
              --ds-background-disabled: #17171708;
              --ds-background-neutral-bold: #292A2E;
              --ds-surface-overlay: #FFFFFF;
              --ds-background-input: #FFFFFF;
              --ds-background-neutral: #0515240F;
              --ds-background-discovery-bold: #964AC0;
              --ds-blanket: #050C1F75;
              --ds-border: #0B122824;
              --ds-border-input: #8C8F97;

              --lns-themeLight-color-body: var(--ds-text);
              --lns-themeLight-color-bodyDimmed: var(--ds-text-subtlest);
              --lns-themeLight-color-disabledContent: var(--ds-text-disabled);
              --lns-themeLight-color-discoveryTitle: var(--ds-text);
              --lns-themeLight-color-bodyInverse: var(--ds-text-inverse);
              --lns-themeLight-color-info: var(--ds-background-information-bold);
              --lns-themeLight-color-success: var(--ds-background-accent-green-bolder);
              --lns-themeLight-color-warning: var(--ds-background-warning-bold);
              --lns-themeLight-color-danger: var(--ds-background-danger-bold);
              --lns-themeLight-color-dangerHover: var(--ds-background-danger-bold-hovered);
              --lns-themeLight-color-dangerActive: var(--ds-background-danger-bold-pressed);
              --lns-themeLight-color-upgrade: var(--ds-background-discovery);
              --lns-themeLight-color-upgradeHover: var(--ds-background-discovery-hovered);
              --lns-themeLight-color-upgradeActive: var(--ds-background-discovery-pressed);
              --lns-themeLight-color-background: var(--ds-surface);
              --lns-themeLight-color-backgroundHover: var(--ds-background-neutral-subtle-hovered);
              --lns-themeLight-color-backgroundActive: var(--ds-background-neutral-subtle-pressed);
              --lns-themeLight-color-backgroundSecondary: var(--ds-surface-sunken);
              --lns-themeLight-color-backgroundSecondary2: var(--ds-surface-sunken);
              --lns-themeLight-color-disabledBackground: var(--ds-background-disabled);
              --lns-themeLight-color-backgroundInverse: var(--ds-background-neutral-bold);
              --lns-themeLight-color-overlay: var(--ds-surface-overlay);
              --lns-themeLight-color-formFieldBackground: var(--ds-background-input);
              --lns-themeLight-color-tabBackground: var(--ds-background-neutral);
              --lns-themeLight-color-discoveryBackground: var(--ds-background-discovery-bold);
              --lns-themeLight-color-discoveryLightBackground: var(--ds-background-discovery);
              --lns-themeLight-color-backdrop: var(--ds-blanket);
              --lns-themeLight-color-discoveryHighlight: var(--ds-background-discovery);
              --lns-themeLight-color-border: var(--ds-border);
              --lns-themeLight-color-formFieldBorder: var(--ds-border-input);
              --lns-themeLight-color-buttonBorder: var(--ds-border);

              --lns-themeLight-color-primaryHover: #4F49F3;
              --lns-themeLight-color-primaryActive: #2D298E;
              --lns-themeLight-color-backdropDark: #292A2EE5;
              --lns-themeLight-color-backdropTwilight: #2D298ECC;
              --lns-themeLight-color-highlight: #625DF526;
              --lns-themeLight-color-focusRing: #625DF5CC;

              .theme-dark {
                --ds-text: #CECFD2;
                --ds-text-subtlest: #96999E;
                --ds-text-disabled: #505258;
                --ds-text-inverse: #1F1F21;
                --ds-background-information-bold: #669DF1;
                --ds-background-accent-green-bolder: #4BCE97;
                --ds-background-warning-bold: #FBC828;
                --ds-background-danger-bold: #F87168;
                --ds-background-danger-bold-hovered: #FD9891;
                --ds-background-danger-bold-pressed: #FFD5D2;
                --ds-background-discovery: #35243F;
                --ds-background-discovery-hovered: #48245D;
                --ds-background-discovery-pressed: #803FA5;
                --ds-surface: #1F1F21;
                --ds-background-neutral-subtle-hovered: #CECED912;
                --ds-background-neutral-subtle-pressed: #E3E4F21F;
                --ds-surface-sunken: #18191A;
                --ds-background-disabled: #01040475;
                --ds-background-neutral-bold: #CECFD2;
                --ds-surface-overlay: #2B2C2F;
                --ds-background-input: #242528;
                --ds-background-neutral: #CECED912;
                --ds-background-discovery-bold: #C97CF4;
                --ds-blanket: #10121499;
                --ds-border: #E3E4F21F;
                --ds-border-input: #7E8188;

                --lns-themeDark-color-body: var(--ds-text);
                --lns-themeDark-color-bodyDimmed: var(--ds-text-subtlest);
                --lns-themeDark-color-disabledContent: var(--ds-text-disabled);
                --lns-themeDark-color-discoveryTitle: var(--ds-text);
                --lns-themeDark-color-bodyInverse: var(--ds-text-inverse);
                --lns-themeDark-color-info: var(--ds-background-information-bold);
                --lns-themeDark-color-success: var(--ds-background-accent-green-bolder);
                --lns-themeDark-color-warning: var(--ds-background-warning-bold);
                --lns-themeDark-color-danger: var(--ds-background-danger-bold);
                --lns-themeDark-color-dangerHover: var(--ds-background-danger-bold-hovered);
                --lns-themeDark-color-dangerActive: var(--ds-background-danger-bold-pressed);
                --lns-themeDark-color-upgrade: var(--ds-background-discovery);
                --lns-themeDark-color-upgradeHover: var(--ds-background-discovery-hovered);
                --lns-themeDark-color-upgradeActive: var(--ds-background-discovery-pressed);
                --lns-themeDark-color-background: var(--ds-surface);
                --lns-themeDark-color-backgroundHover: var(--ds-background-neutral-subtle-hovered);
                --lns-themeDark-color-backgroundActive: var(--ds-background-neutral-subtle-pressed);
                --lns-themeDark-color-backgroundSecondary: var(--ds-surface-sunken);
                --lns-themeDark-color-backgroundSecondary2: var(--ds-surface-sunken);
                --lns-themeDark-color-disabledBackground: var(--ds-background-disabled);
                --lns-themeDark-color-backgroundInverse: var(--ds-background-neutral-bold);
                --lns-themeDark-color-overlay: var(--ds-surface-overlay);
                --lns-themeDark-color-formFieldBackground: var(--ds-background-input);
                --lns-themeDark-color-tabBackground: var(--ds-background-neutral);
                --lns-themeDark-color-discoveryBackground: var(--ds-background-discovery-bold);
                --lns-themeDark-color-discoveryLightBackground: var(--ds-background-discovery);
                --lns-themeDark-color-backdrop: var(--ds-blanket);
                --lns-themeDark-color-discoveryHighlight: var(--ds-background-discovery);
                --lns-themeDark-color-border: var(--ds-border);
                --lns-themeDark-color-formFieldBorder: var(--ds-border-input);
                --lns-themeDark-color-buttonBorder: var(--ds-border);

                --lns-themeDark-color-primaryHover: #4F49F3;
                --lns-themeDark-color-primaryActive: #2D298E;
                --lns-themeDark-color-backdropDark: #292A2EE5;
                --lns-themeDark-color-backdropTwilight: #2D298ECC;
                --lns-themeDark-color-highlight: #625DF526;
                --lns-themeDark-color-focusRing: #625DF5CC;
              }

              all: initial;
              font-family: "Atlassian Sans Ext", ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, system-ui, "Helvetica Neue", sans-serif;
              color: var(--lns-color-body);
            }
            #tooltip-mount-layer-companion {
              z-index: 2147483646;
              position: relative;

              color: var(--lns-color-body);
              pointer-events: auto;
            }
          </style><div class="companion-1b6rwsq"></div></div></template></div></div><wtd-root id="cashback"><template shadowrootmode="open"><div id="root"></div><link rel="stylesheet" href="chrome-extension://emnoomldgleagdjapdeckpmebokijail/style.css"></template></wtd-root><script id="define-custom-element-wtd-root" src="chrome-extension://emnoomldgleagdjapdeckpmebokijail/src/scripts/injected/defineCustomElementsInjected.js" data-params="{&quot;elementName&quot;:&quot;wtd-root&quot;,&quot;eventName&quot;:&quot;customElements.defined&quot;,&quot;isShadowRoot&quot;:true}"></script><script id="define-custom-element-wtd-div" src="chrome-extension://emnoomldgleagdjapdeckpmebokijail/src/scripts/injected/defineCustomElementsInjected.js" data-params="{&quot;elementName&quot;:&quot;wtd-div&quot;,&quot;eventName&quot;:&quot;customElements.defined&quot;,&quot;isShadowRoot&quot;:true}"></script><or-shadow-host><template shadowrootmode="open"><div class="MuiScopedCssBaseline-root mui-1ip0jz5"><or-portal-container><or-portal-container></or-portal-container></or-portal-container></div></template></or-shadow-host><or-shadow-host><template shadowrootmode="open"><div class="MuiScopedCssBaseline-root mui-1ip0jz5"><div class="MuiBox-root mui-3q4fan" id="orca-containter-host"><iframe src="chrome-extension://chmpifjjfpeodjljjadlobceoiflhdid/orca-container.html?41adbd9b-5e90-4c76-824c-bec0e6dc7ef8" title="orca-container" style="width: 100%; height: 100%; border: 0px;"></iframe></div></div></template></or-shadow-host><or-shadow-host><template shadowrootmode="open"><div class="MuiScopedCssBaseline-root mui-1ip0jz5"></div></template></or-shadow-host><wtd-div id="wanteeedContainer" style="position: fixed; display: block; top: 0px; right: 0px; z-index: 2147483647;"><template shadowrootmode="open"><wtd-root id="comparator"><template shadowrootmode="open"><div id="root"><div class="pointer-events-none z-[2147483647] flex select-none flex-col items-end overflow-hidden font-sans antialiased"></div></div><link rel="stylesheet" href="chrome-extension://emnoomldgleagdjapdeckpmebokijail/style.css"></template></wtd-root><iframe id="wanteeedPanel" data-version="2.206.0" allowtransparency="true" style="background-color: rgb(255, 255, 255); border: none; border-radius: 3px; box-shadow: rgb(181, 181, 181) 1px 1px 3px 2px; clip: auto; display: none; margin-left: auto; margin-right: 12px; margin-top: 12px; position: relative; z-index: 2147483647; height: 1px; width: 1px;"></iframe></template></wtd-div><div id="lo-gmail-container"><div id="main-loom-gmail-integration"><div id="loom-main-dropdown-dump"></div><div id="loom-always-hidden-div"></div></div></div><script id="define-custom-element-wtd-root" src="chrome-extension://emnoomldgleagdjapdeckpmebokijail/src/scripts/injected/defineCustomElementsInjected.js" data-params="{&quot;elementName&quot;:&quot;wtd-root&quot;,&quot;eventName&quot;:&quot;customElements.defined&quot;,&quot;isShadowRoot&quot;:true}"></script></body></html>