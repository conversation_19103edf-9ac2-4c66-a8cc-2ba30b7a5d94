ssh -L 5431:cl2-back-prd-paris.c9cia58cd5l0.eu-west-3.rds.amazonaws.com:5432 aws-paris-1


# list db instances (region = paris)
aws-vault exec prd-admin -- aws rds describe-db-instances --region eu-west-3 | jq '.DBInstances[].DBInstanceIdentifier'

# get the max number of connections of an rds instance (postgres)
aws-vault exec prd-admin -- aws rds describe-db-instances --region eu-west-3 --db-instance-identifier cl2-back-prd-paris | jq '.DBInstances[].DBParameterGroups[].DBParameterGroupName'

LEAST({DBInstanceClassMemory/9531392},5000)
DBInstanceClassMemory is in bytes
1073741824*4/9531392 = 453.3333333333333

LEAST({DBInstanceClassMemory/9531392},5000)

# docker swarm: pause a stack
docker stack pause <stack_name>

