The downside is that for the front-end, test timings are lost when we rerun only the failed tests. As a result, CircleCI will distribute the tests pseudo-randomly across instances in the next run. There exists workarounds for this that involve storing the test reports a bit differently. 

I'll try to revisit it later, but it could still be useful as it is.

---

The purpose of this meeting is to:
provide an overview of remaining tasks for the new QQ-XL tandem,
plan the work on this topic for the upcoming week.