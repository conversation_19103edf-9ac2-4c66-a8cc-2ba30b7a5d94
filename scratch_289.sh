# engines/commercial/multi_tenancy/spec/db/seedfile_spec.rb spec/acceptance/stats_comments_spec.rb engines/commercial/custom_maps/spec/acceptance/layers_spec.rb spec/finders/ideas_finder_spec.rb spec/acceptance/resident_native_survey_responses_spec.rb spec/policies/idea_reaction_policy_spec.rb spec/acceptance/invites_spec.rb spec/acceptance/confirmations_spec.rb engines/commercial/admin_api/spec/acceptance/tenants_spec.rb spec/acceptance/stats_initiatives_spec.rb spec/policies/baskets_idea_policy_spec.rb engines/commercial/id_vienna_saml/spec/requests/citizen_authentication_spec.rb engines/commercial/id_vienna_saml/spec/requests/employee_authentication_spec.rb spec/policies/event_policy_spec.rb engines/commercial/public_api/spec/acceptance/v2/basket_ideas_spec.rb spec/acceptance/static_pages_spec.rb spec/serializers/web_api/v1/idea_serializer_spec.rb engines/commercial/smart_groups/spec/rules/role_spec.rb engines/commercial/public_api/spec/acceptance/v2/phases_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/admin_digest_spec.rb engines/free/volunteering/spec/policies/volunteer_policy_spec.rb engines/commercial/analysis/spec/acceptance/stats_users_spec.rb spec/jobs/delete_invites_job_spec.rb engines/commercial/smart_groups/spec/rules/lives_in_spec.rb spec/services/side_fx_user_spec.rb spec/acceptance/idea_spam_report_spec.rb spec/acceptance/project_images_spec.rb spec/models/notifications/internal_comments/internal_comment_on_idea_you_moderate_spec.rb engines/commercial/id_oostende_rrn/spec/acceptance/oostende_rrn_verification_spec.rb spec/acceptance/groups_spec.rb spec/policies/admin_publication_policy_spec.rb spec/acceptance/project_folder_images_spec.rb spec/services/multiloc_service_spec.rb spec/models/notifications/internal_comments/internal_comment_on_unassigned_initiative_spec.rb engines/commercial/smart_groups/spec/rules/registration_completed_at_spec.rb engines/commercial/analytics/spec/acceptance/analytics_posts_spec.rb spec/models/notifications/internal_comments/internal_comment_on_initiative_assigned_to_you_spec.rb engines/commercial/report_builder/spec/services/report_builder/report_permissions_service_spec.rb engines/commercial/bulk_import_ideas/spec/acceptance/phase_users_spec.rb engines/commercial/smart_groups/spec/rules/custom_field_date_spec.rb engines/free/polls/spec/models/response_spec.rb spec/jobs/generate_user_avatar_job_spec.rb spec/services/side_fx_static_page_spec.rb engines/commercial/verification/spec/services/permissions/base_permissions_service_spec.rb spec/services/json_schema_generator_service_spec.rb engines/commercial/multi_tenancy/spec/services/side_fx_app_configuration_spec.rb spec/models/notifications/internal_comments/internal_comment_on_your_internal_comment_spec.rb spec/models/notifications/project_phase_upcoming_spec.rb spec/services/side_fx_follower_spec.rb engines/free/volunteering/spec/services/side_fx_volunteer_service_spec.rb engines/commercial/analysis/spec/serializers/web_api/v1/analysis_serializer_spec.rb spec/models/static_page_spec.rb engines/commercial/idea_custom_fields/spec/acceptance/idea_custom_fields/phase_context/index_spec.rb spec/tasks/single_use/migrate_deprecated_custom_field_widgets_spec.rb spec/models/idea_file_spec.rb engines/commercial/multi_tenancy/spec/services/multi_tenancy/tenant_service_spec.rb engines/commercial/idea_custom_fields/spec/serializers/web_api/v1/idea_serializer_spec.rb engines/commercial/verification/spec/acceptance/locked_attributes_spec.rb spec/models/notifications/threshold_reached_for_admin_spec.rb spec/services/project_folders/side_fx_project_folder_service_spec.rb engines/commercial/custom_maps/spec/services/custom_maps/side_fx_map_config_spec.rb spec/models/concerns/sluggable_spec.rb engines/commercial/verification/spec/services/side_fx_verifications_spec.rb engines/commercial/posthog_integration/spec/lib/posthog_integration/post_hog/client_spec.rb engines/commercial/multi_tenancy/spec/tasks/fix_assignees_task_spec.rb
# use rspec bisect
bin/rspec --bisect engines/commercial/multi_tenancy/spec/db/seedfile_spec.rb spec/acceptance/stats_comments_spec.rb engines/commercial/custom_maps/spec/acceptance/layers_spec.rb spec/finders/ideas_finder_spec.rb spec/acceptance/resident_native_survey_responses_spec.rb spec/policies/idea_reaction_policy_spec.rb spec/acceptance/invites_spec.rb spec/acceptance/confirmations_spec.rb engines/commercial/admin_api/spec/acceptance/tenants_spec.rb spec/acceptance/stats_initiatives_spec.rb spec/policies/baskets_idea_policy_spec.rb engines/commercial/id_vienna_saml/spec/requests/citizen_authentication_spec.rb engines/commercial/id_vienna_saml/spec/requests/employee_authentication_spec.rb spec/policies/event_policy_spec.rb engines/commercial/public_api/spec/acceptance/v2/basket_ideas_spec.rb spec/acceptance/static_pages_spec.rb spec/serializers/web_api/v1/idea_serializer_spec.rb engines/commercial/smart_groups/spec/rules/role_spec.rb engines/commercial/public_api/spec/acceptance/v2/phases_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/admin_digest_spec.rb engines/free/volunteering/spec/policies/volunteer_policy_spec.rb engines/commercial/analysis/spec/acceptance/stats_users_spec.rb spec/jobs/delete_invites_job_spec.rb engines/commercial/smart_groups/spec/rules/lives_in_spec.rb spec/services/side_fx_user_spec.rb spec/acceptance/idea_spam_report_spec.rb spec/acceptance/project_images_spec.rb spec/models/notifications/internal_comments/internal_comment_on_idea_you_moderate_spec.rb engines/commercial/id_oostende_rrn/spec/acceptance/oostende_rrn_verification_spec.rb spec/acceptance/groups_spec.rb spec/policies/admin_publication_policy_spec.rb spec/acceptance/project_folder_images_spec.rb spec/services/multiloc_service_spec.rb spec/models/notifications/internal_comments/internal_comment_on_unassigned_initiative_spec.rb engines/commercial/smart_groups/spec/rules/registration_completed_at_spec.rb engines/commercial/analytics/spec/acceptance/analytics_posts_spec.rb spec/models/notifications/internal_comments/internal_comment_on_initiative_assigned_to_you_spec.rb engines/commercial/report_builder/spec/services/report_builder/report_permissions_service_spec.rb engines/commercial/bulk_import_ideas/spec/acceptance/phase_users_spec.rb engines/commercial/smart_groups/spec/rules/custom_field_date_spec.rb engines/free/polls/spec/models/response_spec.rb spec/jobs/generate_user_avatar_job_spec.rb spec/services/side_fx_static_page_spec.rb engines/commercial/verification/spec/services/permissions/base_permissions_service_spec.rb spec/services/json_schema_generator_service_spec.rb engines/commercial/multi_tenancy/spec/services/side_fx_app_configuration_spec.rb spec/models/notifications/internal_comments/internal_comment_on_your_internal_comment_spec.rb spec/models/notifications/project_phase_upcoming_spec.rb spec/services/side_fx_follower_spec.rb engines/free/volunteering/spec/services/side_fx_volunteer_service_spec.rb engines/commercial/analysis/spec/serializers/web_api/v1/analysis_serializer_spec.rb spec/models/static_page_spec.rb engines/commercial/idea_custom_fields/spec/acceptance/idea_custom_fields/phase_context/index_spec.rb spec/tasks/single_use/migrate_deprecated_custom_field_widgets_spec.rb spec/models/idea_file_spec.rb engines/commercial/multi_tenancy/spec/services/multi_tenancy/tenant_service_spec.rb engines/commercial/idea_custom_fields/spec/serializers/web_api/v1/idea_serializer_spec.rb engines/commercial/verification/spec/acceptance/locked_attributes_spec.rb spec/models/notifications/threshold_reached_for_admin_spec.rb spec/services/project_folders/side_fx_project_folder_service_spec.rb engines/commercial/custom_maps/spec/services/custom_maps/side_fx_map_config_spec.rb spec/models/concerns/sluggable_spec.rb engines/commercial/verification/spec/services/side_fx_verifications_spec.rb engines/commercial/posthog_integration/spec/lib/posthog_integration/post_hog/client_spec.rb engines/commercial/multi_tenancy/spec/tasks/fix_assignees_task_spec.rb

bin/rspec ./spec/tasks/single_use/migrate_deprecated_about_this_report_widget_spec.rb
 ./engines/free/email_campaigns/spec/tasks/email_campaigns_task_spec.rb ./engines/commercial/multi_tenancy/spec/tasks/fix_user_custom_field_order_task_spec.rb