// Serialized reaction
{
  "data": {
    "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
    "type": "reaction",
    "attributes": {
      "mode": "down"
    },
    "relationships": {
      "reactable": {
        "data": {
          "id": "bd81edcb-bbc7-4ed9-a24c-5a2498f0589a",
          "type": "idea"
        }
      }
    }
  }
}

/**
Currently, a user cannot react to ideas in an ideation phase that is not active.

Questions and thoughts:
- How are reactions taken into account when it comes to participation stats?
- The use of Reaction for the common ground "agree/disagree/pass" seems opportunistic.
- What are the current usages of the Reaction model?
- The Phase model starts to be bloated with too many attributes.
- Is '"reacting_enabled": true' true when the phase is not active?
- Can like/dislike be used for agree/disagree? (see attributes of the phase)
- What would it take to implement anonymous participation for common ground? I think currently anonymous participation only applies to ideas, not reactions.
*/

// Let's try to rework the serialization of the reaction for our use case:
// (agree/disagree/pass) on statements

// GET .../phase/:id/ideas/next
// what if we want to get the next statement plus some stats?

{
  data: {

  }
}

// let's say we use:
// GET .../phase/:id/progress
// (or GET .../phase/:id/summary or GET .../phase/:id/common_ground)
{
  data: {
    "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
    "type": "phase-progress",
    "attributes": {
      "num_inputs": 10,
      "num_inputs_reacted": 5 // "reacted" is provisional/not final
    },
    "relationships": {
      "next_input": {
        "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
        "type": "statement"
      }
    }
  },
  included: [
    {
      "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
      "type": "idea",
      "attributes": {
        /* ... */
      }
    }
  ]
}

// Then, either we use:
// POST .../ideas/:id/reactions + GET .../phase/:id/progress
// or we use a dedicated endpoint (less restful):
// POST .../phase/:id/common_ground/reactions
// wit  h the mode and the idea id in the body:
{
    "reaction": {
      "mode": "agree", // or disagree/pass or up/down/pass
      "idea_id": "8f476eb9-c385-42a2-bd65-06aed088f138"
    }
}
// and we return the progress in the response










{
  "data": {
    "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
    "type": "statement",
    "title_multiloc": {
      "en": "Do you agree with this statement?",
      "fr": "Êtes-vous d'accord avec cette déclaration?"
    },
    "relationships": {
      "reactable": {
        "data": {
          "id": "bd81edcb-bbc7-4ed9-a24c-5a2498f0589a",
          "type": "idea"
        }
      }
    }
  }
}




{
  "data": [
    {
      "id": "17143ebf-6ee1-487d-9c73-2691cfb87b26",
      "type": "phase",
      "attributes": {
        "poll_anonymous": false, // not relevant for common ground - use default
        "survey_embed_url": null, // not relevant for common ground - use default
        "survey_service": null, // is used for external surveys - not relevant for common ground - set to null
        "document_annotation_embed_url": null, // not relevant for common ground - use default
        "title_multiloc": {
          "en": "Help out as a volunteer",
        },
        "start_at": "2025-01-19",
        "end_at": "2025-02-01",
        "created_at": "2025-01-20T09:49:50.887Z",
        "updated_at": "2025-01-20T09:49:50.887Z",
        "ideas_count": 0, // num of statements
        "campaigns_settings": {
          "project_phase_started": true
        },
        "participation_method": "common_ground", // the new participation method
        "submission_enabled": false, // for now, we don't want to allow users to submit new statements
        "commenting_enabled": false, // for now, common ground does not support comments
        "reacting_enabled": true, // if we use reactions for common ground, this should also be true
        "reacting_like_method": "unlimited", // always unlimited, actually all the following attributes don't really make sense
        "reacting_like_limited_max": 10, // see questions above
        "reacting_dislike_enabled": false,
        "reacting_dislike_method": "unlimited",
        "reacting_dislike_limited_max": 10,
        "allow_anonymous_participation": false, // not allowed for now?
        "presentation_mode": "card", // does not really make sense for common ground
        "ideas_order": null, // not relevant for common ground
        "input_term": "idea", // could be "statement" by default for common ground
        "prescreening_enabled": false, // not relevant for now since we don't want to allow users to submit new statements
        "manual_voters_amount": null, // not relevant for common ground
        "manual_votes_count": 0, // not relevant for common ground
        "similarity_threshold_title": 0.3, // not relevant since we don't want to allow users to submit new statements
        "similarity_threshold_body": 0.4, // not relevant since we don't want to allow users to submit new statements
        "manual_voters_last_updated_at": null, // not relevant for common ground
        "description_multiloc": {
          "en": "\u003cp\u003eQui et ex. At error quaerat. Fugit ex vitae.\u003c/p\u003e\u003cp\u003eQui optio dolor. Repellat quae quas. Et cumque possimus.\u003c/p\u003e\u003cp\u003eLabore deserunt repellendus. Facilis dolor temporibus. Totam quo aut.\u003c/p\u003e",
        },
        "previous_phase_end_at_updated": false,
        "report_public": null,
        "custom_form_persisted": false, // not relevant for common ground
        "supports_survey_form": false // not relevant for common ground
      },
      "relationships": {
        "manual_voters_last_updated_by": {
          "data": null
        },
        "project": {
          "data": {
            "id": "223b3e3d-77b9-42e2-8d77-516d89ebc1ee",
            "type": "project"
          }
        },
        "user_basket": {
          "data": null
        },
        "report": {
          "data": null
        },
        "permissions": {
          "data": [
            {
              "id": "71c3c795-f86b-4dbf-8dfd-82015bdd211f",
              "type": "permission"
            },
            {
              "id": "ca7d4026-0ce9-42e9-b00a-d18eb59308e2",
              "type": "permission"
            }
          ]
        }
      }
    }
  ],
  "included": [
    {
      "id": "71c3c795-f86b-4dbf-8dfd-82015bdd211f",
      "type": "permission",
      "attributes": {
        "action": "volunteering",
        "permitted_by": "users",
        "global_custom_fields": true,
        "verification_expiry": null,
        "access_denied_explanation_multiloc": {},
        "created_at": "2025-01-20T09:49:56.122Z",
        "updated_at": "2025-01-20T09:49:56.122Z",
        "verification_enabled": false
      },
      "relationships": {
        "permission_scope": {
          "data": {
            "id": "17143ebf-6ee1-487d-9c73-2691cfb87b26",
            "type": "phase"
          }
        },
        "groups": {
          "data": []
        },
        "permissions_custom_fields": {
          "data": []
        },
        "custom_fields": {
          "data": []
        }
      }
    },
    {
      "id": "ca7d4026-0ce9-42e9-b00a-d18eb59308e2",
      "type": "permission",
      "attributes": {
        "action": "attending_event",
        "permitted_by": "users",
        "global_custom_fields": true,
        "verification_expiry": null,
        "access_denied_explanation_multiloc": {},
        "created_at": "2025-01-20T09:49:56.134Z",
        "updated_at": "2025-01-20T09:49:56.134Z",
        "verification_enabled": false
      },
      "relationships": {
        "permission_scope": {
          "data": {
            "id": "17143ebf-6ee1-487d-9c73-2691cfb87b26",
            "type": "phase"
          }
        },
        "groups": {
          "data": []
        },
        "permissions_custom_fields": {
          "data": []
        },
        "custom_fields": {
          "data": []
        }
      }
    }
  ],
  "links": {
    "self": "http://localhost:3000/web_api/v1/projects/223b3e3d-77b9-42e2-8d77-516d89ebc1ee/phases?page%5Bnumber%5D=1",
    "first": "http://localhost:3000/web_api/v1/projects/223b3e3d-77b9-42e2-8d77-516d89ebc1ee/phases?page%5Bnumber%5D=1",
    "last": "http://localhost:3000/web_api/v1/projects/223b3e3d-77b9-42e2-8d77-516d89ebc1ee/phases?page%5Bnumber%5D=1",
    "prev": null,
    "next": null
  }
}

// import from xlsx
// POST http://localhost:3000/web_api/v1/phases/3ba9ed04-6a05-47ec-ade8-cbde6ec24287/importer/bulk_create_async/idea/xlsx

// import from another project phase
// POST .../web_api/v1/phases/3ba9ed04-6a05-47ec-ade8-cbde6ec24287/importer/bulk_create_async/idea
// it should support the same filters than GET .../ideas?...

// the importer is meant to importing ideas from an upload. It's already a bit polluting the route names.
// For the import-from-another-phase, we could use a different endpoint:
// POST .../web_api/v1/phases/3ba9ed04-6a05-47ec-ade8-cbde6ec24287/import
// + parameter to allow/disallow duplicates
// would return the ids of the imported ideas or a job id?

// POST .../web_api/v1/phases/3ba9ed04-6a05-47ec-ade8-cbde6ec24287/copy?allow_duplicates=true&project_id=223b3e3d-77b9-42e2-8d77-516d89ebc1ee



{
  project_id: "223b3e3d-77b9-42e2-8d77-516d89ebc1ee",
  phase_id: "3ba9ed04-6a05-47ec-ade8-cbde6ec24287",
  attributes: [
    "title_multiloc",
    "description_multiloc",
    "..."
  ],
  filters: {

  }
}