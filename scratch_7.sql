SELECT "projects".*
FROM "projects"
         INNER JOIN "projects_topics" ON "projects_topics"."project_id" = "projects"."id"
         INNER JOIN "topics" ON "topics"."id" = "projects_topics"."topic_id"
WHERE "topics"."id" IN ('89fe360e-ff1c-4838-be11-a63948afb734', '005be5af-486c-456a-84d5-949fbd2bd379')
GROUP BY "projects"."id"
HAVING (COUNT(topics.id) = 2)
ORDER BY "projects"."created_at" DESC

SELECT "projects".*
FROM "projects"
         INNER JOIN "admin_publications" "admin_publication" ON "admin_publication"."publication_type" = 'Project' AND
                                                                "admin_publication"."publication_id" = "projects"."id"
         INNER JOIN "projects_topics" ON "projects_topics"."project_id" = "projects"."id"
         INNER JOIN "topics" ON "topics"."id" = "projects_topics"."topic_id"
WHERE "admin_publication"."publication_status" = 'published'
  AND "topics"."id" IN ('be8c0f5c-7817-4d5f-a5f7-6af1d405c910', 'c282e629-0624-4ac2-ab66-cd1208aaee28')
GROUP BY "projects"."id"
HAVING (COUNT(topics.id) = 2)
ORDER BY "projects"."created_at" DESC
