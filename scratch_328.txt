A big thing I overlooked: we're using HTTP to connect to the prerender server, so the basic auth credentials are exposed. That said, it's still much better than having the prerender server open to the internet. I'll leave it as is for now, but we should consider using a Caddy (or Nginx) reverse proxy with HTTPS if new issues arise.

# us-prerender

username: 1GmTrGZ511Danf4ddZ9jJznbhcWVvJ3o
password: ?kc3Y>K+M^R@}uJ).qCh"Rqp{Jy$4K&-

echo -n '1GmTrGZ511Danf4ddZ9jJznbhcWVvJ3o:?kc3Y>K+M^R@}uJ).qCh"Rqp{Jy$4K&-' | base64

docker pull citizenlabdotco/cl2-devops-nginx-prerender-proxy:latest
PRERENDER_BASIC_AUTH=MUdtVHJHWjUxMURhbmY0ZGRaOWpKem5iaGNXVnZKM286P2tjM1k+SytNXlJAfXVKKS5xQ2giUnFwe0p5JDRLJi0=

cd cl2-deployment && docker stack deploy --compose-file docker-compose-production.yml cl2 --with-registry-auth --prune


# europe-prerender

username: y8tXj82PCUzUy7oCHOOkle867nFAftRC
password: }@W^AqWUoyytt].{FC9wwq3[&%!~Ra+b

echo -n 'y8tXj82PCUzUy7oCHOOkle867nFAftRC:}@W^AqWUoyytt].{FC9wwq3[&%!~Ra+b' | base64

docker pull citizenlabdotco/cl2-devops-nginx-prerender-proxy:latest
PRERENDER_BASIC_AUTH=eTh0WGo4MlBDVXpVeTdvQ0hPT2tsZTg2N25GQWZ0UkM6fUBXXkFxV1VveXl0dF0ue0ZDOXd3cTNbJiUhflJhK2I=

cd cl2-deployment && docker stack deploy --compose-file docker-compose-production.yml cl2-prd-bnlx-stack --with-registry-auth --prune





