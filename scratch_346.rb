# manually a campaign mail

tenant = Tenant.find_by(host: 'kra<PERSON>m-connect.be');
tenant.switch!;

campaign = EmailCampaigns::Campaign.find("8435db35-eb95-4c05-ab9e-05349d766774");
recipient = user = User.find("01e38a05-d1b1-4dd6-b9a6-4b46df85520c");
project = Project.find("3a7ad8ae-aced-436e-9a94-51be6d3de172");
phase = Phase.find("4f1e69fe-8260-428a-8d07-3fdde70dddac");

mailer = campaign.mailer_class.with(campaign: campaign, command: {
  recipient: user,
  event_payload: {
    phase_title_multiloc: phase.title_multiloc,
    phase_description_multiloc: phase.description_multiloc,
    phase_start_at: phase.start_at.iso8601,
    phase_end_at: phase.end_at&.iso8601,
    phase_url: Frontend::UrlService.new.model_to_url(phase, locale: Locale.new(recipient.locale)),
    project_title_multiloc: project.title_multiloc,
    project_description_preview_multiloc: project.description_preview_multiloc,
    unfollow_url: Frontend::UrlService.new.unfollow_url(Follower.new(followable: project, user: recipient))
  }
});

mail = mailer.campaign_mail
