SELECT * AS "count"
FROM "production_cl2_back_raw"."cl2_raw_report_builder_reports"
         INNER JOIN (SELECT "production_cl2_back_raw"."cl2_raw_tenants"."id"                   AS "id",
                            "production_cl2_back_raw"."cl2_raw_tenants"."core_lifecycle_stage" AS "core_lifecycle_stage",
                            "production_cl2_back_raw"."cl2_raw_tenants"."name"                 AS "name",
                            "production_cl2_back_raw"."cl2_raw_tenants"."host"                 AS "host",
                            "production_cl2_back_raw"."cl2_raw_tenants"."cluster_name"         AS "cluster_name",
                            "production_cl2_back_raw"."cl2_raw_tenants"."core_locales"         AS "core_locales",
                            "production_cl2_back_raw"."cl2_raw_tenants"."core_timezone"        AS "core_timezone",
                            "Users"."email"                                                    AS "Users__email",
                            "Pricing Plans"."name"                                             AS "Pricing Plans__name"
                     FROM "production_cl2_back_raw"."cl2_raw_tenants"

                              LEFT JOIN "admin_hq"."tenants_owners" AS "Tenants Owners"
                                        ON "production_cl2_back_raw"."cl2_raw_tenants"."id" =
                                           "Tenants Owners"."tenant_id"
                              LEFT JOIN "admin_hq"."users" AS "Users" ON "Tenants Owners"."user_id" = "Users"."id"
                              LEFT JOIN "admin_hq"."tenants_pricing_plans" AS "Tenants Pricing Plans"
                                        ON "production_cl2_back_raw"."cl2_raw_tenants"."id" =
                                           "Tenants Pricing Plans"."tenant_id"
                              LEFT JOIN "admin_hq"."pricing_plans" AS "Pricing Plans"
                                        ON "Tenants Pricing Plans"."pricing_plan_id" = "Pricing Plans"."id") AS "Question 1322"
                    ON "production_cl2_back_raw"."cl2_raw_report_builder_reports"."tenant_id" =
                       "Question 1322"."id"
         INNER JOIN "production_cl2_back_raw"."cl2_raw_content_builder_layouts" AS "Content Builder Layouts"
                    ON "production_cl2_back_raw"."cl2_raw_report_builder_reports"."id" =
                       "Content Builder Layouts"."content_buildable_id"

WHERE "Question 1322"."core_lifecycle_stage" = 'active'
  AND "Content Builder Layouts"."craftjs_json" :: TEXT LIKE '%VisitorsWidget%'
LIMIT 1048575