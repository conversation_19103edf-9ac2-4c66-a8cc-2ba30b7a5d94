require 'json'

h = {
    "tenant"=>{
        "name"=>"big-bro",
        "host"=>"big-bro-trial-wor.citizenlab.co",
        "settings"=>{
            "core"=>{
                "locales"=>[
                    "en"
                ],
                "lifecycle_stage"=>"trial",
                "organization_type"=>"medium_city",
                "organization_name"=>{
                    "en"=>"big-bro"
                },
                "allowed"=>true,
                "enabled"=>true,
                "timezone"=>"Brussels",
                "currency"=>"EUR",
                "color_main"=>"#163A7D",
                "color_secondary"=>"#CF4040",
                "color_text"=>"#163A7D"
            },
            "user_custom_fields"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "password_login"=>"[FILTERED]",
            "facebook_login"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "google_login"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "azure_ad_login"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "franceconnect_login"=>{
                "allowed"=>false,
                "enabled"=>false,
                "environment"=>"production"
            },
            "integration_onze_stad_app"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "verification"=>{
                "allowed"=>false,
                "enabled"=>false,
                "verification_methods"=>[]
            },
            "maps"=>{
                "allowed"=>true,
                "enabled"=>true,
                "tile_provider"=>"https://api.maptiler.com/maps/basic/{z}/{x}/{y}.png?key=DIZiuhfkZEQ5EgsaTk6D",
                "zoom_level"=>12
            },
            "geographic_dashboard"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "participatory_budgeting"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "polls"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "initiatives"=>{
                "allowed"=>true,
                "enabled"=>true,
                "voting_threshold"=>300,
                "days_limit"=>90,
                "threshold_reached_message"=>{
                    "en"=>"<p>The initiators are invited to present their proposal at the next council meeting. We will provide an official update.</p>\n",
                    "ar-SA"=>"<p>تمت دعوة المبادرين لتقديم مُقترحهم في اجتماع المجلس القادم. سنقدم تحديثًا رسميًا بذلك.</p>\n",
                    "en-GB"=>"<p>The initiators are invited to present their proposal at the next council meeting. We will provide an official update.</p>\n",
                    "en-CA"=>"<p>The initiators are invited to present their proposal at the next council meeting. We will provide an official update.</p>\n",
                    "nl-BE"=>"<p>De initiatiefnemers worden uitgenodigd om hun voorstel te komen voorstellen op de volgende gemeenteraad, waarop een officiële reactie volgt.</p>\n",
                    "nl-NL"=>"<p>De initiatiefnemers worden uitgenodigd om hun initiatief te komen voorstellen op de volgende gemeenteraad, waarop een officiële reactie volgt.</p>\n",
                    "fr-BE"=>"<p>Les porteurs de projets sont invités à présenter leur proposition à la prochaine réunion du conseil. Nous fourniront une mise à jour officielle.</p>\n",
                    "fr-FR"=>"<p>Les porteurs de projets sont invités à présenter leur proposition à la prochaine réunion du conseil. Nous fourniront une mise à jour officielle.</p>\n",
                    "de-DE"=>"<p>Die Initiatoren sind eingeladen, ihre Vorschlag bei der nächsten Stadtratssitzung zu präsentieren. Wir werden ein offizielles Update veröffentlichen.</p>\n",
                    "da-DK"=>"<p> Tagerne er inviteret til at præsentere deres borgerforslag på næste rådsmøde. Vi bringer en officiel opdatering. </p>\n",
                    "nb-NO"=>"<p>Inivitiativtakerne er invitert til  å presentere sitt forslag under neste kommunemøte Vi vil gi en offisiell oppdatering.</p>\n",
                    "es-ES"=>"<p>Se invita a los iniciadores a presentar su propuesta en la próxima reunión del consejo. Proporcionaremos una actualización oficial.</p>\n",
                    "es-CL"=>"<p>Se invita a los iniciadores a presentar su propuesta en la próxima reunión del consejo. Proporcionaremos una actualización oficial.</p>\n",
                    "pl-PL"=>"<p>Inicjatorzy są proszeni o przedstawienie swojej propozycji na następnym posiedzeniu rady. Przedstawimy oficjalną aktualizację.</p>\n",
                    "hu-HU"=>"<p>The initiators are invited to present their proposal at the next council meeting. We will provide an official update.</p>\n",
                    "kl-GL"=>"<p> Tagerne er inviteret til at præsentere deres borgerforslag på næste rådsmøde. Vi bringer en officiel opdatering. </p>\n",
                    "ro-RO"=>"<p> Inițiatorii sunt invitați să își prezinte propunerea la următoarea ședință de consiliu local. Vom oferi o actualizare oficială. </p>\n",
                    "pt-BR"=>"<p>Os iniciadores são convidados a apresentar a sua proposta na próxima reunião do conselho. Nós forneceremos uma atualização oficial.</p>\n"
                },
                "eligibility_criteria"=>{
                    "en"=>"<ul> <li>It must fall within the policy areas and competencies of local policy</li> <li>It serves the public interest rather than your individual interest</li> <li>It does not discriminate based on gender, race, age, or background</li> <li>It does not harm others</li> </ul>\n",
                    "ar-SA"=>"<ul> <li> يجب أن تندرج ضمن مجالات وكفاءات السياسة المحلية</li> <li> يجب أن تخدم المصلحة العامة وليس مصلحتك الفردية</li> <li> يجب ألّا تقوم بالتمييز على أساس الجنس، العرق أو العمر أو الخلفية</li> <li>يجب ألّا تؤذي الآخرين</li> </ul>\n",
                    "en-GB"=>"<ul> <li>It must fall within the policy areas and competencies of local policy</li> <li>It serves the public interest rather than your individual interest</li> <li>It does not discriminate based on gender, race, age, or background</li> <li>It does not harm others</li> </ul>\n",
                    "en-CA"=>"<ul> <li>It must fall within the policy areas and competencies of local policy</li> <li>It serves the public interest rather than your individual interest</li> <li>It does not discriminate based on gender, race, age, or background</li> <li>It does not harm others</li> </ul>\n",
                    "nl-BE"=>"<ul> <li>Het moet binnen de competenties van het lokale beleid vallen</li> <li>Het dient het algemeen belang in plaats van jouw individueel belang</li> <li>Het discrimineert niet op basis van geslacht, ras, leeftijd of achtergrond</li> <li>Het brengt anderen geen schade toe</li> </ul>\n",
                    "nl-NL"=>"<ul> <li>Het moet binnen de competenties van het lokale beleid vallen</li> <li>Het dient het algemeen belang in plaats van jouw individueel belang</li> <li>Het discrimineert niet op basis van geslacht, ras, leeftijd of achtergrond</li> <li>Het brengt anderen geen schade toe</li> </ul>\n",
                    "fr-BE"=>"<ul> <li>Ca doit s’inscrire dans les compétences de l’autorité locale</li> <li>Ca doit servir l’intérêt général plutôt que votre intérêt personnel</li> <li>Ca ne peut pas être discriminatoire en terme de sexe, de race, d’âge ou d’origine</li> <li>Ca ne peut pas nuire à autrui</li></ul>\n",
                    "fr-FR"=>"<ul> <li>Ca doit s’inscrire dans les compétences de l’autorité locale</li> <li>Ca doit servir l’intérêt général plutôt que votre intérêt personnel</li> <li>Ca ne peut pas être discriminatoire en terme de sexe, de race, d’âge ou d’origine</li> <li>Ca ne peut pas nuire à autrui</li></ul>\n",
                    "de-DE"=>"<ul> <li>Es muss in die Politikbereiche und Kompetenzen des lokalen Politiks fallen.</li> <li>Es dient eher dem öffentlichen als dem individuellen Interesse</li> <li>Es diskriminiert nicht aufgrund von Geschlecht, Rasse, Alter oder Hintergrund.</li> <li>Es schadet anderen nicht</li></ul>\n",
                    "da-DK"=>"<ul> <li>Det skal falde inden for kommunens politikområder og kompetencer</li> <li>Det tjener den offentlige interesse snarere end din individuelle interesse</li> <li>Det diskriminerer ikke andre borgere i forhold til køn, race, alder eller baggrund</li> <li>Det skader ikke andre</li></ul>\n",
                    "nb-NO"=>"<ul> <li>det skal være innenfor kommunens ansvarsområder</li> <li>Det tjener den offentlige interesse snarere enn din individuelle interesse</li> <li>Det diskriminerer ikke andre innbyggere basert på  kjønn rase, alder eller bakgrunn <li>det skader ikke andre</li></ul>\n",
                    "es-ES"=>"<ul> <li>Debe estar dentro de las áreas y competencias de la política local</li> <li>Es de interés público más que un interés particular.</li> <li>No discrimina en base a género, raza, edad u origen...</li> <li>No daña a los demás</li> </ul>\n",
                    "es-CL"=>"<ul> <li>Debe estar dentro de las áreas y competencias de la política local</li> <li>Es de interés público más que un interés particular.</li> <li>No discrimina en base a género, raza, edad u origen...</li> <li>No daña a los demás</li> </ul>\n",
                    "pl-PL"=>"<ul> <li>To musi wchodzić w zakres obszarów i kompetencji polityki lokalnej</li> <li>Służy interesowi publicznemu, a nie indywidualnemu</li> <li>Nie dyskryminuje ze względu na płeć, rasa, wiek lub pochodzenie</li> <li>Nie szkodzi innym</li> </ul>\n",
                    "hu-HU"=>"<ul> <li>It must fall within the policy areas and competencies of local policy</li> <li>It serves the public interest rather than your individual interest</li> <li>It does not discriminate based on gender, race, age, or background</li> <li>It does not harm others</li> </ul>\n",
                    "kl-GL"=>"Kommunimi politikkeqarfiit piginnaasallu iluaniissaaq. Tamanna illit nammineq soqutigisat pinnagu tamat soqutigisaannut iluaqutaassaaq. Innuttaasut allat arnat angutillu, sumi inunngorsimaneq, ukiut imaluunniit tunuliaqutaasut piinnarlugit assigiinngisitsisoqarneq ajorpoq. Allanut ajoqutaanngilaq.\n",
                    "ro-RO"=>"<ul> <li> Trebuie să se încadreze în domeniile și competențele administrației locale </li> <li> Servește interesul public și nu interesul dvs. personal </li> <li> Nu face discriminări în funcție de sex, rasă, vârstă sau origine </li> <li> Nu dăunează altora </li> </ul>\n",
                    "pt-BR"=>"<ul> <li>Deve enquadrar-se nas áreas de política e competências da política local</li> <li>Serve o interesse público e não o seu interesse individual</li> <li>Não discrimina com base no género, raça, idade ou origem</li> <li>Não prejudica os outros</li> </ul>\n"
                },
                "success_stories"=>[]
            },
            "surveys"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "typeform_surveys"=>{
                "allowed"=>true,
                "enabled"=>true,
                "user_token"=>"4H2ePYrNmXA2Pem59nn5G4jhYXCttxKKrrVakLZncLyE"
            },
            "google_forms_surveys"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "enalyzer_surveys"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "surveymonkey_surveys"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "volunteering"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "workshops"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "machine_translations"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "project_reports"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "clustering"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "similar_ideas"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "admin_project_templates"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "project_folders"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "custom_topics"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "idea_assignment"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "idea_custom_fields"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "smart_groups"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "ideas_overview"=>{
                "allowed"=>true,
                "enabled"=>false
            },
            "disable_downvoting"=>{
                "allowed"=>false,
                "enabled"=>false
            },
            "ideaflow_social_sharing"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "initiativeflow_social_sharing"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "widgets"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "manual_emailing"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "automated_emailing_control"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "pages"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "abbreviated_user_names"=>{
                "allowed"=>true,
                "enabled"=>false
            },
            "moderation"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "project_management"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "granular_permissions"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "project_visibility"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "private_projects"=>{
                "allowed"=>true,
                "enabled"=>true
            },
            "fragments"=>{
                "allowed"=>false,
                "enabled"=>false,
                "enabled_fragments"=>[]
            }
        }
    },
    "template"=>"trial-en_template",
    "cluster_id"=>"production-benelux"
}


puts h.to_json