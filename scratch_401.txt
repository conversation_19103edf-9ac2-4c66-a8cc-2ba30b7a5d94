The renaming mechanism stopped working when `<PERSON><PERSON><PERSON>` was replaced by the new `Tooltip` component in June. The issue is that `Tooltip` is stealing focus the focus from the input it wraps. The current
implementation of `Tooltip` ties the tooltip visibility to its focus state, so there's no way to display it without removing the focus from the input.

Reverting to `<PERSON>ip<PERSON>` doesn't seem to be an option. So, removing the tooltip is probably the simplest solution. It might slightly impact the UX, but nothing too bad IMO. That said, I wonder if the current focus behavior of `Tooltip` could cause issues in other places.

