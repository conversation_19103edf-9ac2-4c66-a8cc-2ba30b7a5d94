# list AMI that matches the following patter: "ubuntu/images/hvm-ssd/ubuntu-bionic-18.04-amd64-server-*"
aws-vault exec prd-admin -- aws ec2 describe-images --owners 099720109477 --filters "Name=name,Values=ubuntu/images/hvm-ssd/ubuntu-bionic-18.04-amd64-server-*" --query 'Images[*].[CreationDate,Name,ImageId]' --output text | sort -k1

# same but include the owner id and sort by owner id
aws-vault exec prd-admin -- aws ec2 describe-images --owners 099720109477 --filters "Name=name,Values=ubuntu/images/hvm-ssd/ubuntu-bionic-18.04-amd64-server-*" --query 'Images[*].[CreationDate,Name,ImageId,OwnerId]' --output text | sort -k4


aws-vault exec prd-admin -- aws ec2 describe-images --owners 099720109477 --filters "Name=name,Values=ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*" --query 'Images[*].[CreationDate,Name,ImageId,OwnerId]' --output text | grep '099720109477' | sort -k1

# get rds instance details: matomo-db
aws-vault exec prd-admin -- aws rds describe-db-instances --db-instance-identifier matomo-db

# SOA record = Start of Authority record
# It is a type of DNS record that specifies authoritative information about a DNS zone, including the primary name server, the email of the domain administrator, the domain serial number, and other items.