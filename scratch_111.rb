# decode base64 string
require 'base64'

str = "IyEvYmluL2Jhc2gKCnN1ZG8gL2Jpbi9kZCBpZj0vZGV2L3plcm8gb2Y9L3Zhci9zd2FwLjEgYnM9MU0gY291bnQ9MjA0OApzdWRvIC9zYmluL21rc3dhcCAvdmFyL3N3YXAuMQpzdWRvIGNobW9kIDYwMCAvdmFyL3N3YXAuMQpzdWRvIC9zYmluL3N3YXBvbiAvdmFyL3N3YXAuMQpzdWRvIGVjaG8gIi92YXIvc3dhcC4xICAgc3dhcCAgICBzd2FwICAgIGRlZmF1bHRzICAgICAgICAwICAgMCIgPiAvZXRjL2ZzdGFiCgpzdWRvIGFwdC1nZXQgLS15ZXMgaW5zdGFsbCBcCiAgICBhcHQtdHJhbnNwb3J0LWh0dHBzIFwKICAgIGNhLWNlcnRpZmljYXRlcyBcCiAgICBjdXJsIFwKICAgIHNvZnR3YXJlLXByb3BlcnRpZXMtY29tbW9uCgpjdXJsIC1mc1NMIGh0dHBzOi8vZG93bmxvYWQuZG9ja2VyLmNvbS9saW51eC91YnVudHUvZ3BnIHwgc3VkbyBhcHQta2V5IGFkZCAtCgpzdWRvIGFkZC1hcHQtcmVwb3NpdG9yeSBcCiAgICJkZWIgW2FyY2g9YW1kNjRdIGh0dHBzOi8vZG93bmxvYWQuZG9ja2VyLmNvbS9saW51eC91YnVudHUgXAogICAkKGxzYl9yZWxlYXNlIC1jcykgXAogICBzdGFibGUiCgpzdWRvIGFwdC1nZXQgLXkgdXBkYXRlCgpzdWRvIGFwdC1nZXQgLXkgaW5zdGFsbCBkb2NrZXItY2UgZG9ja2VyLWNlLWNsaSBjb250YWluZXJkLmlvCgpzdWRvIGdyb3VwYWRkIGRvY2tlcgpzdWRvIHVzZXJtb2QgLWFHIGRvY2tlciB1YnVudHUKCnN1ZG8gZWNobyAiMCA1ICogKiAxIGRvY2tlciBzeXN0ZW0gcHJ1bmUgLWYiID4gL2V0Yy9jcm9uLmQvcHJ1bmUtZG9ja2Vy"
Base64.decode64(str)

