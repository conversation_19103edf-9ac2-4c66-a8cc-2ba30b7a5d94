# call controller action manually
# for instance: WebApi::V1::ProjectsController#copy
# The code looks like this:
tenant = Tenant.find_by(host: 'engage.southwark.gov.uk')
tenant.switch!

custom_forms = CustomForm.all
custom_forms.to_h do |custom_form|
  fields = IdeaCustomFieldsService.new(custom_form).all_fields
  count = fields.where(input_type: 'point').count
  [custom_form.id, count]
end

project_id = "226d1630-14e3-4bbc-bc05-9d4db7e599c8"

project = Project.find(project_id)

CustomMaps::MapConfig.where(mappable: nil).to_sql
map_configs1 = CustomMaps::MapConfig.where(mappable: project)
map_configs2 = CustomMaps::MapConfig.where(mappable: project&.custom_form&.custom_fields)
map_configs3 = CustomMaps::MapConfig.where(mappable: project&.phases&.map(&:custom_form)&.compact&.map(&:custom_fields))

# count map_configs by creation day (not time)
# CustomMaps::MapConfig.group("date(created_at)").count

CustomMaps::MapConfig.where(mappable_id: nil).count



CustomMaps::MapConfig.where(mappable_type: 'CustomField').count