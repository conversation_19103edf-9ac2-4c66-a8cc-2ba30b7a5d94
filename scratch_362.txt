Europe is exploding!

![Untitled](https://prod-files-secure.s3.us-west-2.amazonaws.com/6d71eb32-33fc-4685-8ff3-ba0fbeed07e1/8a203ca7-0a02-424c-ba01-f53e30b4dd9b/Untitled.png)

https://metabase.hq.citizenlab.co/question/1987-tenants-per-cluster-evolution

Issues:

- Slow deployment and db:migrate performance
- Exaggerates issues due to increased volume (e.g. queues, performance)
- Higher business risk of compromising many tenants in case one has a serious issue

Let’s split it up, maybe in 3 clusters? fr-be, nl-dach, Nordics?

# Research & Plan

- The https://github.com/CitizenLabDotCo/cl2-script-migrate-tenant script can be used to move tenants between cluster.
- It could maybe be added a tool to AdminHQ. It would work similarly, but would not

## Questions

- What are the limitations of https://github.com/CitizenLabDotCo/cl2-script-migrate-tenant?
    - Not containerized, making it less reproducible.
    - It can only migrate one tenant at a time.
    - (Still using Ruby 2.7)
- Should we implement a "In maintenance" welcome page
    - Create Maintenance model: Maintenance(start_at: datetime, end_at: datetime) has many MaintenanceUpdates
    - At the tenant level or cluster level?
    - Design?
- Cost estimation?
- Sizing of the new infrastructure?
    - Type of RDS instances
    - Type of EC2 instances
    - See terraform files for other types of resources
- Impact on other systems?
    - AdminHQ
- Impact on maintenance/ops work?
    - More OS updates of ec2 instances
    - More updates of RDS databases
    - Potentially more alarms to monitor
    - Runs of manual rake tasks on each cluster
    - Dimensioning guidelines for new clusters
- Is the right time for a tenant cleanup?
    - Let's estimate what tenants are no longer used using the logs.
- Repartition rules between clusters (cluster definition)?
    - Initial proposal: fr-be, nl-dach, Nordics
    - but what about southern Europe?
    - playground cluster for internal/sales demo platform?
        - number of demo platforms?
        - what about trials? (only 22 tenants, all on the europe cluster)
    - Metabase dashboard

## To do

- [ ]  Test migrating a platform from prod to staging using https://github.com/CitizenLabDotCo/cl2-script-migrate-tenant


# Technical requirements

- As a dev, I want to be able to migrate a tenant from one cluster to another using an admin-hq rake task.
- As an AdminHQ user, I want to be able to migrate a tenant from one cluster to another using a “Data tool”.
- As a platform owner, I want platform to be in maintenance mode during a migration from one cluster to another.


# The plan

## Deployment of new clusters

We've created new clusters several times now, and while the process isn't completely streamlined (let's log areas for improvement as we go through it one more time), it has improved over time. Some manual steps are still involved, such as adding the cluster to the configurations of other systems like AdminHQ, cl2-tenant-setup, and CircleCI. However, most of the work is now automated with Terraform, so this shouldn't be the riskiest part of the job. The main questions regarding new clusters are:
- How should we split the tenants between them (the cluster scope)?
- What should the size of the new infrastructure be?
- In which region should we deploy them?
- What will be the impact of adding new clusters on other systems and maintenance/ops work?

We’ll document the thinking process and decisions regarding these questions in

## Migration of tenants between clusters

The tool is not production-grade and should be refitted for our needs here.

Today, to migrate tenants between clusters, we can use the cl2-script-migrate-tenant script. Currently, it's mainly used for copying a tenant locally to reproduce and debug issues. The tool isn’t production-ready and we will have to adapt/extend it to fit our needs.
