 => sha256:bf4d20c67c0749600e0f21a5f8ff28a37f3fb4ace76 3.23kB / 3.23kB  0.0s
 => => sha256:e4fff0779e6ddd22366469f08626c3ab1884b5cbe 29.13MB / 29.13MB  0.4s
 => => extracting sha256:e4fff0779e6ddd22366469f08626c3ab1884b5cbe1719b26  0.9s
 => => extracting sha256:9f5a9b7f8981ba272d492f62a87270cd013a436ac9a226e7  3.4s
 => => extracting sha256:92ccebb166cc3adda345bcc0643119541d973ec151eb4511  0.0s
 => [front internal] load build context                                    0.7s
 => => transferring context: 44.14MB                                       0.6s
 => [front 2/8] RUN bash /opt/installScripts/node/install-node-version.s  15.1s
 => [front 3/8] RUN node /opt/installScripts/yarn/install-yarn-version.js  0.2s
 => ERROR [front 4/8] RUN node /opt/installScripts/chrome/install-chrome-  0.3s
------
 > [front 4/8] RUN node /opt/installScripts/chrome/install-chrome-version.js 125.0.6422.141-1:
0.162 Installing Chrome version:  125.0.6422.141-1
0.303 2025-07-10 02:05:26 ERROR 404: Not Found.
0.304 failed to download chrome. Check the version?
0.304 chi

