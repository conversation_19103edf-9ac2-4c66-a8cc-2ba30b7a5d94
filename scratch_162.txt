I don't know where to start this conversation, so I'll just drop it here. I'd like to suggest that we refrain from using dynamic composition of strings if it's possible because, in my experience:
- it's been a source of bad copy,
- it can be difficult for/take a lot of time to the proofreader to understand what the placeholder stands for,
- very often, it's impossible to translate because noun/adjective agreement and the conjugation is generally simpler in English.

Today's example: {participants} can participate in this phase.

After checking the other strings and checking in the code, I found that participants can be any of these: ""
