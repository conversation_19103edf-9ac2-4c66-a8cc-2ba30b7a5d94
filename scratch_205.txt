It was easier to implement the polling mechanism from another dedicated page bc it was reusing the same mechanism used elsewhere in AdminHQ. In order to meet the time constraints imposed by the webinar and to limit the impact of this work on the roadmap of the Quality Quest tandem, I opted for this approach. I must admit that I didn't realise you relied so much on that behavior. It's possible to integrate the polling mechanism directly in the same page, but this will come with its own set of challenges and will require more time to implement. This would stray us away from our roadmap which has already been quite shaken during this last tandem cycle.





