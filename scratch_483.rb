# 1. https://omalahti.fi/fi-FI/admin/projects/bb3890a7-bfc9-4fb0-861f-7bc8c85d4825/phases/00f42a31-93ae-479b-9de6-a4162c41cf89/ideas
# 2. https://omalahti.fi/fi-FI/admin/projects/e9d21e0b-2fc5-4092-a1fe-21e83080ba82/phases/b8cb93d2-8189-42e1-9138-3012fe7c7735/ideas
# 3. https://omalahti.fi/fi-FI/admin/projects/c8a0ce77-2564-4ff9-85d0-192176e952ef/phases/7cc87d83-ae14-4144-a651-1f536d31deb9/ideas
# 4. https://omalahti.fi/fi-FI/admin/projects/36dc7325-27fb-484b-838a-a286e787669d/phases/cfdb5589-f986-45f1-af22-7c4f8da2b610/ideas
# 5. https://omalahti.fi/fi-FI/admin/projects/071bf31c-a48f-492f-80c5-c25e8536b743/phases/2238d438-231b-466e-ad4e-2c4557e72ce7/ideas

phase_ids = [
  '00f42a31-93ae-479b-9de6-a4162c41cf89',
  'b8cb93d2-8189-42e1-9138-3012fe7c7735',
  '7cc87d83-ae14-4144-a651-1f536d31deb9',
  'cfdb5589-f986-45f1-af22-7c4f8da2b610',
  '2238d438-231b-466e-ad4e-2c4557e72ce7'
]



project_ids = [
  "bb3890a7-bfc9-4fb0-861f-7bc8c85d4825",
  "c8a0ce77-2564-4ff9-85d0-192176e952ef",
  "593d2da3-6d99-46c4-a7c3-0613d138ac59",
  "b5e83b1d-345d-4a04-bd0e-c6dfd8b1a120",
  "e9d21e0b-2fc5-4092-a1fe-21e83080ba82",
  "36dc7325-27fb-484b-838a-a286e787669d",
  "071bf31c-a48f-492f-80c5-c25e8536b743"
]

from_status = IdeaStatus.find("db2c8206-22fa-49fd-933c-2f0a26a4ee3c")
to_status = IdeaStatus.find("d31df842-7f4d-46bf-ae80-c7190a52b142")

Idea.where(project_id: project_ids, idea_status: from_status).update_all(idea_status_id: to_status.id)
