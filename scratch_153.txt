AMI stands for Amazon Machine Image.

NVMe stands for Non-Volatile Memory Express.
It means that the storage is not volatile, meaning that it is not lost when the instance is stopped or terminated.
"Express" because it accesses the storage via the PCI Express bus, which is faster than the SATA bus.

In "/dev/sda1", "sda" stands for "SCSI disk A", and "1" stands for the first partition.
SCSI disk A is the first disk on the SCSI bus, while SCSI disk B is the second disk on the SCSI bus, and so on.
SCSI disk is a type of disk drive, and SCSI stands for Small Computer System Interface.

The main types of disk drives are:
- IDE (Integrated Drive Electronics)
- SATA (Serial Advanced Technology Attachment)
- SCSI (Small Computer System Interface)
- SAS (Serial Attached SCSI)
- NVMe (Non-Volatile Memory Express)

Typical usage for each type:
- SATA: Desktops, laptops, servers
- SCSI: Servers
- SAS: Servers
- NVMe: Servers
- IDE: Old computers

SATA uses a serial bus, while IDE uses a parallel bus.

OEM stands for Original Equipment Manufacturer.
This means that the manufacturer of the product is not the one selling it.

SKU stands for Stock Keeping Unit.
It is a unique identifier for a product.

