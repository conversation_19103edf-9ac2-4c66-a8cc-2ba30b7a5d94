What did [employee name] excel at over the past 6 months and should continue demonstrating? *

- Being on top of things despite the new addition to the family.
- Always super committed to your work and our mission.
- You managed to create the project library while still carrying out your many other responsibilities.

# Where does [employee name] have the opportunity to improve to make real, impactful differences?

If I had to nitpick, I'd say that you usually have a very precise idea of how things should be implemented, which is generally a good thing. However, sometimes this can hinder the team's initiative and creativity, depending on the context and the people involved. But it's really to say something, and I'm aware it sounds more harsh than I intend it to be.

# What can your manager do to help you succeed more?

I'm not sure it is something that would help me in my work, but I definitely miss the time when we had more opportunities to chat about technical solutions and ideas. Lately, most of our meetings seem to focus more on status updates.

# What can your manager do to help your team succeed more?

I'm a bit concerned about the lack of redundancy and capacity for some responsibilities within the team. I'm thinking particularly about product research and management, but this could apply to other areas as well, such as support. For example, I also don't quite understand why we aren't planning to hire a replacement for <PERSON>. With his departure, I feel the team is one or two exits away from being in a very tricky situation.

# Anything you want to still share with [employee name]?

You're still the rock on which the whole team relies, but we should learn not to.


