-- set search path to the doemee_leiden_nl schema
SET search_path = participateplus_falkirk_gov_uk;

DROP TABLE IF EXISTS project_folders_folders_to_restore;
DROP TABLE IF EXISTS projects_to_restore;
DROP TABLE IF EXISTS admin_publications_to_restore;
DROP TABLE IF EXISTS projects_topics_to_restore;
DROP TABLE IF EXISTS areas_projects_to_restore;
DROP TABLE IF EXISTS groups_projects_to_restore;
DROP TABLE IF EXISTS projects_allowed_input_topics_to_restore;
DROP TABLE IF EXISTS phases_to_restore;
DROP TABLE IF EXISTS surveys_responses_to_restore;
DROP TABLE IF EXISTS polls_questions_to_restore;
DROP TABLE IF EXISTS polls_options_to_restore;
DROP TABLE IF EXISTS polls_responses_to_restore;
DROP TABLE IF EXISTS polls_response_options_to_restore;
DROP TABLE IF EXISTS volunteering_causes_to_restore;
DROP TABLE IF EXISTS volunteering_volunteers_to_restore;
DROP TABLE IF EXISTS custom_forms_to_restore;
DROP TABLE IF EXISTS custom_fields_to_restore;
DROP TABLE IF EXISTS custom_field_options_to_restore;
DROP TABLE IF EXISTS baskets_to_restore;
DROP TABLE IF EXISTS permissions_to_restore;
DROP TABLE IF EXISTS groups_permissions_to_restore;
DROP TABLE IF EXISTS permissions_custom_fields_to_restore;
DROP TABLE IF EXISTS events_to_restore;
DROP TABLE IF EXISTS attendances_to_restore;
DROP TABLE IF EXISTS ideas_to_restore;
DROP TABLE IF EXISTS ideas_topics_to_restore;
DROP TABLE IF EXISTS ideas_phases_to_restore;
DROP TABLE IF EXISTS baskets_ideas_to_restore;
DROP TABLE IF EXISTS followers_to_restore;
DROP TABLE IF EXISTS idea_trending_infos_to_restore;
DROP TABLE IF EXISTS comments_to_restore;
DROP TABLE IF EXISTS internal_comments_to_restore;
DROP TABLE IF EXISTS official_feedbacks_to_restore;
DROP TABLE IF EXISTS reactions_to_restore;
DROP TABLE IF EXISTS spam_reports_to_restore;

-- create the table of project folders to restore
-- CREATE TABLE project_folders_folders_to_restore AS (
--     SELECT *
--     FROM project_folders_folders
--     WHERE id in (
--         'invalid-uuid-to-create-an-empty-table'
--     )
-- );

-- create the table of project to restore
CREATE TABLE projects_to_restore AS (
    SELECT *
    FROM projects
    WHERE id in (
        '560ed132-7b37-412a-976b-0874804c39a6'
    )
);

-- create the table of admin_publications to restore
-- ids are union of the ids of the projects to restore and the ids of the folders to restore
CREATE TABLE admin_publications_to_restore AS (
    SELECT *
    FROM admin_publications
    WHERE publication_id in (SELECT id FROM projects_to_restore)
);

-- has_many :projects_topics, dependent: :destroy
-- create the table of projects_topics to restore
CREATE TABLE projects_topics_to_restore AS (
    SELECT *
    FROM projects_topics
    WHERE project_id in (
        SELECT id FROM projects_to_restore
    )
);

-- has_many :areas_projects, dependent: :destroy
-- create the table of areas_projects to restore
CREATE TABLE areas_projects_to_restore AS (
    SELECT *
    FROM areas_projects
    WHERE project_id in (
        SELECT id FROM projects_to_restore
    )
);

-- has_many :groups_projects, dependent: :destroy
-- create the table of groups_projects to restore
CREATE TABLE groups_projects_to_restore AS (
    SELECT *
    FROM groups_projects
    WHERE project_id in (
        SELECT id FROM projects_to_restore
    )
);

-- has_many :projects_allowed_input_topics, dependent: :destroy
-- create the table of projects_allowed_input_topics to restore
CREATE TABLE projects_allowed_input_topics_to_restore AS (
    SELECT *
    FROM projects_allowed_input_topics
    WHERE project_id in (
        SELECT id FROM projects_to_restore
    )
);

-- has_many :phases, -> { order(:start_at) }, dependent: :destroy
-- create the table of phases to restore
CREATE TABLE phases_to_restore AS (
    SELECT *
    FROM phases
    WHERE project_id in (
        SELECT id FROM projects_to_restore
    )
);

-- has_many :survey_responses, class_name: 'Surveys::Response', dependent: :destroy
-- create the table of survey_responses to restore
CREATE TABLE surveys_responses_to_restore AS (
    SELECT *
    FROM surveys_responses
    WHERE phase_id in (
        SELECT id FROM phases_to_restore
    )
);



-- has_many :poll_questions, class_name: 'Polls::Question', dependent: :destroy
-- create the table of polls_questions to restore
CREATE TABLE polls_questions_to_restore AS (
    SELECT *
    FROM polls_questions
    WHERE phase_id in (
        SELECT id FROM phases_to_restore
    )
);

-- has_many :options, class_name: 'Polls::Option', dependent: :destroy
-- create the table of polls_options to restore
CREATE TABLE polls_options_to_restore AS (
    SELECT *
    FROM polls_options
    WHERE question_id in (
        SELECT id FROM polls_questions_to_restore
    )
);

-- has_many :poll_responses, class_name: 'Polls::Response', dependent: :destroy
-- create the table of polls_responses to restore
CREATE TABLE polls_responses_to_restore AS (
    SELECT *
    FROM polls_responses
    WHERE phase_id in (
        SELECT id FROM phases_to_restore
    )
);

-- has_many :response_options, class_name: 'Polls::ResponseOption', dependent: :destroy
-- create the table of polls_response_options to restore
CREATE TABLE polls_response_options_to_restore AS (
    SELECT *
    FROM polls_response_options
    WHERE option_id in (
        SELECT id FROM polls_options_to_restore
    )
);

-- has_many :causes, class_name: 'Volunteering::Cause', dependent: :destroy
-- create the table of volunteering_causes to restore
CREATE TABLE volunteering_causes_to_restore AS (
    SELECT *
    FROM volunteering_causes
    WHERE phase_id in (
        SELECT id FROM phases_to_restore
    )
);

-- has_many :volunteers, class_name: 'Volunteering::Volunteer', dependent: :destroy
-- create the table of volunteering_volunteers to restore
CREATE TABLE volunteering_volunteers_to_restore AS (
    SELECT *
    FROM volunteering_volunteers
    WHERE cause_id in (
        SELECT id FROM volunteering_causes_to_restore
    )
);


-- has_one :custom_form, as: :participation_context, dependent: :destroy # native_survey only
-- create the table of custom_forms to restore
CREATE TABLE custom_forms_to_restore AS (
    SELECT *
    FROM custom_forms
    WHERE participation_context_id in (
        SELECT id FROM projects_to_restore
        UNION
        SELECT id FROM phases_to_restore
    )
);

-- has_many :custom_fields, -> { order(:ordering) }, as: :resource, dependent: :destroy, inverse_of: :resource
-- create the table of custom_fields to restore
CREATE TABLE custom_fields_to_restore AS (
    SELECT *
    FROM custom_fields
    WHERE resource_id in (
        SELECT id FROM custom_forms_to_restore
    )
);

-- CustomField has_many :options, -> { order(:ordering) }, dependent: :destroy, class_name: 'CustomFieldOption', inverse_of: :custom_field
-- create the table of custom_field_options to restore
CREATE TABLE custom_field_options_to_restore AS (
    SELECT *
    FROM custom_field_options
    WHERE custom_field_id in (
        SELECT id FROM custom_fields_to_restore
    )
);

-- in phases.rb
-- has_many :baskets, dependent: :destroy
-- create the table of baskets to restore
CREATE TABLE baskets_to_restore AS (
    SELECT *
    FROM baskets
    WHERE phase_id in (
        SELECT id FROM phases_to_restore
    )
);

-- has_many :permissions, as: :permission_scope, dependent: :destroy
-- create the table of permissions to restore
CREATE TABLE permissions_to_restore AS (
    SELECT *
    FROM permissions
    WHERE permission_scope_id in (
        SELECT id FROM projects_to_restore
        UNION
        SELECT id FROM phases_to_restore
    )
);

-- has_many :groups_permissions, dependent: :destroy
-- create the table of groups_permissions to restore
CREATE TABLE groups_permissions_to_restore AS (
    SELECT *
    FROM groups_permissions
    WHERE permission_id in (
        SELECT id FROM permissions_to_restore
    )
);

-- has_many :permissions_custom_fields, -> { includes(:custom_field).order('custom_fields.ordering') }, inverse_of: :permission, dependent: :destroy
-- create the table of permissions_custom_fields to restore
CREATE TABLE permissions_custom_fields_to_restore AS (
    SELECT *
    FROM permissions_custom_fields
    WHERE permission_id in (
        SELECT id FROM permissions_to_restore
    )
);

-- has_many :events, -> { order(:start_at) }, dependent: :destroy
-- create the table of events to restore
CREATE TABLE events_to_restore AS (
    SELECT *
    FROM events
    WHERE project_id in (
        SELECT id FROM projects_to_restore
    )
);

-- has_many :attendances, class_name: 'Events::Attendance', dependent: :destroy
-- create the table of attendances to restore
CREATE TABLE attendances_to_restore AS (
    SELECT *
    FROM events_attendances
    WHERE event_id in (
        SELECT id FROM events_to_restore
    )
);

-- create the table of ideas to restore
CREATE TABLE ideas_to_restore AS (
    SELECT *
    FROM ideas
    WHERE project_id in (
        SELECT id FROM projects_to_restore
    )
);

--   has_many :ideas_topics, dependent: :destroy
-- create the table of ideas_topics to restore
CREATE TABLE ideas_topics_to_restore AS (
    SELECT *
    FROM ideas_topics
    WHERE idea_id in (
        SELECT id FROM ideas_to_restore
    )
);

-- has_many :ideas_phases, dependent: :destroy
-- create the table of ideas_phases to restore
CREATE TABLE ideas_phases_to_restore AS (
    SELECT *
    FROM ideas_phases
    WHERE idea_id in (
        SELECT id FROM ideas_to_restore
    )
);

--   has_many :baskets_ideas, dependent: :destroy
-- create the table of baskets_ideas to restore
CREATE TABLE baskets_ideas_to_restore AS (
    SELECT *
    FROM baskets_ideas
    WHERE idea_id in (
        SELECT id FROM ideas_to_restore
    )
);

-- create the table of followers to restore
CREATE TABLE followers_to_restore AS (
    SELECT * FROM followers WHERE followable_id in (
        SELECT id FROM projects_to_restore
        UNION
        SELECT id FROM ideas_to_restore
    )
);

-- has_one :idea_trending_info
-- create the table of idea_trending_infos to restore
CREATE TABLE idea_trending_infos_to_restore AS (
    SELECT *
    FROM idea_trending_infos
    WHERE idea_id in (
        SELECT id FROM ideas_to_restore
    )
);

-- has_many :comments, as: :post, dependent: :destroy
CREATE TABLE comments_to_restore AS (
    SELECT *
    FROM comments
    WHERE (post_type = 'Idea' AND post_id in (SELECT id FROM ideas_to_restore))
);

-- has_many :internal_comments, as: :post, dependent: :destroy
CREATE TABLE internal_comments_to_restore AS (
    SELECT *
    FROM internal_comments
    WHERE (post_type = 'Idea' AND post_id in (SELECT id FROM ideas_to_restore))
);

-- has_many :official_feedbacks, as: :post, dependent: :destroy
CREATE TABLE official_feedbacks_to_restore AS (
    SELECT *
    FROM official_feedbacks
    WHERE (post_type = 'Idea' AND post_id in (SELECT id FROM ideas_to_restore))
);

-- has_many :reactions, as: :reactable, dependent: :destroy
CREATE TABLE reactions_to_restore AS (
    SELECT *
    FROM reactions
    WHERE (reactable_type = 'Idea' AND reactable_id in (SELECT id FROM ideas_to_restore))
       OR (reactable_type = 'Comment' AND reactable_id in (SELECT id FROM comments_to_restore))
);

-- has_many :spam_reports, as: :spam_reportable, class_name: 'SpamReport', dependent: :destroy
CREATE TABLE spam_reports_to_restore AS (
    SELECT *
    FROM spam_reports
    WHERE (spam_reportable_type = 'Idea' AND spam_reportable_id in (SELECT id FROM ideas_to_restore))
       OR (spam_reportable_type = 'Comment' AND spam_reportable_id in (SELECT id FROM comments_to_restore))
);
