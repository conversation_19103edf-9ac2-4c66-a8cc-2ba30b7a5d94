SELECT "users".* FROM "users" WHERE NOT (roles @> '[{"type":"admin"}]');



-- A super admin is a user that is both an admin and has a citizenlab email address
SELECT "users".* FROM "users" WHERE roles @> '[{"type":"admin"}]' AND email ~* 'citizenlab.(eu|be|ch|de|nl|co|uk|us|cl|dk|pl)$'
-- @> means "contains" in postgresql


-- To select all users that are not super admins
SELECT "users".* FROM "users" WHERE NOT (roles @> '[{"type":"admin"}]' AND email ~* 'citizenlab.(eu|be|ch|de|nl|co|uk|us|cl|dk|pl)$')

-- In ruby:
-- where.not('email ~* ?', CITIZENLAB_MEMBER_REGEX_CONTENT)
-- where.not('roles @> ?', [{type: 'admin'}].to_json )
--  User.where.not('roles @> ? AND email ~* ?', [{type: 'admin'}].to_json, User::CITIZENLAB_MEMBER_REGEX_CONTENT)

-- ? is a placeholder for the value of the second argument
-- with named placeholders:
-- User.where.not('roles @> :roles AND email ~* :email_pattern', roles: [{type: 'admin'}].to_json, email_pattern: User::CITIZENLAB_MEMBER_REGEX_CONTENT)

-- Users with a citizenlab email address that are not super admins
-- User.where('email ~* :email_pattern AND NOT (roles @> :roles)', roles: [{type: 'admin'}].to_json, email_pattern: User::CITIZENLAB_MEMBER_REGEX_CONTENT)