# one liner to list all the tags used in a directory of html files (recursively)

find . -name "*.mjml" -exec grep -oP '(?<=<a href=")[^"]*' {} \; | sort | uniq

find . -type f -name "*.mjml" -exec grep -o '<[^/][^>]*>' {} + | sort | uniq -c


find . -type f -name "*.html" -exec grep -o '<[^/][^>]*>' {} + | sort | uniq -c | sort -nr

I apologize for misunderstanding your previous request. If you want to extract only the tag names without the attributes, you can modify the command as follows:

```bash
find . -type f -name "*.mjml" -exec grep -o '<[^/][^>]*>' {} + | sed -E 's/<([^[:space:]]+).*>/\1/' | sort | uniq -c
```

This command adds a `sed` command after the `grep` command to extract only the tag names from the matched tags. The `sed` command uses a regular expression to capture the tag name and replace the entire matched tag with just the tag name. The rest of the command remains the same, listing the tag names along with their occurrence counts. Remember to replace `/path/to/directory` with your actual directory path.
