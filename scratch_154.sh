# drain a docker node (id: 154) and remove it from the swarm
docker node update --availability drain jgpnjr2xvxddkzxpm821s89xi

# get docker swarm join token for manager
docker swarm join-token manager

# join a node to the swarm
docker swarm join SWMTKN-1-4qu27aqf4sm2fth7fy0bu6f0eubyj8s2p184eu668ibc3si6v7-dvjxcb2h5fc6ukjt4e2c7fy20

docker swarm join --token SWMTKN-1-4qu27aqf4sm2fth7fy0bu6f0eubyj8s2p184eu668ibc3si6v7-dvjxcb2h5fc6ukjt4e2c7fy20 172.31.24.234:2377


# drain, demote and leave the swarm (id: 154)
docker node update --availability drain 65qds2aj18uyfax93mc3wtoiu
docker node demote 65qds2aj18uyfax93mc3wtoiu
docker swarm leave


# backup swarm state
cp

# force new cluster
docker swarm init --force-new-cluster --advertise-addr ...

docker tag citizenlabdotco/mon-que:0.5 citizenlabdotco/mon-que:latest
docker push citizenlabdotco/mon-que:latest


cd cl2-deployment && CL_CLUSTER_NAME=eu docker stack deploy --compose-file docker-compose-production-benelux.yml cl2-prd-bnlx-stack --with-registry-auth