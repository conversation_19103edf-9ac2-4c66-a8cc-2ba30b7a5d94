A User has many Roles
A Role has many Permissions
A Permission has many Actions
A Permission has many Resources
A Permission belongs to a Role


Role schema
===========
id: uuid
name: string (multiloc?)
description: string (multiloc?)
policy: jsonb
created_at: datetime
updated_at: datetime

Example of an admin role policy
{
  "permissions": [
    {
      "actions": ["read", "create", "update", "delete"],
      "resources": ["users"]
    },
    {
      "actions": ["read", "create", "update", "delete"],
      "resources": ["roles"]
    },
    {
      "actions": ["read", "create", "update", "delete"],
      "resources": ["api_keys/
    }
  ]
}

RBAC a du potentiel, mais le problème principal est que les différents modérateurs ont les mêmes permissions, mais pour des projets différents.


Hey <PERSON>, I have an appointment from 10.30AM to 11AM (+ 15min-ish of transit). If they meet with multiple tandems, maybe it's possible to flip the order so I can attend. Otherwise, I'm sure you'll do great without me :)

# API

We will introduce a new query parameter in project-related endpoints (and possibly others) that allows users to assume additional roles for the duration of the request. For example:

```
GET .../web_api/v1/projects/:id?roles_token=634c51ffaefbdfd277f03879d36e3464efaa3aef5c87222493a3864c85c1831a
GET .../web_api/v1/phases/:id?roles_token=634c51ffaefbdfd277f03879d36e3464efaa3aef5c87222493a3864c85c1831a
```

## How to find the token?

Draft project will all include a **preview link** in this format:

```
https://demo.stg.govocal.com/en/projects/{project_id}/preview/{roles_token}
```

## Should the front-end be able to check what roles are granted?

Yes, it would probably be better. It could also work without this, but the front-end would need to infer the granted roles from the context, which should be feasible given the limited use of these tokens.
