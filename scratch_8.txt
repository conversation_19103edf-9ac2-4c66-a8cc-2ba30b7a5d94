[TAN-754] Fix variable interpolation on initiatives static page

Correct variable interpolation on the initiative page located at /pages/initiatives. The template expects a variable named `initiativesVotingThreshold` but all occurrences of "voting" have been renamed to "reactions" when proper voting methods were introduced. However, the variable name was not updated in the page content. To fix this, I opted to revert the variable name to `initiativesVotingThreshold` in the front-end instead of running a complex and error-prone data migration.