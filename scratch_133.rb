require 'json'
require 'stackprof'

template = 'global-demo_template'
tenant = FactoryBot::create(:tenant, host: "tenant-10.citizenlab.co")

profile = StackProf.run(mode: :wall, raw: true, interval: 2500) do
  side_fx_tenant = MultiTenancy::SideFxTenantService.new

  side_fx_tenant.before_apply_template(tenant, template)
  side_fx_tenant.around_apply_template(tenant, template) do
    tenant.switch do
      tenant_deserializer = ::MultiTenancy::Templates::TenantDeserializer
      tenant_deserializer.new.resolve_and_apply_template(template, external_subfolder: 'release')
    end
  end
  side_fx_tenant.after_apply_template(tenant, template)
end

File.write("stackprof-apply_tenant_template_job-#{template}.json", JSON.generate(profile))

tenant = Tenant.find_by(host: 'global8.localhost')
profile = StackProf.run(mode: :wall, raw: true, interval: 1000) do
  template = ::MultiTenancy::Templates::Serializer.new(tenant).run
end

File.write("stackprof-serialize_template.json", JSON.generate(profile))
File.write("my-template.yml", template.to_yaml)
