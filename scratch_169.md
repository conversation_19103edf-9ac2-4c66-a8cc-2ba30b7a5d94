# Parsing examples

```shell
ruby-parse -e '1'
ruby-parse -e 'AppConfiguration.first!'
ruby-parse -e '::AppConfiguration.first!'
ruby-parse -e 'if a == 1; puts "a"; end'
```

# Comments
- Rubocop is doing some sort of structural search.
- Rubocop hooks: on_class, on_module, on_def, on_send, on_begin, on_if, on_while, etc.

# Questions
- How to use ruby-parse to parse a file? -> ruby-parse -f file.rb
- What is Abstract about the abstract syntax tree? What would be a concrete syntax tree?
- Ruby ast: what lvasgn stands for? -> local variable assignment