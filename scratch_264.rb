Analytics::FactParticipation.select(:dimension_user_id).distinct.where.not(dimension_user_id: nil)

# page%5Bnumber%5D=1
# page%5Bsize%5D=6
# depth=0
# remove_not_allowed_parents=true
# publication_statuses%5B%5D=published
# publication_statuses%5B%5D=archived

#    publication_filterer = AdminPublicationsFilteringService.new
#     publications = policy_scope(AdminPublication.includes(:parent))
#     publications = publication_filterer.filter(publications, params)
#
#     @publications = publications.includes(:publication, :children)
#       .order(:ordering)
#     @publications = paginate @publications
#
#     render json: linked_json(
#       @publications,
#       WebApi::V1::AdminPublicationSerializer,
#       params: jsonapi_serializer_params(
#         visible_children_count_by_parent_id: publication_filterer.visible_children_counts_by_parent_id
#       )
#     )

publication_filterer = AdminPublicationsFilteringService.new
publications = AdminPublication.includes(:parent)
publications = publication_filterer.filter(publications, {
  page: {
    number: 1,
    size: 6
  },
  depth: 0,
  remove_not_allowed_parents: true,
  publication_statuses: ['published', 'archived']
})

Que




