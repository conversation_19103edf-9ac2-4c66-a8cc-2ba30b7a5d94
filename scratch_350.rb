CustomMaps::MapConfig.where(mappable: nil).count

map_configs = CustomMaps::MapConfig.where(mappable: nil).limit(10)
map_configs.destroy_all

# layers for a batch of map configs
def delete_batch(batch_size)
  batch = CustomMaps::MapConfig.where(mappable: nil).limit(batch_size)
  layers = CustomMaps::Layer.where(map_config: batch)
  layers.delete_all
  batch.delete_all

  CustomMaps::MapConfig.where(mappable: nil).count
end

CustomMaps::Layer.where(map_config: CustomMaps::MapConfig.where(mappable: nil)).count



Tenant.all.each do |tenant|
  tenant.switch do
    puts "Tenant: #{tenant.id} (#{tenant.host})"
    puts "MapConfigs: #{CustomMaps::MapConfig.where(mappable: nil).count}"
    puts "Layers: #{CustomMaps::Layer.where(map_config: CustomMaps::MapConfig.where(mappable: nil)).count}"
    puts "_____________________________"
  end
end