def response_rate
  optional_keys = CustomField.with_resource_type("User")
                             .enabled
                             .where(required: false)
                             .pluck(:key)

  total_count = optional_keys.count * User.count

  response_count = optional_keys.sum do |key|
    User.where('custom_field_values ? :key', key: key).count
  end

  response_rate = response_count / total_count.to_f
  [response_count, total_count, response_rate]
end

Tenant.all.each do |tenant|
  tenant.switch do
    row = response_rate.unshift(tenant.host).map(&:to_s).join(',')
    puts(row)
  end
end
