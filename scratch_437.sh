#!/bin/bash
set -euo pipefail

# Check if there's an existing release PR
existing_pr=$(gh pr list --state open --search "Release in:title" --json url,title --jq '.[0]')

if [ -n "$existing_pr" ]; then
    echo "Found existing release PR: $(echo "$existing_pr" | jq -r '.url')"
    exit 0
fi

# Get today's date
today=$(date +%Y-%m-%d)

# Count existing releases for today
release_count=$(gh pr list --state all --search "Release $today in:title" | wc -l)
release_number=$((release_count + 1))

# Create PR title
pr_title="Release $today ($release_number)"

# Create the PR
pr_url=$(gh pr create \
    --base production \
    --head master \
    --title "$pr_title" \
    --body "")

echo "Created release PR: $pr_url"
