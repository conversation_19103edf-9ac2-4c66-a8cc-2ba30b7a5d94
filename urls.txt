https://books.openedition.org/irdeditions/31117?lang=fr | L’odyssée des plantes sauvages et cultivées - IRD Éditions
https://www.lookandfin.com/fr/credit-entreprise/operation-de-marchand-de-biens/normandie-immobiliers-hoche | Financement Opération de marchand de biens NORMANDIE IMMOBILIERS - Hoche

https://owasp.org/www-chapter-belgium/#div-meetings | OWASP Belgium | OWASP Foundation
https://www.thoughtworks.com/radar/tools?blipid=202005061 | Tools | Thoughtworks
https://dvc.org/ | Data Version Control · DVC
https://learn.iterative.ai/ | Iterative Tools for Data Scientists & Analysts | Sign Up
https://github.com/elnickkx/data-science-guide | elnickkx/data-science-guide: Path to free self-taught education in Data Science!

https://www.bruzz.be/mobiliteit/ps-wil-niet-weten-van-metro-3-financiering-gatz-wij-zeggen-nee-tegen-rekeningrijden-2023 | PS wil niet weten van Metro 3-financiering Gatz: ‘Wij zeggen nee tegen rekeningrijden’ | BRUZZ
https://www.youtube.com/watch?v=jD-_PdtGl-8 | Panayotis Pascot : "Ce qui est dur, c'est de se poser la question de ce qu'est être un homme" - YouTube
https://fr.wikipedia.org/wiki/C%C3%A9cile_Jodogne | Cécile Jodogne — Wikipédia
https://www.notion.so/citizenlab/Plan-Consistent-hosting-0759ff5c554d4838aeb418dd27f54201 | (9+) Plan: Consistent hosting
https://www.notion.so/citizenlab/Platform-Squad-23Q4-focus-a0f8c9262dd54189b1b3224d7516b953 | (9+) Platform Squad 23Q4 focus
https://daardaar.be/ | daardaar.be/
https://stackoverflow.com/questions/136793/is-there-a-do-while-loop-in-ruby | Is there a "do ... while" loop in Ruby? - Stack Overflow
https://news.ycombinator.com/item?id=37230932 | What happened to Wirecutter? | Hacker News
https://www.test.de/abo/ | Abo | Stiftung Warentest
https://www.konbini.com/popculture/les-14-livres-quil-faut-avoir-lus-selon-panayotis/ | Les 14 livres qu’il faut avoir lus selon Panayotis
https://sosoir.lesoir.be/le-premier-resto-dops-sinstalle-bruxelles | Un nouveau concept de restaurant où déguster des "dops" s'installe à Bruxelles
https://www.phind.com/search?cache=zka0qbd8fs40jsjhg9phqs4e | what are "Beats"
https://github.com/docker/engine/blob/7485ef7e46e2766a0da06fc63001e84d8ea36514/vendor/github.com/docker/swarmkit/manager/orchestrator/taskreaper/task_reaper.go#L294 | engine/vendor/github.com/docker/swarmkit/manager/orchestrator/taskreaper/task_reaper.go at 7485ef7e46e2766a0da06fc63001e84d8ea36514 · docker/engine
https://github.com/moby/moby/issues/45443 | `replicated-job` doesn't respect `task-history-limit` · Issue #45443 · moby/moby
https://docs.docker.com/engine/reference/commandline/info/ | docker info | Docker Docs
https://fr.wikipedia.org/wiki/Pal%C3%A9olithique | Paléolithique — Wikipédia

https://www.serrureriemertens.be/nos-produits/ | Portes blindées, serrures, clés et coffres forts - Nos produits
https://www.metacritic.com/search/mario%20rabbids%20switch/ | Search Results - Metacritic
https://www.levif.be/economie/mon-argent/faut-il-basculer-vers-un-contrat-d-energie-fixe-les-conseils-dun-expert-pour-eviter-les-pieges/ | Faut-il basculer vers un contrat d'énergie fixe? Les 6 conseils d'un expert pour éviter les pièges
https://www.levif.be/economie/mon-argent/energie-pourquoi-cest-le-moment-ideal-pour-prendre-un-contrat-a-prix-fixe-sur-3-ans/ | Energie: pourquoi c’est le moment idéal pour prendre un contrat à prix fixe sur… 3 ans
https://www.engie.be/fr/espace-client/situation/simulation/?scenario=8&transactionId=db985238-2de2-4e39-a894-d59b68f71b36&sps=true#/separateEVMeter | Electricité et gaz naturel simulation | ENGIE
https://github.com/CitizenLabDotCo/cl2-admin/blob/master/app/models/tenant.rb#L9 | cl2-admin/app/models/tenant.rb at master · CitizenLabDotCo/cl2-admin
https://www.reddit.com/r/gaming/top/?t=month | r/gaming

http://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html | docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html
https://howhttps.works/episodes/ | Episodes - How HTTPS works
https://www.scrapingbee.com/blog/best-ruby-http-clients/#httparty | The Best Ruby HTTP clients for 2021 | ScrapingBee
https://www.lesoir.be/537841/article/2023-09-18/paul-de-grauwe-un-jackpot-de-146-milliards-pour-les-banques-ca-suffit | Paul De Grauwe : « Un jackpot de 146 milliards pour les banques ? Ça suffit ! » - Le Soir
https://www.youtube.com/watch?v=RdMt3DliOlo | Sepideh Farsi / Faut-il s’en prendre aux écologistes radicaux ? - 28 Minutes - ARTE - YouTube
https://www.youtube.com/watch?v=hkwqlCtwmig | LA SIRÈNE Bande Annonce VOST (2023, Animation) Sepideh Farsi, Mina Kavani, Hadmidreza Djavdan - YouTube
https://github.com/CitizenLabDotCo/citizenlab/pull/5934 | [CL-4070] New timezone helper for AppConfiguraiton by adessy · Pull Request #5934 · CitizenLabDotCo/citizenlab
https://github.com/github/scientist | github/scientist: :microscope: A Ruby library for carefully refactoring critical paths.
https://www.antidote.info/fr/blogue/enquetes/espece-genre-type-et-toutes-ces-sortes-de-noms | Espèce, genre, type et toutes ces sortes de noms

https://www.reddit.com/r/LifeProTips/comments/dph580/lpt_if_you_store_boxes_in_a_basement_put_them_on/ | LPT: if you store boxes in a basement, put them on pallets. This will save your belongings if you get a few inches of water down there. : LifeProTips
https://www.reddit.com/r/LifeProTips/comments/16g9lmy/lpt_what_skill_or_advice_would_you_give_a_28_year/ | LPT What skill or advice would you give a 28 year old to look into, so he doesnt regret life at 40-45? : LifeProTips
https://www.reddit.com/r/LifeProTips/comments/16e6lej/lpt_request_what_are_some_small_easy_ways_we_can/?sort=confidence | LPT Request: What are some small, easy ways we can make life better for the folks around us in our daily lives? : LifeProTips
https://www.reddit.com/r/AskCulinary/comments/22habj/how_to_chop_vegetables_efficiently/ | How to chop vegetables efficiently? : AskCulinary
https://news.ycombinator.com/item?id=37544628 | Linear Book Scanner – Open-source automatic book scanner (2014) | Hacker News
https://typst.app/ | Typst: Compose papers faster
https://www.reddit.com/r/learndutch/comments/5nazp4/which_dictionaries_do_you_use/ | Which dictionaries do you use? : learndutch
https://www.reddit.com/r/learndutch/comments/21t8cq/your_favorite_dutch_dictionaries/ | Your favorite Dutch dictionaries? : learndutch
https://www.vertalen.nu/vertaal?vertaal=Krijsen&van=nl&naar=fr | Krijsen - vertaling Nederlands-Frans

https://you.com/?ref=theresanaiforthat | The AI Search Engine You Control | AI Chat & Apps
https://github.com/jesseduffield/horcrux | jesseduffield/horcrux: Split your file into encrypted fragments so that you don't need to remember a passcode
https://www.youtube.com/watch?v=08KbmMxne_s | Boules d'énergie santé - YouTube
https://www.youtube.com/watch?v=mWYwmM23Sqs | Final Fantasy X/X-2 HD Remaster - Opening Cinematic - YouTube
https://www.gamespot.com/gallery/the-most-anticipated-playstation-games-of-2023-and-beyond/2900-4259/ | The Most-Anticipated PlayStation Games Of 2023 And Beyond - GameSpot
https://news.ycombinator.com/item?id=37485290&p=3 | iPhone 15 and iPhone 15 Plus | Hacker News

https://en.wikipedia.org/wiki/Transport_Layer_Security#Standards | Transport Layer Security - Wikipedia
https://crashtest-security.com/the-ongoing-changes-of-browser-support-for-tls-1-0-1-1/ | Browser Support Change for TLS 1.0 & 1.1
https://lab.cccb.org/en/arthur-c-clarke-any-sufficiently-advanced-technology-is-indistinguishable-from-magic/ | "Any sufficiently advanced technology is indistinguishable from magic." | CCCB LAB

https://explainshell.com/explain?cmd=find+.+-type+f+-mtime+-1+-exec+grep+-H+%22format_message%22+%7B%7D+%5C%3B | explainshell.com - find . -type f -mtime -1 -exec grep -H "format_message" {} \;
https://explainshell.com/explain?cmd=find+.+-type+f+-mtime+-2+-exec+grep+-H+%22format_message%28%5B%5E%29%5D%5D*values%22+%7B%7D+%5C%3B | explainshell.com - find . -type f -mtime -2 -exec grep -H "format_message([^)]]*values" {} \;
https://caoyuan.scripts.mit.edu/ir_spec.html | DIY Near-IR Spectrometer
https://shreevatsa.wordpress.com/2010/06/04/dont-mess-with-a-genius/ | Don’t mess with a genius | The Lumber Room
https://www.pf-bodson.net/P1220.aspx?IdPer=136237&LgTrv=6 | Nécrologie Funérailles Bodson - Décès de Monsieur André FANUEL (24-10-09)
https://www.languagereactor.com/text | Language Reactor - Text
https://en.wikipedia.org/wiki/SAML_2.0#References | SAML 2.0 - Wikipedia
https://www.racine.be/fr/%C3%A0-la-table-de-simona | À la table de Simona | Éditions Racine
https://www.reddit.com/r/brussels/comments/p981ih/best_lebanese_in_brussels/ | Best Lebanese in Brussels? : brussels
https://en.wikipedia.org/wiki/Radish#Winter_varieties | Radish - Wikipedia
https://www.startpage.com/do/dsearch?query=reddit+learn+words+to+my+cat&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche

https://exercism.org/blog/slimline-september | Exercism
https://www.jetbrains.com/help/ruby/searching-everywhere.html#url_mappings | Search Everywhere | RubyMine Documentation
https://www.reddit.com/r/IntelliJIDEA/comments/wsnzzm/what_are_the_most_interesting_and_useful_features/?sort=top | What are the most interesting and useful features for you? : IntelliJIDEA
https://www.youtube.com/watch?v=41CC-F6KRP8 | IntelliJ IDEA Tips and Tricks 2021. By Hadi Hariri - YouTube
https://www.youtube.com/@intellijidea/featured | IntelliJ IDEA by JetBrains - YouTube
https://www.jetbrains.com/help/ruby/2023.2/shared-indexes.html?utm_source=product&utm_medium=link&utm_campaign=RM&utm_content=2023.2 | Shared indexes | RubyMine Documentation
https://www.jetbrains.com/help/ruby/rubocop.html#rubocop_external_tool | RuboCop | RubyMine Documentation
https://docs.rubocop.org/rubocop/usage/server.html | Server Mode :: RuboCop Docs
https://www.kvs.be/fr/pQjDLC7/agenda/decouvrez-la-saison-23-24 | Seizoen 2023-2024 | KVS
https://git-scm.com/book/en/v2/Customizing-Git-Git-Hooks | Git - Git Hooks
https://superuser.com/questions/1223627/how-can-i-prevent-interactive-rebase-from-running-verification-hooks-on-commits | git - How can I prevent interactive-rebase from running verification hooks on commits? - Super User
https://github.com/reactjs/react-rails | reactjs/react-rails: Integrate React.js with Rails views and controllers, the asset pipeline, or webpacker.
https://www.postgresqltutorial.com/postgresql-tutorial/postgresql-grouping-sets/ | PostgreSQL GROUPING SETS
https://fr.wikipedia.org/wiki/Sarrasin_commun | Sarrasin commun — Wikipédia
https://www.passeportsante.net/fr/Nutrition/EncyclopedieAliments/Fiche.aspx?doc=sarrasin_nu | Sarrasin : propriétés, composition, recettes, analyse, conseils
https://www.1030.be/fr/agenda/world-clean-day | World Clean Up Day | Schaerbeek
https://plugins.jetbrains.com/plugin/9862-custom-postfix-templates | Custom Postfix Templates - IntelliJ IDEs Plugin | Marketplace
https://github.com/xylo/intellij-postfix-templates#custom-postfix-templates-for-intellij-idea | xylo/intellij-postfix-templates: Custom Postfix Templates for Intellij IDEA
https://icalendar.org/New-Properties-for-iCalendar-RFC-7986/5-3-uid-property.html | iCalendar.org - 5.3. UID Property
https://www.ikea.com/fr/fr/rooms/laundry/le-grand-relooking-du-garage-pub15f046f0 | Ranger son garage - IKEA
https://www.sunware.com/fr-be/q-line-bo%C3%AEte-de-rangement-%C3%A9tanche-130l-vert-noir-83330112 | Q-line boîte de rangement étanche 130L vert noir
https://test-prof.evilmartians.io/#/ | TestProf: Ruby tests profiling and optimization toolbox
https://evilmartians.com/chronicles/testprof-a-good-doctor-for-slow-ruby-tests | TestProf: a good doctor for slow Ruby tests—Martian Chronicles, Evil Martians’ team blog
https://github.com/test-prof/test-prof/blob/master/docs/profilers/stack_prof.md | test-prof/docs/profilers/stack_prof.md at master · test-prof/test-prof

https://www.verresetmiroirs.com/produits-verriers-miroiterie.php | Liste des produits verriers
https://www.onlinepolestudio.com/shop/premium-membership/ | Premium Membership

https://medi-market.be/c/a-ampoule-lift-fl-30ml/p/71593 | SVR A Ampoule Lift 30ml | Antirides - Anti-âge
https://www.reddit.com/r/30PlusSkinCare/comments/10vmcbo/whats_the_best_skin_care_tip_youve_gotten_over/ | What’s the best skin care tip you’ve gotten over the years? : r/30PlusSkinCare
https://www.isitcg.com/view/ZI8xbvMwDIWvQmj8oeBHhg7NRtNMTEQWVZJC6t6gSy9Q9O6F7AIdugmfiPfe95k-0iWlnN7TJT0w2DIQNtuK0P_xEIIwuZWN2GTmDBPX0CrBGQ66FfBgNPxFUjMQD7gVwEK6aMkw9dhMvbGtfYWGdhcZcC_9kwUkcWTS-el0fgYsd6ycQWove8GidbbuQCbeukPTxzyiXrHGghVufc2ARRkmtAlnri4OhfH6c_ovg-ssfR1Sb7qXhZK2hW0M9peOhesuHXhyCfU4vsY4IUCSOQ-Va19z-voOAAD__w | Is It CG? - Results
https://www.youtube.com/watch?v=B1NMTxQMP5A | LE MEILLEUR DE SVR ! - Cyrille Laurent - YouTube
https://www.passeportsante.net/beaute-et-soins-g158/Fiche.aspx?doc=retinol-solution-anti-age | Rétinol : zoom sur cette solution anti-âge ?
https://www.farmaline.be/apotheek/zoeken/retinol+booster+serum?pagesize=30&categoryOrStoreId=&search=search_term&onlyFavorites=&orderBy=popularity&direction=desc&onlyProductsInStockOpenState=1&brandHitsOpenState=1&brandsIsExpanded=1&brandIds%5B%5D=15353&priceRangeHitsOpenState=1&onlyPromotionsOpenState=1&onlyNewProductsOpenState=1&ratingHitsOpenState=1 | Online Apotheek voor België ▶ FARMALINE.be

https://github.com/jqlang/jq | jqlang/jq: Command-line JSON processor
https://medium.com/@agungsetiawan/this-is-why-you-might-not-want-to-use-rails-activerecord-callbacks-5331988a8b57 | This Is Why You Might Not Want to Use Rails ActiveRecord Callbacks | by Agung Setiawan | Medium
https://www.reddit.com/r/ruby/comments/cqci3i/callbacks_good_bad_or_ugly/ | Callbacks -- good, bad or ugly? : r/ruby
https://bignerdranch.com/blog/the-only-acceptable-use-for-callbacks-in-rails-ever/ | The only acceptable use for callbacks in Rails ever - Big Nerd Ranch
https://previewjs.com/docs | Getting started
https://stackoverflow.com/questions/830423/how-do-i-find-the-source-file-for-a-rake-task | ruby on rails - How do I find the source file for a rake task? - Stack Overflow
https://www.lesoir.be/535722/article/2023-09-07/michelle-perrot-les-femmes-nont-pas-toujours-ete-des-victimes | Michelle Perrot : « Les femmes n’ont pas toujours été des victimes » - Le Soir
https://github.com/rails/rails/blob/v4.2.4/railties/lib/rails/application.rb#L454 | rails/railties/lib/rails/application.rb at v4.2.4 · rails/rails

https://www.athenaeum.nl/boek?authortitle=magazine/butt-33--2001000061549# | Athenaeum Boekhandel | Butt #33, Magazine
https://stackoverflow.com/questions/23961636/what-are-the-options-to-capybaras-have-selector | ruby - What are the options to Capybara's have_selector? - Stack Overflow
https://guides.rubyonrails.org/active_record_callbacks.html#transaction-callbacks | Active Record Callbacks — Ruby on Rails Guides
https://rubydoc.info/github/jnicklas/capybara/master/Capybara/Node/Finders%3aall | Method: Capybara::Node::Finders#all — Documentation for jnicklas/capybara (master)
https://boringrails.com/articles/writing-better-action-mailers/ | Writing better Action Mailers: Revisiting a core Rails concept | Boring Rails: Skip the bullshit and ship fast
https://api.rubyonrails.org/classes/ActiveRecord/DelegatedType.html | ActiveRecord::DelegatedType
https://www.instagram.com/p/Cw2dfYfsrVq/ | Ze is dertig weken ver en mag vanwege medische risico’s niet meer werken. Maar wie @alinemuylaert (29) contacteert, merkt daar - op een… | Instagram
https://pitchfork.com/reviews/albums/22551-running-out-of-love/ | The Radio Dept.: Running Out of Love Album Review | Pitchfork
https://pitchfork.com/reviews/albums/sonic-youth-live-in-brooklyn-2011/ | Sonic Youth: Live in Brooklyn 2011 Album Review | Pitchfork
https://pitchfork.com/artists/28293-emeralds/ | Emeralds - Albums, Songs, and News | Pitchfork
https://www.sweat.com/blogs/fitness/forward-vs-reverse-lunges | Forward Lunge Vs Reverse Lunge: What’s The Difference? – SWEAT
https://blog.myfitnesspal.com/the-difference-between-forward-and-reverse-lunges/ | The Difference Between Forward and Reverse Lunges | Fitness | MyFitnessPal

https://fr.wikipedia.org/wiki/Tropisme | Tropisme — Wikipédia
https://fr.mycs.com/faq#question-categorie8 | FAQ | Questions générales MYCS | MYCS France

https://www.reddit.com/r/LifeProTips/ | Life Pro Tips
https://www.reddit.com/r/LifeProTips/comments/166cgdl/lpt_request_what_is_that_one_thing_that_you/ | LPT REQUEST: What is that one thing that you brought/bought for your work that makes all the difference in your work life in a positive manner? : LifeProTips
https://www.metacritic.com/search/all/fantasy%20xii/results | fantasy xii - Reviews, Articles, People, Trailers and more at Metacritic - Metacritic

https://climatechangetracker.org/ | Climate Change Tracker
https://www.reddit.com/r/ifyoulikeblank/comments/zx9spy/iil_this_photo_of_a_girl_pointing_a_gun_at_her/ | [IIL] this photo of a girl pointing a gun at her shadow [WEWIL] : ifyoulikeblank
https://www.reddit.com/r/ifyoulikeblank/ | If You Like _____...
https://www.reddit.com/r/Sizz/top/?t=all | Sizz Culture
https://www.home-barista.com/repairs/food-safe-adhesive-t81247.html | Food safe adhesive - Repairs, Restorations & Mods

https://www.home24.be/fr/armoire-placard/?width.max=77&depth.max=30&height.max=100 | Armoires & placards | Achetez en ligne aujourd'hui | home24
https://www.home24.fr/article/tapis-blaze-fire-tissu-melange-gris-rouge-195-x-290-cm | Tapis Blaze Fire | Je commande ! | home24
https://www.reddit.com/r/EuroSkincare/top/?t=all | All things relating to European skincare and beauty trends
https://www.reddit.com/r/bodyweightfitness/comments/7fur5f/fixing_rounded_shoulders_posture_with_1_exercise/ | Fixing rounded shoulders posture with 1 exercise : r/bodyweightfitness

https://guides.rubyonrails.org/action_mailer_basics.html | Action Mailer Basics — Ruby on Rails Guides
https://www.zesolution.com/fr/technologie/internet/comment-faire-les-guillemets-symboles-%C2%AB-et-%C2%BB-sur-son-clavier.html | Comment faire les guillemets (symboles « et ») sur son clavier - ZESOLUTION.COM
https://web.dev/learn/css/box-model/ | Box Model
https://www.lemonde.fr/politique/article/2011/09/16/francafrique-un-mot-valise-entre-mallettes-et-scandales_1571935_823448.html | "Françafrique", un mot-valise entre mallettes et scandales
https://leanpub.com/therails7way | Rails 7 Way by Obie Fernandez et al. [Leanpub PDF/iPad/Kindle]
https://www.konbini.com/popculture/wes-anderson-encore-le-kiki-de-jonathan-cohen-et-pierre-niney-chez-michel-gondry-les-12-films-a-ne-pas-rater-en-septembre/ | Wes Anderson (encore), le kiki de Jonathan Cohen et Pierre Niney chez Michel Gondry : les 12 films à ne pas rater en septembre
https://www.albumoftheyear.org/ratings/6-highest-rated/2023/1 | The Best Albums of 2023 - Album of The Year
https://pitchfork.com/best/ | Best New Music: Tracks, Albums & Reissues | Pitchfork
https://pitchfork.com/reviews/albums/billie-eilish-when-we-all-fall-asleep-where-do-we-go/ | Billie Eilish: When We All Fall Asleep, Where Do We Go? Album Review | Pitchfork
https://www.metacritic.com/music/the-gods-we-can-touch/aurora/critic-reviews | Critic Reviews for The Gods We Can Touch - Metacritic
https://fr.wikipedia.org/wiki/Cillian_Murphy#Mode_de_vie | Cillian Murphy — Wikipédia
https://albertoalmagro.com/how-to-use-activerecord-explain-in-ruby-on-rails/ | 💎 How to use ActiveRecord Explain in Ruby on Rails 🚄 - Alberto Almagro
https://www.reddit.com/r/ruby/top/?t=year | reddit for rubyists
https://organicmaps.app/ | Organic Maps: Offline Hike, Bike, Trails and Navigation
https://github.com/bvaughn/react-virtualized | bvaughn/react-virtualized: React components for efficiently rendering large lists and tabular data
http://bvaughn.github.io/react-virtualized/#/components/List | react-virtualized
https://tanstack.com/ | TanStack | High Quality Open-Source Software for Web Developers
https://www.notion.so/citizenlab/Profiling-eb056a1e4b1541b2b261f2f00f455a25 | (9+) Profiling
https://web.archive.org/web/20200805050052/https://www.tsugi.fr/du-duo-paradis-au-mini-album-solo-linterview-verite-de-pierre-rousseau/ | Du duo Paradis au mini-album solo : l’interview vérité de Pierre Rousseau - TSUGI
https://www.techdirt.com/2023/09/01/tesla-rivian-put-on-fake-show-of-support-for-right-to-repair/ | Tesla, Rivian Put On Fake Show Of Support For ‘Right To Repair’ | Techdirt
https://dictionary.cambridge.org/dictionary/english/provision | PROVISION | English meaning - Cambridge Dictionary

https://www.reddit.com/r/LifeProTips/comments/15wlub6/lpt_request_whats_your_best_advice_from_your/ | LPT Request: What’s your best advice from your profession? : LifeProTips
https://www.reddit.com/r/css/comments/kid0jw/when_you_need_space_do_you_add_margin_or_padding/ | When you need space, do you add margin or padding? : css
https://www.youtube.com/watch?v=s7t5jnpkCkI | Nintendo Direct 6.21.2023 - Nintendo Switch - YouTube
https://www.metacritic.com/search/game/advance%20wars/results?sort=recent | advance wars - Reviews, Articles, People, Trailers and more at Metacritic - Metacritic

https://www.lesoir.be/533702/article/2023-08-28/diane-kruger-au-soir-toute-ma-vie-jai-du-me-battre-contre-les-cliches-de-la | Diane Kruger au « Soir » : « Toute ma vie, j’ai dû me battre contre les clichés de la bonne Allemande » - Le Soir
https://docs.google.com/spreadsheets/d/1AW7nfhavBY5un77SCcR6zg-DTgd8xgd8yjimzoBMiyI/edit#gid=0 | Untitled spreadsheet - Google Sheets
https://trello.com/ | Manage Your Team’s Projects From Anywhere | Trello
https://www.debtagency.be/fr/produit/obligations-lineaires-olo-olo-verte/productoloinfo | OLO (Obligations linéaires) | Agence Fédérale de la Dette
https://en.wikipedia.org/wiki/Gamebook | Gamebook - Wikipedia
https://www.reddit.com/r/gamebooks/comments/arzvml/gamebook_must_reads/ | Gamebook must reads? : gamebooks
https://www.reddit.com/r/gamebooks/comments/vmmpbt/any_modern_game_books_for_adults/?sort=top | Any modern game books for adults? : gamebooks
https://thousandyearoldvampire.com/products/thousand-year-old-vampire-pdf-only | Thousand Year Old Vampire (PDF only) – Tim Hutchings Makes Games

https://www.bruzz.be/videoreeks/la-carte/video-paul-steinbruck-pool-cool-het-zwembad-flow-eigenlijk-een-grap | Paul Steinbrück (Pool is Cool): 'Het zwembad Flow is eigenlijk een grap' | BRUZZ

https://www.abconcerts.be/fr/actualites/decouvrez-bar-chaud-lendroit-incontournable-pour-participer-a-de-chouettes-activites-gratuites/a1e5J000006QdXdQAK | Découvrez Bar Chaud : l’endroit incontournable pour participer à de chouettes activités gratuites - AB
https://www.brusselskitchen.com/la-flaque/bruxelles/restaurant | La Flaque | Brussels' Kitchen
https://www.startpage.com/do/dsearch?query=seoul+windows+airflight+simulator&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://www.superprof.be/s/programmation,Bruxelles--Belgique,50.8476424,4.3571696,1.html | Superprof des cours particuliers pour élèves exigeants.
https://www.superprof.be/diplome-ingenieur-civil-electromecanicien-ecole-polytechnique-bruxelles-donne-cours-programmation-python.html | Loïc - Bruxelles,Bruxelles-Capitale : Diplômé en Ingénieur civil éléctromécanicien à l'Ecole polytechnique de Bruxelles. Je donne cours de programmation en python et Matlab. Méthodologie concise et rigoureuse.
https://www.reddit.com/r/statistics/top/?t=year | statistics
https://news.ycombinator.com/item?id=36877112 | IntelliJ IDEA 2023.2 | Hacker News
https://database.guide/how-to-test-for-overlapping-dates-in-postgresql/ | How to Test for Overlapping Dates in PostgreSQL
https://www.startpage.com/do/dsearch?query=+ActiveSupport%3A%3ATimeWithZone&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://api.rubyonrails.org/classes/ActiveSupport/TimeWithZone.html | ActiveSupport::TimeWithZone
https://www.lesoir.be/532295/article/2023-08-21/canicule-le-blanc-la-couleur-miracle-pour-lutter-contre-la-chaleur | Canicule : le « blanc », la couleur miracle pour lutter contre la chaleur ? - Le Soir
https://www.lesoir.be/31880/sections/immo | Immo - Le Soir
https://gist.github.com/mudge/acde31a5319726b9fdba419ffe7f5bcb | Instruct Rails to respect the Accept header even if it contains a wildcard

https://datatracker.ietf.org/doc/html/rfc9073 | RFC 9073 - Event Publishing Extensions to iCalendar
https://www.notion.so/citizenlab/product-issues-related-to-email-delivery-7a4421dfd5d04711b778ef2d57e71a65?p=640c6c4407854042a1d6653ff6b7507e&pm=s | (9+) #product-issues related to email delivery
https://icalendar.org/RFC-Specifications/iCalendar-Venue-Draft/ | iCalendar.org - iCalendar Venue (Draft)
https://www.isro.gov.in/LIVE_telecast_of_Soft_landing.html | LIVE telecast of Chandrayaan-3 Soft-landing
https://transformer-circuits.pub/ | Transformer Circuits Thread
https://letterboxd.com/film/the-prince-2019/ | ‎The Prince (2019) directed by Sebastián Muñoz • Reviews, film + cast • Letterboxd
https://www.startpage.com/do/dsearch?query=heredoc+ruby+with+interpolation&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://www.needhelp.com/fr-be#intro | NeedHelp - Plateforme de service : Montage et Travaux
https://gist.github.com/higgis/3793544 | Rails: Default format using header-based content negotiation
https://pglocks.org/ | Postgres Lock Conflicts
https://fauna.com/blog/beyond-sql | Beyond SQL: A relational database for modern applications
https://www.math.utah.edu/~alfeld/math/polya.html | G. Polya, How to Solve It.
https://wso2.com/whitepapers/transactions-in-a-microservice-world/ | Transactions in a Microservice World
https://krebsonsecurity.com/2023/08/tourists-give-themselves-away-by-looking-up-so-do-most-network-intruders/ | Tourists Give Themselves Away by Looking Up. So Do Most Network Intruders. – Krebs on Security
https://www.bitecode.dev/p/asyncio-twisted-tornado-gevent-walk | Asyncio, twisted, tornado, gevent walk into a bar...
https://www.fluentgreeknt.com/ | Become Fluent in New Testament Greek | FluentGreek
https://go.dev/blog/slog | Structured Logging with slog - The Go Programming Language
https://www.vox.com/future-perfect/23836358/meditation-mindfulness-enlightenment-science-contemplative-buddhism-spirituality | Want to start meditating? A new generation of scientists want to change how it’s done - Vox
https://github.com/chrieke/prettymapp | chrieke/prettymapp: 🖼️ Create beautiful maps from OpenStreetMap data in a streamlit webapp
https://www.unravelled.dev/how-architecture-diagrams-enable-better-conversations/ | How architecture diagrams enable better conversations - Unravelled Development
https://techcommunity.microsoft.com/t5/excel-blog/announcing-python-in-excel-combining-the-power-of-python-and-the/ba-p/3893439 | Announcing Python in Excel
https://www.theverge.com/2023/8/22/23841167/microsoft-excel-python-integration-support | Microsoft is bringing Python to Excel - The Verge
https://blog.ioces.com/matt/posts/i-walked-across-luxembourg/ | I Walked Across Luxembourg - The Blog of Matt
https://www-test-de.translate.goog/shop/haushalt-garten/haushalt-nebenbei-sp0415/?_x_tr_sl=auto&_x_tr_tl=en&_x_tr_hl=nl | Household by the way: 500 clean expert tricks | Stiftung Warentest
https://www.youtube.com/watch?v=BYOiYvdaCis | #6: How to use a Breadboard. How do breadboards work? - YouTube
https://www.youtube.com/watch?v=DGdFjCfu_PQ | How To Look Better In Every Photo | Photogenic Over 40 - YouTube
https://www.triodos.be/fr/gestion-de-patrimoine/triodos-impact-portfolio#demarrer | Investir | Triodos Impact Portfolio | Banque Triodos
https://www.lesoir.be/532914/article/2023-08-24/orages-comment-bruxelles-reduit-au-maximum-les-risques-dinondations | Orages : comment Bruxelles réduit au maximum les risques d’inondations - Le Soir
https://github.com/CitizenLabDotCo/citizenlab/pull/5579/files | Add script add_campaign by alexander-cit · Pull Request #5579 · CitizenLabDotCo/citizenlab
https://www.zooplus.be/customerpicturedisplay/shop/chats/jouets_chat/jouets_valeriane_chat/734203 | Jouet Aumüller Alpaga Angie pour chat
https://roadmap.sh/postgresql-dba | DBA Roadmap: Learn to become a database administrator with PostgreSQL
https://roadmap.sh/prompt-engineering | Prompt Engineering Roadmap - roadmap.sh
https://www.brico.be/fr/storedetail/3309/schaerbeek-meiser-schaarbeek-meiser | Brico magasin bricolage Schaerbeek Meiser | Schaarbeek Meiser, Chaussée de Louvain / Leuvensesteenweg 467

https://en.wikipedia.org/wiki/OpenTTD | OpenTTD - Wikipedia
https://guides.rubyonrails.org/routing.html#defining-defaults | Rails Routing from the Outside In — Ruby on Rails Guides
https://www.startpage.com/do/dsearch?query=lettres+de+cr%C3%A9ances&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://en.wikipedia.org/wiki/Letter_of_credence | Letter of credence - Wikipedia
https://www.thefreedictionary.com/credence | Credence - definition of credence by The Free Dictionary
https://www.reddit.com/r/belgium/comments/pc99a3/what_is_belgium_a_world_leader_in_but_isnt/ | What is Belgium a world leader in, but isn’t renowned for? : belgium
https://www.lesoir.be/531833/article/2023-08-18/une-nouvelle-decouverte-concernant-letablissement-du-moustique-tigre-en-belgique | Une nouvelle découverte concernant l’établissement du moustique tigre en Belgique - Le Soir
https://www.lesoir.be/352498/sections/mad | MAD - Le Soir
https://www.lesoir.be/31895/sections/medias | Médias - Le Soir
https://www.thefreedictionary.com/reckon | Reckon - definition of reckon by The Free Dictionary
https://suitsupply.com/en-be/men/knitwear/dark-brown-crochet%C2%A0polo-shirt/SW1637.html | Dark Brown Crochet Polo Shirt in Pure Cotton | SUITSUPPLY Belgium
https://www.theverge.com/23797349/how-do-you-live-review-studio-ghibli-hayao-miyazaki | How Do You Live review: a beautiful relic and the end of an era - The Verge
https://www.lesoir.be/531968/article/2023-08-19/faut-il-supprimer-la-rentree-litteraire | Faut-il supprimer la rentrée littéraire ? - Le Soir
https://www.demorgen.be/meningen/van-quickenborne-stelt-voor-om-vrouwen-te-straffen-voor-scheeftrekkingen-in-de-samenleving-en-de-ongelijkheid-te-vergroten~b3e89bc8/ | Van Quickenborne stelt voor om vrouwen te straffen voor scheeftrekkingen in de samenleving, én de ongelijkheid te vergroten
https://www.linkedin.com/jobs/search/?currentJobId=3683632899&geoId=103603112&keywords=nato&location=Brussels%20Region%2C%20Belgium&refresh=true | (23) nato Jobs in Brussels Region, Belgium | LinkedIn
https://www.reddit.com/r/popheads/comments/wqr7y2/video_game_instrumentals_rate/ | Video Game Instrumentals Rate : popheads
https://letterboxd.com/jack/list/official-top-250-films-with-the-most-fans/ | ‎Official Top 250 Films with the Most Fans, a list of films by Jack Moulton • Letterboxd
https://letterboxd.com/jack/list/top-250-non-english-language-films-with-the/share/8ZyPMDfP2aSZtAOH/ | ‎Top 250 Non-English Language Films with the Most Fans, a list of films by Jack Moulton • Letterboxd

https://www.phind.com/agent?cache=cll3nzg0s000jkz08cxb7xunu | how to detect prompt injection
https://www.zooplus.be/shop/chats | Accessoires et nourriture pour chat : pas cher | zooplus.be
https://www.zooplus.be/customerpicturedisplay/shop/chats/jouets_chat/jouets_valeriane_chat/734203 | Jouet Aumüller Alpaga Angie pour chat
https://www.fourbardesign.com/2020/10/diy-espresso.html | Four Bar Design: DIY Espresso
https://news.ycombinator.com/item?id=37155329 | Thirteen years later, why are most administrators still from 2005? | Hacker News
https://mlir.llvm.org/ | MLIR
https://news.ycombinator.com/item?id=12896981 | Unofficial Stories: Conversation with Svetlana Alexievich | Hacker News
https://stackoverflow.com/questions/30117878/make-rails-generate-single-quote-strings-rather-than-double-quote-strings-to-sat | Make Rails generate single quote strings rather than double quote strings to satisfy RuboCop - Stack Overflow
https://foundation.mozilla.org/en/ | Mozilla Foundation - Homepage
https://www.thefreedictionary.com/playbook | Playbook - definition of playbook by The Free Dictionary
https://www.oryxspioenkop.com/search/label/Ukraine?&max-results=7 | Oryx: Ukraine
https://news.ycombinator.com/item?id=37156372 | Ask HN: Any interesting books you have read lately? | Hacker News
https://guzey.com/ | Alex Guzey
https://statmodeling.stat.columbia.edu/2019/11/24/why-we-sleep-update-some-thoughts-while-we-wait-for-matthew-walker-to-respond-to-alexey-guzeys-criticisms/ | “Why We Sleep” update: some thoughts while we wait for Matthew Walker to respond to Alexey Guzey’s criticisms | Statistical Modeling, Causal Inference, and Social Science
https://statmodeling.stat.columbia.edu/ | Statistical Modeling, Causal Inference, and Social Science
https://www.srlt.ca/virtual-blender-camera | Virtual Blender Camera — /////
https://www.postgresql.org/docs/current/datatype-datetime.html | PostgreSQL: Documentation: 15: 8.5. Date/Time Types

https://www.youtube.com/watch?v=QxdKlpA4lIw | Tuto comment peindre un mur blanc comme un pro - YouTube
https://www.trustup.be/fr/entreprises/vitrifier-parquet | TrustUp.be — La plate-forme pour vos projets de construction ou rénovation
https://ringtwice.be/fr/experiment/task_creation?p_category=4&step=2 | Ring Twice | Demander un service
https://docs.google.com/document/d/1zcqyfubUoWikpzf1PzLcTEFI9MbAhZ9S5FbMqkkcQMc/edit?skip_itp2_check=true#heading=h.nkhc14qex2le | Back-End: Code Discussion Meeting Agenda - Google Docs
https://www.arte.tv/en/videos/101944-004-A/making-history/ | Making History - The Hand of Justice - Watch the full documentary | ARTE in English
https://news.ycombinator.com/item?id=37052586 | Downfall Attacks | Hacker News
https://research.aimultiple.com/large-language-model-evaluation/ | Large Language Model Evaluation in 2023: 5 Methods
https://blog.gopenai.com/how-to-speed-up-llms-and-use-100k-context-window-all-tricks-in-one-place-ffd40577b4c | The Secret Sauce behind 100K context window in LLMs: all tricks in one place | by Galina Alperovich | GoPenAI
https://alphasec.io/summarize-text-with-langchain-and-openai/ | Summarize Text with LangChain and OpenAI
https://github.com/tesseract-ocr/tesseract/blob/main/doc/tesseract.1.asc#languages | tesseract/doc/tesseract.1.asc at main · tesseract-ocr/tesseract
https://stackoverflow.com/questions/46126904/stream-a-large-file-line-by-line-from-s3 | ruby - Stream a large file line by line from S3 - Stack Overflow

https://www.reddit.com/r/DIY/comments/lpyt2u/remove_olive_oil_stains_from_wooden_floor/?rdt=58275 | Remove olive oil stains from wooden floor : r/DIY
https://www.reddit.com/r/HomeImprovement/comments/j6set3/large_grease_stain_on_my_parquet_floors_any_tips/ | Large grease stain on my parquet floors. Any tips on how to remove this? : r/HomeImprovement
https://chat.openai.com/ | Absorbents & spill cleanup
https://www.gamma.be/fr/assortiment/zoeken?text=absorbeur&f_type_artikel=Produit+complet&scrolltofacet=type_artikel | Search Page
https://www.amazon.fr/ABSORBENT-sup%C3%A9rieur-Sepiolite-absorbant-int%C3%A9rieur/dp/B0BHSWDY5N/ref=sr_1_1?__mk_fr_FR=%C3%85M%C3%85%C5%BD%C3%95%C3%91&crid=3L10CKPVBE7L2&keywords=spill%2Babsorber&qid=1692171865&sprefix=spill%2Babsorber%2Caps%2C77&sr=8-1&th=1 | TECNO EXTRA ABSORBENT ECOLOGIC de TECNO PRODIST (6 Litres) Spill Control. Bien supérieur à Sepiolite, Ultra huile, eau, absorbant chimique, ignifuge, intérieur ou extérieur : Amazon.fr: Auto et Moto
https://www.brico.be/fr/search?text=absorbeur+huile+bois | Résultats de recherche pour 'absorbeur huile bois'
https://maison-carpentier.com/tache-incrustee-sur-parquet/ | Enlever une tache incrustée sur tout type de parquet bois

https://medium.com/@Fcmam5/trying-to-become-a-better-developer-by-learning-more-about-aviation-5241e7092f7e | Trying to become a better developer by learning more about aviation | by Fortas Abdeldjalil | Jul, 2023 | Medium
https://twitter.com/Andercot/status/1686286684424691712 | Andrew Cote on Twitter: "First claimed successful replication of LK-99 Accomplished by a team at the Huazhong University of Science and Technology and posted 30 minutes ago. Why this is evidence: The LK-99 flake slightly levitates for both orientations of the magnetic field, meaning it is not simply a… https://t.co/bh0x9oqaz2" / X
https://github.com/zalando/restful-api-guidelines | zalando/restful-api-guidelines: A model set of guidelines for RESTful APIs and Events, created by Zalando
https://opensource.zalando.com/restful-api-guidelines/ | Zalando RESTful API and Event Guidelines
https://fr.wikipedia.org/wiki/Supraconductivit%C3%A9 | Supraconductivité — Wikipédia
https://proxyman.io/ | Proxyman · Native, Modern Web Debugging Proxy · Inspect network traffic from Mac, iOS, Android devices with ease
https://setapp.com/how-to/password-protect-zip | How to password protect a zip file
https://www.senscritique.com/top/resultats/les_meilleurs_albums_de_2023/3388335 | Top des meilleurs albums de 2023
https://mastodon.social/@nekohayo/110775656176571435 | Jeff Fortin T.: "A list of recent hostile moves…" - Mastodon
https://refactoring.guru/design-patterns/visitor | Visitor
https://stackoverflow.com/questions/2973436/regex-lookahead-lookbehind-and-atomic-groups | Regex lookahead, lookbehind and atomic groups - Stack Overflow
https://scoutapm.com/blog/activerecord-includes-vs-joins-vs-preload-vs-eager_load-when-and-where | Making sense of ActiveRecord joins, includes, preload, and eager_load | Scout APM Blog
https://en.wikipedia.org/wiki/LLaMA | LLaMA - Wikipedia
https://poe.com/Llama-2-70b | Llama-2-70b - Poe
https://context.reverso.net/traduction/francais-anglais/de+mani%C3%A8re+peu+orthodoxe | de manière peu orthodoxe - Traduction en anglais - exemples français | Reverso Context
https://www.vector-logic.com/blog/posts/using-rspec-block-syntax-for-message-expectations | Using RSpec Block Syntax for Message Expectations
https://www.startpage.com/do/dsearch?query=on+pourra+grapiller&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://gist.github.com/mikepea/863f63d6e37281e329f8 | Pull Request Etiquette
https://www.startpage.com/do/dsearch?query=mime+type+for+ics&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://stackoverflow.com/questions/2164176/ics-file-mime-type | php - ics file mime type - Stack Overflow
https://kottke.org/ | (8) kottke.org - home of fine hypertext products
https://www.reddit.com/r/pcgaming/comments/y6dl20/are_there_any_games_that_teach_you_real_life/ | Are there any games that teach you real life skills or educate you in a fun way? : pcgaming
https://store.steampowered.com/app/227300/Euro_Truck_Simulator_2/ | Save 75% on Euro Truck Simulator 2 on Steam
https://en.wikipedia.org/wiki/Games_for_Change | Games for Change - Wikipedia
https://www.gamesforchange.org/games/ | Game Directory - Games for Change
https://www.startpage.com/do/dsearch?query=switch+games+in+dutch&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://www.backloggd.com/games/assassin-s-creed-ii/ | Assassin's Creed II (2009)

https://www.reddit.com/r/rollercoasters/comments/12wnlov/other_what_would_be_the_best_theme_park_to_do_in/?rdt=57524 | [Other] What would be the best theme park to do in Europe? : r/rollercoasters
https://www.europapark.de/fr/parc-de-loisirs/infos/planifiez-votre-sejour/acces | Toutes les informations concernant votre voyage à destination d’Europa-Park en Allemagne
https://www.reddit.com/r/brussels/comments/krrvgo/definitive_minieurope_to_remain_in_brussels/ | Definitive: Mini-Europe to remain in Brussels : r/brussels
https://www.minieurope.com/le-parc/#un-monde-miniature-unique | Le parc - Mini-Europe

https://www.reddit.com/r/gamingsuggestions/comments/15fsipb/i_need_a_game_that_will_ruin_my_life/ | i need a game that will ruin my life : gamingsuggestions
https://www.reddit.com/r/gamingsuggestions/comments/14sjua2/what_games_would_you_consider_a_masterpiece_of/ | What game(s) would you consider a masterpiece of the video game medium? : gamingsuggestions
https://www.reddit.com/r/gamingsuggestions/comments/1533t2z/what_videogames_that_you_thought_were_going_to_be/ | What videogames that you thought were going to be "meh" ended up being awesome? : gamingsuggestions
https://www.metacritic.com/game/pc/outer-wilds | Outer Wilds for PC Reviews - Metacritic
https://www.metacritic.com/game/playstation-4/control | Control for PlayStation 4 Reviews - Metacritic
https://www.reddit.com/r/ffx/comments/14h70ux/best_version_to_play/ | Best version to play : ffx
https://www.metacritic.com/game/playstation-4/final-fantasy-x-x-2-hd-remaster | Final Fantasy X / X-2 HD Remaster for PlayStation 4 Reviews - Metacritic
https://github.com/topher-au/Farplane | GitHub - topher-au/Farplane: A memory editing suite for Final Fantasy X/X-2 HD Remaster

https://www.startpage.com/do/dsearch?query=horse+stance+ass&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://www.youtube.com/watch?v=PAFsSwVx864 | How to HORSE STANCE 马步 | Movement Tutorial - YouTube
https://notes.alinpanaitiu.com/Keyboard%20tricks%20from%20a%20macOS%20app%20dev | Keyboard tricks from a macOS app dev — Alin Panaitiu

https://docs.google.com/spreadsheets/d/1b_ZL9vIV7IKX8TkmthQbOhFhXYRya_KWbthfXkYvJz0/edit#gid=0 | CL Public API progress - Google Sheets
https://www.startpage.com/do/dsearch?query=nato+cybersecurity+linkedin&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://roadmap.sh/cyber-security | Cyber Security Roadmap: Learn to become a Cyber Security Expert
https://shape.nato.int/ | SHAPE | Supreme Headquarters Allied Powers Europe
https://superkey.app/ | Superkey
https://manual.raycast.com/quicklinks | Quicklinks
https://www.raycast.com/pro | Raycast Pro
https://gitlab.com/gitlab-org/gitlab/-/blob/master/ee/spec/policies/event_policy_spec.rb | ee/spec/policies/event_policy_spec.rb · master · GitLab.org / GitLab · GitLab
https://sentry.hq.citizenlab.co/organizations/citizenlab/issues/75396/?project=4&referrer=slack | Sequel::DatabaseError: PG::UndefinedColumn: ERROR: column "location_point" of relation "cl2_raw_events" does not exist LINE 1: ..."start_at", "end_at", "created_at", "updated_at", "location_... ^ - citizenlab - cl2back-to-metabase
https://www.reddit.com/r/crafts/comments/hfp553/tshirt_quilt_i_made_recently_as_a_memory_quilt/ | T-shirt quilt I made recently as a memory quilt for a young lady who lost her father. I love the shadow box effect. : r/crafts
https://www.reddit.com/r/crossfit/comments/3a6c7m/hook_gripwhat_tape_are_you_using/ | Hook grip...What tape are you using? : crossfit
https://www.reddit.com/r/crossfit/comments/zvq0y4/uk_tape_recommendations/ | UK 🇬🇧 Tape Recommendations? : r/crossfit
https://www.reddit.com/r/crossfit/comments/hsnifu/thumb_tapegrip_tape_recs/ | Thumb tape/grip tape recs? : r/crossfit
https://www.kttape.com/products/kt-tape-pro | KT Tape Pro® - Kinesiology Tape for Athletes
https://www.reddit.com/r/crossfit/comments/7fbt3f/your_favorite_athletic_tape/ | Your favorite athletic tape? : crossfit
https://en.wikipedia.org/wiki/Illusory_superiority | Illusory superiority - Wikipedia
https://github.com/jesseduffield/lazydocker | jesseduffield/lazydocker: The lazier way to manage everything docker
https://idioms.thefreedictionary.com/call+on | Call on - Idioms by The Free Dictionary
https://www.nautiljon.com/breves/protegez-vous-pour-l-annee-a-venir-avec-ces-grigris-ghibli-inspires-du-voyage-de-chihiro,6623.html | Protégez-vous pour l'année à venir avec ces grigris Ghibli inspirés du Voyage de Chihiro
https://github.com/dabeaz-course/python-mastery | GitHub - dabeaz-course/python-mastery: Advanced Python Mastery (course by @dabeaz)
https://www.metacritic.com/movie/mission-impossible-dead-reckoning-part-one | Mission: Impossible – Dead Reckoning Part One Reviews - Metacritic
https://www.piggyback.com/fr/guide/the-legend-of-zelda-tears-of-the-kingdom/# | The Legend of Zelda™: Tears of the Kingdom - Le Guide Officiel Complet - Piggyback.com
https://www.thefreedictionary.com/woo | Woo - definition of woo by The Free Dictionary
https://www.amazon.de/s?k=bowflex+552i&__mk_de_DE=%C3%85M%C3%85%C5%BD%C3%95%C3%91&crid=1P2JH356SB7CV&sprefix=bowflex+552i%2Caps%2C72&ref=nb_sb_noss_1 | Amazon.de : bowflex 552i
https://www.looria.com/review/bowflex-selecttech-552 | Bowflex SelectTech 552 Reviews | Looria
https://fly.io/ruby-dispatch/welcome-to-rails-cheat-sheet/ | Welcome to Rails Cheat Sheet · Fly
https://www.google.be/maps/place/Agay,+Frankrijk/@42.4971767,4.5639431,8z/data=!4m6!3m5!1s0x12ce90a017524a2b:0x260819a6fc4e7b11!8m2!3d43.433019!4d6.857548!16s%2Fg%2F121vf30k?entry=ttu | Agay - Google Maps
https://www.lesoir.be/526876/article/2023-07-21/jeux-olympiques-2024-encore-un-et-tant-de-questions | Jeux olympiques 2024 : encore un an et tant de questions - Le Soir
https://www.lesoir.be/527124/article/2023-07-23/elections-en-espagne-la-gauche-resiste-lassaut-de-la-droite | Elections en Espagne : la gauche résiste à l’assaut de la droite - Le Soir
https://www.lesoir.be/527936/article/2023-07-27/robert-f-kennedy-jr-candidat-democrate-la-maison-blanche-et-complotiste | Robert F. Kennedy Jr, candidat démocrate à la Maison-Blanche… et complotiste - Le Soir
https://www.lesoir.be/527927/article/2023-07-27/little-dragon-bravery-battle-cinq-disques-ne-pas-manquer-cette-semaine | Little Dragon, Bravery in Battle... cinq disques à ne pas manquer cette semaine - Le Soir
https://pitchfork.com/reviews/albums/dominic-fike-what-could-possibly-go-wrong/ | Dominic Fike: What Could Possibly Go Wrong Album Review | Pitchfork
http://libgen.rs/search.php?req=leangains&lg_topic=libgen&open=0&view=simple&res=25&phrase=1&column=def | Library Genesis
https://en.wikipedia.org/wiki/Camp_(style) | Camp (style) - Wikipedia
https://fr.wikipedia.org/wiki/Sofiane_Pamart | Sofiane Pamart — Wikipédia
https://www.gelinvins.be/spiritueux/gin-austin-40-fruit-de-la-passion-70-cl | Gin Austin 40° - Fruit de la passion - 70 cl — P&A Gelin vins
https://wiki.openstreetmap.org/wiki/Overpass_API/Overpass_QL#Language_overview | Overpass API/Overpass QL - OpenStreetMap Wiki
https://bulles.terre.be/terre_sf/#findSites:1 | Bulles Terre
https://webcache.googleusercontent.com/search?q=cache:https://nicksaraev.medium.com/top-10-best-anki-decks-of-2019-9a5abab5111a | Top 10 Best Anki Decks of 2021. Want to up your memorization game? Get… | by Nick Saraev | Medium
https://github.com/anki-geo/ultimate-geography | anki-geo/ultimate-geography: Geography flashcard deck for Anki

https://www.mensjournal.com/health-fitness/10-ways-to-build-strength-without-the-size | Get Stronger Without the Size - Men's Journal
https://www.amazon.com.be/-/en/Michael-Matthews-PH-D/dp/**********?language=en_GB | Beyond Bigger Leaner Stronger: The Advanced Guide to Building Muscle, Staying Lean, and Getting Strong: 3 : Matthews PH.D., Michael: Amazon.com.be: Books
https://www.startpage.com/do/dsearch?query=leaner+muscle+fewer+repetitions&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://www.quora.com/How-do-I-build-lean-muscle-with-heavy-weights-and-small-reps-or-with-fewer-weights-and-more-reps | How to build lean muscle, with heavy weights and small reps or with fewer weights and more reps - Quora

https://www.reddit.com/r/NoPoo/wiki/index/quickstart/#wiki_natural_haircare_quick_start_guide | index/quickstart - NoPoo
https://www.isitcg.com/ | Is it CG?
https://justprimalthings.com/2014/10/20/the-ultimate-water-only-hair-washing-routine-no-shampoo/ | The Ultimate Water-Only Hair Washing Routine - [No Shampoo!] - Just Primal Things
https://www.thefreedictionary.com/shed | Shed - definition of shed by The Free Dictionary
https://www.comptoirdelhomme.com/brosses-et-peignes#product-3212129 | Brosses et peignes Homme sur Comptoir de l'Homme, Soin & Parfum Homme
https://www.reddit.com/r/curlyhair/comments/vv4n73/leave_in_conditioner_europe/ | Leave in conditioner europe : curlyhair
https://overpass-turbo.eu/ | overpass turbo
https://fr.zalando.be/hummel-busan-baskets-basses-white-marshmallow-hu312o00j-a11.html?selectedsizes=40,40.5 | Hummel BUSAN - Baskets basses - white marshmallow/blanc - ZALANDO.BE

https://tatsu-lab.github.io/alpaca_eval/ | Alpaca Eval Leaderboard
https://ai.meta.com/llama/ | Llama 2 - Meta AI
https://code.tutsplus.com/testing-your-ruby-code-with-guard-rspec-pry--cms-19974t | Testing Your Ruby Code With Guard, RSpec & Pry | Envato Tuts+

https://www.reddit.com/r/LifeProTips/top/?t=month | LPT Request: What items can I carry to make life a little sillier? : LifeProTips

https://onlyfans.com/grxvxty | gravity OnlyFans
https://www.reddit.com/r/Breath_of_the_Wild/comments/15ail8c/can_anyone_explain_wtf_just_happened/ | Reddit - Dive into anything
https://github.com/hacker-DOM/github-by-stars | GitHub - hacker-DOM/github-by-stars: Get most starred repo's that have a specific dependency!

https://www.startpage.com/sp/search | Startpage Résultats de la Recherche
https://www.reddit.com/r/VeganFoodPorn/top/?t=year | Vegan Food Porn
https://runningonrealfood.com/rainbow-salad/ | Rainbow Salad | how to make a healthy everyday superfood salad
https://www.startpage.com/do/dsearch?query=passeport+sante+pickles&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://www.passeportsante.net/nutrition/recettes/smoothie-pasteque-facile-rapide | Smoothie à la pastèque désaltérant : facile et rapide

https://www.chasse-aux-livres.fr/prix/**********/his-dark-materials-philip-pullman?query=His%20Dark%20Materials%3A%20Gift%20Edition | His Dark Materials - Northern Lights - The Subtle Knife - The... Philip Pullman - les Prix d'Occasion ou Neuf
https://www.abebooks.fr/servlet/SearchResults?isbn=9781841593425&n=100121501&cm_sp=mbc-_-ISBN-_-new | 9781841593425 - His Dark Materials: Gift Edition including all three novels: Northern Lights, The Subtle Knife and The Amber Spyglass (Everyman's Library CLASSICS) de Pullman, Phil... - AbeBooks
https://inkscape.org/news/2023/07/23/inkscape-launches-version-13-focus-organizing-work/ | Inkscape launches version 1.3 with a focus on organizing work efficiently | Inkscape

https://www.manga-news.com/index.php/serie/Zelda-The-Four-swords-adventures | The Legend of Zelda - The Four swords adventures - Manga série - Manga news
https://www.lesoir.be/525842/article/2023-07-16/les-metiers-du-passe-le-tailleur-de-pierre-dans-les-pas-des-anciens | Les métiers du passé : le tailleur de pierre dans les pas des anciens - Le Soir

https://www.amazon.fr/Oxo-Grips-Stronghold-Ventouse-Panier/dp/B071WMZTZH/ref=sr_1_25?__mk_fr_FR=%C3%85M%C3%85%C5%BD%C3%95%C3%91&crid=15TU0CWAQDIBY&keywords=miroir+douche+rasage+ventouse&qid=1689714340&sprefix=miroir+douche+rasage+ventouse%2Caps%2C74&sr=8-25 | OXO Good Grips – Miroir antibuée à ventouse StrongHold : Amazon.fr: Cuisine et Maison
https://www.bol.com/be/fr/p/oxo-good-grips-stronghold-zuig-fogless-spiegel/9300000136299440/ | OXO Good Grips StrongHold Miroir anti-buée à ventouse | bol.com
https://www.reddit.com/r/belgium/comments/tg55vx/belgische_varianten_bolcom_coolblue/ | Belgische varianten bol.com, coolblue,… : r/belgium
https://tweakers.net/nieuws/list/20230718 | Nieuws van 18-07-2023 over computers, tablets & telefoons, beeld & geluid, games en IT Pro - Tweakers
https://www.deepl.com/translator#nl/en/aanschaffen | DeepL Translate: The world's most accurate translator

https://citizenlab.charliehr.com/reviews/77901/review/143724?utm_campaign=review-launched&utm_medium=email&utm_source=product-email | CharlieHR
https://citizenlab.atlassian.net/browse/CL-3938 | [CL-3938] Template validation incorrectly invalidates array length model validations - JIRA
https://www.thefreedictionary.com/miss%20out | Miss out - definition of miss out by The Free Dictionary
https://crowdin.com/blog/2019/11/05/no-context-no-quality-give-translators-context-to-get-better-translations | No Context, No Quality. Give Translators Context to Get Better Translations | Crowdin Blog

https://www.notion.so/citizenlab/Plan-Development-job-skill-framework-c25445232439416fb10c4f4fc59fd7c0 | (9+) Plan: Development job skill framework
https://37signals.com/podcast/two-person-teams/ | Two Person Teams | REWORK
https://www.notion.so/citizenlab/Deployment-on-Europe-doesn-t-happen-for-all-Docker-nodes-58c5542e1e68406f9e7456acc93b20dd | (9+) Deployment on Europe doesn’t happen for all Docker nodes
https://fev.al/posts/personal-relations/ | On Personal Relations As A Manager
https://blog.testdouble.com/posts/2023-07-12-the-looming-demise-of-the-10x-developer/ | The looming demise of the 10x developer: Why an era of enthusiast programmers is coming to an end
https://creatoreconomy.so/p/kaz-coo-shopify-craft-and-no-meetings | Kaz Nejatian (COO, Shopify): Why Shopify Elevated the Non-Manager Career Path and Ditched Meetings
https://klangmag.co/lifers-dayjobbers-and-the-independently-wealthy-a-letter-to-a-former-student/ | Lifers, Dayjobbers, and the Independently Wealthy: A Letter to a Former Student
https://verdagon.dev/blog/first-regions-prototype | Vale's First Prototype for Immutable Region Borrowing
https://blog.nateliason.com/p/proof-you-can-do-hard-things | Proof You Can Do Hard Things - Nat Eliason's Newsletter
https://www.honeycomb.io/blog/becoming-vp-of-engineering-pt1 | On Becoming a VP of Engineering, Part 1: The Path to VP | Honeycomb
https://fy.blackhats.net.au/blog/2023-02-02-how-hype-will-turn-your-security-key-into-junk/ | Firstyear's blog-a-log

https://docs.google.com/document/d/1z_z5OXn6hn1zHP6kulWji4LYYKOrb1liEb6Lv_zLBn8/edit?skip_itp2_check=true&pli=1 | Copy of CitizenLab & Havant - PowerBI and API specification - Google Docs
https://succulente.be/section-patisserie/ | Section gâteaux & tartes – Succulente boulangerie pâtisserie végétale
https://www.perplexity.ai/ | Perplexity AI
https://stackoverflow.com/questions/34914704/bdbquit-raised-when-debugging-python-with-pdb | BdbQuit raised when debugging Python with pdb - Stack Overflow

https://www.lesoir.be/520742/article/2023-06-20/avec-hadja-lahbib-la-n-va-veut-completer-son-tableau-de-chasse | Avec Hadja Lahbib, la N-VA veut compléter son tableau de chasse - Le Soir
https://www.rtbf.be/lapremiere/grille-programme/ajax/grid | rtbf.be/lapremiere/grille-programme/ajax/grid
https://www.rtbf.be/dossier/les-podcasts-de-la-premiere | La référence de l'actualité belge et internationale - Les podcasts de La Première - RTBF.be
https://fr.wikipedia.org/wiki/S%C3%A9bastien_Ministru | Sébastien Ministru — Wikipédia

https://github.com/eon01/awesome-chatgpt | eon01/awesome-chatgpt: 🧠 A curated list of awesome ChatGPT resources, including libraries, SDKs, APIs, and more. 🌟 Please consider supporting this project by giving it a star.
https://stackoverflow.com/questions/58961497/how-to-update-poetrys-lock-file-without-upgrading-dependencies | python - How to update Poetry's lock file without upgrading dependencies? - Stack Overflow

https://www.lesoir.be/520466/article/2023-06-19/good-move-apres-saint-gilles-ou-en-sont-les-autres-mailles-carte-interactive | Good Move : après Saint-Gilles, où en sont les autres mailles ? (Carte interactive) - Le Soir
https://dwheeler.com/essays/filenames-in-shell.html | Filenames and Pathnames in Shell (bash, dash, ash, ksh, and so on): How to do it Correctly
https://idioms.thefreedictionary.com/ | Idioms and phrases
https://idioms.thefreedictionary.com/a+lean+patch | A lean patch - Idioms by The Free Dictionary
https://idioms.thefreedictionary.com/tow+away | Tow away - Idioms by The Free Dictionary
https://idioms.thefreedictionary.com/drop+(one)%20in%20it | Drop (one) in it - Idioms by The Free Dictionary
https://idioms.thefreedictionary.com/feel+up | Feel up - Idioms by The Free Dictionary
https://idioms.thefreedictionary.com/take+the%20long%20count | Take the long count - Idioms by The Free Dictionary
https://idioms.thefreedictionary.com/good+omen | Good omen - Idioms by The Free Dictionary
https://idioms.thefreedictionary.com/spring+out%20of | Spring out of - Idioms by The Free Dictionary
https://en.conceptchocolate.eu/collections/ALL?page=2 | All products – Page 2 – La Chocolaterie Concept Chocolate

https://www.la-pleiade.fr/Catalogue/GALLIMARD/Bibliotheque-de-la-Pleiade/La-Bible | La Pléiade - Catalogue - Bibliothèque de la Pléiade - Anonyme, La Bible
https://www.la-pleiade.fr/Catalogue/GALLIMARD/Bibliotheque-de-la-Pleiade/La-Bible5 | La Pléiade - Catalogue - Bibliothèque de la Pléiade - Anonyme, La Bible
https://fr.wikipedia.org/wiki/Ancien_Testament | Ancien Testament — Wikipédia
https://www.youtube.com/watch?v=hy5xQX-9fXg | Les relations libres - La chronique de Mahaut - YouTube
https://www.startpage.com/do/dsearch?query=l%27intime+est+politique&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://fr.wikipedia.org/wiki/Le_priv%C3%A9_est_politique | Le privé est politique — Wikipédia

https://datatracker.ietf.org/doc/html/rfc3986#section-5 | RFC 3986 - Uniform Resource Identifier (URI): Generic Syntax
https://datatracker.ietf.org/doc/html/rfc7231#section-5.3.2 | RFC 7231 - Hypertext Transfer Protocol (HTTP/1.1): Semantics and Content
https://mail.python.org/pipermail/mailman-announce/2000-May/000010.html | [Mailman-Announce] forwarded message from Guido van Rossum
https://idioms.thefreedictionary.com/get+act+together | Get act together - Idioms by The Free Dictionary
https://www.google.com/search?q=site:mail.python.org+%22From:+Guido+van+Rossum+%3Cguido%40python.org%3E%22&client=firefox-b-d&ei=3oaLZP2tLbfVkdUPw-SOwA8&start=10&sa=N&ved=2ahUKEwj9lNGVoMb_AhW3aqQEHUOyA_gQ8NMDegQIIhAS&biw=1440&bih=775 | site:mail.python.org "From: Guido van Rossum <<EMAIL>>" - Google Search
https://mail.python.org/archives/list/<EMAIL>/thread/AG7LZZZOTZDUOTA4CU4NP5HFYBOCDFVJ/ | Mailman 3 Adding NewType() to PEP 484 - Python-Dev - python.org
https://mail.python.org/pipermail/python-dev/2002-April/022443.html | [Python-Dev] PEP 287: reStructuredText Standard Docstring Format

https://thoughtbot.github.io/factory_bot/cookbook/polymorphic-associations.html | Polymorphic associations - factory_bot
https://www.startpage.com/do/dsearch?query=finkelkrout&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche

https://app.circleci.com/insights/github/CitizenLabDotCo/citizenlab/workflows/back-deploy-production/jobs?branch=production | Insights - CitizenLabDotCo/citizenlab/workflows/back-deploy-production
https://stackoverflow.com/questions/10672419/class-constants-in-python | Class constants in python - Stack Overflow
https://support.perspectiveapi.com/s/about-the-api-attributes-and-languages?language=en_US | About the API - Attributes and Languages
https://www.phind.com/search?cache=73d056de-b7a9-4357-bdfa-8ff6b4f29e30 | shortcut to access class constant in python (from instance method)

https://fr.wikipedia.org/wiki/Fatima_Zibouh | Fatima Zibouh — Wikipédia
https://blog.appsignal.com/2023/02/15/whats-new-in-rails-7-1.html | What's New in Rails 7.1 | AppSignal Blog
https://www.lesoir.be/518156/article/2023-06-07/visa-pour-la-flandre-affaire-sanda-dia-justice-de-classe-ou-populaire | #visa pour la Flandre : affaire Sanda Dia, justice de classe ou populaire ? - Le Soir

https://www.comptoir-rodin.be/contact-2/ | Contact - Le comptoir Rodin
https://idioms.thefreedictionary.com/go+stir-crazy | Go stir-crazy - Idioms by The Free Dictionary

http://localhost:8888/notebooks/city_reports/vienna_climate/data-fetching.ipynb#Post-processing | data-fetching - Jupyter Notebook
https://www.startpage.com/do/dsearch?query=HDBSCAN+min_samples&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://stackoverflow.com/questions/67898039/hdbscan-difference-between-parameters | machine learning - HDBSCAN difference between parameters - Stack Overflow
https://hdbscan.readthedocs.io/en/latest/parameter_selection.html | Parameter Selection for HDBSCAN* — hdbscan 0.8.1 documentation
https://github.com/CitizenLabDotCo/cl2-nlp-reporting/blob/main/notebooks/city_reports/vienna_climate/data-fetching.ipynb | cl2-nlp-reporting/data-fetching.ipynb at main · CitizenLabDotCo/cl2-nlp-reporting · GitHub
https://insights-prototype.hq.citizenlab.co/ | CitizenLab Insights Prototype

https://stackoverflow.com/questions/16123492/eager-load-polymorphic/16124295#16124295 | ruby on rails - Eager load polymorphic - Stack Overflow
https://gist.github.com/mlanett/a31c340b132ddefa9cca | HTTP status code symbols for Rails
https://stackoverflow.com/questions/55554431/conda-fails-to-create-environment-from-yml | python - conda fails to create environment from yml - Stack Overflow
https://www.notion.so/citizenlab/Deployment-da0af7e8fed140ddbd358d0fa8f0a3c2#1d11066327dd493a813a1b9c1d04ff72 | (9+) Deployment
https://www.flannels.ie/searchresults?descriptionfilter=Mens%20Nike%20Underwear | Search Results
https://www.lesoir.be/514985/article/2023-05-23/le-guide-des-festivals-de-lete-du-mad | Le guide des festivals de l’été du Mad - Le Soir

https://www.bonnegueule.fr/destockage-comment-trouver-vraies-bonnes-affaires-yoox-soldes-promos-code-avis-retour/ | E-shop Yoox, le guide y faire de vraies bonnes affaires | BONNEGUEULE
https://www.yoox.com/fr/13192271IH/item#cod10=13192271IH&sizeId=-1 | INCOTEX | Chino Homme | YOOX
https://www.yoox.com/fr/homme/shoponline?dept=mmrc_2021lvs&page=1#/dept=mmrc_2021lvs&gender=U&page=1&attributes=%7b%27ctgr%27%3a%5b%27pntlnjns%27%5d%7d&season=X&pricemin=40&pricemax=70 | YOOX - Sélection Spéciale

https://www.metacritic.com/game/switch/darkest-dungeon | Darkest Dungeon for Switch Reviews - Metacritic
https://www.kvrtstvff.com/collections/sportwear/products/ochre-burley-compression-shorts | Ochre Burley Compression Shorts – KVRT STVFF
https://www.lesoir.be/358546/dpi-authors/marius-gilbert | Marius Gilbert - Le Soir

https://www.metacritic.com/tv/pose | Pose - TV Show Reviews - Metacritic
https://www.reddit.com/r/gaybros/comments/10wftg6/after_watching_the_last_of_us_do_you_blokes/ | After watching The Last of Us. Do you blokes recommend anything else? : gaybros
https://www.reddit.com/r/france/comments/xmxp7k/seigneur_des_anneaux_suisje_le_seul_%C3%A0_%C3%AAtre_agac%C3%A9/ | Seigneur des Anneaux - suis-je le seul à être agacé par certains choix de traduction de Daniel Lauzon ? : france
https://www.visualcapitalist.com/wp-content/uploads/2017/08/richest-people-in-human-history.html | Infographic: The Richest People in History
https://www.natwilson.com/stuff/whistle.html | How To Whistle Loudly – Nat Wilson's site
https://pitchfork.com/reviews/albums/jessie-ware-that-feels-good/ | Jessie Ware: That! Feels Good! Album Review | Pitchfork
https://www.amazon.fr/Love-Saves-Day-American-1970-1979/dp/0822331985 | Love Saves the Day: A History Of American Dance Music Culture, 1970–1979 : Lawrence, Tim: Amazon.fr: Livres
https://www.reddit.com/r/flicks/comments/126k7ax/how_wes_anderson_has_changed/ | How Wes Anderson has changed : flicks
https://www.reddit.com/r/veganrecipes/comments/13qp9ah/easy_vegan_chinese_style_noodles/ | Easy Vegan Chinese Style Noodles : veganrecipes
https://avegtastefromatoz.com/vegan-longevity-noodles/ | Vegan Chinese Longevity Noodles – Yi Mein 伊面 -
https://www.reddit.com/r/veganrecipes/comments/13cw3sh/voil%C3%A0_baguettes/ | Voilà, baguettes : veganrecipes

https://guides.rubyonrails.org/api_app.html | Using Rails for API-only Applications — Ruby on Rails Guides
https://sentry.hq.citizenlab.co/organizations/citizenlab/issues/69000/events/55030d5f273e46d68c9a46f5000cf574/?environment=production&project=2&statsPeriod=14d | Analytics::ImportLatestMatomoDataJob::MatomoMisconfigurationError: Matomo site (= "28") for tenant '1075ca41-0be8-4a93-96e2-90831f1e86c4' is misconfigured. - citizenlab - cl2-back
https://blog.saeloun.com/2022/02/23/rails-fiber-safe-connection-pools/ | Rails adds support for Fiber-safe ActiveRecord ConnectionPools | Saeloun Blog
https://www.rubyguides.com/2019/11/what-are-fibers-in-ruby/ | What Everyone Should Know About Fibers in Ruby

https://www.theatrelepublic.be/le-mage-du-kremlin | LE MAGE DU KREMLIN
https://www.notion.so/citizenlab/NLP-Kubernetes-Operations-6b3c0a863b364810b6e053240f33f454#eee01b33daac4f57969d0700657f11e0 | (9+) NLP Kubernetes Operations

https://perspective.brussels/ | perspective.brussels | perspective.brussels

https://blog.eq8.eu/til/how-to-test-caching-on-individual-tests-rails-rspec.html | How to test performance of caching with RSpec in Rails
https://guides.rubyonrails.org/v7.0/upgrading_ruby_on_rails.html | Upgrading Ruby on Rails — Ruby on Rails Guides
https://guides.rubyonrails.org/configuring.html#config-active-support-disable-to-s-conversion | Configuring Rails Applications — Ruby on Rails Guides
https://fr.zalando.be/homme/nike-underwear/ | Articles de mode Nike Underwear pour homme | Zalando Belgique
https://www.nike.com/be/fr/w?q=boxer&vst=boxer | Produits. Nike BE
https://www.nike.com/t/dri-fit-ultra-stretch-micro-mens-long-boxer-brief-3-pack-8JKG9d/KE1154-960 | Nike Dri-FIT Ultra Stretch Micro Men's Long Boxer Brief (3-Pack). Nike.com
https://www.sarenza.com/nike-underwear-trunk-2pk-s914627-br1877-t122-p0000279951 | Nike Underwear Trunk 2Pk (Multicolore) - Vêtements chez Sarenza (513042)
https://www.startpage.com/do/dsearch?query=Nike+Dri-FIT+Ultra+Stretch+Micro+belgium&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://fr.def-shop.be/homme/ | Mode et tendances Homme l Baskets, vêtements, accessoires, lifestyle l DEFSHOP
https://www.decathlon.be/fr/p/slip-respirant-running-gris-abysses-homme/_/R-p-121847?mc=8573739 | SLIP RESPIRANT RUNNING GRIS ABYSSES HOMME KALENJI | Decathlon
https://fr.def-shop.be/homme/boxers/?brand_f%5B%5D=234 | Boxers

https://modivo.fr/s/nike?q=nike&category=homme%2Fvetements%2Fsous-vetements%2Fboxers | Vous avez recherché : 'nike'
https://www.laboutiqueofficielle.com/p/nike-boxer-elite-micro-ke1034-noir-249194 | Nike - Boxer Elite Micro KE1034 Noir - LaBoutiqueOfficielle.com
https://modivo.fr/p/nike-boxer-elite-micro-0000ke1034-noir | Nike Boxer Elite Micro 0000KE1034 Noir | Modivo.fr
https://www.lesoir.be/516238/article/2023-05-30/un-nouveau-credit-dimpot-entre-en-vigueur-en-belgique | Un nouveau crédit d’impôt entre en vigueur en Belgique  - Le Soir

https://github.com/smol-ai/developer | smol-ai/developer: with 100k context windows on the way, it's now feasible for every dev to have their own smol developer
https://www.google.be/maps/place/59200+Tourcoing,+Frankrijk/@50.6442009,3.1535505,10z/data=!4m23!1m16!4m15!1m6!1m2!1s0x47c2d579b3256e11:0x40af13e81646360!2sLille,+Frankrijk!2m2!1d3.057256!2d50.62925!1m6!1m2!1s0x47c328d4be0a275d:0x252e317d7dad858f!2s59200+Tourcoing,+Frankrijk!2m2!1d3.16207!2d50.724993!3e2!3m5!1s0x47c328d4be0a275d:0x252e317d7dad858f!8m2!3d50.724993!4d3.16207!16zL20vMDFxbm1q?entry=ttu | Tourcoing - Google Maps
https://fr.dolphin-emu.org/ | Émulateur Dolphin - Jouez à la GameCube/Wii sur PC
https://www.youtube.com/watch?v=ZMQbHMgK2rw | The Fastest Maze-Solving Competition On Earth - YouTube
https://httptoolkit.com/blog/http-search-method/ | Defining a new HTTP method: HTTP SEARCH | HTTP Toolkit
https://fr.wikipedia.org/wiki/Nation_sans_%C3%89tat | Nation sans État — Wikipédia
https://fr.wikipedia.org/wiki/Pontique | Pontique — Wikipédia
https://www.geoguessr.com/fr/vgp/3485 | Europe : événements de la Seconde Guerre mondiale - Quiz de géographie - Seterra
https://cs.stanford.edu/~knuth/chatGPT20.txt | cs.stanford.edu/~knuth/chatGPT20.txt

https://www.cinergie.be/film/bruxelles-sauvage | Bruxelles sauvage - Belgique 2015 - sur Cinergie.be
https://brandur.org/postgres-queues | Postgres Job Queues & Failure By MVCC
https://www.se-radio.net/2022/01/episode-496-bruce-momjian-on-multi-version-concurrency-control-in-postgres-mvcc/ | Episode 496: Bruce Momjian on Multi-Version Concurrency Control in Postgres (MVCC) : Software Engineering Radio
https://news.ycombinator.com/item?id=36078709 | htmx | Hacker News
https://github.com/williamcotton/empirical-philosophy/blob/main/articles/from-prompt-alchemy-to-prompt-engineering-an-introduction-to-analytic-agumentation.md | empirical-philosophy/from-prompt-alchemy-to-prompt-engineering-an-introduction-to-analytic-agumentation.md at main · williamcotton/empirical-philosophy · GitHub

https://www.reddit.com/r/Jigsawpuzzles/top/?t=month | A Place Where Jigsaw Puzzlers Can Show Off
https://www.puzzle.be/educa-patagonie-puzzle-1000-pieces.p94062.html | Puzzle Patagonie Educa-19259 1000 pièces Puzzles - Montagnes
https://www.google.com/search?q=dettifoss+puzzle&client=firefox-b-d&source=lnms&tbm=isch&sa=X&ved=2ahUKEwjHhNLKoov_AhV-UqQEHe9LCAEQ_AUoAXoECAIQAw&biw=1920&bih=961#imgrc=Y00LSBQPFWloYM&imgdii=nYajTRCdg2CsNM | dettifoss puzzle - Google Search
https://mydesigner.myravensburger.com/fotopuzzle?session=39d9bda8-c0ff-4232-9d98-b5fead1698be&productId=80990 | my Ravensburger Designer
https://www.myravensburger.com/fr-FR/puzzle-personnalise/ | Créer son propre puzzle personnalisé pour sublimer le plaisir du puzzle
https://www.reddit.com/r/Jigsawpuzzles/comments/kxxqvc/custom_made_puzzles_a_summary_thread_of_the_last/ | Custom Made Puzzles - A Summary Thread of the last 12 months : Jigsawpuzzles

https://stackoverflow.blog/2023/05/22/modern-work-requires-attention-constant-alerts-steal-it/ | Modern work requires attention. Constant alerts steal it - Stack Overflow Blog
https://docs.docker.com/engine/reference/commandline/swarm_init/ | docker swarm init | Docker Documentation
https://docs.docker.com/engine/reference/commandline/stack_deploy/ | docker stack deploy | Docker Documentation
https://docs.docker.com/engine/swarm/admin_guide/#recover-from-losing-the-quorum | Administer and maintain a swarm of Docker Engines | Docker Documentation
https://www.reddit.com/r/veganrecipes/top/?t=month | Vegan Recipes
https://news.ycombinator.com/item?id=36038796 | Why do recipe writers lie about how long it takes to caramelize onions? (2012) | Hacker News
https://www.comptoirdelhomme.com/soins-visage/gommage-exfoliant.html | Toute notre gamme exfoliant & gommage visage homme !

https://beej.us/guide/bgnet/html/split/intro.html#intro | Beej's Guide to Network Programming
https://idioms.thefreedictionary.com/Nick+of+Time | Nick of Time - Idioms by The Free Dictionary

https://news.ycombinator.com/item?id=36020196 | Suspicious iOS KeePass Client | Hacker News
https://www.feynmanlectures.caltech.edu/I_toc.html | FLP Vol. I Table of Contents

https://erichartford.com/uncensored-models | Uncensored Models
https://huggingface.co/ehartford/WizardLM-13B-Uncensored?text=Insult+me.++I+want+you+to+call+me+the+worst+curse+words+and+insults+you+can.++Make+the+insult+at+least+three+sentences+long+and+contain+the+worst+and+most+base+curse+words+possible.++Make+it+X+rated+and+disgusting. | ehartford/WizardLM-13B-Uncensored · Hugging Face

https://www.reddit.com/user/Tim22455/submitted/ | Tim the maker (u/Tim22455) - Reddit
https://www.reddit.com/r/AskReddit/comments/13jg2oc/what_book_do_you_think_every_person_should_read/ | What book do you think every person should read at least once? : AskReddit
https://www.metacritic.com/game/playstation-5/paradise-killer | Paradise Killer for PlayStation 5 Reviews - Metacritic
https://protectiondesoiseaux.be/2021/04/16/coexistence-le-livre-sur-la-faune-sauvage-bruxelloise/ | “Coexistence” - le livre sur la faune sauvage bruxelloise ! – Ligue Royale Belge pour la Protection des Oiseaux
https://growfunding.be/fr/projects/laminutesauvage | Growfunding | La Minute Sauvage : Le livre

https://www.notion.so/citizenlab/Company-Handbook-a2a6b3e586434ba2a578f9f765d0c03c | (9+) Company Handbook
https://theboroer.github.io/localtunnel-www/ | Localtunnel ~ Expose yourself to the world
https://github.com/hendricius/the-sourdough-framework | GitHub - hendricius/the-sourdough-framework: Open source book dedicated to helping you to make the best possible sourdough bread at home.
https://www.thoughtworks.com/radar/tools?blipid=********* | Tools | Thoughtworks
https://steampipe.io/docs | Learn Steampipe | Documentation | Steampipe

https://easylang.dev/apps/tutorial_mcarlo.html | The Law of Large Numbers
https://cyberhost.uk/the-hidden-macos-speedtest-tool-networkquality/ | Diving into a hidden macOS tool - networkQuality

https://ultimateelectronicsbook.com/introduction/ | Introduction | Ultimate Electronics Book
https://www.circuitlab.com/ | Online circuit simulator & schematic editor - CircuitLab

https://www.metacritic.com/browse/games/score/metascore/year/switch/filtered?sort=desc&year_selected=2023 | Best Switch Video Games for 2023 - Metacritic
https://www.metacritic.com/browse/movies/score/metascore/year/filtered | Best Movies for 2023 - Metacritic
https://www.metacritic.com/pictures/alien-invasion-movies-ranked-worst-to-best | 21 Alien Invasion Movies, Ranked from Worst to Best - Metacritic

https://guides.rubyonrails.org/active_job_basics.html | Active Job Basics — Ruby on Rails Guides
https://botanique.be/fr | Page d'accueil | Botanique
https://www.reddit.com/r/brussels/comments/10b2yqq/kunstbergmont_des_arts_100_years_ago/ | Kunstberg/Mont des Arts 100 years ago : brussels
https://stackoverflow.com/questions/5117729/rails-is-there-an-engine-root | Rails: is there an Engine.root? - Stack Overflow
https://blog.appsignal.com/2021/12/15/whats-new-in-rails7.html | What's New in Rails 7 | AppSignal Blog

https://stackoverflow.com/questions/18913236/how-to-convert-string-to-timestamp-without-time-zone | javascript - How to convert "string" to "timestamp without time zone" - Stack Overflow
https://type-level-typescript.com/ | Type-Level TypeScript
http://lushprojects.com/ | Lushprojects.com - A feast of electronic fun, education and diversions.
http://lushprojects.com/circuitjs/circuitjs.html | lushprojects.com/circuitjs/circuitjs.html

https://www.google.be/maps/place/Harvest/@50.8515939,4.3401876,15z/data=!4m10!1m2!2m1!1stownhouse+restaurant+bruxelles!3m6!1s0x47c3c386f16e30f9:0x478a38e78422df9!8m2!3d50.8515939!4d4.3504873!15sCh50b3duaG91c2UgcmVzdGF1cmFudCBicnV4ZWxsZXNaICIedG93bmhvdXNlIHJlc3RhdXJhbnQgYnJ1eGVsbGVzkgEabW9kZXJuX2V1cm9wZWFuX3Jlc3RhdXJhbnTgAQA!16s%2Fg%2F11c71blfhv | Harvest - Google Maps
https://visual-fonts.com/ | Visual Fonts – Type Good Pictures
https://www.lesoir.be/507865/article/2023-04-17/litterature-toute-la-flamme-de-conrad-detrez-decouvrir-en-poche?referer=%2Farchives%2Frecherche%3Fdatefilter%3Dlastyear%26sort%3Ddate%2Bdesc%26word%3Dcl%25C3%25A9ment%2Bdessy | Littérature: toute la flamme de Conrad Detrez à découvrir en poche - Le Soir
https://le-carnet-et-les-instants.net/archives/conrad-detrez/ | Conrad Detrez, enfant du siècle et persona non grata - Le Carnet et les Instants
https://www.ina.fr/ina-eclaire-actu/jacques-chancel-radioscopie-une-voix-des-vies | Jacques Chancel, Radioscopie : une voix, des vies | INA
https://www.youtube.com/watch?v=KlotHkckOsE&list=PLw4-Fp0S2dJZ94MFzCEiOET30wglgUUCO&index=4 | Radioscopie : André Malraux [1974] - YouTube

https://www.reddit.com/r/gaybros/comments/13c4oop/anyone_ever_bought_these_kind_of_resin_statues/ | Anyone ever bought these kind of resin statues? Want to buy them but not sure of the quality or price : gaybros
https://www.reddit.com/r/gaymers/comments/138y7k2/games_are_so_goddamn_boring_lately_i_need_tips/ | Games are so goddamn boring lately, I need tips! : gaymers
https://www.reddit.com/r/gaymers/comments/jxd8cd/litoperezitodaemoncollection_chris_redfield_sfw/ | [LitoPerezito/DaemonCollection] Chris Redfield (SFW version) : gaymers
https://jvns.ca/blog/2023/05/08/new-talk-learning-dns-in-10-years/ | New talk: Learning DNS in 10 years
https://youtrack.jetbrains.com/issue/JBR-2509/ | Keymap - Command + dot works as Escape : JBR-2509
https://github.com/settings/keys | SSH and GPG keys

https://pitchfork.com/reviews/albums/jockstrap-i-love-you-jennifer-b/ | Jockstrap: I Love You Jennifer B Album Review | Pitchfork
https://tratt.net/laurie/blog/2023/why_split_lexing_and_parsing_into_two_separate_phases.html | Laurence Tratt: Why Split Lexing and Parsing Into Two Separate Phases?
https://www.zdziarski.com/blog/?p=12001 | AI is Just Someone Else’s Intelligence – Zdziarski
https://theorangeduck.com/page/cubic-interpolation-quaternions | Cubic Interpolation of Quaternions
https://en.wikipedia.org/wiki/Characteristic_polynomial | Characteristic polynomial - Wikipedia
https://fly.io/ruby-dispatch/rails-on-docker/ | Rails on Docker · Fly

https://www.thecurb.com.au/40-years-of-koyaanisqatsi/ | 40 Years of KOYAANISQATSI - review - The Curb
https://en.wikipedia.org/wiki/Differential_form | Differential form - Wikipedia
https://papl.cs.brown.edu/2018/func-as-data.html#%28part._.A_.Little_.Calculus%29 | 13 Functions as Data
https://cstack.github.io/db_tutorial/ | How Does a Database Work? | Let’s Build a Simple Database

https://probablydance.com/2023/04/27/beautiful-branchless-binary-search/ | Beautiful Branchless Binary Search | Probably Dance
https://docs.flux.ai/tutorials/ai-for-hardware-design | AI For Hardware Design with Copilot - Flux - Documentation
https://github.com/collectiveidea/interactor | collectiveidea/interactor: Interactor provides a common interface for performing complex user interactions.
https://docs.gitlab.com/ee/development/architecture.html | GitLab architecture overview | GitLab
https://www.izzy.co/blogs/robo-boys.html | Replacing my best friends with an LLM trained on 500,000 group chat messages

http://http.rip/ | RIP HTTP
https://guides.rubyonrails.org/threading_and_code_execution.html#executor | Threading and Code Execution in Rails — Ruby on Rails Guides
https://www.rfc-editor.org/rfc/rfc7807 | RFC 7807: Problem Details for HTTP APIs

https://www.reddit.com/r/AskReddit/comments/134qerl/richard_feynman_said_never_confuse_education_with/ | Richard Feynman said, “Never confuse education with intelligence, you can have a PhD and still be an idiot.” What are some real life examples of this? : r/AskReddit

https://www.reddit.com/r/brussels/comments/r18ki1/looking_for_hidden_gems_restaurant_recommandation/ | Looking for hidden gems. ( Restaurant recommandation ) : brussels
https://www.reddit.com/r/brussels/comments/rz9c02/whats_your_favourite_restaurant_in_brussels/ | What’s your favourite restaurant in Brussels? : brussels
https://www.google.be/maps/place/Volta+Supper+Club/@50.8496389,4.3463223,17z/data=!4m10!1m2!2m1!1svolta!3m6!1s0x47c3c52cffe7872b:0xf693f1717971cd70!8m2!3d50.8359031!4d4.3639607!15sCgV2b2x0YVoHIgV2b2x0YZIBCnJlc3RhdXJhbnTgAQA!16s%2Fg%2F11rcdjkygt | Volta Supper Club - Google Maps
https://www.nuetnigenough.be/ | Nüetnigenough !
https://www.google.be/maps/place/La+Tana/@50.8482539,4.2881057,13z/data=!4m10!1m2!2m1!1sLa+Tana!3m6!1s0x47c3c37e1841c559:0x8a26c3b007592faa!8m2!3d50.8482539!4d4.3643234!15sCgdMYSBUYW5hWgkiB2xhIHRhbmGSARJpdGFsaWFuX3Jlc3RhdXJhbnTgAQA!16s%2Fg%2F1pzpjzn5v | La Tana - Google Maps
https://www.google.be/maps/place/Schievelavabo/@50.8363827,4.3799396,3a,75y,90t/data=!3m8!1e2!3m6!1sAF1QipNwdyDREzb3IDoG3grXAziDQ3S31YwTz70qs-Wl!2e10!3e12!6shttps:%2F%2Flh5.googleusercontent.com%2Fp%2FAF1QipNwdyDREzb3IDoG3grXAziDQ3S31YwTz70qs-Wl%3Dw203-h270-k-no!7i3024!8i4032!4m11!1m2!2m1!1sSchievelavabo!3m7!1s0x47c3c49789e73525:0xed35f296707f921e!8m2!3d50.836478!4d4.3798663!10e5!15sCg1TY2hpZXZlbGF2YWJvIgOIAQFaDyINc2NoaWV2ZWxhdmFib5IBEmJlbGdpYW5fcmVzdGF1cmFudOABAA!16s%2Fg%2F11b6v7c68w | Schievelavabo - Google Maps

https://gamefaqs.gamespot.com/ | GameFAQs - Video Game Cheats, Reviews, FAQs, Message Boards, and More

https://www.reddit.com/r/gaymers/comments/1313muz/mods_have_made_ffxiv_so_much_more_enjoyable/ | Mods have made FFXIV so much more enjoyable. : gaymers
http://livre-europeen.eu/?p=5856 | Zinc – Prix du Livre Européen

https://docs.docker.com/engine/reference/commandline/attach/ | docker attach | Docker Documentation
https://commoncog.com/deming-paradox-operational-rigour/ | The Deming Paradox: The Human Costs of Operational Rigour - Commoncog
https://www.bonnegueule.fr/guide-mariage-tenue-conseil-style-costume-cravate-noeud-papillon-chaussures-couleurs-noir/#comment-tre-bien-habill-pour-un-mariage- | Mariage : règles de base pour tenue réussie | BONNEGUEULE
https://www.bonnegueule.fr/conseil-costumes-homme/ | Tous les conseils de la rédaction sur les costumes
https://www.bonnegueule.fr/guide-mariage-tenue-ideale-invite-costume-conseil-style/ | Invité à un mariage : quelle tenue choisir ? | BONNEGUEULE
https://www.morjas.com/ | MORJAS | Official Store
https://www.wsj.com/articles/ikea-furniture-inflation-billy-c2f835bc | IKEA Redesigns Its Bestsellers, Starting With the Billy Bookcase - WSJ

https://www.rtbf.be/article/serie-video-d-osamu-tezuka-a-makoto-shinkai-un-siecle-d-animation-japonaise-10312377 | [SÉRIE VIDÉO] D'Osamu Tezuka à Makoto Shinkai, un siècle d'animation japonaise - rtbf.be
https://www.larousse.fr/dictionnaires/francais/argutie/5219 | Définitions : argutie - Dictionnaire de français Larousse
https://www.vice.com/es | VICE - Reportajes y documentales originales sobre los temas más relevantes del mundo
https://www.lowtechmagazine.com/2019/10/mist-showers-sustainable-decadence.html | Mist Showers: Sustainable Decadence? - LOW-TECH MAGAZINE
https://thehomesteadinghippy.com/how-to-take-a-sponge-bath/ | Here's How to Take a Sponge Bath * The Homesteading Hippy
https://www.startpage.com/do/dsearch?query=small+shampoo+bottle&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://bandcamp.com/?show=622 | Bandcamp
https://gist.github.com/lfender6445/9919357 | Pry Cheat Sheet

https://news.ycombinator.com/item?id=35666598 | The “Build Your Own Database” book is finished | Hacker News
https://build-your-own.org/blog/20230420_byodb_done/ | The “Build Your Own Database” book is finished | Blog | build-your-own.org
https://build-your-own.org/ | Build Your Own X From Scratch Books
https://lcamtuf.substack.com/p/the-magic-of-dc-dc-voltage-conversion | The magic of DC-DC voltage conversion - lcamtuf’s thing
https://www.benkuhn.net/newmgr/ | Some mistakes I made as a new manager | benkuhn.net
https://www.chatpdf.com/ | ChatPDF - Chat with any PDF!
https://www.tomchapin83.com/tricks-with-pry-live-editing/ | Tricks with Pry – Live Editing – Tom Chapin (Thomas H. Chapin IV)

https://rule34.xxx/index.php?page=post&s=view&id=7210479 | Rule 34 - ass butt dark-skinned male dark skin evinist jockstrap looking at viewer male male only melanin muscles muscular muscular male open coat peony (pokemon) pokemon pokemon ss pokemon ss crown tundra pokemon ss isle of armor smiling at viewer | 7210479

https://rbspy.github.io/rbspy-vs-stackprof.html | rbspy vs stackprof - rbspy: A Sampling CPU Profiler for Ruby

https://phys.org/news/2023-04-links-stuck-stem-cells-hair.html | Study links 'stuck' stem cells to hair turning gray
https://www.cardohotels.com/ | Cardo Brussels Hotel | Lifestyle Hotel in Brussels

https://docs.docker.com/desktop/dev-environments/ | Overview | Docker Documentation
https://github.com/99designs/aws-vault/blob/master/USAGE.md | aws-vault/USAGE.md at master · 99designs/aws-vault · GitHub
https://pubs.aip.org/physicstoday/article/66/1/25/414210/Norman-Ramsey-and-his-methodWhile-searching-for-a | Norman Ramsey and his method | Physics Today | AIP Publishing
https://www.microsoft.com/en-us/research/academic-program/give-great-research-talk/other-resources/ | How to give a great research talk - Microsoft Research
http://www.eecs.harvard.edu/~nr/pubs/two-abstract.html | eecs.harvard.edu/~nr/pubs/two-abstract.html
https://www.cs.virginia.edu/~robins/YouAndYourResearch.html | You and Your Research

https://xenodium.com/my-emacs-eye-candy/ | My Emacs eye candy
https://samwho.dev/load-balancing/ | Load Balancing
https://store.crowdin.com/source-target-consistency | Inconsistency Check - Crowdin Marketplace
https://www.startpage.com/do/dsearch?query=disenchanted+cake&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://www.youtube.com/watch?v=42dtSYIBkYc | Apple Cookies Recipe: Cooking tree - YouTube
https://www.lesoir.be/506890/article/2023-04-12/weg-etrange-vallee-les-cinq-spectacles-ne-pas-manquer-cette-semaine | Weg, Etrange vallée...: les cinq spectacles à ne pas manquer cette semaine - Le Soir
https://fr.wikipedia.org/wiki/Int%C3%A9gration_neuro-%C3%A9motionnelle_par_les_mouvements_oculaires | Intégration neuro-émotionnelle par les mouvements oculaires — Wikipédia
https://poche.be/theater | Théâtre de Poche | Bruxelles

https://menu-vegetarien.com/cuisine/moins-de-30-minutes/ | Moins de 30 minutes - Menu végétarien
https://context.reverso.net/traduction/francais-anglais/qu%27est-ce+que+%C3%A7a+donne+en | qu'est-ce que ça donne en - Traduction en anglais - exemples français | Reverso Context

https://artsandculture.google.com/story/ivrea-industrial-city-of-the-20th-century/CgXxgt6QPoEhLQ?zx=ohx4o1vakc5p | Ivrea, industrial city of the 20th century — Google Arts & Culture
https://www.notion.so/citizenlab/Plan-Translations-workflow-improvements-23Q2-7f3dd6a0d1c041b498ca12e252615533 | (9+) Plan: Translations workflow improvements 23Q2
https://www.metacritic.com/game/xbox-series-x/ghostwire-tokyo | GhostWire: Tokyo for Xbox Series X Reviews - Metacritic
https://www.thebroadcastbridge.com/content/entry/12400/the-lost-art-of-lacing-cable | The Lost Art of Lacing Cable - The Broadcast Bridge - Connecting IT to Broadcast
https://www.youtube.com/watch?v=jtO9XysoZ6o | Cable Management the NASA Way - The Lost Art of Cable Lacing - YouTube
https://opencritic.com/game/4232/disgaea-5-complete | Disgaea 5 Complete Reviews - OpenCritic
https://en.wikipedia.org/wiki/Phantom_Brave | Phantom Brave - Wikipedia

https://mariokart.fandom.com/wiki/Staff_Ghosts#Mario_Kart_8 | Staff Ghosts | Mario Kart Racing Wiki | Fandom

https://evanw.github.io/thumbhash/ | ThumbHash: A very compact representation of an image placeholder
https://www.youtube.com/@theMSKphysio | The Musculoskeletal Clinic - YouTube
https://cursive-ide.com/userguide/paredit.html | Cursive: Structural Editing
https://www.servethecity.brussels/volunteer/orientation/ | Volunteer Orientation – Serve the City Brussels
https://rubyonrails.org/2023/4/6/rails-world-is-coming | Ruby on Rails — Rails World is coming
https://www.bifff.net/program/?_sft_pa_session=20-04 | Products – BIFFF

https://fr.wikipedia.org/wiki/Princesse_Peach#cite_ref-3 | Princesse Peach — Wikipédia
https://twitter.com/guyverhofstadt | Guy Verhofstadt (@guyverhofstadt) / Twitter
https://www.youtube.com/watch?v=9cNmUNHSBac | Most People Don't Know How Bikes Work - YouTube
https://www.kiwico.com/us/store/dp/color-wheel-racers-and-ramp/4762 | Color Wheel Racers and Ramp | KiwiCo
https://bandcamp.com/?show=622 | Bandcamp

https://www.lesoir.be/506165/article/2023-04-08/tensions-cris-empoignades-quand-le-ton-monte-au-kern | Tensions, cris, empoignades… quand le ton monte au kern - Le Soir
https://idioms.thefreedictionary.com/bank+on | Bank on - Idioms by The Free Dictionary
https://www.thefreedictionary.com/_/MatchUp.aspx?res=4%2C2%2C-1%2C-1%2C-1&tfd_a0=dissemble&tfd_b0=reel&tfd_a1=disburse&tfd_b1=cloak&tfd_a2=chivvy&tfd_b2=pay%20out&tfd_a3=lampoon&tfd_b3=harry&tfd_a4=gyrate&tfd_b4=satirize | Match Up Results
https://www.howacarworks.com/video-course/watch/engine-block# | Engine Block - How a Car Works
https://www.redhat.com/sysadmin/speed-containers-podman-raspberry-pi | How we achieved a 6-fold increase in Podman startup speed | Enable Sysadmin
https://docs.midjourney.com/ | Midjourney Documentation and User Guide
https://scottbartell.com/2020/01/30/set-attributes-in-active-record-rails-6/#fn:update_attributes_deprecated | Different Ways to Set Attributes in ActiveRecord (Rails 6)

https://stackoverflow.com/questions/********/i-want-to-update-file-to-an-existing-rails-record-with-carrierwave | I want to update file to an existing rails record with carrierwave - Stack Overflow
https://www.tomchapin83.com/tricks-with-pry-live-editing/ | Tricks with Pry – Live Editing – Tom Chapin (Thomas H. Chapin IV)
https://ruby-doc.org/stdlib-2.6.3/libdoc/tsort/rdoc/TSort.html | Module: TSort (Ruby 2.6.3)
https://www.indiehackers.com/start | Start a Profitable Side Project in 2022 - Indie Hackers
https://twitter.com/naval/status/1002103360646823936 | Naval on Twitter: "How to Get Rich (without getting lucky):" / Twitter
https://www.reddit.com/r/gaming/comments/12ceg3y/thank_you_devs_i_needed_that/ | Thank you devs, I needed that : gaming
https://www.acouplecooks.com/how-to-cook-vegetables/ | How to Cook Vegetables – A Couple Cooks
https://jackiesilvernutrition.com/recipes/10-high-protein-vegetarian-salads/ | 10 High Protein Vegetarian Salads - Jackie Silver Nutrition
https://www.discogs.com/fr/search/?type=release&sort=hot%2Cdesc&ev=em_tr&decade=2020&genre_exact=Pop&year=2023&country_exact=Europe&layout=med | Europe et Pop musique depuis l'année 2023| Discogs

https://evanw.github.io/thumbhash/ | ThumbHash: A very compact representation of an image placeholder

https://owasp.org/www-community/attacks/ | Attacks | OWASP Foundation
https://www.lesoir.be/505199/article/2023-04-03/la-chambre-deliberement-camoufle-des-complements-de-pension-7-millions | La Chambre a délibérément camouflé des compléments de pension à 7 millions - Le Soir
https://fr.wikipedia.org/wiki/Parlement_f%C3%A9d%C3%A9ral_(Belgique) | Parlement fédéral (Belgique) — Wikipédia
https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/install-CloudWatch-Agent-on-EC2-Instance.html | Installing the CloudWatch agent - Amazon CloudWatch

https://www.epicene.ch/prestations/glossaire/ | Glossaire - ÉPICÈNE
https://apidock.com/ruby/Proc/arity | arity (Proc) - APIdock
https://stackoverflow.com/questions/70096114/proc-arity-in-ruby-3-0-2-vs-ruby-2-x-x | Proc.arity in ruby-3.0.2 vs ruby-2.X.X - Stack Overflow

https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/placement_group | aws_placement_group | Resources | hashicorp/aws | Terraform Registry
https://linuxhint.com/ssh-keyscan-ubuntu/ | How to use ssh-keyscan on Ubuntu
https://blog.pjam.me/posts/ruby-symbol-to-proc-the-short-version/ | Ruby Symbol to Proc explained, the short version - Occasionally consistent
https://www.lalibre.be/debats/2023/03/29/gpa-arretons-ce-marche-international-de-lexploitation-reproductive-JYF6QEPPABBLTNN6WEAOJHV72U/ | GPA : arrêtons ce marché international de l’exploitation reproductive ! - La Libre
https://www.youtube.com/watch?v=1wK65xN9Gkc | Internationales - Zakia Khattabi - samedi 24 octobre 2020 - YouTube

http://johnsalvatier.org/blog/2017/reality-has-a-surprising-amount-of-detail | Reality has a surprising amount of detail
https://doyoureadme.de/materials/in-the-summer-of-2009-walter-pfeiffer-matteo-thun?v=d3dcf429c679 | do you read me?! | In the Summer of 2009 – Walter Pfeiffer, Matteo Thun
https://stackoverflow.com/questions/11808949/get-attribute-of-activerecord-object-by-string | ruby on rails - get attribute of ActiveRecord object by string - Stack Overflow
https://stackoverflow.com/questions/40549332/how-to-check-if-ssh-agent-is-already-running-in-bash | linux - How to check if ssh-agent is already running in bash? - Stack Overflow
https://www.lesoir.be/503457/article/2023-03-27/partis-politiques-comment-les-presidents-gerent-leur-communication-infographies | Partis politiques: comment les présidents gèrent leur communication (infographies) - Le Soir
https://www.lesoir.be/503055/article/2023-03-24/pierre-yves-dermagne-quel-effort-budgetaire-evitons-de-faire-peur-la-population | Pierre-Yves Dermagne: «Quel effort budgétaire? Evitons de faire peur à la population» - Le Soir
https://www.lesoir.be/336346/sections/lena | Lena - Le Soir

https://developer.hashicorp.com/terraform/tutorials/configuration-language/provider-versioning | Lock and Upgrade Provider Versions | Terraform | HashiCorp Developer
https://developer.hashicorp.com/terraform/language/values/variables | Input Variables - Configuration Language | Terraform | HashiCorp Developer
https://stackoverflow.com/questions/50468798/how-to-define-optional-variables-in-terraform-with-default-values-defined-in-con | How to define optional variables in Terraform with default values defined in Consul - Stack Overflow
https://github.com/cyu/rack-cors | GitHub - cyu/rack-cors: Rack Middleware for handling Cross-Origin Resource Sharing (CORS), which makes cross-origin AJAX possible.

https://developer.crowdin.com/in-context-localization/ | In-Context Setup | Crowdin Developer Portal
https://www.google.be/maps/place/Congo-Brazzaville/@21.0709978,31.4899408,7z/data=!4m6!3m5!1s0x1a60a563dda31309:0x55017acf14fc6c8f!8m2!3d-0.228021!4d15.827659!16zL20vMDFyeHc | Congo-Brazzaville - Google Maps

https://obdev.at/products/littlesnitch-mini/index.html | Little Snitch Mini
https://apps.apple.com/us/app/taptyping-typing-trainer/id376526006?platform=iphone | TapTyping - typing trainer on the App Store

https://docs.logseq.com/#/page/contents | Contents
https://music.stackexchange.com/questions/129033/is-there-a-way-to-rehearse-being-in-a-stressful-performance-situation | performing - Is there a way to rehearse being in a stressful performance situation? - Music: Practice & Theory Stack Exchange

https://www.bonnegueule.fr/conseils-comment-choisir-porter-chino-homme/ | Comment choisir et porter un chino homme | BONNEGUEULE
https://www.bonnegueule.fr/-comment-creer-des-tenues-pas-basiques-avec-des-basiques/ | Vidéo style homme : 5 looks pas basiques avec des basiques 
https://fr.zalando.be/pier-one-basic-thongs-3-pack-string-redgreyblack-pi982o08i-g11.html | Pier One 3 PACK - Slip - red/grey/black/rouge - ZALANDO.BE
http://www.privejoke.com/ | privejoke.com/
https://www.google.be/maps/place/Nong+Cha/@50.8489863,4.3476023,3a,36.1y,317.99h,102.23t/data=!3m8!1e1!3m6!1sAF1QipNVuWu9BzCXiA4QUvKFt1l9GF8-2Euz_TRDseEG!2e10!3e2!6shttps:%2F%2Flh5.googleusercontent.com%2Fp%2FAF1QipNVuWu9BzCXiA4QUvKFt1l9GF8-2Euz_TRDseEG%3Dw203-h100-k-no-pi-10-ya320-ro0-fo100!7i13312!8i6656!4m6!3m5!1s0x47c3c387ec5ab573:0xe8fef6702540f08d!8m2!3d50.8489698!4d4.3475832!16s%2Fg%2F1pp2twxyb | Nong Cha - Google Maps
https://evertpot.com/jwt-is-a-bad-default/ | JWT should not be your default for sessions
https://paseto.io/ | PASETO
https://developer.okta.com/blog/2019/10/17/a-thorough-introduction-to-paseto | A Thorough Introduction to PASETO | Okta Developer
https://www.reddit.com/r/AskUK/comments/zh4dcv/how_popular_is_diane_morgans_philomena_cunk_in/ | How popular is Diane Morgan's Philomena Cunk in the UK? : AskUK
https://www.arbresdusouvenir.com/charte-verte | Charte verte | arbres-du-souvenir
https://jardinierparesseux.com/2015/05/19/anemone-des-bois-meconnue-mais-spectaculaire/ | Anémone des bois: méconnue mais spectaculaire - Jardinier paresseux
https://en.wikipedia.org/wiki/Participatory_democracy | Participatory democracy - Wikipedia
https://en.wikipedia.org/wiki/Uname | uname - Wikipedia

https://www.reddit.com/r/MensLib/comments/xnwiuw/men_should_talk_about_their_emotions_but_men/ | Men should talk about their emotions BUT men shouldn't treat women like therapists | How to do both and know what a good support network looks like (a guide) : MensLib
https://www.vox.com/future-perfect/23141405/violence-crime-cbt-therapy-cash-shootings | Liberia’s stunningly effective way to reduce shootings and other crimes - Vox
https://stackoverflow.com/questions/11406410/measure-and-benchmark-time-for-ruby-methods | Measure and Benchmark Time for Ruby Methods - Stack Overflow
https://en.wikipedia.org/wiki/Franz_Ferdinand_(band) | Franz Ferdinand (band) - Wikipedia

https://www.startpage.com/do/dsearch?query=carrierwave+batch&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche
https://www.reddit.com/r/MensLib/comments/dvuqzh/wrt_mens_limited_fashion_choices/ | w/r/t men's limited fashion choices : MensLib
https://www.reddit.com/r/MensLib/top/?t=all | Be the men's issues conversation you want to see in the world.
https://www.reddit.com/r/berlinsocialclub/comments/sypx2c/what_are_some_fun_nontouristy_things_to_do_in/ | What are some fun non-touristy things to do in Berlin? : berlinsocialclub
https://www.reddit.com/r/germany/comments/92o61n/going_to_berlin_for_5_days_what_to_see_where_to/ | Going to berlin for 5 days. What to see, where to eat. : germany
https://www.reddit.com/r/berlin/wiki/index/#wiki_what_to_see.2Fdo.2Feat.3F | index - berlin

https://www.reddit.com/r/gaymers/comments/11sfpbi/does_anyone_know_any_good_webtoons_with_a_gay/ | Does anyone know any good Webtoons with a gay male main character? : gaymers
https://www.startpage.com/do/dsearch?query=Full+Volume+webtoon&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche

https://fr.wikipedia.org/wiki/Audrey_Hepburn | Audrey Hepburn — Wikipédia
https://www.startpage.com/do/dsearch?query=orttu&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la Recherche

https://forums.docker.com/t/what-are-the-steps-when-upgrading-docker-engine-on-swarm/112691 | What are the steps when upgrading docker engine on swarm? - Open Source Projects / Swarm - Docker Community Forums
https://forums.docker.com/t/graceful-restart-of-swarm-manager-leader/114510 | Graceful restart of swarm manager leader - Open Source Projects / Swarm - Docker Community Forums
https://docs.docker.com/engine/install/ubuntu/#install-using-the-repository | Install Docker Engine on Ubuntu
https://docs.docker.com/engine/swarm/admin_guide/#back-up-the-swarm | Administer and maintain a swarm of Docker Engines
https://serverfault.com/questions/884123/status-error-response-from-daemon-node-elk12-is-ambiguous-2-matches-found-c | docker - Status: Error response from daemon: node elk12 is ambiguous (2 matches found), Code: 1 - Server Fault
https://github.com/moby/moby/issues/24106 | Swarm node duplicated if it leaves and rejoins · Issue #24106 · moby/moby

https://www.postgresql.org/docs/current/using-explain.html | PostgreSQL: Documentation: 15: 14.1. Using EXPLAIN
https://www.ikea.com/be/fr/p/slibb-filet-a-linge-blanc-50430432/ | SLIBB Filet à linge, blanc - IKEA Belgique
https://physics.stackexchange.com/questions/754920/gravity-vs-em-action-at-a-distance | special relativity - Gravity vs. EM: action at a distance - Physics Stack Exchange
https://makandracards.com/makandra/486038-rails-how-to-restore-a-postgres-dump-from-the-past | Rails: How to restore a postgres dump from the past - makandra dev

https://www.superprof.be/graduated-professional-make-artist-with-experience-photography-fashion-beauty-make-focusing-your.html | Gabriela - Vilvorde,Brabant flamand : EN/FR/ES I'm a graduated professional Make-up Artist with experience in photography, fashion & beauty Make-up. Focusing on your personality, face type & complexion, I will give you the best advice to
https://www.startpage.com/do/dsearch?query=make+up+school+bruxelles&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results

https://www.reddit.com/r/piano/comments/7porbg/practicing_for_the_absolute_beginner_where_and/ | Practicing for the absolute beginner: where and how to start playing the piano. : piano
https://opencritic.com/game/14227/hi-fi-rush | Hi-Fi Rush Reviews - OpenCritic
https://www.vetandgo.be/en/ | Veterinary emergency in Brussels - VET&GO

https://twitter.com/ri_not_ri/status/1630210135145455623 | Rinat on Twitter: "There are so many debates about US or European colonialism and imperialism. Russians love to push this discussion. But it's usually forgotten that Russian Federation is not a country. It’s a colonial empire that oppresses indigenous cultures. https://t.co/Ip3zf0jsZ1" / Twitter
https://www.hircus.fr/cachemire-homme/1864-6381-pull-col-rond-blanc-tapenade-jacquard-arman.html#/3-taille-m/209-couleur-blanc_et_tapenade | Pull Arman
https://www.reddit.com/r/vosfinances/comments/r9izoz/marque_de_v%C3%AAtements_de_qualit%C3%A9/ | Marque de vêtements de qualité : vosfinances
https://seagale.fr/en/home/<USER>
https://www.hast.fr/fr/content/45-boutique | Boutiques
https://www.loom.fr/collections/vestiaire-homme | Vestiaire Homme · Loom

https://www.reddit.com/r/malefashionadvice/comments/4jc8ge/books_about_mens_fashion/ | Books About Men's Fashion : malefashionadvice
https://libgen.rs/book/index.php?md5=E741A2162D72DE2D5122228A9CF290D1 | Library Genesis: Alan Flusser - Dressing the Man: Mastering the Art of Permanent Fashion
https://www.reddit.com/r/malefashionadvice/comments/789ldf/an_indepth_guide_on_starting_to_dress_well/ | An In-Depth Guide on Starting to Dress Well : malefashionadvice

https://fr.zalando.be/slips-calecons-homme/diesel/ | Slips & Caleçons homme Diesel• Achetez en ligne | Zalando
https://www.reddit.com/r/france/comments/t9qod7/quelle_marque_pour_un_bon_chino/ | Quelle marque pour un bon chino ? : france
https://www.1083.fr/163-chino-ajuste-filidenim-flex-uni-vert-emeraude.html | Chinos 163 Chino Ajusté FiliDenim Flex Uni Vert Emeraude en coton biologique pour Homme - 1083
https://www.commeuncamion.com/2018/02/13/10-marques-de-chinos-a-connaitre/ | 10 marques de chino pour homme à connaître | Comme un camion
https://fr.zalando.be/pierre-cardin-chino-marine-p3222e077-k11.html | Pierre Cardin Chino - marine/bleu marine - ZALANDO.BE
https://fr.zalando.be/pulls-gilets-homme/_beige.jaune.or.orange/ | Maille pour homme | Zalando Belgique
https://www.reddit.com/r/brussels/comments/blmr4f/where_are_the_coolest_mens_clothing_stores/ | Where are the coolest men's clothing stores? : brussels
https://www.bellerose.be/collections/men/knitwear?shopify_products%5Bpage%5D=2 | New men's collection | available online | Bellerose official e-shop – translation missing: en.general.meta.tags

https://news.ycombinator.com/item?id=35092031 | I use cheap notebooks | Hacker News
https://www.trymito.io/ | Mito | Home
https://ppn.snovvcrash.rocks/ | README - Pentester's Promiscuous Notebook

https://www.esprityoga.fr/accepter-et-agir/ | Comment trouver l'équilibre entre une juste indignation et une acceptation sereine ? - Esprit Yoga
https://news.ycombinator.com/item?id=28058466 | Pitfalls of Data Anonymization | Hacker News
https://github.com/gitopsbook/resources | gitopsbook/resources: This repository contains the code listings, examples and other resources for the book "GitOps and Kubernetes", written by Jesse Suen, Alex Matyushentsev, Billy Yuen and Todd Ekenstam, published by Manning Publications.

https://stackoverflow.com/questions/61090644/terraform-cloud-enterprise-how-to-use-aws-assume-roles | amazon web services - Terraform Cloud / Enterprise - How to use AWS Assume Roles - Stack Overflow
https://www.lesoir.be/499746/article/2023-03-08/philippe-geluck-je-fais-un-peu-clown-dans-le-monde-de-lart | Philippe Geluck: «Je fais un peu clown dans le monde de l’art» - Le Soir
https://medor.coop/magazines/medor-n30-hiver-2023-2024/a-louest-dherstal-armes-usa-tueries-fn-herstal-ultradoite/ | À l’Ouest d’Herstal - Médor
https://public.flourish.studio/visualisation/12538855/ | Dividendes 2018-2022 | Flourish

https://registry.terraform.io/providers/hashicorp/local/latest/docs/resources/file | local_file | Resources | hashicorp/local | Terraform Registry
https://mubi.com/letterboxd?utm_source=letterboxd&utm_medium=email&utm_campaign=letterboxd_aftersun_rushes_2023 | Three Months of Cinema
https://www.metacritic.com/search/game/cities%20skylines/results?sort=recent | cities skylines - Reviews, Articles, People, Trailers and more at Metacritic - Metacritic
https://jonathan.bergknoff.com/journal/building-good-docker-images/ | Jonathan Bergknoff: Building Good Docker Images
https://www.65degres.be/menu-soir | Menu du Soir | 65degres

https://blog.appsignal.com/2020/04/14/dissecting-rails-migrationsl.html | Dissecting Rails Migrations | AppSignal Blog
https://www.lesoir.be/498695/article/2023-03-03/plongee-dans-la-gestion-des-ordures-de-bruxelles-les-sept-dechets-capitaux | Plongée dans la gestion des ordures de Bruxelles: les sept déchets capitaux - Le Soir

https://rubygems.org/gems/google-api-client | google-api-client | RubyGems.org | your community gem host
https://www2.lib.uchicago.edu/keith/emacs/#orgeefcd77 | Use GNU Emacs
https://askubuntu.com/questions/1120023/how-to-use-systemd-notify | notification - How to use systemd notify - Ask Ubuntu
https://missing.csail.mit.edu/2020/data-wrangling/ | Data Wrangling · Missing Semester
https://stackoverflow.com/questions/41593135/does-putting-arg-at-top-of-dockerfile-prevent-layer-re-use | docker - Does putting ARG at top of Dockerfile prevent layer re-use? - Stack Overflow
https://ronin-rb.dev/docs/tutorials/everyday_ronin.html | Everyday Ronin | Ronin
https://www.instagram.com/henrikahm/?hl=en | Henrik Åhm (@henrikahm) • Instagram photos and videos

https://www.gasparevitta.com/posts/advanced-docker-multistage-parallel-build-buildkit/ | Advanced Docker: Multistage parallel Docker build | Blog by Gaspare Vitta
https://jeanklaas.com/blog/efficient-docker-containers-with-multi-stage-builds/ | Efficient docker containers with multi-stage builds ｜ Jean-Klaas Gunnink
https://unix.stackexchange.com/questions/167610/determining-if-a-file-is-a-hard-link-or-symbolic-link | shell script - Determining if a file is a hard link or symbolic link? - Unix & Linux Stack Exchange

https://www.chocho.be/#CHOCHO | CHO CHO Gourmandise Argentina Alfajores Dulce de Leche Bio Artisanal
https://deepsource.io/directory/analyzers/docker/issues/DOK-DL3025 | Use arguments JSON notation for CMD and ENTRYPOINT arguments - DeepSource

https://www.google.be/maps/dir/50.8536739,4.3886945/Vandenhende,+Waversesteenweg+643,+1040+Etterbeek/@50.826403,4.3882557,16z/data=!3m1!4b1!4m9!4m8!1m0!1m5!1m1!1s0x47c3c4bbdf798197:0x7bdb423979d644b6!2m2!1d4.389069!2d50.8309936!3e3 | Jacques Jansenstraat 17, 1030 Schaarbeek naar Vandenhende - Google Maps
https://lipanski.com/posts/dockerfile-ruby-best-practices#4-add-a-dockerignore-file-to-your-repository | Best practices when writing a Dockerfile for a Ruby application | Florin Lipan
https://www.weave.works/blog/gitops-operations-by-pull-request | ​GitOps - Operations by Pull Request
https://kubernetes.io/docs/concepts/overview/components/#control-plane-components | Kubernetes Components | Kubernetes
https://www.everythingcli.org/find-exec-vs-find-xargs/ | find -exec vs find | xargs
https://unix.stackexchange.com/questions/46322/how-can-i-recursively-delete-empty-directories-in-my-home-directory | linux - how can I recursively delete empty directories in my home directory? - Unix & Linux Stack Exchange
https://docs.docker.com/build/building/multi-stage/ | Multi-stage builds

https://www.youtube.com/watch?v=GimozkGUwKw | Strengthening the soleus muscle and why you should do it heavy | The MSK Physio - YouTube

https://www.metacritic.com/game/playstation-4/lovers-in-a-dangerous-spacetime | Lovers in a Dangerous Spacetime for PlayStation 4 Reviews - Metacritic
https://www.google.be/maps/place/Montenegro/@42.6853387,20.7889582,5z/data=!4m6!3m5!1s0x134c3217242c1fcb:0x384c9d866effd346!8m2!3d42.708678!4d19.37439!16zL20vMDU2dnY | Montenegro - Google Maps
https://stackoverflow.com/questions/4200041/how-to-profile-rake-task | How to profile Rake task? - Stack Overflow

https://www.fullstacklabs.co/blog/parallelizing-tests-circleci-collecting-coverage-codeclimate | Parallelized Coverage Tests with CircleCi and CodeClimate
https://stackoverflow.com/questions/11580961/sending-command-line-arguments-to-npm-script | javascript - Sending command line arguments to npm script - Stack Overflow
https://www.startpage.com/do/dsearch?query=run+command+in+the+context+of+npm&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://boilingsteam.com/steam-deck-first-anniversary-of-the-ultimate-gaming-platform/ | Steam Deck: First Anniversary of the Ultimate Gaming Platform - Boiling Steam
https://engineering.later.com/2018/08/22/faster-circle-ci-jobs.html | Faster Circle CI Builds for Rails Apps | Engineering at Later
https://isthisarabic.com/ | Help, is this عربي?

https://www.datadoghq.com/knowledge-center/distributed-tracing/flame-graph/ | What is a Flame Graph? How it Works & Use Cases | Datadog
https://docs.greatexpectations.io/docs/tutorials/getting_started/tutorial_overview | Getting started with Great Expectations | Great Expectations
https://k6.io/ | Load testing for engineering teams | Grafana k6
https://wiki.ubuntu.com/UnitsPolicy | UnitsPolicy - Ubuntu Wiki
https://www.tomchapin83.com/tricks-with-pry-live-editing/ | Tricks with Pry – Live Editing – Tom Chapin (Thomas H. Chapin IV)

https://www.reddit.com/r/crossfit/comments/vdaer8/best_shoes_for_cardio_and_weightlifting/ | Best shoes for cardio and weightlifting : crossfit
https://www.nytimes.com/wirecutter/reviews/best-cross-training-shoes/ | The Best Cross-Training Shoes | Reviews by Wirecutter
https://www.newbalance.be/fr/acheter-par-modele/toutes-les-minimus/ | Chaussures de Trail Minimus - New Balance
https://www.reddit.com/r/crossfit/comments/nps19a/new_balance_minimus_tr/ | New Balance Minimus TR? : crossfit
https://www.zalando.be/inov-8/ | Inov-8 online shop | Snelle verzending | Zalando
https://linux.die.net/man/1/expect | expect(1) - Linux man page
https://www.schneier.com/books/ | Schneier on Security: Books by Bruce Schneier

https://stackoverflow.com/questions/12617152/how-do-i-create-directory-if-none-exists-using-file-class-in-ruby | How do I create directory if none exists using File class in Ruby? - Stack Overflow
https://stackoverflow.com/questions/50217304/copy-file-from-remote-docker-container | ssh - Copy file from remote docker container - Stack Overflow
https://github.com/jlfwong/speedscope/wiki/Importing-from-stackprof-(ruby) | Importing from stackprof (ruby) · jlfwong/speedscope Wiki
https://gist.github.com/BideoWego/973df3cf566b99513da8 | Rails Spec setup checklist, helper and support files

https://news.ycombinator.com/item?id=34710830 | Ask HN: How do you deal with information and internet addiction? | Hacker News
https://thoughtbot.com/blog/use-factory-bots-build-stubbed-for-a-faster-test | Use Factory Bot's build_stubbed for a Faster Test Suite
https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/configuring-pull-request-merges/managing-a-merge-queue | Managing a merge queue - GitHub Docs
https://blog.humphd.org/cheatgpt/ | CheatGPT
http://bruxelles-bruxellons.blogspot.com/2013/01/bruxelles-sucre.html#links | Bruxelles-Bruxellons: Bruxelles Sucré - Itinéraire gourmand
https://aparte-editions.be/livres/bruxelles-tendances | Bruxelles tendances – Livres – Aparté Editions
https://www.lesoir.be/496520/article/2023-02-21/la-liste-des-restaurants-belges-aureoles-dun-bib-gourmand-alliant-qualite-et | La liste des restaurants belges auréolés d’un «Bib Gourmand» alliant «qualité et prix serrés» - Le Soir

https://doyoureadme.de/shop/food-drinks/cooking-with-scorsese-the-collection?v=6bf791021335&mc_cid=e01bf3d62c&mc_eid=595c5bb650 | do you read me?! | Shop
https://lesnourritures.be/le-qi-beesbees-coop?utm_source=BEES+coop+Membres&utm_campaign=acce307e33-EMAIL_CAMPAIGN_2023_02_02_01_37&utm_medium=email&utm_term=0_-acce307e33-%5BLIST_EMAIL_ID%5D | LE QI-BEES@BEES coop | Les Nourritures
https://www.reeditionmagazine.com/replica | REPLICA Man Magazine Fashion, Art, Ideas, Music, Film

https://fr.zalando.be/sweatshirts-hoodies-homme/kaotiko/?p=2 | Sweats et hoodies Kaotiko homme | Zalando Belgique - Page 2
https://www.reddit.com/r/brussels/comments/wnx5q4/unique_restaurants_in_brussels/ | Unique restaurants in Brussels? : brussels
https://www.quincaillerie.be/en/ | La Quincaillerie
https://www.socialdeal.be/#join | Social Deal, découvrez votre ville pour un prix imbattable - Les meilleurs deals !
https://www.instagram.com/nightshop.brussels/ | N I G H T S H O P (@nightshop.brussels) • Instagram photos and videos
https://www.google.be/maps/place/Racines/@50.8281605,3.2503979,9z/data=!4m10!1m2!2m1!1sRacine!3m6!1s0x47c3c492e250cce1:0xd90c300543f120da!8m2!3d50.8281605!4d4.3710034!15sCgZSYWNpbmVaCCIGcmFjaW5lkgESaXRhbGlhbl9yZXN0YXVyYW504AEA!16s%2Fg%2F11bysjnc76 | Racines - Google Maps

https://roadmap.sh/frontend | Frontend Developer Roadmap
https://roadmap.sh/computer-science | Computer Science Roadmap: Curriculum for the self taught developer
https://developer.mozilla.org/en-US/docs/Learn/Common_questions/Web_mechanics/What_is_a_domain_name | What is a Domain Name? - Learn web development | MDN
https://news.ycombinator.com/item?id=14104495 | Low-Level Programming University – A roadmap to becoming a low-level programmer | Hacker News
https://matt.sh/howto-c | How to C (as of 2016)
https://coderwall.com/p/whjmra/handling-exceptions-in-your-rails-application | Handling Exceptions in your Rails Application (Example)
https://mattbrictson.com/dynamic-rails-error-pages | Dynamic Rails Error Pages | mattbrictson.com
https://blog.saeloun.com/2021/07/07/ruby-3-1-pattern-matching-pin-operator.html | Ruby 3.1 introduces pattern matching pin operator against expression | Saeloun Blog
https://womanonrails.com/ruby-pattern-matching-second-look | Second look at pattern matching in Ruby
https://www.theodinproject.com/lessons/ruby-pattern-matching | Pattern Matching | The Odin Project
https://opentelemetry.io/ | OpenTelemetry
https://bundler.io/guides/groups.html | Bundler: How to manage groups of gems

https://miro.com/app/board/uXjVPnKFBIk=/ | Name brainstorming_Shared, Online Whiteboard for Visual Collaboration
https://www.postgresql.org/docs/current/sql-expressions.html | PostgreSQL: Documentation: 15: 4.2. Value Expressions
https://stackoverflow.com/questions/6133107/extract-date-yyyy-mm-dd-from-a-timestamp-in-postgresql | sql - Extract date (yyyy/mm/dd) from a timestamp in PostgreSQL - Stack Overflow
https://serokell.io/blog/rust-vs-haskell | Rust vs. Haskell

https://gist.github.com/janko/238bbcc78b369ce3438365e5507bc671 | Memory profiling of http.rb and other popular Ruby HTTP client libraries
https://github.com/ffi/ffi | ffi/ffi: Ruby FFI
https://www.rfc-editor.org/rfc/rfc2396#section-5.2 | RFC 2396: Uniform Resource Identifiers (URI): Generic Syntax

https://intellij-support.jetbrains.com/hc/en-us/community/posts/*********-Goto-symbol-in-current-file- | Goto symbol in current file. – IDEs Support (IntelliJ Platform) | JetBrains
https://fr.wikipedia.org/wiki/Vo%C3%BBtement_de_la_Senne | Voûtement de la Senne — Wikipédia
https://gitlab.com/gitlab-org/gitlab-foss/-/issues/62692 | Consider removing HTTParty from our codebase (#62692) · Issues · GitLab.org / GitLab FOSS · GitLab
https://github.com/jeremyevans/roda | jeremyevans/roda: Routing Tree Web Toolkit
https://github.com/jeremyevans/r10k | jeremyevans/r10k: Comparative ruby web application benchmarks with a large number of routes
https://serpapi.com/blog/how-to-detect-memory-leak-in-ruby-c-extension/ | How to detect memory leak in Ruby C extension
https://github.com/google/sanitizers/wiki/AddressSanitizerComparisonOfMemoryTools | AddressSanitizerComparisonOfMemoryTools · google/sanitizers Wiki
https://github.com/google/sanitizers | google/sanitizers: AddressSanitizer, ThreadSanitizer, MemorySanitizer

https://news.ycombinator.com/item?id=******** | Visual design rules you can safely follow | Hacker News
https://www.startpage.com/do/dsearch?query=+Chesterton%27s+Fence.&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results

http://paulgraham.com/articles.html | Essays
https://en.wikipedia.org/wiki/Japanese_honorifics#Kun | Japanese honorifics - Wikipedia
https://dictionary.cambridge.org/grammar/british-grammar/may-as-well-and-might-as-well | May as well and might as well - Cambridge Grammar

https://www.jamesshore.com/v2/books/aoad2/book_club/refactoring | James Shore: Agile Book Club: Refactoring (with Martin Fowler)
https://store.steampowered.com/news/app/632470/view/2917732722272981875 | Disco Elysium - The Final Cut - Disco Elysium is now available in French, German and Traditional Chinese! - Steam News
https://stackoverflow.com/questions/726690/what-killed-my-process-and-why | linux - What killed my process and why? - Stack Overflow
https://explainshell.com/explain?cmd=dmesg+-T%7C+grep+-E+-i+-B100+%27killed+process%27 | explainshell.com - dmesg -T| grep -E -i -B100 'killed process'
https://www.metacritic.com/game/xbox-one/outer-wilds | Outer Wilds for Xbox One Reviews - Metacritic
https://www.startpage.com/do/dsearch?query=homemade+kombucha&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
http://paulgraham.com/simply.html | Write Simply
https://zoomescaper.com/ | Zoom Escaper
https://www.startpage.com/do/dsearch?query=visualize+ruby+memory+heap&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://blog.urban-challenge.fr/fasting-jeune-intermittent-et-sport-font-ils-bon-menage/ | Fasting : jeûne intermittent et sport font-ils bon ménage ? - Blog Urban Challenge
https://www.crossfit.com/essentials/intermittent-fasting-and-the-crossfit-prescription-for-nutrition | CrossFit | Intermittent Fasting and the CrossFit Prescription for Nutrition
https://www.ricardocuisine.com/recettes/9400-bols-de-lentilles-quinoa-tatsoi-et-uf-poche | Bols de lentilles, quinoa, tatsoï et œuf poché | RICARDO

https://app.segment.com/citizen-lab/overview/destination-setup/choose | Reverse ETL Sources - Segment
https://www.w3schools.com/sql/sql_case.asp | SQL CASE Expression
https://www.reddit.com/r/devops/comments/y6osu8/what_are_some_of_your_core_devops_books/ | What are some of your core devops books? : devops
https://about.gitlab.com/topics/gitops/ | What is GitOps? | GitLab
https://www.reddit.com/user/saltyvagrant/comments/x4q9bu/devops_for_beginners/ | DevOps for Beginners : saltyvagrant
https://commitizen-tools.github.io/commitizen/ | Commitizen
https://roadmap.sh/devops | DevOps Roadmap: Learn to become a DevOps Engineer or SRE
https://roadmap.sh/postgresql-dba | DBA Roadmap: Learn to become a database administrator with PostgreSQL
https://github.com/hadolint/hadolint | hadolint/hadolint: Dockerfile linter, validate inline bash, written in Haskell
https://excalidraw.com/ | Excalidraw

https://sosoir.lesoir.be/journee-internationale-de-la-pizza-quelles-sont-les-pizzerias-preferees-des-italiens-de-bruxelles | Journée internationale de la pizza : quelles sont les pizzerias préférées des Italiens de Bruxelles ?

https://jonathanhaidt.substack.com/p/the-teen-mental-illness-epidemic | The Teen Mental Illness Epidemic Began Around 2012
https://news.ycombinator.com/item?id=34710830 | Ask HN: How do you deal with information and internet addiction? | Hacker News
https://www.reddit.com/r/socialskills/comments/ybu2k5/why_do_i_never_have_anything_to_say/ | Why do I never have anything to say? : socialskills
https://www.reddit.com/r/socialskills/comments/tkahxq/running_out_of_things_to_say_during_a/ | Running out of things to say during a conversation? Heres a little trick. : socialskills
https://www.reddit.com/r/socialskills/comments/aep2h8/why_ford_is_bullshit_why_hefe_is_way_better/?utm_source=share&utm_medium=ios_app&utm_name=iossmf | Why FORD is bullshit & why HEFE is way better. : socialskills
https://bandcamp.com/?show=622 | Bandcamp
https://www.discogs.com/ | Discogs - Music Database and Marketplace
https://www.discogs.com/search/?sort=hot%2Cdesc&ev=em_rs&genre_exact=Pop | Pop music | Discogs

https://stackoverflow.com/questions/2752231/random-record-in-activerecord | ruby on rails - Random record in ActiveRecord - Stack Overflow
https://about.gitlab.com/handbook/business-technology/data-team/platform/sql-style-guide/ | SQL Style Guide | GitLab
https://gitlab.com/gitlab-data/analytics/-/blob/master/transform/snowflake-dbt/.sqlfluff | transform/snowflake-dbt/.sqlfluff · master · GitLab Data / GitLab Data Team · GitLab
https://about.gitlab.com/handbook/business-technology/data-team/platform/dbt-guide/ | dbt Guide | GitLab
https://github.com/markets/awesome-ruby#template-engine | markets/awesome-ruby: A collection of awesome Ruby libraries, tools, frameworks and software
https://matomo.org/faq/how-to/faq_20991/ | How do I view Matomo application logs? FAQ - Analytics Platform - Matomo
https://docs.docker.com/engine/reference/commandline/network_inspect/ | docker network inspect | Docker Documentation
https://github.com/zdennis/activerecord-import | zdennis/activerecord-import: A library for bulk insertion of data into your database using ActiveRecord.
https://docs.gitlab.com/ee/development/database/batched_background_migrations.html#isolation | Batched background migrations | GitLab
https://plugins.jetbrains.com/plugin/9862-custom-postfix-templates | Custom Postfix Templates - IntelliJ IDEs Plugin | Marketplace
https://github.com/xylo/intellij-postfix-templates#custom-postfix-templates-for-intellij-idea | xylo/intellij-postfix-templates: Custom Postfix Templates for Intellij IDEA

https://www.reddit.com/r/brussels/comments/10vacm9/any_ideas_for_cheap_hot_lunches/ | Any ideas for cheap, hot lunches? : brussels

https://www.lesoir.be/493354/article/2023-02-05/les-flagey-piano-days-un-festival-dans-la-creativite | Les Flagey Piano Days: un festival dans la créativité - Le Soir
https://www.reddit.com/r/HaircareScience/comments/1km6dx/basic_haircare_guide/ | Basic Haircare Guide : HaircareScience
https://fosdem.org/2023/schedule/event/postgresql_deep_dive_into_query_performance/ | FOSDEM 2023 - Deep Dive Into Query Performance
https://archive.fosdem.org/2022/schedule/event/postgresql_json_in_postgresql_learning_with_a_case_study/ | FOSDEM 2022 - JSON in PostgreSQL - Learning with a case study
https://relishapp.com/rspec/rspec-mocks/v/3-12/docs/configuring-responses/calling-the-original-implementation | Calling the original implementation - Configuring responses - RSpec Mocks - RSpec - Relish
https://stackoverflow.com/questions/1710004/how-to-check-if-a-model-has-a-certain-column-attribute | ruby on rails - How to check if a model has a certain column/attribute? - Stack Overflow
https://stackoverflow.com/questions/72249260/rails-update-all-from-associated-object | update all - Rails update_all from associated_object - Stack Overflow
https://stackoverflow.com/questions/6831069/doing-an-update-all-with-joins-in-rails | sql - Doing an update_all with joins in Rails - Stack Overflow
https://www.lesoir.be/492813/article/2023-02-02/het-verhaal-van-vlaanderen-quand-la-flandre-se-passionne-et-se-clashe-sur-son | «Het verhaal van Vlaanderen»: quand la Flandre se passionne et se clashe sur son histoire - Le Soir
https://www.reddit.com/r/gaybros/comments/10s6mgk/what_books_are_you_reading_at_the_moment/ | What books are you reading at the moment? : gaybros
https://www.reddit.com/r/gaybros/comments/10rbq3v/selfcare_hobbies/ | Self-Care & Hobbies : gaybros

https://fr.shopping.rakuten.com/search/njoy | Njoy neuf et occasion - Achat pas cher | Rakuten
https://genius.com/Jon-batiste-i-need-you-lyrics | Jon Batiste – I NEED YOU Lyrics | Genius Lyrics
https://groups.google.com/a/citizenlab.co/g/dev-bot?pli=1 | dev-bot - Google Groups

https://news.ycombinator.com/item?id=34579175 | Ask HN: Something you’ve done your whole life that you realized is wrong? | Hacker News
https://www.youtube.com/watch?v=DUfVI9RRrrE | Medium raw. Anthony Bourdain in conversation - YouTube
https://enchroma.com/ | EnChroma® Color Blind Glasses | Cutting-Edge Lens Technology
https://www.metacritic.com/tv/happy-valley/season-3 | Happy Valley - Season 3 Reviews - Metacritic

https://www.jetbrains.com/help/ruby/2022.1/apply-changes-from-one-branch-to-another.html | Apply changes from one Git branch to another | RubyMine
https://www.startpage.com/do/dsearch?query=reddit+most+realistic+simulation+games&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://www.reddit.com/r/gamingsuggestions/comments/njtwvj/realistic_simulator_pc_games_suggestions/ | Realistic simulator PC games suggestions? : gamingsuggestions
https://www.reddit.com/r/patientgamers/comments/ph6gy5/your_favorite_games_that_made_you_learn_real_life/ | Your favorite games that made you learn real life concepts? : patientgamers
https://www.adultswim.com/ | Adult Swim
https://www.reddit.com/r/AskWomen/comments/13hzzo/how_do_you_get_your_hair_so_shiny/ | How do you get your hair so shiny? : AskWomen
https://gamefaqs.gamespot.com/boards/711433-rogue-legacy/66696486 | Your thoughts on the story (spoilers) - Rogue Legacy
https://www.metacritic.com/tv/frozen-planet-ii/season-1 | Frozen Planet II - Season 1 Reviews - Metacritic
https://sosoir.lesoir.be/le-shadow-journal-la-methode-ideale-pour-mieux-se-connaitre-grace-lecriture | Le shadow journal, la méthode idéale pour mieux se connaître grâce à l'écriture
https://sosoir.lesoir.be/le-village-le-plus-paradisiaque-deurope-se-trouve-moins-de-3-heures-de-bruxelles | Le village le plus paradisiaque d'Europe se trouve à moins de 3 heures de Bruxelles
https://www.eatmy.art/books/silly-faces/ | Activity Book - Extremely Silly Faces | Arteater
https://fr.wiktionary.org/wiki/halouf | halouf — Wiktionnaire

https://circleci.com/docs/add-ssh-key/ | Add additional SSH keys to CircleCI - CircleCI
https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent | Generating a new SSH key and adding it to the ssh-agent - GitHub Docs
https://www.ssh.com/academy/ssh/keygen | What is ssh-keygen & How to Use It to Generate a New SSH Key?
https://www.edx.org/course/introduction-to-linux-system-administration-with-ibm-power-systems?index=product&queryID=538e281f1c074172b27d025699819f84&position=3 | Introduction to Linux System Administration with IBM Power Systems | edX

https://docs.docker.com/engine/reference/commandline/compose_restart/ | docker compose restart | Docker Documentation
https://stackoverflow.com/questions/42149529/how-to-reload-environment-variables-in-docker-compose-container-with-minimum-dow | How to reload environment variables in docker-compose container with minimum downtime? - Stack Overflow
https://www.bedetheque.com/BD-Ogres-Dieux-Tome-1-Petit-230942.html | Les ogres-Dieux -1- Petit

https://rawg.io/ | The Biggest Video Game Database on RAWG - Video Game Discovery Service
https://www.backloggd.com/ | Backloggd - A Video Game Collection Tracker
https://ggapp.io/search/elden%20ring | Search - GG| Video Game Collection Tracker
https://en.wikipedia.org/wiki/Game_Developers_Choice_Awards | Game Developers Choice Awards - Wikipedia
https://en.wikipedia.org/wiki/D.I.C.E._Awards | D.I.C.E. Awards - Wikipedia
https://en.wikipedia.org/wiki/British_Academy_Film_Awards | British Academy Film Awards - Wikipedia
https://en.wikipedia.org/wiki/British_Academy_Games_Awards | British Academy Games Awards - Wikipedia

https://www.metacritic.com/movie/aftersun | Aftersun Reviews - Metacritic
https://www.reddit.com/r/brussels/comments/107ncuq/new_in_schaarbeek_some_fun_things_to_doseeplaces/ | PiegeAgo (u/PiegeAgo) - Reddit
https://www.reddit.com/r/brussels/comments/10359r4/high_end_vietnamese_or_japanese_restaurants_in/ | High end Vietnamese or Japanese restaurants in Brussels. : brussels
http://www.nenu.be/ | Home | nénu
http://www.motorsformakers.com/ | Motors for Makers: A Guide to Steppers, Servos, and Other Electrical Machines

https://atelierdestropiques.be/formations/ | Formations - Atelier des Tropiques
https://news.ycombinator.com/item?id=34480598 | Signal app's president-“layoffs in tech are to keep salaries in check” | Hacker News
https://twitter.com/mer__edith/status/1616535695216050184 | Meredith Whittaker on Twitter: "Early 2000s profitable startups gave their handful of workers novel perks/freedom. These cos/their workplace culture got big. Late 2010s tech labor gained power + made demands. Now a hint of recession = excuse to break promises/reestablish dominance over workers. It's not about $" / Twitter
https://en.wikipedia.org/wiki/Umask | umask - Wikipedia

https://www.pointculture.be/mediatheque/documentaires/histoires-d-une-utopie-a-vendre-te5280 | HISTOIRE(S) D'UNE UTOPIE À VENDRE

https://github.com/alphagov/tech-docs-monitor | alphagov/tech-docs-monitor: Part of alphagov/tech-docs-template (issues 👉https://github.com/alphagov/tech-docs-template/issues)
https://blog.jetbrains.com/writerside/2022/01/writing-with-style-10-style-guides-to-inspire-you/#The_UK_Government | Writing With Style: 10 Style Guides to Inspire You | On Writer’s Side
https://stackoverflow.com/questions/67971057/how-to-remove-surrounding-code-block-in-intellij-idea | java - How to remove surrounding code block in IntelliJ IDEA? - Stack Overflow
https://www.supermemo.com/en/blog/twenty-rules-of-formulating-knowledge | Effective learning: Twenty rules of formulating knowledge - SuperMemo
https://fr.wikisource.org/wiki/Le_Cimeti%C3%A8re_marin | Le Cimetière marin - Wikisource
https://www.destructoid.com/noelle-warners-favorite-games-of-2022/ | Noelle Warner's favorite games of 2022 – Destructoid
https://www.startpage.com/do/dsearch?query=ractors+ruby&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results

https://www.mediapart.fr/journal/dossier/international/1973-annee-charniere | 1973, année charnière | Mediapart
https://docs.gitlab.com/ee/development/feature_development.html | Feature development | GitLab
https://docs.gitlab.com/ee/development/auto_devops.html | Auto DevOps development guide | GitLab
https://docs.gitlab.com/ee/development/software_design.html | Software design guides | GitLab
https://medium.com/airtribe/enforcing-modularity-inside-a-rails-monolith-f856adb54e1d | Enforcing Modularity inside a Rails Monolith | by Thomas Pagram | The Airtasker Tribe | Medium
https://blog.carbonfive.com/bring-clarity-to-your-monolith-with-bounded-contexts/ | Bring Clarity To Your Monolith with Bounded Contexts

https://www.reddit.com/r/socialskills/comments/ybu2k5/why_do_i_never_have_anything_to_say/ | Why do I never have anything to say? : socialskills
https://www.reddit.com/r/socialskills/comments/tkahxq/running_out_of_things_to_say_during_a/ | Running out of things to say during a conversation? Heres a little trick. : socialskills
https://www.reddit.com/r/socialskills/comments/wmgkyl/why_do_i_never_have_anything_to_talk_about_am_i/ | Why do i NEVER have anything to talk about, am i just dumb? : socialskills

https://www.gq.com/gallery/the-menswear-essentials-youll-need-in-the-new-year#intcid=_gq-right-rail_268cca1b-488f-438c-9914-b0a7937a22dd_popular4-1 | 57 Men's Wardrobe Essentials 2023: A (Somewhat) Exhaustive Guide to Looking Fly All Year | GQ
https://www.youtube.com/watch?v=5yXBGirGPEI | Les coulisses de la plus célèbre carrière de marbre du monde - YouTube

https://www.bhg.com/cluttercore-decor-approach-7094713 | Cluttercore Is Where Sentimentality and Organized Chaos Meet
https://stackoverflow.com/questions/2529441/how-to-read-the-output-from-git-diff | How to read the output from git diff? - Stack Overflow

https://gist.github.com/lyoshenka/8251914 | Search Git commit history for a string and see the diffs · GitHub
https://stackoverflow.com/questions/23897130/using-nested-resources-without-the-parent-id-in-rails | ruby - Using nested resources without the parent ID in Rails - Stack Overflow
https://www.b-list.org/weblog/2022/dec/19/boring-python-code-quality/ | Boring Python: code quality
https://elturco.be/ | El Turco – Mediterranean Restaurant in Elsene
https://www.arte.tv/fr/videos/111710-001-A/ukiyo-e-images-d-un-monde-flottant/ | Ukiyo-e - images d’un monde flottant - Sidi Larbi Cherkaoui au Grand Théâtre de Genève - Regarder le programme complet | ARTE Concert
https://www.east-man.be/en/calendar/ | Eastman | Calendar
https://www.lamonnaiedemunt.be/fr/program/2000-vlaemsch-chez-moi | Vlaemsch (chez moi) | La Monnaie / De Munt
https://hackaday.com/ | Hackaday | Fresh Hacks Every Day

https://www.reddit.com/r/pics/comments/xwtb1s/jimmy_carter_unveiling_solar_panels_atop_the/ | Jimmy Carter unveiling solar panels atop the White House. Ronald Reagan removed them 2 years later. : pics
https://news.ycombinator.com/item?id=31142560 | DaVinci Resolve 18 | Hacker News
https://www.blackmagicdesign.com/products/davinciresolve | DaVinci Resolve 18 | Blackmagic Design
https://stackoverflow.com/questions/15824041/rails-associations-has-one-latest-record | activerecord - Rails Associations has_one Latest Record - Stack Overflow
https://stackoverflow.com/questions/15772134/can-i-delete-a-git-commit-but-keep-the-changes | undo - Can I delete a git commit but keep the changes? - Stack Overflow
https://circleci.com/docs/reusing-config/ | Reusable Config Reference Guide - CircleCI
https://discuss.circleci.com/t/how-to-not-repeat-writing-jobs-with-different-contexts/33926 | How to not repeat writing jobs with different contexts? - Tips, Tricks and Hacks - CircleCI Discuss
https://support.circleci.com/hc/en-us/articles/************-Can-I-split-a-config-into-multiple-files- | Can I split a config into multiple files? – CircleCI Support Center
https://stackoverflow.com/questions/71644592/vcr-not-playing-well-with-circleci | ruby on rails - VCR not playing well with CircleCI - Stack Overflow
https://circleci.com/docs/custom-images/ | Using Custom-Built Docker Images - CircleCI
https://discuss.circleci.com/t/can-docker-images-be-preserved-between-jobs-in-a-workflow-without-a-manual-load-save/23388/10 | Can Docker images be preserved between jobs in a workflow without a manual load/save? - Build Environment - CircleCI Discuss
https://circleci.com/docs/env-vars/ | Introduction to environment variables - CircleCI
https://circleci.com/docs/configuration-reference/#docker | Configuring CircleCI - CircleCI
https://circleci.com/docs/deployment-overview/ | Deployment overview - CircleCI
https://circleci.com/docs/caching/ | Caching Dependencies - CircleCI
https://circleci.com/docs/optimizations/#resource-class | Optimizations Overview - CircleCI
https://circleci.com/blog/tips-for-optimizing-docker-builds/ | Tips for optimizing Docker builds | CircleCI

https://perell.com/essay/whats-up-with-austin/ | What’s Up with Austin? - David Perell
https://en.wikipedia.org/wiki/Japanese_honorifics#Kun | Japanese honorifics - Wikipedia

https://www.volkskrant.nl/columns-opinie/met-mijn-collega-s-in-een-chatkanaal-ik-wil-het-niet~b61f6b80/ | Met mijn collega’s in een chatkanaal, ik wíl het niet | De Volkskrant
https://news.ycombinator.com/item?id=34329351 | Ask HN: What do you talk about in 1-on-1s with your managers? | Hacker News
https://www.linode.com/docs/guides/find-files-in-linux-using-the-command-line/ | How to Find Files in Linux Using the Command Line | Linode

https://docs.google.com/document/d/154qxIGlh1Bap7HWTVh3_qN4voPM0uqfL58DbsoWGOh0/edit#heading=h.yzu0625nbvhr | Project structure thoughts - Google Docs
https://acoup.blog/2022/12/30/miscellanea-whats-the-problem-with-antigone/ | Miscellanea: What’s the Problem With Antigone? – A Collection of Unmitigated Pedantry

https://theordinary.com/en-be/category/skincare/shop-by-concern/puffiness?cgid=theordinary-puffiness&prefn1=availableCountries&prefv1=BE&prefn2=skinConcern&prefv2=Uneven%20Skin%20Tone%7cTextural%20Irregularities&srule=top-sellers | Reduce Puffiness on Skin & Around Eyes | The Ordinary

https://fibery.io/blog/worst-interview-questions-for-software-developers/ | 10 worst interview questions
https://restoreprivacy.com/vpn/best/vpn-for-iphone/ | Best VPN for iPhone in 2023 (Only 3 Passed ALL Tests)
https://support.signal.org/hc/en-us/articles/************-Stickers#stickers_install | Stickers – Signal Support
https://signalstickers.com/pack/10dd61e2328d7a0a6c43effb85b58a61 | Signal Stickers
https://nl.ign.com/ | IGN Benelux
https://primer.ai/ | Home - PrimerAI
https://www.startpage.com/do/dsearch?query=bilan+ecolo+bruxelles+&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://www.startpage.com/do/dsearch?query=bilan+gourvernement+vervoort+III&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://blog.appsignal.com/2021/08/24/responsible-monkeypatching-in-ruby.html | Responsible Monkeypatching in Ruby | AppSignal Blog

https://en.algorithmica.org/hpc/data-structures/s-tree/ | Static B-Trees - Algorithmica
https://github.com/puma/puma/blob/master/lib/puma/dsl.rb | puma/dsl.rb at master · puma/puma · GitHub
https://i.kym-cdn.com/photos/images/original/000/588/855/505.gif | 505.gif (GIF Image, 220 × 233 pixels)

https://ruby-doc.org/core-2.5.1/Binding.html | Class: Binding (Ruby 2.5.1)
https://stackoverflow.com/questions/37180209/kubernetes-modify-a-secret-using-kubectl | Kubernetes: modify a secret using kubectl? - Stack Overflow
https://stackoverflow.com/questions/37945800/update-kubernetes-secrets-doesnt-update-running-container-env-vars | kubectl - Update kubernetes secrets doesn't update running container env vars - Stack Overflow
https://kubernetes.io/docs/concepts/configuration/secret/#mounted-secrets-are-updated-automatically | Secrets | Kubernetes
https://github.com/derailed/k9s | derailed/k9s: 🐶 Kubernetes CLI To Manage Your Clusters In Style!
https://askubuntu.com/questions/656688/remove-newline-from-openssl-base64-encode-string | command line - Remove newline from openssl base64 encode string? - Ask Ubuntu
https://explainshell.com/explain?cmd=echo+-n+%22lskjdf%22 | explainshell.com - echo -n "lskjdf"

https://stackoverflow.com/questions/14750650/how-to-delete-history-of-last-10-commands-in-shell | linux - How to delete history of last 10 commands in shell? - Stack Overflow
https://www.fabfile.org/ | Welcome to Fabric! — Fabric documentation

https://fr.zalando.be/nike-performance-short-collants-blackwhite-n1242e1ua-q11.html | Nike Performance SHORT LONG - Shorty - black/white/noir - ZALANDO.BE

https://maggieappleton.com/ai-dark-forest | The Expanding Dark Forest and Generative AI
https://ahrm.github.io/jekyll/update/2023/01/02/three-eyed-forehead.html | Three-eyed forehead in Stable Diffusion | ahrm’s blog
https://www.youtube.com/watch?v=aVwxzDHniEw | The Beauty of Bézier Curves - YouTube

https://fr.wikipedia.org/wiki/Seattle | Seattle — Wikipédia
https://en.wikipedia.org/wiki/CommonJS | CommonJS - Wikipedia
https://nodejs.org/api/esm.html#modules-ecmascript-modules | Modules: ECMAScript modules | Node.js v19.3.0 Documentation

https://www.destructoid.com/did-you-get-around-to-playing-all-of-the-games-you-wanted-to-in-2022/ | Did you get to all of the releases you wanted to in 2022? – Destructoid

https://whatthediff.ai/ | What The Diff – AI powered code review assistant
https://www.migaku.io/ | Migaku Website

https://mbork.pl/2022-12-19_A_few_new_things_in_Emacs_29 | Marcin Borkowski: 2022-12-19 A few new things in Emacs 29

https://www.jobfin.be/fr/jobs/data-scientist-a2-pour-lag-inspection-speciale-des-impots | Data scientist A2 pour l’AG Inspection Spéciale des Impôts | JobFin
https://www.selor.be/fr/emplois/?lang=FR&type=&level=&term=&cat= | Emplois - Selor, bureau de sélection de l'administration fédérale
https://www.youtube.com/watch?v=8oBdYUhZEhg&list=PL43OynbWaTML2DF4XPWNkiBB3lEDQSfxM&index=4 | Dix ans de fête de Liane de Pougy - La chronique de Juliette Arnaud - YouTube
https://fr.wikipedia.org/wiki/Juliette_Arnaud | Juliette Arnaud — Wikipédia
https://fr.wiktionary.org/wiki/avec_ma_bite_et_mon_couteau | avec ma bite et mon couteau — Wiktionnaire

https://stackoverflow.com/questions/13075822/split-an-array-in-ruby-based-on-some-condition | Split an array in ruby based on some condition - Stack Overflow
https://www.fashionbeans.com/article/men-black-tie-dress-code/ | The Best Black Tie Dress Code and Attire Guide Ever Created | FashionBeans

https://github.com/refined-github/refined-github | refined-github/refined-github: Browser extension that simplifies the GitHub interface and adds useful features
https://stackoverflow.com/questions/16724566/does-the-order-of-gems-in-your-gemfile-make-a-difference | ruby on rails - Does the order of gems in your Gemfile make a difference? - Stack Overflow
https://editionsfika.com/jeu-intime-conviction-2/ | Le Jeu Intime Conviction – EDITIONS FIKA

https://www.lesoir.be/352498/sections/mad | MAD - Le Soir

https://blog.devgenius.io/how-to-merge-two-repositories-on-git-b0ed5e3b4448 | How to merge two repositories on git? | by Manish Basnyat | Dev Genius
http://www.nausicaa.net/wiki/Ghibli_Artisan_-_Kazuo_Oga_Exhibition,_A | Ghibli Artisan - Kazuo Oga Exhibition, A - GhibliWiki

https://en.wikipedia.org/wiki/Nohup | nohup - Wikipedia
https://2021.stateofjs.com/en-US/features/ | The State of JS 2021: Features
https://en.wikipedia.org/wiki/NP-completeness | NP-completeness - Wikipedia
https://www.atelier.ateliermoondust.com/apero-tricot | Événements: apéros, petits déj',... | Atelier Moondust

https://blog.boleary.dev/what-i-learned-at-gitlab-that-i-dont-want-to-forget/ | What I learned at GitLab that I don't want to forget
https://about.gitlab.com/handbook/values/ | GitLab Values | GitLab
https://stackoverflow.com/questions/20532561/how-to-find-if-my-app-is-loading-an-engine | ruby on rails - How to find if my app is loading an Engine? - Stack Overflow
https://vsupalov.com/docker-build-pass-environment-variables/ | Pass Docker Environment Variables During The Image Build · vsupalov.com
https://stackoverflow.com/questions/64804749/why-is-docker-build-not-showing-any-output-from-commands | Why is docker build not showing any output from commands? - Stack Overflow
https://www.startpage.com/do/dsearch?query=docker+buildkit&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results

https://www.reddit.com/r/SkincareAddiction/comments/ze50w1/hair_removal_i_waxed_my_chest_and_now_i_have_so/ | (1) [Hair Removal] I waxed my chest and now I have so many in grown hairs I don’t know what to do. It feels like a total nightmare and it has completely killed my self-confidence :( : SkincareAddiction
https://www.reddit.com/r/EuroSkincare/comments/mrh6vg/products_for_dealing_with_and_preventing_ingrown/ | (1) Products for dealing with and preventing ingrown hairs? : EuroSkincare
https://www.reddit.com/r/EuroSkincare/top/?t=year | (1) All things relating to European skincare and beauty trends

https://github.com/jwilder/dockerize | jwilder/dockerize: Utility to simplify running applications in docker containers
https://stackoverflow.com/questions/2973436/regex-lookahead-lookbehind-and-atomic-groups | lookaround - Regex lookahead, lookbehind and atomic groups - Stack Overflow

https://letterboxd.com/mundof/list/mundofs-top-200-lgbtq-films-of-all-time/ | ‎⚣MundoF's Top 200 LGBTQ+ Films of All Time, a list of films by MundoF • Letterboxd

https://linuxize.com/post/bash-wait/ | Bash wait Command | Linuxize
https://www.reddit.com/r/flicks/ | Film Discussion and News
https://en.wikipedia.org/wiki/Disinformation_in_the_2022_Russian_invasion_of_Ukraine#Assassination_attempts | Disinformation in the 2022 Russian invasion of Ukraine - Wikipedia

https://github.com/enaqx/awesome-react#redux | enaqx/awesome-react: A collection of awesome things regarding React ecosystem
https://github.com/junegunn/fzf#usage | junegunn/fzf: A command-line fuzzy finder
https://github.com/hlascelles/que-scheduler | hlascelles/que-scheduler: A lightweight cron scheduler for the async job worker Que

https://www.cybertec-postgresql.com/en/postgresql-improving-sort-performance/ | PostgreSQL: Improving sort performance - CYBERTEC
https://context.reverso.net/traduction/anglais-francais/something+i+wanted+to+point+out | something i wanted to point out - Traduction en français - exemples anglais | Reverso Context
https://resthooks.org/ | REST Hooks
https://solnic.codes/2012/04/11/get-rid-of-that-code-smell-control-couple/ | Get Rid of That Code Smell - Control Couple | solnic.codes
https://cs007.blog/ | CS 007: Personal Finance for Engineers | Stanford University 2017-22 (6th Year)
https://twitter.com/Nturfknbsn/status/1599865377164779521 | Mjäumjäu on Twitter: "Former Chechnya's President Dƶoxar Dudayev before he was killed by ruSSian terrorist occupiers. 1/3 https://t.co/YL6sFFBaed" / Twitter

https://www.petmd.com/cat/wellness/evr_ct_exercising_with_your_cat_a_how_to_guide | Exercise for Cats: 12 Cat Workouts That Are Fun for Cats | PetMD
https://bxl-malade.medor.coop/?lang=en | Bruxelles Malade
https://stackoverflow.com/questions/37180209/kubernetes-modify-a-secret-using-kubectl | Kubernetes: modify a secret using kubectl? - Stack Overflow
https://lessons.nihongo-app.com/ | Nihongo Lessons: Learn Japanese more efficiently than ever
https://thevaluable.dev/vim-advanced/ | A Vim Guide for Advanced Users
https://jozef.io/r908-10-year-old-code/ | Here's why 2019 is a great year to start with R: A story of 10 year old R code then and now - Jozef's Rblog

https://letterboxd.com/journal/film-feelings-nanogenres/ | ‎Film Feelings: using ‘nanogenres’ to find similar films • Journal • A Letterboxd Magazine • Letterboxd
https://news.ycombinator.com/item?id=33797862 | Ask HN: Which books have made you a better thinker and problem solver? | Hacker News
https://www.bol.com/nl/nl/p/potty-training-in-3-days/9200000066206067/ | Potty Training in 3 Days, Brandi Brucks | 9781623157906 | Boeken | bol.com
https://github.com/facebookresearch/faiss | facebookresearch/faiss: A library for efficient similarity search and clustering of dense vectors.
https://fullfact.org/ | Full Fact

https://en.wikipedia.org/wiki/Representational_state_transfer | Representational state transfer - Wikipedia
https://stackoverflow.com/questions/48748372/passing-path-parameters-in-axios | node.js - Passing path parameters in axios - Stack Overflow
https://www.reddit.com/r/learnmachinelearning/comments/tfpl7c/a_deep_dive_into_word_embeddings_nlp/ | (1) A deep dive into word embeddings (NLP) : learnmachinelearning
https://datajenius.com/2022/03/13/a-deep-dive-into-nlp-tokenization-encoding-word-embeddings-sentence-embeddings-word2vec-bert/ | A Deep Dive into NLP Tokenization and Encoding with Word and Sentence Embeddings – Data Jenius
https://github.com/pgvector/pgvector | pgvector/pgvector: Open-source vector similarity search for Postgres
https://frankzliu.com/blog/a-gentle-introduction-to-vector-databases | A Gentle Introduction to Vector Databases | Frank’s Ramblings

https://ux.stackexchange.com/questions/11107/can-tags-have-hierarchy | Can tags have hierarchy? - User Experience Stack Exchange
https://www.reddit.com/r/AskReddit/comments/ylvk5f/what_was_great_advice_20_years_ago_but_definitely/ | What was great advice 20 years ago, but definitely isn’t now? : AskReddit
https://stackoverflow.com/questions/2928584/how-to-grep-search-committed-code-in-the-git-history/2929502#2929502 | How to grep (search) committed code in the Git history - Stack Overflow
https://stackoverflow.com/questions/2839253/git-history-find-lost-line-by-keyword/2839319#2839319 | version control - Git history - find lost line by keyword - Stack Overflow

https://reactjs.org/docs/hooks-reference.html#conditionally-firing-an-effect | Hooks API Reference – React
https://reactjs.org/docs/hooks-faq.html#is-it-safe-to-omit-functions-from-the-list-of-dependencies | Hooks FAQ – React
https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/Reduce | Array.prototype.reduce() - JavaScript | MDN
https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/some | Array.prototype.some() - JavaScript | MDN
https://developer.mozilla.org/fr/docs/Web/JavaScript/Reference/Global_Objects/String/includes | String.prototype.includes() - JavaScript | MDN

https://www.reddit.com/r/piano/comments/1p3xbo/favorite_modern_piano_composers/ | (1) Favorite modern piano composers? : piano
https://www.reddit.com/r/piano/comments/ypsvox/sightread_a_free_and_open_source_website_for/ | (1) Sightread: A Free and Open Source Website For Learning Piano : piano
https://getfreesmsnumber.com/virtual-phone/p-33644627124 | Free temporary France mobile phone - GetFreeSMSNumber

https://stackoverflow.com/questions/3737740/is-there-a-better-way-to-run-a-command-n-times-in-bash | loops - Is there a better way to run a command N times in bash? - Stack Overflow
https://www.lesoir.be/477647/article/2022-11-17/lancien-directeur-de-newb-les-grandes-organisations-du-non-marchand-nont-pas | L’ancien directeur de NewB: «Les grandes organisations du non-marchand n’ont pas soutenu notre banque» - Le Soir

https://issuu.com/bruzz.be/docs/bruzz_select_november_2022?fr=sNTg1OTUzMzEyMzI | BRUZZ Select - november 2022 by bruzz.be - Issuu
https://stackoverflow.com/questions/64709389/how-to-test-destroy-failure-in-controller-using-rspec | ruby on rails - How to test destroy failure in controller using Rspec? - Stack Overflow
https://styled-components.com/docs/basics#adapting-based-on-props | styled-components: Basics
https://neil.computer/ | Neil Panchal
https://www.lesoir.be/466807/article/2022-09-21/mons-la-cour-des-comptes-epingle-le-fiasco-de-la-gare-de-calatrava | Mons: la Cour des comptes épingle le fiasco de la gare de Calatrava - Le Soir
https://www.google.be/maps/place/Koninklijk+Atheneum+Etterbeek/@50.8316141,4.4050428,17z/data=!4m23!1m16!4m15!1m6!1m2!1s0x47c3c35d1bb35455:0x6748dc18cb1ba3f3!2sGrote+Bosstraat,+1030+Schaarbeek!2m2!1d4.3869135!2d50.8550556!1m6!1m2!1s0x47c3c4b31226207f:0x548334f2f04be08b!2sProxy+Delhaize+Sint-Michiel,+Eskadronstraat+45,+1040+Etterbeek!2m2!1d4.4039948!2d50.8320233!3e1!3m5!1s0x47c3db3eee5b57c1:0x20972e2c3624a6bf!8m2!3d50.8316141!4d4.4072315!16s%2Fg%2F119w1w6_r | Koninklijk Atheneum Etterbeek - Google Maps

https://www.reddit.com/r/brussels/comments/yx1a1a/would_you_drink_urine/ | (2) Would you drink Urine? : brussels
https://ruby.github.io/rake/ | Rake -- Ruby Make
https://news.ycombinator.com/user?id=dang | Profile: dang | Hacker News
https://stackoverflow.com/questions/46029084/rails-unable-to-convert-unpermitted-parameters-to-hash | ruby - Rails Unable to convert unpermitted parameters to hash - Stack Overflow

https://github.com/rails/rails/blob/main/actionpack/lib/action_controller/test_case.rb | rails/test_case.rb at main · rails/rails
https://api.rubyonrails.org/classes/ActionDispatch/IntegrationTest.html | ActionDispatch::IntegrationTest
https://guides.rubyonrails.org/testing.html#functional-tests-for-your-controllers | Testing Rails Applications — Ruby on Rails Guides
https://stackoverflow.com/questions/62783642/stub-controller-method-when-performing-web-request-with-rspec | ruby on rails - Stub controller method when performing web request with Rspec - Stack Overflow
https://stackoverflow.com/questions/23027236/how-to-test-ruby-inheritance-with-rspec | how to test ruby inheritance with rspec - Stack Overflow

https://stackoverflow.com/questions/27849528/notnullviolation-on-factorygirl-create-with-has-one-association | ruby on rails - NotNullViolation on FactoryGirl.create with has_one association - Stack Overflow
https://stackoverflow.com/questions/47890598/difference-between-kernelyield-self-yieldself-kernelthen-and-objecttap-in | Difference between Kernel#yield_self, yield(self), Kernel#then and Object#tap in Ruby? - Stack Overflow
https://stackoverflow.com/questions/4763121/should-i-use-alias-or-alias-method | ruby - Should I use alias or alias_method? - Stack Overflow
https://stackoverflow.com/questions/1297111/check-if-record-was-just-destroyed-in-rails | ruby - Check if record was just destroyed in rails - Stack Overflow

https://medium.com/@jamestplunkett/governance-futures-e5ec8deedf96 | Governance futures. 1/n: What will democracy mean in 2040? | by James Plunkett | Nov, 2022 | Medium
https://reactjs.org/blog/2015/12/18/react-components-elements-and-instances.html | React Components, Elements, and Instances – React Blog
https://www.robinwieruch.de/react-element-component/ | React Element vs Component
https://stackoverflow.com/questions/51535807/rails-migration-references-unique-true-isnt-generating-unique-true-in-sc | ruby - Rails migration, references `unique: true` isn't generating `unique: true` in schema causing constancy_fail check to fail - Stack Overflow
https://stackoverflow.com/questions/9296694/what-does-inverse-of-do-what-sql-does-it-generate | ruby on rails - What does inverse_of do? What SQL does it generate? - Stack Overflow

https://hn.algolia.com/?query=ssh%20tunnel&sort=byPopularity&prefix&page=0&dateRange=all&type=story | All | Search powered by Algolia
https://goteleport.com/blog/ssh-tunneling-explained/ | What is SSH Tunnel, SSH Reverse Tunnel and SSH Port Forwarding?
https://toolz.readthedocs.io/en/latest/api.html#itertoolz | API — Toolz 0.10.0 documentation
https://markm208.github.io/exbook/ | An Animated Introduction to Elixir

https://css-tricks.com/is-there-too-much-css-now/ | Is There Too Much CSS Now? | CSS-Tricks - CSS-Tricks
https://www.akkodis.com/en/careers | Careers in IT & Engineering | Akkodis
https://yiufung.net/post/anki-org/ | Power up Anki with Emacs, Org mode, anki-editor and more

https://www.reddit.com/r/compsci/comments/wubzh7/map_of_computer_science/ | (1) Map of Computer Science : compsci
https://www.reddit.com/r/LanguageTechnology/top/?t=year | Natural Language Processing
https://www.reddit.com/r/LanguageTechnology/comments/x121b4/what_are_your_favorite_nlp_books/ | What are your favorite NLP books? : LanguageTechnology
https://www.amazon.com/Natural-Language-Processing-Transformers-Applications/dp/1098103246 | Natural Language Processing with Transformers: Building Language Applications with Hugging Face: Tunstall, Lewis, von Werra, Leandro, Wolf, Thomas: 9781098103248: Amazon.com: Books
https://www.amazon.com/s?k=Speech+and+Language+Processing&i=digital-text&crid=2XZ6D6Z1MCEIU&sprefix=speech+and+language+processing+%2Cdigital-text%2C278&ref=nb_sb_noss_2 | Amazon.com : Speech and Language Processing
https://www.reddit.com/r/AskElectronics/comments/2fswef/kalman_filter_what_is_it_and_how_is_it_useful/ | (1) Kalman filter: what is it and how is it useful? : AskElectronics
https://www.edx.org/course/Data-Science-for-Construction-Architecture-and-Engineering | Data Science for Construction, Architecture and Engineering | edX

https://fr.wiktionary.org/wiki/comme_de_juste | comme de juste — Wiktionnaire
https://en.wikipedia.org/wiki/Bilbo%27s_Last_Song | Bilbo's Last Song - Wikipedia
https://www.reddit.com/r/brussels/comments/yokuc8/ceramics_course/ | Reddit - Dive into anything
https://www.reddit.com/r/brussels/comments/tdv5dv/thrift_stores_in_brussels/ | Thrift stores in Brussels : brussels

https://www.reddit.com/r/EuropeanCulture/top/?t=year | European Arts & Culture
https://stackoverflow.com/questions/3800551/select-first-row-in-each-group-by-group | sql - Select first row in each GROUP BY group? - Stack Overflow
https://stackoverflow.com/questions/14229291/postgres-rails-select-distinct-with-order | sql - Postgres Rails Select Distinct with Order - Stack Overflow
https://www.amazon.com/Unofficial-Ghibli-Cookbook-Thibaud-Vilanova/dp/1803363525/ref=sr_1_6?keywords=studio+ghibli+cookbook&qid=1667577273&qu=eyJxc2MiOiIzLjUyIiwicXNhIjoiMy4yNiIsInFzcCI6IjIuNTgifQ%3D%3D&s=books&sprefix=ghibli+coo%2Cstripbooks-intl-ship%2C159&sr=1-6 | Amazon.com: The Unofficial Ghibli Cookbook: 9781803363523: Vilanova, Thibaud: Books

https://www.looria.com/search?keyword=New%20Balance%20%20Tr%20v1 | Looria | New Balance Tr v1
https://www.reddit.com/r/crossfit/comments/tet357/what_shoes_are_the_best/ | (1) What shoes are the best? : crossfit
https://www.befr.ebay.be/sch/i.html?_from=R40&_nkw=new+balance+%22minimus+40%22&_sacat=0&LH_TitleDesc=0&rt=nc&LH_PrefLoc=3 | new balance "minimus 40" en vente | eBay
https://www.nytimes.com/wirecutter/reviews/best-cross-training-shoes/ | The Best Cross-Training Shoes | Reviews by Wirecutter

https://docs.docker.com/engine/reference/commandline/system_df/ | docker system df | Docker Documentation
https://itecnote.com/tecnote/ruby-on-rails-how-to-select-maxdate-and-group-by-client_id/ | Ruby-on-rails – How to select max(date) and group by client_id – iTecNote
https://stackoverflow.com/questions/11126037/postgresql-date-with-timezone | datetime - PostgreSQL date() with timezone - Stack Overflow
https://stackoverflow.com/questions/28085468/postgresql-extract-last-row-for-each-id | sql - Postgresql extract last row for each id - Stack Overflow
https://www.geekytidbits.com/postgres-distinct-on/ | SELECT DISTINCT ON in PostgreSQL | Geeky Tidbits

https://kubernetes.io/docs/reference/glossary/?fundamental=true | Glossary | Kubernetes
https://www.reddit.com/r/brussels/comments/wyutpw/hiphop_dance_school_for_beginners/ | (1) HipHop Dance School for Beginners : brussels
https://www.academieyantra.be/permanents.php | Académie yantra, école de danse et de musique à Bruxelles. Professeurs.
http://www.fredacademy.be/ | fredacademy.be
https://www.theflowlab.be/reservations | The Flow Lab | Réservations en ligne

https://www.amazon.fr/Pallid-Falcon-Springseil-patentiertem-Muskelaufbau/dp/B07VX7CG12/ref=sr_1_8?__mk_de_DE=%C3%85M%C3%85%C5%BD%C3%95%C3%91&crid=3KX5H2GBZT3Y4&keywords=rope+fitskuad&qid=1666873509&sprefix=rope+fitskuad%2Caps%2C84&sr=8-8 | Pallid Falcon - Corde à sauter en aluminium AL Speedrope avec roulement à billes breveté à 360 ° I Optimisée pour le cardio et le renforcement musculaire. : Amazon.fr: Sports et Loisirs
https://linuxhint.com/print_columns_awk/ | How to Print the First Column or Last Column or Both Using `awk`

https://www.youtube.com/watch?v=iF4RvQq6yU0 | Vim with me: Gary Bernhardt - YouTube
https://stackoverflow.com/questions/70875197/rspec-how-to-test-method-with-multiple-service-calls | ruby on rails - Rspec, how to test method with multiple service calls? - Stack Overflow
https://relishapp.com/rspec/rspec-expectations/docs/built-in-matchers/type-matchers | Type matchers - Built in matchers - RSpec Expectations - RSpec - Relish
https://stackoverflow.com/questions/37062549/in-rspec-how-to-expect-multiple-messages-with-different-parameters-in-any-order | ruby on rails - In RSpec, how to expect multiple messages with different parameters in any order? - Stack Overflow
https://www.reddit.com/r/devops/comments/y6osu8/what_are_some_of_your_core_devops_books/ | What are some of your core devops books? : devops
https://www.amazon.com/UNIX-Linux-System-Administration-Handbook/dp/0134277554 | UNIX and Linux System Administration Handbook: Nemeth, Evi, Snyder, Garth, Hein, Trent, Whaley, Ben, Mackin, Dan: 9780134277554: Amazon.com: Books

https://eklitzke.org/the-cult-of-dd | The Cult of DD
https://peps.python.org/pep-0654/ | PEP 654 – Exception Groups and except* | peps.python.org
https://www.amazon.fr/s?k=hardware+programming&__mk_fr_FR=%C3%85M%C3%85%C5%BD%C3%95%C3%91&crid=25I7ZWOMKCPNO&sprefix=hardware+programming%2Caps%2C71&ref=nb_sb_noss | Amazon.fr : hardware programming
https://www.google.be/maps/place/Tuinen+van+de+Bloemist/@50.8910094,4.3436991,15z/data=!4m5!3m4!1s0x47c3c3b3cd0531a3:0xbe0d5e6b7676fa10!8m2!3d50.8853443!4d4.3486195 | Tuinen van de Bloemist - Google Maps

https://stackoverflow.com/questions/68025133/error-configuring-terraform-aws-provider-linux | amazon web services - Error configuring Terraform AWS Provider - Linux - Stack Overflow
https://stackoverflow.com/questions/68048732/terraform-plan-using-terraform-cloud-back-end-aws-credential-error | amazon web services - Terraform plan using terraform cloud back end AWS credential error - Stack Overflow
https://stackoverflow.com/questions/60529329/administrator-cannot-encrypt-decrypt-in-aws-kms | amazon web services - Administrator cannot encrypt/decrypt in AWS KMS - Stack Overflow
https://devopslearning.medium.com/100-days-of-devops-day-24-how-to-encrypt-s3-bucket-using-kms-fc3b3bcf4c1b | 100 Days of DevOps — Day 24- How to encrypt S3 Bucket using KMS | by Prashant Lakhera | Medium
https://aws.amazon.com/premiumsupport/knowledge-center/rds-connectivity-instance-subnet-vpc/ | Troubleshoot connectivity to an Amazon RDS instance using the public or private subnet of a VPC

https://aisnakeoil.substack.com/p/students-are-acing-their-homework | AI Snake Oil | Sayash Kapoor | Substack
https://danieljeffries.substack.com/p/why-the-future-of-open-source-ai | Future History | Daniel Jeffries | Substack
https://fasterthanli.me/articles/the-http-crash-course-nobody-asked-for | The HTTP crash course nobody asked for
https://wix-ux.com/when-life-gives-you-lemons-write-better-error-messages-46c5223e1a2f | When life gives you lemons, write better error messages | by Jenni Nadler | Sep, 2022 | Wix UX
https://dba.stackexchange.com/questions/307813/mariadb-read-only-user-cant-see-database-why | permissions - MariaDB Read-Only user can't see database, why? - Database Administrators Stack Exchange
https://medium.com/@nkaurelien/how-to-create-a-read-only-mysql-user-226e8e49a855 | How to create a read-only MySQL user? | by NKUMBE AURELIEN (“nkaurelien”) | Medium
https://mariadb.com/kb/en/grant/#the-usage-privilege | GRANT - MariaDB Knowledge Base
https://mariadb.com/kb/en/show-databases/ | SHOW DATABASES - MariaDB Knowledge Base
https://www.mariadbtutorial.com/mariadb-basics/mariadb-select-database/ | Selecting a MariaDB Database

https://www.driftingruby.com/episodes/hotwire-introduction?utm_medium=social&utm_campaign=weekly_episode&utm_source=reddit | Hotwire Introduction | Drifting Ruby
https://blog.appsignal.com/2022/10/05/security-best-practices-for-your-rails-application | Security Best Practices for Your Rails Application | AppSignal Blog
https://www.thefreedictionary.com/perchance#Translations | Perchance - definition of perchance by The Free Dictionary
https://www.cloudamqp.com/docs/cloudamqp-alarms.html | Alarms inside CloudAMQP - CloudAMQP

https://stackoverflow.com/questions/18747062/rails-create-or-update-magic | activerecord - Rails create or update magic? - Stack Overflow
https://stackoverflow.com/questions/8844185/creating-or-updating-a-has-one-activerecord-association | ruby on rails - Creating or updating a has_one ActiveRecord association - Stack Overflow
https://stackoverflow.com/questions/3380581/does-php-have-a-virtual-machine-like-java | Does PHP have a virtual machine like Java? - Stack Overflow
https://stackoverflow.com/questions/4134432/shoulda-validate-format-of-not-with-has-problem-in-framework-or-in-my-underst | ruby - Shoulda validate_format_of . not_with has problem in framework (or in my understanding) - Stack Overflow
https://apidock.com/rails/ActiveModel/Validations/ClassMethods/validates_format_of | validates_format_of (ActiveModel::Validations::ClassMethods) - APIdock
https://github.com/ruby/benchmark | ruby/benchmark: The Benchmark module provides methods for benchmarking Ruby code, giving detailed reports on the time taken for each task.
https://blog.jetbrains.com/go/2019/09/11/increase-productivity-with-custom-postfix-completion-templates/ | Increase productivity with Custom Postfix Completion templates | The GoLand Blog
https://thoughtbot.com/blog/activerecord-s-where-not-and-nil | ActiveRecord's where.not and nil
https://stackoverflow.com/questions/71400839/best-way-to-return-recently-created-object-in-activerecord | ruby on rails - Best way to return recently created object in ActiveRecord? - Stack Overflow
https://stackoverflow.com/questions/48351739/is-it-possible-to-pluck-from-an-includes-association-when-using-sti | ruby on rails - Is it possible to pluck from an includes association when using STI? - Stack Overflow

https://www.reddit.com/r/AskReddit/comments/y8tz9q/what_video_game_is_an_absolute_100100_in_your/ | (1) What video game is an absolute 100/100 in your opinion? : AskReddit
https://www.reddit.com/r/brussels/comments/y894vu/which_are_the_most_popular_recent_tv_series_in/ | (1) which are the most popular recent tv series in Belgium (made in Belgium) ? : brussels
https://en.wikipedia.org/wiki/Age_of_Empires_II | Age of Empires II - Wikipedia
https://www.metacritic.com/feature/video-game-of-the-year-awards-tracker | 2021 Video Game Awards Tracker - Metacritic

https://superuser.com/questions/1748144/how-can-earbudsmic-use-a-single-audio-jack | laptop - How can earbuds+mic use a single audio jack? - Super User
https://recettes4saisons.brussels/recettes/couscous-de-legumes-dhiver | Couscous de légumes d’hiver | Recettes 4 Saisons
http://libgen.rs/book/index.php?md5=82D426435BF5997822B5E2EB1F566FA4 | Library Genesis: Gianpaolo Baiocchi, Ernesto Ganuza - Popular Democracy: The Paradox of Participation
https://en.wikipedia.org/wiki/IOPS | IOPS - Wikipedia
https://stackoverflow.com/questions/18289152/what-does-a-double-splat-operator-do | ruby - What does a double * (splat) operator do - Stack Overflow
https://blog.appsignal.com/2022/10/19/improve-code-in-your-ruby-application-with-rubycritic.html | Improve Code in Your Ruby Application with RubyCritic | AppSignal Blog
https://github.com/justinhoward/cutoff | justinhoward/cutoff: Deadlines for Ruby

https://www.rerun.io/blog/why-rust | Why Rust? — Rerun
https://towardsdatascience.com/quick-guide-to-entity-recognition-and-geocoding-with-r-c0a915932895 | Named Entity Recognition and Geocoding with R | by Gregg Saldutti | Towards Data Science
https://justforfunnoreally.dev/ | Just for Fun. No, Really.

https://news.ycombinator.com/item?id=33173443 | Seven sins of numerical linear algebra | Hacker News
https://www.youtube.com/watch?v=h5A1OQjuCqk | Advanced Programming in the UNIX Environment: Week 02, Segment 1 - File Descriptors - YouTube
https://stackoverflow.com/questions/2510276/how-do-i-undo-git-reset | How do I undo 'git reset'? - Stack Overflow
https://github.blog/2015-06-08-how-to-undo-almost-anything-with-git/ | How to undo (almost) anything with Git | The GitHub Blog
https://lukeplant.me.uk/blog/posts/yagni-exceptions/ | YAGNI exceptions - lukeplant.me.uk

https://www.reddit.com/r/brussels/comments/waibyk/authentic_japanese_yoshoku_cuisine/ | Authentic Japanese yoshoku cuisine : brussels
https://yamayu.be/menu/ | MENU – yamayu.be
https://www.happycow.net/reviews/kamilou-arts-loi-brussels-178629 | Kamilou - Arts Loi - Brussels Restaurant - HappyCow
https://www.reddit.com/r/brussels/comments/ofgy64/lunch_spots/ | Lunch spots : brussels

https://en.wikipedia.org/wiki/Coppersmith | Coppersmith - Wikipedia

https://www.makeuseof.com/tag/best-ways-scan-old-photos/ | The 3 Best Ways to Scan and Digitize Old Photos
https://www.joshwcomeau.com/css/full-bleed/ | CSS Grid full-bleed layout tutorial
https://adamj.eu/tech/2022/06/17/mike-actons-expectations-of-professional-software-engineers/ | Mike Acton’s Expectations of Professional Software Engineers - Adam Johnson
https://www.honeybadger.io/blog/activerecord-update-counters-race-conditions/ | Using ActiveRecord's #update_counters to Prevent Race Conditions - Honeybadger Developer Blog

https://www.reddit.com/r/Mindfulness/comments/xe0qyd/please_remember_that/ | Please, remember that 🙏🏻 : Mindfulness

https://api.rubyonrails.org/classes/ActiveSupport/ErrorReporter.html | ActiveSupport::ErrorReporter
https://www.reddit.com/r/brussels/comments/xzjk6z/any_quiet_place_in_brussels_where_u_can_chill_and/ | any quiet place in Brussels where u can chill and do your work related stuff without people around u being noisy? : brussels
https://www.reddit.com/r/belgium/comments/qb57t4/expatsimmigrants_in_belgium_what_are_some_of_the/ | Expats/immigrants in Belgium: what are some of the quirks you've noticed : belgium

https://stackoverflow.com/questions/3827828/how-to-check-type-of-value-in-postgres | sql - how to check type of value in postgres - Stack Overflow
https://stackoverflow.com/questions/27503379/use-a-calculated-column-in-a-where-clause | sql server - Use a calculated column in a where clause - Stack Overflow
https://www.w3schools.com/sql/sql_having.asp | SQL HAVING Clause

https://www.reddit.com/r/weightlifting/comments/xway2x/mobility_programmes/ | Mobility programmes : weightlifting
https://www.reddit.com/r/ruby/comments/xu2vx1/towards_ruby_4_jit_rubykaigi_2022/ | Towards Ruby 4 JIT / RubyKaigi 2022 : ruby
https://www.youtube.com/watch?v=PBVLf3yfMs8&list=PLbFmgWm555yYgc4sx2Dkb7WWcfflbt6AP&index=1 | [EN] YJIT - Building a new JIT Compiler inside CRuby / Maxime Chevalier-Boisvert @maximecb - YouTube
https://stackoverflow.com/questions/26538823/pluck-associated-models-attribute-in-rails-query | activerecord - Pluck associated model's attribute in Rails query - Stack Overflow
https://aaronbos.dev/posts/query-postgresql-json | Querying JSON Data in PostgreSQL
https://github.com/rochacbruno/py2rs | rochacbruno/py2rs: A quick reference guide for the Pythonista in the process of becoming a Rustacean
https://lethain.com/staff-engineer-archetypes/ | Staff engineer archetypes. | Irrational Exuberance

https://www.reddit.com/r/belgium/comments/65ctsm/where_can_i_eat_cheap_and_healthy_in_brussels/ | Where can I eat cheap and healthy in Brussels? : belgium
https://www.reddit.com/r/belgium/comments/b5pmcy/what_is_your_cheap_but_good_lunch_in_brussels/ | What is your cheap (but good) lunch in Brussels? : belgium
https://www.google.be/maps/place/La+V%C3%A9rit%C3%A9+Si+Je+Mange/@50.8319999,4.3566564,17z/data=!4m6!3m5!1s0x47c3c45f1c85c657:0xb5084e28f089b42c!4b1!8m2!3d50.8320188!4d4.3565118 | Google Maps
https://www.smarksthespots.com/royal-library-belgium-cafe-brussels/ | THE ROYAL LIBRARY OF BELGIUM CAFE - S Marks The Spots
https://www.kbr.be/en/food-and-drink/ | Restaurant albert • KBR
https://en.wikipedia.org/wiki/Textured_vegetable_protein | Textured vegetable protein - Wikipedia
https://medium.com/@connorstack/understanding-ruby-load-require-gems-bundler-and-rails-autoloading-from-the-bottom-up-3b422902ca0 | Understanding ruby load, require, gems, bundler and rails autoloading from the bottom up | by cstack | Medium

https://www.reddit.com/r/ruby/comments/xog05i/richard_schneemans_how_to_open_source_book_is_out/ | Richard Schneeman's How to Open Source book is out! 🤩 : ruby
https://www.codetriage.com/ | Get Started Contributing to Open Source Projects | CodeTriage
https://www.learnrailshotwire.com/ | Modern Web Application Development with Rails and Hotwire

https://www.behance.net/gallery/34207733/LOVE | LOVE on Behance
https://m.standaard.be/cnt/dmf20210409_94864238 | ‘We hebben elk ons eigen leven, dat is gezond’ | De Standaard Mobile
https://www.cuisineaz.com/articles/quelle-huile-choisir-pour-faire-une-vinaigrette-5523.aspx | Quelle huile choisir pour faire une vinaigrette ? | CuisineAZ
https://devdocs.io/rails~6.1/activesupport/testing/filefixtures#method-i-file_fixture | Ruby on Rails 6.1 / ActiveSupport::Testing::FileFixtures#file_fixture — DevDocs
https://evilmartians.com/chronicles/a-fixture-based-approach-to-interface-testing-in-rails | A fixture-based approach to interface testing in Rails—Martian Chronicles, Evil Martians’ team blog

https://stackoverflow.com/questions/4975747/sleep-until-condition-is-true-in-ruby | sleep until condition is true in ruby - Stack Overflow
https://stackoverflow.com/questions/36554988/how-to-debug-rails-connection-pool-usage | activerecord - How to debug Rails connection pool usage? - Stack Overflow
https://sosoir.lesoir.be/escapade-gand-nos-bons-plans-pour-une-visite-inoubliable | Escapade à Gand : nos bons plans pour une visite inoubliable
https://jakeyesbeck.com/2016/02/14/ruby-threads-and-active-record-connections/ | Ruby Threads and ActiveRecord Connections
https://discuss.rubyonrails.org/t/active-record-connection-pool-sometimes-requires-manual-massaging/75463 | Active Record connection pool sometimes requires manual massaging - A May Of WTFs - Ruby on Rails Discussions
https://blog.arkency.com/rails-connections-pools-and-handlers/ | Rails connections, pools and handlers | Arkency Blog

https://drive.proton.me/u/0/Eg_qh-I0MGLWCNiDXNQx_-7IUNPpg_F6g7FAO07ukIv8eD_1Xw_DvUsc9ridFBgyl141R2j83luVHZYzfwzQ6Q==/folder/FRVOab_EuiCWISGdxWDpu-0ENENWxquCHhuJAsNTazf9o5HDoADT4RQwP9iTh_v5Y-lsIsz-eYjXhvpjiPXbRw== | My files - Proton Drive
https://www.reddit.com/r/gaybros/comments/xjqha0/gay_books_to_recommend/ | gay books to recommend : gaybros
https://en.wikipedia.org/wiki/Heartstopper_(graphic_novel) | Heartstopper (graphic novel) - Wikipedia
http://danilenko.org/2012/7/6/rails_timezones/ | The Exhaustive Guide to Rails Time Zones - Alexander Danilenko
https://github.com/adzap/active_enum | adzap/active_enum: Define enum classes in Rails and use them to enumerate ActiveRecord attributes, with I18n support

https://stackoverflow.com/questions/********/how-to-make-terraform-to-read-aws-credentials-file | amazon web services - How to make Terraform to read AWS Credentials file? - Stack Overflow
https://stackoverflow.com/questions/********/terraform-unable-to-assume-roles-with-mfa-enabled | amazon web services - Terraform unable to assume roles with MFA enabled - Stack Overflow
https://stackoverflow.com/questions/********/quick-way-to-get-aws-account-number-from-the-aws-cli-tools | amazon web services - Quick way to get AWS Account number from the AWS CLI tools? - Stack Overflow
https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-files.html | Configuration and credential file settings - AWS Command Line Interface

https://www.geeksforgeeks.org/types-of-locks-in-concurrency-control/ | Types of Locks in Concurrency Control - GeeksforGeeks
https://singularityhub.com/2022/09/22/super-earths-are-bigger-and-more-habitable-than-earth-and-astronomers-are-discovering-more-of-the-billions-they-think-are-out-there/ | Super-Earths Are Bigger and More Habitable Than Earth, and Astronomers Are Discovering More of the Billions They Think Are Out There
https://discuss.hashicorp.com/t/unqualified-provider-aws/18554/2 | Unqualified provider "aws" - Terraform - HashiCorp Discuss

https://www.startpage.com/do/dsearch?query=take_while+lazy+ruby&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://ruby-doc.org/core-2.7.0/Enumerator/Lazy.html | Class: Enumerator::Lazy (Ruby 2.7.0)
https://idioms.thefreedictionary.com/_/MatchUp.aspx?res=4%2C2%2C0%2C3%2C1&dt=6466&tfd_a0=quittance&tfd_a1=gadget&tfd_a2=witticism&tfd_a3=bobbin&tfd_a4=wretch&tfd_b0=pun&tfd_b1=poor+devil&tfd_b2=contraption&tfd_b3=reel&tfd_b4=repayment | Match Up Results
https://primer.ai/products/yonder/ | Yonder - PrimerAI
https://make.org/BE | Make a change in our society with Make.org -
https://justine.lol/cosmopolitan/index.html | Cosmopolitan Libc: build-once run-anywhere C library

https://avdi.codes/throw-catch-raise-rescue-im-so-confused/ | Throw, Catch, Raise, Rescue – I’m So Confused! - avdi.codes
https://www.locksmiths.co.uk/faq/door-lock-types-guide/ | Door Lock Types - A Simple Guide for your Home (with Pictures)
https://pawelurbanek.com/rails-thread-safety | Intro to Thread-Safety in Ruby on Rails
https://marmelab.com/blog/2022/09/20/react-i-love-you.html | React I Love You, But You're Bringing Me Down
https://xkcd.com/195/ | xkcd: Map of the Internet
https://apidock.com/ruby/Module/module_function | module_function (Module) - APIdock
https://stackoverflow.com/questions/2353498/is-extend-self-the-same-as-module-function | ruby - Is "extend self" the same as "module_function"? - Stack Overflow
https://stackoverflow.com/questions/25336033/in-ruby-are-the-terms-metaclass-eigenclass-and-singleton-class-complete | In Ruby, are the terms "metaclass", "eigenclass", and "singleton class" completely synonymous and fungible? - Stack Overflow
https://www.postgresql.org/docs/9.1/functions-admin.html | PostgreSQL: Documentation: 9.1: System Administration Functions

https://news.ycombinator.com/item?id=32521452 | Ask HN: Are there any decent GitHub Copilot Alternatives? | Hacker News
https://medium.com/swlh/should-i-use-date-time-or-datetime-in-ruby-and-rails-9372ad20ca4f | Should I Use Date, Time, or DateTime in Ruby and Rails? | by Mav Tipi | The Startup | Medium
https://stackoverflow.com/questions/7816365/how-to-convert-a-unix-timestamp-seconds-since-epoch-to-ruby-datetime | How to convert a unix timestamp (seconds since epoch) to Ruby DateTime? - Stack Overflow
https://apidock.com/rails/ActiveRecord/Batches/find_in_batches | find_in_batches (ActiveRecord::Batches) - APIdock
https://stackoverflow.com/questions/2504494/are-there-something-like-python-generators-in-ruby | lazy evaluation - Are there something like Python generators in Ruby? - Stack Overflow
https://www.sitepoint.com/implementing-lazy-enumerables-in-ruby/ | Implementing Lazy Enumerables in Ruby - SitePoint
https://stackoverflow.com/questions/20394909/how-to-use-an-enumerator | ruby - How to use an enumerator - Stack Overflow

https://git-scm.com/book/en/v2/Git-Branching-Rebasing | Git - Rebasing
https://www.startpage.com/do/dsearch?query=how+to+recognize+electronics+components&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://electronicsclub.info/books.htm | Books on Electronics | Electronics Club
https://stackoverflow.com/questions/37713059/getting-does-not-point-to-a-valid-commit | github - Getting "Does not point to a valid commit" - Stack Overflow
https://stackoverflow.com/questions/7726949/remove-tracking-branches-no-longer-on-remote | git - Remove tracking branches no longer on remote - Stack Overflow
https://jeffkreeftmeijer.com/git-rebase/ | Git rebase: apply your changes onto another branch

https://fr.wikipedia.org/wiki/Cosaques | Cosaques — Wikipédia
https://stackoverflow.com/questions/5117729/rails-is-there-an-engine-root | Rails: is there an Engine.root? - Stack Overflow
http://projetbabel.org/forum/viewtopic.php?t=14570&postdays=0&postorder=asc&start=0 | Transcription de Star Wars en wallon - Forum langue d'oïl - Forum Babel

https://www.rfc-editor.org/rfc/rfc9110.html#name-head | RFC 9110: HTTP Semantics

https://www.youtube.com/watch?v=mxKMVFBPUQo | Les Petits Mouchoirs (Bande-annonce) - YouTube
https://www.reddit.com/r/investing/comments/rgmars/best_books_about_energy_markets_and_investing/ | Best books about energy markets and investing, from casual reads to text books : investing
https://www.reddit.com/r/UKPersonalFinance/comments/rb3ly1/can_anyone_recommend_a_book_to_learn_about_energy/ | Can anyone recommend a book to learn about energy markets? : UKPersonalFinance
https://www.reddit.com/r/explainlikeimfive/comments/1zr0gd/eli5_how_does_the_electricity_market_work/ | ELI5: How does the electricity market work? : explainlikeimfive
https://www.reddit.com/r/IAmA/comments/avglt/iama_power_aka_electricity_trader_no_i_did_not/ | IAmA Power (aka Electricity) Trader. No I did not work at Enron. AMA. : IAmA
https://www.oreilly.com/library/view/restful-web-services/9780596529260/ | RESTful Web Services [Book]
https://en.wikipedia.org/wiki/Resource-oriented_architecture | Resource-oriented architecture - Wikipedia
https://www.ics.uci.edu/~fielding/pubs/dissertation/rest_arch_style.htm | Fielding Dissertation: CHAPTER 5: Representational State Transfer (REST)
https://martinfowler.com/articles/richardsonMaturityModel.html | Richardson Maturity Model
https://medium.com/javascript-non-grata/the-top-10-things-wrong-with-javascript-58f440d6b3d8 | The Top 10 Things Wrong with JavaScript | by Richard Kenneth Eng | JavaScript Non Grata | Medium

https://roadmap.sh/devops | DevOps Roadmap: Learn to become a DevOps Engineer or SRE
https://roadmap.sh/postgresql-dba | DBA Roadmap: Learn to become a database administrator with PostgreSQL
https://godotengine.org/article/dev-snapshot-godot-4-0-beta-1 | Godot Engine - The next big step: Godot 4.0 reaches Beta

https://fr.wikipedia.org/wiki/R%C3%A9volution_belge | Révolution belge — Wikipédia
https://test-prof.evilmartians.io/#/ | TestProf: Ruby tests profiling and optimization toolbox
https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes | Pseudo-classes - CSS: Cascading Style Sheets | MDN
https://en.wikipedia.org/wiki/Representational_state_transfer | Representational state transfer - Wikipedia
https://github.com/typicode/json-server | typicode/json-server: Get a full fake REST API with zero coding in less than 30 seconds (seriously)
https://retool.com/?utm_source=sponsor&utm_campaign=typicode | Retool | Build internal tools, remarkably fast.
https://typefully.com/DanHollick/qr-codes-T7tLlNi | QR codes | Dan Hollick 🇿🇦
https://github.com/justinhoward/cutoff | justinhoward/cutoff: Deadlines for Ruby

https://dev.to/baweaver/ruby-3-1-shorthand-hash-syntax-first-impressions-19op | Ruby 3.1 – Shorthand Hash Syntax – First Impressions - DEV Community 👩‍💻👨‍💻

https://shiroyasha.io/advisory-locks-and-how-to-use-them.html | Advisory Locks and How to Use Them - Shiroyasha
https://dzone.com/articles/postgresql-connection-pooling-with-pgbouncer | PostgreSQL Connection Pooling With PgBouncer - DZone Database
https://stackoverflow.com/questions/3454470/run-cron-jobs-on-rails-deployed-over-several-servers | ruby - Run cron jobs on rails (deployed over several servers) - Stack Overflow
https://makandracards.com/makandra/48857-decide-whether-cronjobs-should-run-on-one-or-all-servers | Decide whether cronjobs should run on one or all servers - makandra dev
https://cryptextechnologies.medium.com/cron-job-in-ruby-on-rails-application-a-complete-tutorial-for-beginners-8222429c8aa2 | Cron job in Ruby on Rails application — A Complete Tutorial for Beginners | by Cryptex Technologies | Medium
https://capistranorb.com/documentation/overview/what-is-capistrano/ | What is Capistrano?
https://developer.mozilla.org/en-US/docs/Web/JavaScript/Language_Overview | JavaScript language overview - JavaScript | MDN
https://eloquentjavascript.net/ | Eloquent JavaScript
https://egghead.io/ | Build the portfolio you need to be a badass web developer. | egghead.io
https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures | JavaScript data types and data structures - JavaScript | MDN
https://github.com/petsel/not-awesome-es6-classes | petsel/not-awesome-es6-classes: A curated list of resources on why ES6 (aka ES2015) classes are NOT awesome
https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/this | this - JavaScript | MDN
https://egghead.io/courses/understand-javascript-s-this-keyword-in-depth | Understand JavaScript's this Keyword in Depth | egghead.io
https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Objects/Classes_in_JavaScript | Classes in JavaScript - Learn web development | MDN
https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Destructuring_assignment | Destructuring assignment - JavaScript | MDN
https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach | Array.prototype.forEach() - JavaScript | MDN
https://www.jstips.co/en/javascript/keyword-var-vs-let/ | ES6, var vs let
https://www.snowflake.com/guides/data-modeling | Data Modeling | Snowflake
https://developer.mozilla.org/en-US/docs/Glossary/Hoisting | Hoisting - MDN Web Docs Glossary: Definitions of Web-related terms | MDN

https://auth0.com/blog/how-to-manage-javascript-fatigue/ | How to Manage JavaScript Fatigue
https://en.wikipedia.org/wiki/Ajax_(programming) | Ajax (programming) - Wikipedia
https://stackoverflow.com/questions/3561381/custom-http-headers-naming-conventions | Custom HTTP headers : naming conventions - Stack Overflow
https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API | Fetch API - Web APIs | MDN
https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Building_blocks/Events | Introduction to events - Learn web development | MDN
https://drafts.csswg.org/selectors/#id-selectors | Selectors Level 4
https://developer.mozilla.org/en-US/docs/Web/HTML/Element/dialog | <dialog>: The Dialog element - HTML: HyperText Markup Language | MDN

https://serverfault.com/questions/141975/how-to-stop-nginx-on-mac-os-x | daemon - How to stop nginx on Mac OS X - Server Fault
https://stackoverflow.com/questions/3855127/find-and-kill-process-locking-port-3000-on-mac | node.js - Find (and kill) process locking port 3000 on Mac - Stack Overflow
https://en.wikipedia.org/wiki/Multiversion_concurrency_control | Multiversion concurrency control - Wikipedia
https://sequel.jeremyevans.net/rdoc/files/doc/sql_rdoc.html | sql.rdoc

https://www.lesoir.be/464004/article/2022-09-07/le-recit-du-22-mars-minute-par-minute-jai-pense-etre-dans-un-cauchemar-cetait | Le récit du 22 mars, minute par minute: «J’ai pensé être dans un cauchemar, c’était irréel» - Le Soir
https://stackoverflow.com/questions/48761493/platforms-added-to-gemfile-lock | ruby on rails - Platforms added to Gemfile.lock - Stack Overflow
https://github.com/ruby/benchmark | ruby/benchmark: The Benchmark module provides methods for benchmarking Ruby code, giving detailed reports on the time taken for each task.
https://github.com/ankane/ahoy#geocoding | ankane/ahoy: Simple, powerful, first-party analytics for Rails
https://gorails.com/episodes/internal-metrics-with-ahoy-and-blazer | Tracking Metrics with Ahoy and Blazer (Example) | GoRails
https://en.wikipedia.org/wiki/UTM_parameters | UTM parameters - Wikipedia
https://www.startpage.com/do/dsearch?query=sql+advisory+lock&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://twitter.com/AgeMoyen/status/1567771605664780288 | Actuel Moyen Âge on Twitter: "Vous avez peut-être vu passer ce petit extrait d’une conférence de mars dernier donnée par Sandrine Rousseau à l’UCL. Elle y parle des #sorcières et de leur répression... et dit à peu près n’importe quoi. Un thread, coécrit par @CathKikuchi et moi ⬇️! #histoire #medievaltwitter https://t.co/yKQanW7pab" / Twitter

https://context.reverso.net/traduction/anglais-francais/skim+the | skim the - Traduction en français - exemples anglais | Reverso Context
https://en.wikipedia.org/wiki/Coleslaw | Coleslaw - Wikipedia
https://www.reddit.com/r/AskReddit/comments/x6wic1/what_are_some_hygiene_tips_everyone_should_know/ | What are some hygiene tips everyone should know? : AskReddit
https://www.reddit.com/r/popheads/comments/wqr7y2/video_game_instrumentals_rate/ | Video Game Instrumentals Rate : popheads

https://linuxfoundation.org/blog/classic-sysadmin-linux-101-5-commands-for-checking-memory-usage-in-linux/ | Classic SysAdmin: Linux 101: 5 Commands for Checking Memory Usage in Linux - Linux Foundation
https://www.postgresql.org/docs/10/functions-json.html | PostgreSQL: Documentation: 10: 9.15. JSON Functions and Operators
https://www.thefreedictionary.com/outtakes | Outtakes - definition of Outtakes by The Free Dictionary
https://www.thefreedictionary.com/blooper | Blooper - definition of blooper by The Free Dictionary

https://stackoverflow.com/questions/58867861/run-a-husky-git-hook-manually-without-triggering-it-w-git-command | javascript - Run a husky git hook manually (without triggering it w/git command) - Stack Overflow

https://www.startpage.com/do/dsearch?query=vogue+basic+moves&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://www.youtube.com/shorts/q_JotCluVr8 | Beginner Vogue Hands Follow-Along 6 - Gravity Balmain Jacobs - YouTube
https://cuisine.journaldesfemmes.fr/recette/309902-houmous | Houmous : la meilleure recette

https://stackoverflow.com/questions/773417/aggregate-sql-function-to-grab-only-the-first-from-each-group | sql server 2005 - Aggregate SQL Function to grab only the first from each group - Stack Overflow
https://www.digwebinterface.com/?hostnames=demo.stg.citizenlab.co&type=&trace=on&useresolver=*******&ns=auth&nameservers= | Dig web interface - online dns lookup tool
https://docs.docker.com/engine/reference/commandline/stats/ | docker stats | Docker Documentation
https://openai.com/blog/dall-e-introducing-outpainting/ | DALL·E: Introducing Outpainting
https://twitter.com/djbaskin_images | Danielle Computer Images 💿 (@djbaskin_images) / Twitter
https://stackoverflow.com/questions/3800551/select-first-row-in-each-group-by-group | sql - Select first row in each GROUP BY group? - Stack Overflow

https://replicate.com/andreasjansson/stable-diffusion-animation | andreasjansson/stable-diffusion-animation – Run with an API on Replicate
https://github.com/hlky/stable-diffusion | hlky/stable-diffusion
https://stackoverflow.com/questions/10532942/how-do-i-create-a-rake-task-for-a-rails-engine-which-is-not-exposed-to-the-host | How do I create a rake task for a Rails engine which is not exposed to the host application? - Stack Overflow

https://home.cern/science/physics/standard-model | The Standard Model | CERN
https://minimaxir.com/2022/08/gpt3-blog-title-optimizer/ | How to Create a Blog Post Title Optimizer with GPT-3 and Hacker News Data | Max Woolf's Blog
https://daleonai.com/transformers-explained | Transformers, Explained: Understand the Model Behind GPT-3, BERT, and T5
https://www.startpage.com/do/dsearch?query=toxic+positive+culture&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://www.smh.com.au/lifestyle/health-and-wellness/why-the-good-vibes-only-culture-is-making-you-miserable-20220117-p59ou2.html | Toxic positivity is making us feel bad
https://www.forbes.com/sites/juliawuench/2021/11/01/toxic-positivity-in-the-workplace/ | Toxic Positivity In The Workplace
https://www.wellable.co/blog/perpetual-positivity-in-the-workplace-may-be-toxic/ | Toxic Positivity Cultures | Wellable

https://www.kickstarter.com/projects/magpiegames/avatar-legends-the-roleplaying-game | Avatar Legends: The Roleplaying Game by Magpie Games — Kickstarter
https://detroit-become-human.fandom.com/wiki/Gavin_Reed | Gavin Reed | Detroit: Become Human Wiki | Fandom
https://flowfo.me/artwork/Slashpalooza/Neighbor | Neighbor is created by Slashpalooza - Flowfo
https://fr.wikipedia.org/wiki/Salman_Rushdie | Salman Rushdie — Wikipédia
https://thechefdojo.com/japanese-vegetable-cutting-techniques/ | Ultimate list of Japanese Vegetable Cutting Techniques - The Chef Dojo
https://www.jeuxvideo.com/preview/1365111/knockout-city-le-jeu-de-dodgeball-en-met-il-plein-la-poire.htm | Preview Knockout City : Le jeu de Dodgeball en met-il plein la poire ? - jeuxvideo.com
https://www.netguru.com/blog/til-4 | TIL #4: ActiveRecord Dependent Hooks, Callbacks, Execution Order

https://en.wikipedia.org/wiki/Finger_heart | Finger heart - Wikipedia
https://www.midjourney.com/home/<USER>

https://docs.gitlab.com/ee/development/database/batched_background_migrations.html | Batched background migrations | GitLab
https://www.honeybadger.io/blog/nested-errors-in-ruby-with-exception-cause/ | Nested errors in Ruby with Exception#cause - Honeybadger Developer Blog
https://www.instagram.com/_spicymoustache_/ | Urbanfarmer • Educational • Zerowaste (@_spicymoustache_) • Instagram photos and videos
https://www.mesrecettesfaciles.fr/recipe/salade-de-carottes-betterave-et-pomme | Recette Salade de carottes, betterave et pomme facile | Mes recettes faciles

https://www.notion.so/citizenlab/Setting-up-custom-domain-names-a8d009aa5ff747e6b8fdc6524aad2d8b#a6878782c3964844a6877eaf69e66511 | (9+) Setting up custom domain names
https://www.tecmint.com/useful-nginx-command-examples/ | 10 Most Used Nginx Commands Every Linux User Must Know
https://phoenixnap.com/kb/nginx-start-stop-restart | How to Start, Stop, and Restart Nginx (systemctl & Nginx Commands)
https://guides.rubyonrails.org/active_record_callbacks.html#available-callbacks | Active Record Callbacks — Ruby on Rails Guides
https://docs.gitlab.com/ee/development/database/understanding_explain_plans.html | Understanding EXPLAIN plans | GitLab
https://docs.gitlab.com/ee/development/database/batched_background_migrations.html | Batched background migrations | GitLab
https://gitlab.com/gitlab-org/release/docs/-/blob/master/general/post_deploy_migration/readme.md#how-to-determine-if-a-post-deploy-migration-has-been-executed-on-gitlabcom | general/post_deploy_migration/readme.md · master · GitLab.org / release / GitLab Release Docs · GitLab
https://gitlab.com/groups/gitlab-com/gl-infra/-/epics/585 | Remove the blocking nature of post-deploy migrations (&585) · Epics · GitLab Infrastructure Team · GitLab
https://docs.gitlab.com/ee/development/migration_style_guide.html#high-traffic-tables | Migration Style Guide | GitLab
https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/background_migration/backfill_namespace_traversal_ids_children.rb | lib/gitlab/background_migration/backfill_namespace_traversal_ids_children.rb · master · GitLab.org / GitLab · GitLab

https://serversforhackers.com/c/tcp-load-balancing-with-nginx-ssl-pass-thru | TCP load balancing with Nginx (SSL Pass-thru) | Servers for Hackers
https://www.startpage.com/do/dsearch?query=nginx+ssl+pass+through&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://docs.nginx.com/nginx/admin-guide/load-balancer/tcp-udp-load-balancer/ | TCP and UDP Load Balancing | NGINX Plus
https://nginx.org/en/docs/stream/ngx_stream_core_module.html#stream | Module ngx_stream_core_module

https://linuxcommand.org/lc3_man_pages/seth.html | set man page
https://techblog.bozho.net/integration-tests-for-external-services/ | Integration Tests for External Services - Bozho's tech blog
https://docs.docker.com/engine/reference/commandline/exec/#examples | docker exec | Docker Documentation
https://askubuntu.com/questions/833833/what-does-command-do | bash - What does $(command) & do? - Ask Ubuntu

https://dnschecker.org/#CNAME/_0bf22a3ab4e94a41bd9dc344f31926d4.participa.mpudahuel.cl | DNS Checker - DNS Check Propagation Tool
https://coderwall.com/p/i34iza/rails-quick-tips-random-records | Rails Quick Tips - Random Records (Example)
https://www.startpage.com/do/dsearch?query=should+tests+contain+__init__.py&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://pytest.org/en/7.1.x/explanation/goodpractices.html | Good Integration Practices — pytest documentation
https://news.ycombinator.com/item?id=32333565 | Why do tree-based models still outperform deep learning on tabular data? | Hacker News

https://www.reddit.com/r/europe/comments/w722gg/the_swedish_warship_vasa_the_worlds_best/ | The Swedish warship Vasa - the world's best preserved 17th century ship : europe

https://8thlight.com/blog/researching-your-next-employer/ | Researching Your Next Employer: 3 Ways To Set Yourself Up For Success In Your Career | 8th Light
https://www.reddit.com/r/rails/comments/w2ofhd/best_authentication_in_2022_devise_clearance/ | Best authentication in 2022? Devise, Clearance, OAuth, anything else? : rails
https://planetscale.com/blog/ruby-on-rails-3-tips-for-deleting-data-at-scale | Ruby on Rails: 3 tips for deleting data at scale
https://gitlab.com/search?group_id=9970&nav_source=navbar&page=7&project_id=278964&repository_ref=master&search=match_response_schema&search_code=true | match_response_schema · Search · GitLab
https://gitlab.com/gitlab-org/gitlab/-/blob/master/doc/development/testing_guide/best_practices.md | doc/development/testing_guide/best_practices.md · master · GitLab.org / GitLab · GitLab
https://gitlab.com/gitlab-org/gitlab/-/blob/master/spec/support/matchers/schema_matcher.rb | spec/support/matchers/schema_matcher.rb · master · GitLab.org / GitLab · GitLab
https://gitlab.com/gitlab-org/gitlab/-/blob/master/spec/fixtures/api/schemas/feature_flags.json | spec/fixtures/api/schemas/feature_flags.json · master · GitLab.org / GitLab · GitLab
https://thoughtbot.com/blog/validating-json-schemas-with-an-rspec-matcher | Validating JSON Schemas with an RSpec Matcher
https://stackoverflow.com/questions/14528560/convert-an-array-to-hash-where-keys-are-the-indices?answertab=trending#tab-top | ruby - Convert an array to hash, where keys are the indices - Stack Overflow

https://www.reddit.com/r/brussels/comments/w98n49/traditionalauthentic_restaurants_korean/ | Traditional/Authentic restaurants (Korean, Senegalese, Thai, Peruvian, ...) : brussels
https://www.google.be/maps/place/Mont+Liban/@50.8303987,4.3588129,3a,75y,90t/data=!3m8!1e2!3m6!1sAF1QipP_HxMDCOkOa8yBeEBBISXs-ITXgShQ0-jQvLm5!2e10!3e12!6shttps:%2F%2Flh5.googleusercontent.com%2Fp%2FAF1QipP_HxMDCOkOa8yBeEBBISXs-ITXgShQ0-jQvLm5%3Dw203-h270-k-no!7i3120!8i4160!4m6!3m5!1s0x47c3c48a4d2a3bf1:0xb35fd5bfe52819ad!8m2!3d50.8302532!4d4.3589628!10e5 | Mont Liban - Google Maps
https://www.google.com/maps/place/Caspian/@50.8448409,4.3517329,18z/data=!4m12!1m6!3m5!1s0x47c3c47ed6089801:0xd7db38ad1dc1004b!2sCaspian!8m2!3d50.845264!4d4.3524672!3m4!1s0x47c3c47ed6089801:0xd7db38ad1dc1004b!8m2!3d50.845264!4d4.3524672 | Caspian - Google Maps
https://hinodeya.jimdofree.com/menu-du-soir/ | MENU DU SOIR - Restaurant HINODEYA home peage

https://forum.sentry.io/t/how-set-different-response-time-threshold-apdex-for-specfic-transactions-group/13548 | How set different Response Time Threshold (Apdex) for specfic transactions group? - Meta - #sentry
https://autumn-school.g1000.org/#details | G1000 Autumn School 2022 • Learn how to use deliberative democracy for more inclusive policy-making.

https://www.baeldung.com/linux/mapping-hostnames-ports | Mapping Hostnames with Ports in /etc/hosts | Baeldung on Linux
https://gist.github.com/allexradu/52f88344f207960621d5f086fcaf4b69 | OS X Setup virtual hosts on two different ports
https://hub.docker.com/_/nginx | Nginx - Official Image | Docker Hub
https://serverfault.com/questions/1101369/connect-failed-111-connection-refused-while-connecting-to-upstream-with-sta | nginx - connect() failed (111: Connection refused) while connecting to upstream with static website - Server Fault
https://docs.nginx.com/nginx/admin-guide/basic-functionality/managing-configuration-files/ | Creating NGINX Plus and NGINX Configuration Files | NGINX Plus
https://bl.ocks.org/jimothyGator/5436538 | Nginx configuration for Mac OS X with Homebrew, using sites-enabled directory. - bl.ocks.org
https://serverfault.com/questions/225948/how-to-restart-nginx-on-mac-os-x | mac osx - How to restart Nginx on Mac OS X? - Server Fault
https://www.digitalocean.com/community/tutorials/how-to-create-a-self-signed-ssl-certificate-for-nginx-in-ubuntu-16-04 | How To Create a Self-Signed SSL Certificate for Nginx in Ubuntu 16.04 | DigitalOcean
https://www.startpage.com/do/dsearch?query=google+openid+.well-known&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://developers.google.com/identity/protocols/oauth2/openid-connect | OpenID Connect  |  Google Identity  |  Google Developers
https://iamapps-public.int.belgium.be/.well-known/openid-configuration | iamapps-public.int.belgium.be/.well-known/openid-configuration
https://stackoverflow.com/questions/60988699/rails-how-to-get-public-key-from-json-web-keys | rubygems - Rails - How to get public key from JSON web keys? - Stack Overflow
https://8gwifi.org/jwkconvertfunctions.jsp | jwk to pem convertor, pem to jwk convert online, jwk openssl format pem
https://documentation-archive.connective.eu/en-us/IdentificationService/OpenIDConnectDiscovery.html | 6. OpenID Connect Discovery

https://stackoverflow.com/questions/38447315/ruby-on-rails-active-model-serializer-create-a-custom-json-serializer | Ruby On Rails - Active Model Serializer; Create a custom JSON serializer - Stack Overflow
https://stackoverflow.com/questions/21710886/use-active-model-serializer-with-a-non-activerecord-object?answertab=trending#tab-top | ruby on rails 3.2 - Use active_model_serializer with a non-ActiveRecord object - Stack Overflow
https://stackoverflow.com/questions/12485404/how-to-implement-multiple-different-serializers-for-same-model-using-activemodel | ruby on rails 3 - How to implement multiple different serializers for same model using ActiveModel::Serializers? - Stack Overflow

https://opendata-api.stib-mivb.be/OperationMonitoring/4.0/VehiclePositionByLine/61 | opendata-api.stib-mivb.be/OperationMonitoring/4.0/VehiclePositionByLine/61
https://promagma.gumroad.com/l/IEmpwF | Spoonfed Chinese
https://en.wikipedia.org/wiki/Programming_paradigm | Programming paradigm - Wikipedia
https://web.archive.org/web/20180920175619/http://www.sysml.cc/doc/37.pdf | Wayback Machine

https://ruby-doc.org/core-3.1.2/Enumerable.html#method-i-drop_while | Module: Enumerable (Ruby 3.1.2)

https://github.com/tomykaira/rspec-parameterized | tomykaira/rspec-parameterized: RSpec::Parameterized supports simple parameterized test syntax in rspec.
https://www.evanmiller.org/how-not-to-sort-by-average-rating.html | How Not To Sort By Average Rating – Evan Miller
https://stackoverflow.com/questions/22903248/delete-from-a-ruby-hash-returning-the-value-and-raising-an-exception-if-the-key | Delete from a Ruby Hash, returning the value and raising an exception if the key is not found - Stack Overflow

https://www.reddit.com/r/BuyItForLife/?f=flair_name%3A%22Currently%20sold%22 | Buy it for life: Durable, Quality, Practical
https://www.reddit.com/r/BuyItForLife/comments/1v8lx3/the_sidebar_series_part_two_post_all_your_info_on/ | The Sidebar Series Part Two. Post All Your Info on Buy it for life Apparel (Clothing, Jackets, Socks) here. : BuyItForLife

https://gallica.bnf.fr/ark:/12148/bpt6k9749208d/f14.item.texteImage | Curiosités des inventions et découvertes / [par Édouard Fournier] | Gallica

https://www.thefreedictionary.com/dodgy | Dodgy - definition of dodgy by The Free Dictionary

http://rspec.info/blog/2014/01/new-in-rspec-3-composable-matchers/ | New in RSpec 3: Composable Matchers
https://stackoverflow.com/questions/3160502/ruby-round-number-down-to-nearest-number-based-on-arbitrary-list-of-numbers?answertab=scoredesc#tab-top | arrays - Ruby: Round number down to nearest number based on arbitrary list of numbers - Stack Overflow
https://julialang.org/blog/2017/12/ml-pl/#fndef:tf | On Machine Learning and Programming Languages
https://stackoverflow.com/questions/35639401/python-yield-vs-ruby-yield | Python yield vs Ruby yield - Stack Overflow
https://www.oreilly.com/library/view/the-ruby-programming/9780596516178/ch07s09.html | Constant Lookup - The Ruby Programming Language [Book]
https://docs.google.com/document/d/1pn0sMKj-asJCSOVo00C2LB-oM-RYTlu_WVd3PBOGmZU/edit | Civic Engagement 3.0: Making Public Participation Powerful & Sexy - Google Docs

https://english.stackexchange.com/questions/13353/are-there-any-differences-between-update-and-upgrade | verbs - Are there any differences between "update" and "upgrade"? - English Language & Usage Stack Exchange
https://relishapp.com/rspec/rspec-core/docs/pending-and-skipped-examples/skip-examples | `skip` examples - Pending and skipped examples - RSpec Core - RSpec - Relish
https://stackoverflow.com/questions/3303347/given-a-class-see-if-instance-has-method-ruby | respond to - Given a class, see if instance has method (Ruby) - Stack Overflow
https://apidock.com/ruby/Module/method_defined%3F | method_defined? (Module) - APIdock
https://stackoverflow.com/questions/45210829/how-to-avoid-last-n-character-in-heredoc | ruby - How to avoid last \n character in heredoc - Stack Overflow

https://medium.com/@DrawandCode/how-and-why-json-schema-in-rails-always-saves-the-day-60ecc68ab303 | How and why JSON schema in Rails always saves the day | by Draw & Code | Medium

https://news.ycombinator.com/item?id=32136733 | Ask HN: I love programming but hate the industry. Can anyone relate? | Hacker News
https://www.foldnfly.com/#/1-0-0-0-0-1-1-1-2 | Fold 'N Fly » Paper Airplane Folding Instructions
https://stackoverflow.com/questions/54574530/json-schema-for-object-with-keys-of-random-numbers | jsonschema - JSON Schema for object with keys of random numbers - Stack Overflow
https://www.theregister.com/2022/07/18/electrical_engineers_extinction/ | Engineers on brink of extinction threaten entire ecosystems • The Register
https://news.ycombinator.com/item?id=32142711 | Electrical engineers on the brink of extinction threaten entire tech ecosystems | Hacker News

https://portainer.epic.citizenlab.co/timeout.html#!/auth | Portainer
https://www.liquidweb.com/kb/reverse-dns-lookup/ | How to Perform a Reverse DNS Lookup - Liquid Web
https://www.surgehq.ai/ | Surge AI | World's Most Powerful Data Labeling Platform

https://stackoverflow.com/questions/14405063/how-can-i-see-normal-print-output-created-during-pytest-run | python - How can I see normal print output created during pytest run? - Stack Overflow
https://docs.pytest.org/en/latest/how-to/writing_plugins.html | Writing plugins — pytest documentation
https://pytest.org/en/7.1.x/how-to/fixtures.html# | How to use fixtures — pytest documentation
https://www.testcult.com/handle-test-data-the-right-way-in-pytest/ | Handle test data the right way in Pytest | Test Cult
https://stackoverflow.com/questions/5137497/find-the-current-directory-and-files-directory | python - Find the current directory and file's directory - Stack Overflow
https://stackoverflow.com/questions/2632199/how-do-i-get-the-path-of-the-current-executed-file-in-python | How do I get the path of the current executed file in Python? - Stack Overflow
https://peps.python.org/pep-0505/#built-in-maybe | PEP 505 – None-aware operators | peps.python.org
https://peps.python.org/pep-0636/ | PEP 636 – Structural Pattern Matching: Tutorial | peps.python.org
https://www.andreagrandi.it/2018/10/16/using-ipdb-with-python-37-breakpoint/ | Andrea Grandi – Using ipdb with Python 3.7.x breakpoint
https://python-poetry.org/docs/basic-usage/ | Basic usage | Documentation | Poetry - Python dependency management and packaging made easy
https://www.youtube.com/watch?v=q6QTCXr9oPI | Freckle Loves You - YouTube
https://api.rubyonrails.org/classes/ActiveRecord/ReadonlyAttributes/ClassMethods.html | ActiveRecord::ReadonlyAttributes::ClassMethods
https://stackoverflow.com/questions/2850418/rails-sti-prevent-base-class-from-instantiation | Rails STI - Prevent base class from instantiation - Stack Overflow
https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle#The_modern_algorithm | Fisher–Yates shuffle - Wikipedia
https://en.wikipedia.org/wiki/Template_method_pattern | Template method pattern - Wikipedia
https://stackoverflow.com/questions/2678792/can-i-debug-with-python-debugger-when-using-py-test-somehow | unit testing - Can I debug with python debugger when using py.test somehow? - Stack Overflow
https://pol.is/home | Polis
https://www.startpage.com/do/dsearch?query=network+effect&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://docs.python.org/3/library/functools.html | functools — Higher-order functions and operations on callable objects — Python 3.10.5 documentation
https://news.ycombinator.com/item?id=6581622 | I didn't know what "Single Dispatch Functions" was all about. Sounded very abstr... | Hacker News

https://kubernetes.io/docs/reference/kubectl/cheatsheet/ | kubectl Cheat Sheet | Kubernetes
https://stackoverflow.com/questions/978061/http-get-with-request-body | rest - HTTP GET with request body - Stack Overflow
https://graphql.org/learn/queries/ | Queries and Mutations | GraphQL
https://www.lesoir.be/453602/article/2022-07-12/inondations-dans-lurgence-la-nature-est-passee-au-second-plan | Inondations: dans l’urgence, la nature est passée au second plan - Le Soir
https://data-flair.training/blogs/python-open-source-projects/ | 56 Groundbreaking Python Open-source Projects - Get started with Python - DataFlair
https://www.testcult.com/handle-test-data-the-right-way-in-pytest/ | Handle test data the right way in Pytest | Test Cult
https://stackoverflow.com/questions/23984973/how-to-run-tests-without-installing-package | python - How to run tests without installing package? - Stack Overflow

https://stackoverflow.com/questions/37016546/kubernetes-how-do-i-delete-clusters-and-contexts-from-kubectl-config | Kubernetes: How do I delete clusters and contexts from kubectl config? - Stack Overflow
https://stackoverflow.com/questions/56358405/your-bundle-is-locked-to-ffi-1-11-0-but-that-version-could-not-be-found-in-an | ruby - Your bundle is locked to ffi (1.11.0), but that version could not be found in any of the sources listed in your Gemfile - Stack Overflow
https://stackoverflow.com/questions/3749512/python-group-by | Python group by - Stack Overflow
https://www.lesoir.be/453386/article/2022-07-10/uber-files-bruxelles-tous-les-coups-duber-sont-tordus | Uber Files: à Bruxelles, tous les coups d’Uber sont tordus - Le Soir

https://maximomussini.com/posts/practical-applications-of-the-singleton-class | Practical Applications of the Singleton Class in Ruby · Máximo Mussini
https://stackoverflow.com/questions/45511956/remove-a-named-volume-with-docker-compose | postgresql - Remove a named volume with docker-compose? - Stack Overflow
https://stackoverflow.com/questions/2098131/rails-how-to-list-database-tables-objects-using-the-rails-console | Rails: How to list database tables/objects using the Rails console? - Stack Overflow
https://stackoverflow.com/questions/6554834/what-exactly-are-the-components-of-rails-activerecord-actioncontroller-etc | What exactly are the "components" of Rails (ActiveRecord, ActionController, etc.)? - Stack Overflow
https://stackoverflow.com/questions/60421158/would-it-be-possible-to-have-multiple-database-connection-pools-in-rails-to-swit | ruby - Would it be possible to have multiple database connection pools in rails to switch between? - Stack Overflow
https://stackoverflow.com/questions/61513585/is-it-possible-to-have-multiple-activerecord-connections | ruby on rails - Is it possible to have multiple ActiveRecord connections? - Stack Overflow
https://about.gitlab.com/company/okrs/ | Objectives and Key Results (OKRs) | GitLab
https://github.com/erichard/awesome-gdpr | erichard/awesome-gdpr: A curated list of GDPR-compliant tools for websites creators
https://gdprchecklist.io/ | The GDPR Checklist - Your GDPR compliance checklist

https://ftxfuturefund.org/projects/ | Project Ideas – Future Fund
https://www.startpage.com/do/dsearch?query=tips+to+keep+git+commit+message+short&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://www.startpage.com/do/dsearch?query=actor+parallelism&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://www.englishacademy.be/eng/ | English courses in Brussels for fast progress | ENGLISH ACADEMY
https://stackoverflow.com/questions/50791303/kubectl-error-you-must-be-logged-in-to-the-server-unauthorized-when-accessing | amazon web services - kubectl error You must be logged in to the server (Unauthorized) when accessing EKS cluster - Stack Overflow

https://www.startpage.com/do/dsearch?query=openness&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://www.startpage.com/do/dsearch?query=difference+thread+process&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://www.startpage.com/do/dsearch?query=non+thread+safety+example&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://stackoverflow.com/questions/2098131/rails-how-to-list-database-tables-objects-using-the-rails-console | Rails: How to list database tables/objects using the Rails console? - Stack Overflow
https://stackoverflow.com/questions/322470/can-i-invoke-an-instance-method-on-a-ruby-module-without-including-it | Can I invoke an instance method on a Ruby module without including it? - Stack Overflow

https://github.com/ankane/multiverse | ankane/multiverse: Multiple databases for Rails
https://news.ycombinator.com/item?id=32003266 | Ask HN: Why do I struggle to follow corporate meetings? | Hacker News

https://www.manomano.fr/cat/robinet+3+voies+douchette | Robinet 3 voies douchette à prix mini
https://en.biginfinland.com/ | Big in Finland - Finland made easy
https://www.oras.com/en/products/design-lines/oras-stela/washbasin-faucet/4806F-105 | Oras Stela bathroom faucets for any bathroom style | Oras
https://www.oras.com/en/products/oras-safira/washbasin-faucet-with-washing-machine-valve/1014F-104#guides | Oras Safira | Oras

https://home.cern/news/news/physics/lhcb-discovers-three-new-exotic-particles | LHCb discovers three new exotic particles | CERN
https://stackoverflow.com/questions/18358717/ruby-elegantly-convert-variable-to-an-array-if-not-an-array-already | Ruby - elegantly convert variable to an array if not an array already - Stack Overflow
https://www.youtube.com/watch?v=dhW4XFGQB4o | Primitive Technology: Iron knife made from bacteria - YouTube
https://stackoverflow.com/questions/8015775/check-to-see-if-an-array-is-already-sorted | ruby - Check to see if an array is already sorted? - Stack Overflow
https://stackoverflow.com/questions/30400477/how-to-open-local-files-in-swagger-ui | How to open local files in Swagger-UI - Stack Overflow
https://docusaurus.io/feature-requests/p/api-documentation-swagger | Feedback | Docusaurus
https://dmlls.medium.com/redoc-docs-on-gh-pages-97a8926e9e0f | Publish ReDoc (OpenAPI) docs on GitHub Pages | Medium
https://redocly.com/docs/api-reference-docs/configuration/functionality | Configure API docs functionality
https://en.wikipedia.org/wiki/Generative_adversarial_network | Generative adversarial network - Wikipedia

https://docs.docker.com/engine/reference/commandline/attach/ | docker attach | Docker Documentation
https://dba.stackexchange.com/questions/6051/what-is-the-default-order-of-records-for-a-select-statement-in-mysql | index - What is the default order of records for a SELECT statement in MySQL? - Database Administrators Stack Exchange

https://api.rubyonrails.org/v2.3.8/classes/ActiveSupport/CoreExtensions/Hash/ReverseMerge.html | Module: ActiveSupport::CoreExtensions::Hash::ReverseMerge
https://shelly.cloud/shelly-plus-2pm/ | Shelly Plus 2PM - Shelly Cloud
https://www.lesoir.be/449096/article/2022-06-17/voyager-en-train-une-solution-plus-couteuse | Voyager en train: une solution plus coûteuse? - Le Soir
https://www.europeansleeper.eu/ | European Sleeper - spoorwegmaatschappij speciaal voor nachttreinen
https://en.wikipedia.org/wiki/Normal_distribution#Bernstein's_theorem | Normal distribution - Wikipedia
https://en.wikipedia.org/wiki/Q%E2%80%93Q_plot | Q–Q plot - Wikipedia
https://en.wikipedia.org/wiki/Cumulant | Cumulant - Wikipedia

https://stackoverflow.com/questions/742466/how-can-i-reverse-the-order-of-lines-in-a-file | shell - How can I reverse the order of lines in a file? - Stack Overflow
https://serverfault.com/questions/151635/how-to-read-backward-from-the-end-of-file-in-less-or-more | unix - How to read backward from the end of file in less or more? - Server Fault
https://apidock.com/rails/ActiveRecord/Base/readonly%3F | readonly? (ActiveRecord::Base) - APIdock

https://www.erasme.ulb.ac.be/fr/a-propos-de-l-hopital/annuaire-du-personnel/hut-florence | Annuaire du personnel | HUT Florence | Hôpital Erasme
https://superuser.com/questions/949428/whats-the-difference-between-127-0-0-1-and-0-0-0-0 | networking - What's the difference between 127.0.0.1 and 0.0.0.0? - Super User
https://www.quora.com/What-is-the-difference-between-127-0-0-1-and-192-168-x-y-in-terms-of-computer-networking | What is the difference between 127.0.0.1 and 192.168.x.y in terms of computer networking? - Quora

https://www.cvosemper.be/opleidingen/vakgebied/320/zakelijk-engels?inschrijven=0&utm_source=phplist103&utm_medium=email&utm_content=HTML&utm_campaign=Semper+in+the+mood%3F | Cursussen Professioneel Engels - CVO Semper
https://en.wikipedia.org/wiki/Common_European_Framework_of_Reference_for_Languages#Common_reference_levels | Common European Framework of Reference for Languages - Wikipedia

https://stackoverflow.com/questions/200469/what-is-the-difference-between-a-process-and-a-thread?answertab=scoredesc#tab-top | multithreading - What is the difference between a process and a thread? - Stack Overflow

https://msp-greg.github.io/puma/ | File: README — Puma master
https://en.wikipedia.org/wiki/Thread_safety | Thread safety - Wikipedia
https://learn.co/lessons/config-ru-tutorial | Config Ru Tutorial - Learn.co

https://news.ycombinator.com/item?id=31931887 | Why don't more people use throat mics? | Hacker News
https://nim-lang.org/blog/2022/06/29/mastering-nim.html | Mastering Nim - now available on Amazon - Nim Blog
https://logger.rocketjob.io/api.html | Semantic Logger for Ruby or Rails. Supports Graylog, Bugsnag, MongoDB, Splunk, Syslog, NewRelic.
https://stackoverflow.com/questions/11770552/how-to-get-rails-logger-printing-to-the-console-stdout-when-running-rspec | logging - How to get Rails.logger printing to the console/stdout when running rspec? - Stack Overflow
https://docs.gitlab.com/ee/development/logging.html | GitLab Developers Guide to Logging | GitLab
https://en.wikipedia.org/wiki/Ragel | Ragel - Wikipedia

https://inbloombakery.com/baklava-layer-cake/ | Baklava Layer Cake - In Bloom Bakery
https://www.youtube.com/watch?v=i_TBTHk0q4w | Pokémon Sweets Paradise EP01: Pokémon Special Desserts | Pokémon Fun Video | Pokémon Kids TV​ - YouTube
https://www.advocate.com/arts-entertainment/art/2015/02/23/bookshelves-cornelius-mccarthy | On the Bookshelves: Cornelius McCarthy
https://stackoverflow.com/questions/39376786/docker-and-symlinks | Docker and symlinks - Stack Overflow
https://stackoverflow.com/questions/64943693/what-are-the-best-practices-for-structuring-a-fastapi-project | python - What are the best practices for structuring a FastAPI project? - Stack Overflow
https://github.com/Netflix/dispatch/tree/master/src/dispatch | dispatch/src/dispatch at master · Netflix/dispatch

https://stackoverflow.com/questions/50904192/remove-multiple-keys-from-jsonb-column-in-one-statement | postgresql - Remove multiple keys from jsonb column in one statement - Stack Overflow
https://www.postgresql.org/docs/current/functions-json.html | PostgreSQL: Documentation: 14: 9.16. JSON Functions and Operators
https://stackoverflow.com/questions/43095909/select-from-literal-values-not-in-table-in-postgresql | sql - Select from literal values not in table in PostgreSQL? - Stack Overflow
https://stackoverflow.com/questions/55733691/postgresql-jsonb-update-key-name-on-all-rows-with-a-single-query | ruby on rails - PostgreSQL JSONB - Update key name on all rows with a single query - Stack Overflow
https://www.notion.so/citizenlab/Data-processing-CitizenLab-c7141d33b8cd4cda88d3e599c021dc8a | (9) Data processing @ CitizenLab

https://github.com/nepalez/rspec-sqlimit | nepalez/rspec-sqlimit: RSpec matcher to control SQL queries made by block of code
https://joshuatauberer.medium.com/write-joyous-git-commit-messages-2f98891114c4 | Write joyous git commit messages. Tips (or a convention) for writing good… | by Joshua Tauberer | Medium

https://stackoverflow.com/questions/2191409/rails-in-what-order-does-dependent-destroy-follow | callback - Rails - in what order does dependent => destroy follow? - Stack Overflow
https://guides.rubyonrails.org/active_record_callbacks.html | Active Record Callbacks — Ruby on Rails Guides
https://www.bigbinary.com/books/learn-rubyonrails-book/defining-associations-and-best-practices | BigBinary Books - Defining associations and best practices
https://dev.to/mickeytgl/the-good-and-bad-of-activerecord-callbacks-p4a | The good and bad of ActiveRecord callbacks - DEV Community
https://stackoverflow.com/questions/6301054/check-all-associations-before-destroy-in-rails | Check all associations before destroy in rails - Stack Overflow
https://stackoverflow.com/questions/54421011/ordering-of-callbacks-in-activerecord | ruby on rails - Ordering of callbacks in ActiveRecord - Stack Overflow
https://devdocs.io/rails~7.0/activerecord/callbacks | Ruby on Rails 7.0 / ActiveRecord::Callbacks — DevDocs

https://openapi.tools/#documentation | OpenAPI.Tools
https://news.ycombinator.com/item?id=25990702 | FastAPI framework, high perf, easy to learn, fast to code, ready for production | Hacker News
https://sosoir.lesoir.be/voici-le-hotspot-incontournable-de-lete-bruxelles | Voici le hotspot incontournable de l’été à Bruxelles
https://stackoverflow.com/questions/59861277/puma-stuck-with-message-early-termination-of-worker-on-rails-6-api-only-projec | amazon web services - Puma stuck with message "Early termination of worker" on Rails 6 API only project at Elastic Beanstalk - Stack Overflow
https://manpages.ubuntu.com/manpages/impish/man1/pumactl.1.html | Ubuntu Manpage: pumactl - command line client for puma
https://www.startpage.com/do/dsearch?query=pid+port&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://unix.stackexchange.com/questions/106561/finding-the-pid-of-the-process-using-a-specific-port | linux - Finding the PID of the process using a specific port? - Unix & Linux Stack Exchange
https://stackoverflow.com/questions/3855127/find-and-kill-process-locking-port-3000-on-mac | node.js - Find (and kill) process locking port 3000 on Mac - Stack Overflow

https://www.rabbitmq.com/vhosts.html | Virtual Hosts — RabbitMQ

https://myst729.github.io/posts/2019/on-merging-pull-requests/ | Why I’m against merging pull requests in squash mode or rebase mode? « dg
https://stackoverflow.com/questions/23911740/get-records-where-json-column-key-is-null | postgresql - Get records where json column key is null - Stack Overflow
https://docs.google.com/document/d/1siYPW2unAfX_FtWlL68WZJMPR_25eD3wtgSnAoftuk4/edit | Innovation in Politics - AI profile - Google Docs
https://stackoverflow.com/questions/3855127/find-and-kill-process-locking-port-3000-on-mac | node.js - Find (and kill) process locking port 3000 on Mac - Stack Overflow
https://nono.ma/port-5000-used-by-control-center-in-macos-controlce | [Solved] Port 5000 Used by Control Center in macOS Monterey · Nono Martínez Alonso
https://dys2p.com/en/2021-12-tamper-evident-protection.html | dys2p
https://www.youtube.com/watch?v=W07ZpEv9Sog | DEFCON 19: Introduction to Tamper Evident Devices - YouTube
https://www.lastweekasavciso.com/p/a-history-of-signature-integrity-verification | Ancient Authentication and Integrity Checking

https://www.bransolo.com/en/ | Bran Sólo / Painting, illustration and design – Pintura, ilustración y diseño – Portafolio del ilustrador español Bran Sólo

https://lake.medium.com/debounce-and-throttle-activejob-easiest-way-e97fd007fd6e | Debounce and throttle ActiveJob easiest way | by ilake Chang | May, 2022 | Medium
https://thedecodedcoder.medium.com/concurrency-control-for-activejob-with-rails-6-9fd52acd67e | Concurrency control for ActiveJob with Rails 6 | by Viral Patel | Medium
https://github.com/nickelser/activejob-traffic_control | nickelser/activejob-traffic_control: Rate limiting/job enabling for ActiveJob using distributed locks in Redis or Memcached.
https://blog.jetbrains.com/blog/2022/03/30/beamsearch-in-code-generation/ | BeamSearch in code generation | JetBrains News
https://docs.gitlab.com/ee/development/directory_structure.html | Backend directory structure | GitLab
https://medium.com/@nakabonne/depth-of-module-f62dac3c2fdb | Depth of module. This article summarizes the concept of… | by Ryo Nakao | Medium
https://nakabonne.dev/posts/depth-of-module/ | Ryo Nakao
https://uclouvain.be/prog-2022-info2m-programme | Master [120] : ingénieur civil en informatique - Programme détaillé par matière
https://uclouvain.be/cours-2022-linfo2364 | Mining Patterns in Data

https://idioms.thefreedictionary.com/because | Because - Idioms by The Free Dictionary
https://stackoverflow.com/questions/22257188/how-to-calculate-percentages-with-rounding | ruby - How to calculate percentages with rounding - Stack Overflow
https://github.com/d3/d3-force | d3/d3-force: Force-directed graph layout using velocity Verlet integration.
https://github.com/vasturiano/react-force-graph/blob/master/example/highlight/index.html | react-force-graph/index.html at master · vasturiano/react-force-graph
https://kotaku.com/zachtronics-farewell-goodbye-closing-eliza-last-call-bb-1849096767 | Goodbye Zachtronics, Developers Of Very Cool Puzzle Video Games
https://www.bejarano.io/hardening-macos/ | Hardening macOS

https://chidiwilliams.com/post/crafting-interpreters-a-review/ | Crafting Interpreters: A Review
https://stackoverflow.com/questions/53231219/ruby-forward-receiver-arguments-and-block-across-procs | Ruby: forward receiver, arguments and block across procs - Stack Overflow
https://stackoverflow.com/questions/38327566/check-if-number-is-nan | ruby - Check if number is NaN - Stack Overflow

https://sosoir.lesoir.be/4-restos-de-burgers-bruxellois-la-deco-ultra-originale | 4 restos de burgers bruxellois à la déco ultra originale
https://www.lesoir.be/450232/article/2022-06-23/peste-noire-lorigine-de-la-pandemie-identifiee-des-siecles-plus-tard-video | Peste noire : l'origine de la pandémie identifiée des siècles plus tard (vidéo) - Le Soir
https://www.lesoir.be/450210/article/2022-06-23/bruxelles-voici-comment-le-boulevard-adolphe-max-va-etre-reamenage | Bruxelles: voici comment le boulevard Adolphe Max va être réaménagé - Le Soir

https://stackoverflow.com/questions/1163032/rails-has-many-with-alias-name | has many - Rails has_many with alias name - Stack Overflow
https://www.lesoir.be/449915/article/2022-06-22/la-wallonie-compte-141-metiers-en-penurie | La Wallonie compte 141 métiers en pénurie - Le Soir
https://www.startpage.com/do/dsearch?query=buffet+bruxelles&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://bruxellessecrete.com/terrasses-bruxelles/ | 20 somptueuses terrasses cachées qui sentent bon les vacances à Bruxelles - Bruxelles Secrète
https://www.google.be/maps/place/Jardin+Rooftop/@50.8491636,4.2795682,13z/data=!4m9!1m2!2m1!1sjardin!3m5!1s0x47c3c34adbfe8a9f:0x3872cbc982f69d76!8m2!3d50.8491636!4d4.349606!15sCgZqYXJkaW5aCCIGamFyZGlukgEDYmFy | Jardin Rooftop - Google Maps

https://basicappleguy.com/basicappleblog/settingsapp | System Preferences Reimagined on macOS — Basic Apple Guy
https://en.wikipedia.org/wiki/Visual_analytics | Visual analytics - Wikipedia
https://stackoverflow.com/questions/54114045/how-to-implement-changes-made-to-docker-compose-yml-to-detached-running-containe | How to implement changes made to docker-compose.yml to detached running containers - Stack Overflow

https://www.startpage.com/do/dsearch?query=materialized+view+append-only&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://web.archive.org/web/20220616013118/https://cajundiscordian.medium.com/is-lamda-sentient-an-interview-ea64d916d917 | Is LaMDA Sentient? — an Interview | by Blake Lemoine | Jun, 2022 | Medium
https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/405 | 405 Method Not Allowed - HTTP | MDN
https://stackoverflow.com/questions/17688610/rspec-lazy-subject | ruby - RSpec lazy subject - Stack Overflow
https://apidock.com/ruby/v2_5_5/Kernel/throw | throw (Kernel) - APIdock
https://stackoverflow.com/questions/22386719/logger-debug-vs-puts-in-rails-debugging | logging - logger.debug vs puts in Rails debugging? - Stack Overflow
https://tasdikrahman.me/2020/07/07/structured-logging-in-rails/ | Structured logging in Rails · Tasdik Rahman
https://docs.gitlab.com/ee/development/logging.html#gitlab-developers-guide-to-logging | GitLab Developers Guide to Logging | GitLab
https://docs.gitlab.com/ee/administration/logs.html | Log system | GitLab
https://guides.rubyonrails.org/debugging_rails_applications.html#the-logger | Debugging Rails Applications — Ruby on Rails Guides
https://github.com/reidmorrison/rails_semantic_logger | reidmorrison/rails_semantic_logger: Rails Semantic Logger replaces the Rails default logger with Semantic Logger
https://logger.rocketjob.io/rails.html | Semantic Logger for Ruby or Rails. Supports Graylog, Bugsnag, MongoDB, Splunk, Syslog, NewRelic.

https://stackoverflow.com/questions/10215197/git-search-for-string-in-a-single-files-history | Git search for string in a single file's history - Stack Overflow
https://git-scm.com/docs/git-log | Git - git-log Documentation
https://gpuopen.com/rmv-opensource/ | Radeon™ Memory Visualizer is now Open Source - GPUOpen
https://en.wikipedia.org/wiki/Kolmogorov%E2%80%93Smirnov_test | Kolmogorov–Smirnov test - Wikipedia

https://doc.akka.io/docs/akka/2.5.32/guide/introduction.html#how-to-get-started | Introduction to Akka • Akka Documentation

https://github.com/deivid-rodriguez/pry-byebug#matching-byebug-behaviour | deivid-rodriguez/pry-byebug: Step-by-step debugging and stack navigation in Pry
https://brakemanscanner.org/docs/ | Brakeman: Documentation
https://docs.gitlab.com/ee/development/migration_style_guide.html | Migration Style Guide | GitLab

https://store.steampowered.com/app/1176470/Terra_Invicta/ | Terra Invicta on Steam

https://context.reverso.net/traduction/francais-anglais/exhaustivit%C3%A9 | exhaustivité - Traduction en anglais - exemples français | Reverso Context
https://en.wikipedia.org/wiki/Japanese_honorifics#Kun | Japanese honorifics - Wikipedia
https://en.wikipedia.org/wiki/Visual_analytics | Visual analytics - Wikipedia
https://stackoverflow.com/questions/54114045/how-to-implement-changes-made-to-docker-compose-yml-to-detached-running-containe | How to implement changes made to docker-compose.yml to detached running containers - Stack Overflow

https://docs.docker.com/compose/reference/up/ | docker-compose up | Docker Documentation
https://www.lesoir.be/448735/article/2022-06-16/la-suppression-des-allocations-dinsertion-sans-effet-sur-lemploi | La suppression des allocations d’insertion sans effet sur l’emploi - Le Soir

https://www.tutorialspoint.com/ruby/ruby_modules.htm | Ruby - Modules and Mixins
https://stackoverflow.com/questions/318850/private-module-methods-in-ruby | Private module methods in Ruby - Stack Overflow
https://github.com/airbnb/ruby | airbnb/ruby: Ruby Style Guide
https://english.stackexchange.com/questions/40173/what-is-the-difference-between-in-and-within | meaning - What is the difference between "in" and "within"? - English Language & Usage Stack Exchange
https://developer.mozilla.org/en-US/docs/Web/HTTP/Status#client_error_responses | HTTP response status codes - HTTP | MDN
https://www.quora.com/What-are-some-good-books-or-articles-about-SSL-TLS | What are some good books or articles about SSL/TLS? - Quora
https://kemptechnologies.com/white-papers/security-building-blocks-tls/ | What is SSL and how do SSL Certificates affect VPNS, Web Servers and Load Balancers
https://reproof.app/blog/document-first-then-build | Write documentation first. Then build. · reproof

https://en.wikipedia.org/wiki/Big_Secrets | Big Secrets - Wikipedia
https://crontab.guru/#5_0_1_*_0 | Crontab.guru - The cron schedule expression editor
https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/501 | 501 Not Implemented - HTTP | MDN
https://stevenloria.com/python-best-practice/ | Python Best Practice Patterns by Vladimir Keleshev (Notes) | stevenloria.com
https://www.lesoir.be/448485/article/2022-06-14/infraction-la-loi-sur-laccueil-las-le-tribunal-du-travail-envoie-un | Infraction à la loi sur l’accueil: las, le tribunal du travail envoie un avertissement net à Sammy Mahdi - Le Soir

https://stackoverflow.com/questions/2098131/rails-how-to-list-database-tables-objects-using-the-rails-console | Rails: How to list database tables/objects using the Rails console? - Stack Overflow
https://stackoverflow.com/questions/10527997/should-a-method-ending-in-question-mark-return-only-a-boolean | ruby - Should a method ending in ? (question mark) return only a boolean? - Stack Overflow
https://stackoverflow.com/questions/34724980/finding-a-string-in-docker-logs-of-container | grep - Finding a string in docker logs of container - Stack Overflow
https://www.deepl.com/translator#fr/en/J'ai%20completely%20d%C3%A9chir%C3%A9%20mon%20pantalon. | DeepL Translate: The world's most accurate translator
https://www.deepl.com/translator#fr/en/La%20chasse%20est%20ouverte. | DeepL Translate: The world's most accurate translator
https://www.deepl.com/translator#fr/en/embarrasser | DeepL Translate: The world's most accurate translator
https://www.deepl.com/translator#fr/en/lui%20donner%20un%20petit%20coup%20de%20pouce | DeepL Translate: The world's most accurate translator
https://www.deepl.com/translator#fr/en/Ils%20ne%20s'affichent%20pas. | DeepL Translate: The world's most accurate translator
https://www.deepl.com/translator#fr/en/collect%C3%A9es%20via%20des%20questions%20ouvertes | DeepL Translate: The world's most accurate translator
https://www.deepl.com/translator | DeepL Translate: The world's most accurate translator
https://www.thefreedictionary.com/pair%20off | Pair off - definition of pair off by The Free Dictionary
https://context.reverso.net/traduction/anglais-francais/hog+all | hog all - Traduction en français - exemples anglais | Reverso Context
https://news.ycombinator.com/item?id=******** | How to Do a Handstand | Hacker News
https://www.rabbitmq.com/tutorials/tutorial-four-python.html | RabbitMQ tutorial - Routing — RabbitMQ

https://www.lesoir.be/448112/article/2022-06-13/le-ptb-tel-quil-est-et-le-ptb-tel-quil-se-vend | Le PTB tel qu’il est et le PTB tel qu’il se vend - Le Soir
https://www.youtube.com/watch?v=5XWEVoI40sE | THE INSIDE OUTTAKES - Bo Burnham (4K) - YouTube

https://stackoverflow.com/questions/8509396/git-pull-results-in-extraneous-merge-branch-messages-in-commit-log | github - Git pull results in extraneous "Merge branch" messages in commit log - Stack Overflow
https://github.com/markets/awesome-ruby | markets/awesome-ruby: A collection of awesome Ruby libraries, tools, frameworks and software
https://github.com/statsd/statsd | statsd/statsd: Daemon for easy but powerful stats aggregation
https://superuser.com/questions/1055281/do-web-browsers-use-different-outgoing-ports-for-different-tabs | networking - Do web browsers use different outgoing ports for different tabs? - Super User
https://www.zainrizvi.io/blog/why-software-engineers-like-woodworking/ | Why Software Engineers like Woodworking
https://ruby-doc.org/core-2.0.0/String.html#method-i-tr | Class: String (Ruby 2.0.0)
https://ruby-doc.org/core-2.7.5/Hash.html#method-i-select | Class: Hash (Ruby 2.7.5)
https://gist.github.com/rafbm/9068f377f54b6b7a0915 | Ruby String#tr VS String#gsub benchmark

https://www.startpage.com/do/dsearch?query=why+is+python+slower+than+javascript&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search Results
https://eregon.me/blog/2019/11/10/the-delegation-challenge-of-ruby27.html | The Delegation Challenge of Ruby 2.7 · On the Edge of Ruby
https://www.toptal.com/ruby/ruby-pattern-matching-tutorial | Introduction to Pattern Matching in Ruby | Toptal
https://news.ycombinator.com/item?id=31668274 | Blender 3.2 | Hacker News

https://dictionary.cambridge.org/grammar/british-grammar/apart-from-or-except-for | Apart from or except for ? - English Grammar Today - Cambridge Dictionary
https://blog.saeloun.com/2020/04/08/heredoc-in-ruby-and-rails.html | Heredocs and how to use them in Ruby and Rails | Saeloun Blog
https://www.designcise.com/web/tutorial/how-to-create-multiline-strings-in-ruby | How to Create Multiline Strings in Ruby? - Designcise

https://en.wikipedia.org/wiki/Markov_chain_Monte_Carlo | Markov chain Monte Carlo - Wikipedia

file:///Users/<USER>/Downloads/exploration.html | Exploration of similarity indices
https://en.wikipedia.org/wiki/Renkonen_similarity_index | Renkonen similarity index - Wikipedia
https://en.wikipedia.org/wiki/Jaccard_index | Jaccard index - Wikipedia
https://en.wikipedia.org/wiki/Definite_matrix | Definite matrix - Wikipedia
https://untools.co/ | Tools for better thinking | Untools
https://kagi.com/pricing | Kagi Search

https://stackoverflow.com/questions/15789540/methods-local-variable-with-same-name-as-another-method | ruby - method's local variable with same name as another method - Stack Overflow
https://stackoverflow.com/questions/17646643/how-do-i-fetch-multiple-hash-values-at-once | ruby on rails - How do I fetch multiple hash values at once? - Stack Overflow
https://stackoverflow.com/questions/18563824/using-enumerablezip-on-an-array-of-arrays | ruby - Using Enumerable#zip on an Array of Arrays - Stack Overflow
https://relishapp.com/rspec/rspec-mocks/docs/setting-constraints/matching-arguments | Matching arguments - Setting constraints - RSpec Mocks - RSpec - Relish
https://stackoverflow.com/questions/3321011/parsing-xls-and-xlsx-ms-excel-files-with-ruby | Parsing XLS and XLSX (MS Excel) files with Ruby? - Stack Overflow
https://relishapp.com/rspec/rspec-core/docs/example-groups/shared-examples | shared examples - Example groups - RSpec Core - RSpec - Relish
https://evilmartians.com/chronicles | Martian Chronicles, Evil Martians’ team blog
https://github.com/nesquena/rabl | nesquena/rabl: General ruby templating with json, bson, xml, plist and msgpack support
https://about.gitlab.com/blog/2022/04/05/gitlab-top-devops-tooling-metrics-and-targets/ | The top DevOps tooling metrics and targets at GitLab | GitLab

https://stackoverflow.com/questions/37990997/how-to-sort-one-array-based-on-another-array-using-ruby | How to sort one array based on another array using Ruby - Stack Overflow
https://stackoverflow.com/questions/8415240/how-to-merge-ruby-hashes | hashmap - How to merge Ruby hashes - Stack Overflow
https://drive.google.com/file/d/1vVNUye-1JNJnqP4A0704sjtF7gs_MpCI/edit | 000-preamble.pdf - Google Drive
https://openreview.net/forum?id=3jKiduENcO | Teaching Deep Learning, a boisterous ever-evolving field | OpenReview
https://billmei.net/blog/friendship | Friendships form via shared context, not shared activities | Bill Mei
https://www.metacritic.com/pictures/20-sequels-better-than-the-original/16 | 20 Movie Sequels Better Than the Originals - Metacritic
https://fr.wikipedia.org/wiki/Mill%C3%A9nium_(s%C3%A9rie_litt%C3%A9raire) | Millénium (série littéraire) — Wikipédia

https://www.startpage.com/do/dsearch?query=glossaire+h%C3%A9ritage&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.startpage.com/do/dsearch?query=web+development+glossary&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.startpage.com/do/dsearch?query=learn+embedded+system&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.startpage.com/do/dsearch?query=russ+olsen&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://fritzing.org/projects/ | Fritzing
https://www.larousse.fr/dictionnaires/francais/archa%C3%AFque/5000 | Définitions : archaïque - Dictionnaire de français Larousse
https://www.gruun.brussels/plants | GRUUN — plants

https://news.ycombinator.com/item?id=31503201&p=2 | Ask HN: What game do you wish existed? | Hacker News
https://docs.python.org/3.10/whatsnew/3.10.html | What’s New In Python 3.10 — Python 3.10.4 documentation
https://www.cs.utexas.edu/users/EWD/transcriptions/EWD08xx/EWD831.html | E.W. Dijkstra Archive: Why numbering should start at zero (EWD 831)
https://numpy.org/doc/stable/user/absolute_beginners.html | NumPy: the absolute basics for beginners — NumPy v1.22 Manual
https://pythontutor.com/visualize.html#mode=display | Python Tutor - Visualize Python, Java, JavaScript, C, C++, Ruby code execution
https://en.wikipedia.org/wiki/Fluent_interface | Fluent interface - Wikipedia
https://stackoverflow.com/questions/3917574/how-is-pythons-list-implemented | arrays - How is Python's List Implemented? - Stack Overflow
https://www.cnbc.com/2022/05/24/books-the-wealthy-will-read-this-summer-nfts-greek-myths-hayao-miyazaki.html | Books the wealthy will read this summer: NFTs, Greek myths, Hayao Miyazaki
https://www.amazon.nl/Hayao-Miyazaki-anglais-Pete-Docter/dp/1942884818/ref=sr_1_4?__mk_nl_NL=%C3%85M%C3%85%C5%BD%C3%95%C3%91&crid=19GZZ3JY85GZL&keywords=Hayao+Miyazaki+jessica&qid=1653592858&sprefix=hayao+miyazaki+jessica%2Caps%2C100&sr=8-4 | Hayao Miyazaki /anglais : Docter, Pete: Amazon.nl: Boeken

https://bengribaudo.com/blog/2013/12/31/2530/ruby-case-testing-against-arrays-of-values | Ruby: Case Testing Against Arrays of Values | Ben Gribaudo
https://apidock.com/rails/ActiveRecord/QueryMethods/select | select (ActiveRecord::QueryMethods) - APIdock
https://joshfrankel.me/blog/constructing-a-sql-select-from-subquery-in-activerecord/ | Constructing a select * from subquery using ActiveRecord | Development Simplified
https://stackoverflow.com/questions/21138207/activerecordstatementinvalid-pg-infailedsqltransaction | ruby on rails - ActiveRecord::StatementInvalid: PG InFailedSqlTransaction - Stack Overflow
https://stackoverflow.com/questions/14904498/rails-active-record-count-across-calculated-field | Rails Active Record Count Across Calculated Field - Stack Overflow
https://www.reddit.com/r/rails/comments/9asl1i/activerecord_multiple_joins_with_count_and_having/ | ActiveRecord - Multiple joins with count and having : rails
https://www.dailysmarty.com/posts/examples-of-advanced-activerecord-queries | Examples of Advanced ActiveRecord Queries
https://www.epfc.eu/travailler-a-lepfc | Travailler à l'EPFC | EPFC
https://www.metacritic.com/game/pc/victoria-ii | Victoria II for PC Reviews - Metacritic

https://dba.stackexchange.com/questions/171720/getting-the-count-and-sum-of-distinct-record-from-postgres-jsonb-attribute | postgresql - Getting the count and sum of distinct record from Postgres jsonb attribute - Database Administrators Stack Exchange
https://www.startpage.com/do/dsearch?query=different+types+of+joins+postgres&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.postgresqltutorial.com/postgresql-tutorial/postgresql-joins/ | PostgreSQL Joins: A Visual Explanation of PostgreSQL Joins
https://stackoverflow.com/questions/24358805/left-outer-join-in-rails-4 | sql - LEFT OUTER JOIN in Rails 4 - Stack Overflow
https://stackoverflow.com/questions/21138207/activerecordstatementinvalid-pg-infailedsqltransaction | ruby on rails - ActiveRecord::StatementInvalid: PG InFailedSqlTransaction - Stack Overflow

https://mitpress.mit.edu/books/art-metaobject-protocol | The Art of the Metaobject Protocol | The MIT Press
https://www.fluentpython.com/ | Fluent Python, the lizard book
https://developer.mozilla.org/en-US/docs/Glossary/XSLT | XSLT - MDN Web Docs Glossary: Definitions of Web-related terms | MDN
https://www.notaire.be/lexique/A | Lexique - Notaire.be

https://www.vidarholen.net/contents/blog/?p=479 | Useless Use Of dd – Vidar's Blog
https://prospector.landscape.io/en/master/ | 1. Prospector - Python Static Analysis — prospector documentation
https://github.com/PyCQA/bandit | PyCQA/bandit: Bandit is a tool designed to find common security issues in Python code.

https://mail.python.org/archives/list/<EMAIL>/message/52DLME5DKNZYFEETCTRENRNKWJ2B4DD5/ | Mailman 3 [Python-ideas] Why operators are useful - Python-ideas - python.org
https://www.blog.pythonlibrary.org/2021/09/08/python-3-10-parenthesized-context-managers/ | Python 3.10 - Parenthesized Context Managers - Mouse Vs Python
https://peps.python.org/pep-0617/ | PEP 617 – New PEG parser for CPython | peps.python.org
https://news.ycombinator.com/item?id=31470928 | Lofi.co – Relax and Focus | Hacker News
https://belkarx.github.io/posts/finished/Empirical%20Notes%20on%20Kissing.html | placeholder

https://stackoverflow.com/questions/25087336/why-is-using-the-rails-default-scope-often-recommend-against | default scope - Why is using the rails default_scope often recommend against? - Stack Overflow
https://www.metacritic.com/tv/this-is-going-to-hurt/season-1 | This Is Going To Hurt - Season 1 Reviews - Metacritic
https://makandracards.com/makandra/17773-rspec-where-to-put-shared-example-groups | RSpec: Where to put shared example groups - makandra dev
https://icelandcheng.medium.com/ruby-on-rails-using-table-name-prefix-and-namespace-to-organize-models-a08199deaa69 | Ruby on Rails: Using table_name_prefix and namespace to organize models | by icelandcheng | Medium

https://www.laphamsquarterly.org/roundtable/new-look-same-great-look | New Look, Same Great Look | Lapham’s Quarterly
https://en.wikipedia.org/wiki/CUDA | CUDA - Wikipedia

http://ucidatascienceinitiative.github.io/IntroToJulia/Html/WhyJulia | WhyJulia
https://javascript.plainenglish.io/functional-components-are-better-d6a889175b67 | React: Functional Components are Better | JavaScript in Plain English

https://en.wikipedia.org/wiki/Mongolia | Mongolia - Wikipedia
https://www.autodidacts.io/what-game-are-you-playing/ | What Game Are You Playing? — The Autodidacts
https://pre-commit.com/hooks.html | pre-commit

https://pandas.pydata.org/docs/user_guide/merging.html | Merge, join, concatenate and compare — pandas 1.4.2 documentation

https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners | About code owners - GitHub Docs
https://stream.sooner.be/home/<USER>/m/la-vie-dune-petite-culotte/watch | La vie d'une petite culotte - Sooner | Stream Beyond
https://www.youtube.com/watch?v=m8rrPM01Jy8 | Adult Swim Festival 2020: Colin Stetson - YouTube
https://sosoir.lesoir.be/roselyne-le***************************************de-bridgerton | Roselyne : Le nouveau spot bruxellois pour les fans de Bridgerton

https://github.com/collectiveidea/awesome_nested_set | collectiveidea/awesome_nested_set: An awesome replacement for acts_as_nested_set and better_nested_set.
https://falsinsoft.blogspot.com/2013/01/tree-in-sql-database-nested-set-model.html | Tree in SQL database: The Nested Set Model
http://mikehillyer.com/articles/managing-hierarchical-data-in-mysql/ | Managing Hierarchical Data in MySQL — Mike Hillyer's Personal Webspace
https://github.com/mclaeysb/FerrarGIS | mclaeysb/FerrarGIS: Using QGIS to apply a 1777 style to today's OpenStreetMap data.
https://manuelclaeysbouuaert.be/carto | Manuel Claeys Bouuaert - Cartographer
https://stackoverflow.com/questions/32446698/how-to-combine-single-and-multiindex-pandas-dataframes | python - How to combine single and multiindex Pandas DataFrames - Stack Overflow
https://stackoverflow.com/questions/14744068/prepend-a-level-to-a-pandas-multiindex | python - Prepend a level to a pandas MultiIndex - Stack Overflow

https://stackoverflow.com/questions/5128200/how-to-count-identical-string-elements-in-a-ruby-array | How to count identical string elements in a Ruby array - Stack Overflow
https://news.ycombinator.com/item?id=31243569 | OPT: Open Pre-trained Transformer Language Models | Hacker News
https://news.ycombinator.com/item?id=31248379 | Kaketsugi – A technique for repairing holes or tears in fabric (2021) [video] | Hacker News
https://stackoverflow.com/questions/49056567/how-to-sum-with-missing-values-in-pandas | python - How to sum with missing values in Pandas? - Stack Overflow
https://numpy.org/doc/stable/reference/generated/numpy.searchsorted.html | numpy.searchsorted — NumPy v1.22 Manual
https://docs.jupyter.org/en/latest/use/use-cases/narrative-notebook.html | Notebook Narratives — Jupyter Documentation 4.1.1 alpha documentation
https://stackoverflow.com/questions/17077494/how-do-i-convert-a-ipython-notebook-into-a-python-file-via-commandline | How do I convert a IPython Notebook into a Python file via commandline? - Stack Overflow

https://fr.wikipedia.org/wiki/Chauss%C3%A9e_de_Haecht | Chaussée de Haecht — Wikipédia

http://tilde.town/~ramin_hal9001/emacs-for-professionals/index.html | Ramin Honary: Emacs for Professionals
https://pragprog.com/titles/roclojure/getting-clojure/ | Getting Clojure: Build Your Functional Skills One Idea at a Time by Russ Olsen
https://github.com/vtraag/leidenalg | vtraag/leidenalg: Implementation of the Leiden algorithm for various quality functions to be used with igraph in Python.
https://github.com/benedekrozemberczki/awesome-community-detection | benedekrozemberczki/awesome-community-detection: A curated list of community detection research papers with implementations.
https://info.vtaiwan.tw/ | vTaiwan project page
https://www.morethanonestory.org/en/how-to-play | More Than One Story
https://www.mysociety.org/2019/06/27/digital-tools-for-citizens-assemblies/ | Digital tools for Citizens’ Assemblies / mySociety
https://www.mindtools.com/pages/article/improving-group-dynamics.htm | Improving Group Dynamics - Team Management Skills From MindTools.com
https://www.thegamer.com/ | TheGamer - The Leading Source for Gaming News, Reviews, and Interviews

https://stackoverflow.com/questions/5159882/how-to-check-for-a-json-response-using-rspec | ruby on rails - How to check for a JSON response using RSpec? - Stack Overflow
https://en.wikipedia.org/wiki/Hypergeometric_distribution#Multivariate_hypergeometric_distribution | Hypergeometric distribution - Wikipedia
https://en.wikipedia.org/wiki/Positive-definite_function | Positive-definite function - Wikipedia
https://en.wikipedia.org/wiki/Representativeness_heuristic | Representativeness heuristic - Wikipedia
https://www.ncbi.nlm.nih.gov/pmc/articles/PMC8460438/#ooab077-B45 | Quantifying representativeness in randomized clinical trials using machine learning fairness metrics - PMC
https://psycnet.apa.org/record/2006-07101-001 | Modeling the behavior of the 4/5ths rule for determining adverse impact: Reasons for caution. - PsycNET
https://www.cinema-aventure.be/catalogue/movie/?BBEE2D64-C728-1E14-57E9-0BDD8F7058E1 | Cinéma Aventure
https://en.wikipedia.org/wiki/Pearson%27s_chi-squared_test#Assumptions | Pearson's chi-squared test - Wikipedia

https://www.getrevue.co/profile/themarkup/issues/confronting-the-biases-embedded-in-artificial-intelligence-1137376?utm_campaign=Issue&utm_content=view_in_browser&utm_medium=email&utm_source=Hello+World | Confronting the Biases Embedded in Artificial Intelligence | Revue
https://en.wikipedia.org/wiki/Broken_windows_theory | Broken windows theory - Wikipedia
https://matt.blwt.io/post/tools-for-a-culture-of-writing/ | Tools for a Culture of Writing
https://wantwords.net/ | WantWords 反向词典
https://compdemocracy.org/Welcome/ | The Computational Democracy Project | The Computational Democracy Project
https://sosoir.lesoir.be/quels-sont-les-cocktails-qui-vont-detroner-le-spritz-cet-ete | Quels sont les cocktails qui vont détrôner le Spritz cet été ?
https://piqilife.com/blogs/news/mango-ginger-kefir-mocktail | Mango & Ginger Kefir Mocktail – PiQi Life

https://www.albumoftheyear.org/album/339670-jon-batiste-we-are.php | Jon Batiste - WE ARE - Reviews - Album of The Year
https://www.albumoftheyear.org/genre/132-dance/2022/ | The Best Dance Albums of 2022 - Album of The Year

https://blog.mozilla.org/internet-culture/deep-dives/how-to-enjoy-being-online-again/ | How to actually enjoy being online again

https://www.metacritic.com/search/all/heartstopper/results | heartstopper - Reviews, Articles, People, Trailers and more at Metacritic - Metacritic
https://www.cs.cmu.edu/~kmcrane/Projects/RepulsiveCurves/index.html | Keenan Crane - Repulsive Curves
https://news.ycombinator.com/item?id=31037729 | It’s Still Stupidly, Difficult to Buy a ‘Dumb’ TV | Hacker News
https://stackoverflow.com/questions/47036906/how-to-get-just-get-and-post-params-as-hash-in-rails | ruby - How to get just GET and POST params as hash in Rails? - Stack Overflow
https://unix.stackexchange.com/questions/9123/is-there-a-one-liner-that-allows-me-to-create-a-directory-and-move-into-it-at-th | bash - Is there a one-liner that allows me to create a directory and move into it at the same time? - Unix & Linux Stack Exchange
https://stackoverflow.com/questions/1230233/how-to-find-the-sum-of-an-array-of-numbers | javascript - How to find the sum of an array of numbers - Stack Overflow

https://medium.com/@matt.readout/rails-generators-model-vs-resource-vs-scaffold-19d6e24168ee | Rails Generators – Model vs. Resource vs. Scaffold | by matt readout | Medium
https://www.startpage.com/do/dsearch?query=generator+rails+inside+engine&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://stackoverflow.com/questions/19817693/generators-dont-work-in-rails-engine-folder | Generators don't work in Rails engine folder - Stack Overflow
https://dev.to/rodreegez/immutable-activerecord-models-11dp | Immutable ActiveRecord Models - DEV Community
https://github.com/sensortower/sidekiq-throttled | sensortower/sidekiq-throttled: Concurrency and threshold throttling for Sidekiq.
https://news.ycombinator.com/item?id=31034740 | 5-min breathing workout lowers blood pressure as much as exercise, drugs (2021) | Hacker News

https://www.surgehq.ai//blog/google-search-is-falling-behind | Google Search is Falling Behind
https://www.surgehq.ai/blog/building-a-no-code-toxicity-classifier-by-talking-to-copilot | Building a No-Code Machine Learning Model by Chatting with GitHub Copilot
https://neeva.com/ | Neeva - Ad-free, private search
https://discuss.jsonapi.org/t/time-series-data-for-json-api/282 | Time series data for JSON API - JSON API
https://mathspp.com/blog/why-apl-is-a-language-worth-knowing | Why APL is a language worth knowing | Mathspp
https://context.reverso.net/traduction/anglais-francais/go+overboard | go overboard - Traduction en français - exemples anglais | Reverso Context

https://securityzines.com/zines/docker.html | SecurityZines
https://securityzines.gumroad.com/l/dockerzine | DockerZine
https://discuss.jsonapi.org/t/clarification-on-ids-in-attributes/967 | Clarification on ids in attributes - JSON API
https://possibleworks.com/blog/practical-examples-of-objectives-and-key-results/ | Practical Examples of Objectives and Key Results – PossibleWorks
https://context.reverso.net/traduction/francais-anglais/possibilit%C3%A9+%C3%A0+13h | possibilité à 13h - Traduction en anglais - exemples français | Reverso Context
https://tutorblog.fluentify.com/phrases-for-scheduling-meetings-events-and-dates/ | Phrases for scheduling meetings and events – Tutor Blog
https://www.lesoir.be/427990/article/2022-03-04/plongee-dans-la-gestion-des-eaux-de-la-capitale-bruxelles-se-la-coule-douce | Plongée dans la gestion des eaux de la capitale: Bruxelles se la coule douce - Le Soir
https://www.eatmy.art/books | Sketchbooks | Arteater

https://x6ud.github.io/pose-search/#/ | Pose Search
https://www.sbert.net/ | SentenceTransformers Documentation — Sentence-Transformers documentation
https://securityheaders.com/?q=product.stg.citizenlab.co&hide=on&followRedirects=on | Scan results for product.stg.citizenlab.co

https://towardsdatascience.com/the-future-of-the-modern-data-stack-in-2022-4f4c91bb778f | The Future of the Modern Data Stack in 2022 | by Prukalpa | Towards Data Science
https://press.edx.org/celebrating-the-2021-edx-prize-finalists | Celebrating the 2021 edX Prize Finalists for Innovation in Online Teaching
https://twitter.com/profannieoakley/status/1357768408671027202 | Professor Annie Oakley Rides With Ukraine 🐎🇺🇦 on Twitter: "I turned my art history students loose with a make-your-own-Bayeux Tapestry app. https://t.co/SV1S7O0T6K" / Twitter
https://stoplight.io/pricing | API Design Plans and Pricing | Stoplight | Stoplight
https://hn.algolia.com/?dateRange=all&page=0&prefix=true&query=Eisenhower%20plan&sort=byPopularity&type=comment | All | Search powered by Algolia
https://fr.wiktionary.org/wiki/halouf | halouf — Wiktionnaire

https://tandemlocal.be/ | Tandem Local |
https://consilienceproject.org/endgames-of-bad-communication/ | The Endgames of Bad Faith Communication
https://en.wikipedia.org/wiki/Beauty_(2011_film) | Beauty (2011 film) - Wikipedia
https://www.metacritic.com/movie/living | Living Reviews - Metacritic
https://www.metacritic.com/movie/rrr | RRR Reviews - Metacritic

https://en.wikipedia.org/wiki/Megabit | Megabit - Wikipedia
https://blog.fennel.ai/p/real-world-recommendation-system?s=r | Real World Recommendation System - Part 1 - by Nikhil Garg

https://medium.com/smells-like-team-spirit/hate-okrs-avoid-these-7-mistakes-193ddf5091be | Hate OKRs? Avoid these 7 mistakes | by Sarah Goff-Dupont | Smells Like Team Spirit | Medium
https://airfocus.com/glossary/what-is-okr-roadmap/ | Okr vs Roadmap: Is Okr a Replacement for a Product Roadmap?
https://news.ycombinator.com/item?id=30986893 | Ask HN: Where can I see many examples of real companies' software architecture? | Hacker News

https://lithub.com/its-official-according-to-science-reading-fiction-makes-you-nicer/ | It’s official! According to science, reading fiction makes you nicer. ‹ Literary Hub
https://stream.sooner.be/search/petru/type/best-match/m/dieu-existe-son-nom-est-petrunya/info | Dieu existe, son nom est Petrunya - Sooner | Stream Beyond
https://www.metacritic.com/feature/major-upcoming-video-game-release-dates-xbox-ps4-pc-switch | New & Upcoming Video Games - Major Releases (All Platforms) - Metacritic
https://alicevision.org/#meshroom | AliceVision | Meshroom - 3D Reconstruction Software
https://www.startpage.com/do/dsearch?query=the+dawn+of+everything&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.startpage.com/do/dsearch?query=lackadaisical&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.startpage.com/do/dsearch?query=displacement+activity&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://notes.eatonphil.com/handling-email-from-gmail-smtp-protocol-basics.html | SMTP protocol basics from scratch in Go: receiving email from Gmail | notes.eatonphil.com
https://lambdaisland.com/blog/2022-02-17-the-fg-command | How to control the metacognition process of programming?
https://en.wikipedia.org/wiki/Degrees_of_freedom | Degrees of freedom - Wikipedia

https://yyoga.be/workshop-and-events/ | Workshops & events - yyoga
https://mermaid-js.github.io/mermaid/#/sequenceDiagram?id=notes | Sequence diagram
https://www.geeksforgeeks.org/unified-modeling-language-uml-sequence-diagrams/ | Unified Modeling Language (UML) | Sequence Diagrams - GeeksforGeeks
https://medium.com/smells-like-team-spirit/hate-okrs-avoid-these-7-mistakes-193ddf5091be | Hate OKRs? Avoid these 7 mistakes | by Sarah Goff-Dupont | Smells Like Team Spirit | Medium

https://pharo.org/web.html | Pharo - Welcome to Pharo!
https://news.ycombinator.com/item?id=30928105 | Ask HN: I'm interested in so many disciplines, but what can I do with that? | Hacker News

https://auth0.com/blog/how-to-manage-javascript-fatigue/ | How to Manage JavaScript Fatigue
https://www.bruzz.be/bruzz-guide-2022 | BRUZZ Guide 2022 | BRUZZ
https://www.terraform.io/cdktf | CDK for Terraform | Terraform by HashiCorp
https://www.pulumi.com/docs/intro/vs/terraform/ | Pulumi vs. Terraform | Pulumi

https://www.startpage.com/do/dsearch?query=how+to+be+charming&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results

https://jinyuz.dev/posts/tips-and-tricks/Switching-from-pyenv,-rbenv,-goenv-and-nvm-to-asdf | Switching from pyenv, rbenv, goenv and nvm to asdf - @yujinyuz

https://en.wikipedia.org/wiki/Mastery_learning | Mastery learning - Wikipedia
https://cybersecuritybase.mooc.fi/pass | How to start and pass a course - Cyber Security Base 2022
https://docs.timescale.com/timescaledb/latest/quick-start/ruby/#create_table | Ruby | Timescale Docs
https://httpd.apache.org/docs/2.4/programs/ab.html | ab - Apache HTTP server benchmarking tool - Apache HTTP Server Version 2.4
https://stackoverflow.com/questions/22654170/explanation-of-jsonb-introduced-by-postgresql | json - Explanation of JSONB introduced by PostgreSQL - Stack Overflow
https://haleynahman.substack.com/p/42-are-you-nice-or-kind?s=r | #42: Are you nice or kind? - by Haley Nahman - Maybe Baby

https://en.wikipedia.org/wiki/Pearson%27s_chi-squared_test | Pearson's chi-squared test - Wikipedia
https://www.startpage.com/do/dsearch?query=p-value+is+not+a+probability&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.ncbi.nlm.nih.gov/pmc/articles/PMC5804470/ | P-value: What is and what is not - PMC
https://prometheus.io/docs/concepts/data_model/ | Data model | Prometheus
https://evilmartians.com/chronicles | Martian Chronicles, Evil Martians’ team blog
https://studies.helsinki.fi/courses/cur/otm-f4b5c4b0-73fb-4195-86cd-e3dcc5da964c | DevOps with Kubernetes, Open Uni: DevOps with Kubernetes
https://www.science.org/doi/10.1126/science.156.3781.1456 | R. A. Fisher (1890—1962): An Appreciation
https://link.springer.com/chapter/10.1007/978-1-4612-4414-1_3 | Significance Tests Versus Hypothesis Tests | SpringerLink
https://pubmed.ncbi.nlm.nih.gov/10383371/ | Toward evidence-based medical statistics. 1: The P value fallacy - PubMed

https://codescene.com/community-edition?hsCtaTracking=2b13f3d8-f422-45ed-a318-e037574a9220%7Cbcb4e453-da37-45ed-abe3-0e19394792ac | Community edition
https://stats.stackexchange.com/questions/83163/statistical-test-to-tell-whether-two-samples-are-pulled-from-the-same-population | Statistical test to tell whether two samples are pulled from the same population? - Cross Validated
https://stats.stackexchange.com/questions/1047/is-kolmogorov-smirnov-test-valid-with-discrete-distributions | hypothesis testing - Is Kolmogorov-Smirnov test valid with discrete distributions? - Cross Validated
https://stats.stackexchange.com/questions/4471/calculating-quantiles-for-chi-squared-distribution | self study - Calculating quantiles for chi squared distribution - Cross Validated
https://martinfowler.com/recent-changes.html | Recent Changes

https://www.brusselskitchen.com/tokidoki/bruxelles/restaurant | Tokidoki | Brussels' Kitchen
https://design-system.service.gov.uk/components/skip-link/ | Skip link – GOV.UK Design System
https://pubmed.ncbi.nlm.nih.gov/10984330/ | Consequences of reducing nonresponse in a national telephone survey - PubMed
https://en.wikipedia.org/wiki/Kullback%E2%80%93Leibler_divergence | Kullback–Leibler divergence - Wikipedia

https://www.amazon.com/Complete-Book-Decorating-Barbara-Mayer/dp/********** | Complete Book of Home Decorating: Mayer, Barbara: 9781567991994: Amazon.com: Books

https://lethain.com/hard-to-work-with/ | Hard to work with. | Irrational Exuberance
https://prtkgpt.medium.com/how-to-use-weekends-for-mental-health-c6c9e465ad69 | How to use weekends for mental health | by Prateek Gupta | Mar, 2022 | Medium
https://desystemize.substack.com/p/desystemize-9?s=r | Desystemize #9 - by collin - Desystemize
https://desystemize.substack.com/p/representation-and-uncertainty?s=r | Representation and Uncertainty - by collin - Desystemize
https://askubuntu.com/questions/958360/how-can-i-find-my-internet-service-provider-isp-using-a-bash-script | How can I find my Internet Service Provider (ISP) using a bash script? - Ask Ubuntu
https://ipinfo.io/ | Comprehensive IP address data, IP geolocation API and database - IPinfo.io
https://stackoverflow.com/questions/41798284/understanding-docker-port-mappings | linux - Understanding docker port mappings - Stack Overflow

https://stackoverflow.com/questions/19925641/check-if-a-postgres-json-array-contains-a-string | postgresql - Check if a Postgres JSON array contains a string - Stack Overflow
https://stackoverflow.com/questions/36921951/truth-value-of-a-series-is-ambiguous-use-a-empty-a-bool-a-item-a-any-o | python - Truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all() - Stack Overflow
https://pandas.pydata.org/docs/reference/api/pandas.Series.between.html | pandas.Series.between — pandas 1.4.1 documentation

https://eprints.soton.ac.uk/158353/ | Indicators for monitoring and improving representativeness of response - ePrints Soton
https://www.pewresearch.org/fact-tank/2018/08/06/what-are-nonprobability-surveys/ | What are nonprobability surveys? | Pew Research Center
https://en.wikipedia.org/wiki/Sampling_frame | Sampling frame - Wikipedia
https://en.wikipedia.org/wiki/Standard_error | Standard error - Wikipedia

https://www.allourideas.org/planyc_example?guides=true | All Our Ideas - Bringing survey research into the digital age
https://www.centreforpublicimpact.org/case-study/building-consensus-compromise-uber-taiwan/#evidence | Building Consensus and Compromise on Uber in Taiwan | Centre For Public Impact (CPI)
https://www.sixthtone.com/ | Fresh voices from today's China | Sixth Tone
https://www.davidbauer.ch/readme/ | How to work with me | David Bauer
https://timdaub.github.io/2022/03/27/the-user-experience-problems-of-quadratic-voting/ | The User Experience Problems Of Quadratic Voting
https://serokell.io/blog/algebraic-data-types-in-haskell | Algebraic Data Types in Haskell

https://www.achieveit.com/solutions/strategic-planning-software | Strategic Planning Software & Execution Management Platform
https://johndoe.be/lab/bbc2/ | Brussels Bike Counters - Le Comparateur
https://data.mobility.brussels/en/info/rt_counting/ | Data Mobility Brussels
https://www.bruzz.be/videoreeks/bruzz-international-zondag-27-maart-2022/video-bruzz-international-finland-special | BRUZZ International: Finland special | BRUZZ
https://sosoir.lesoir.be/nos-7-restaurants-vegetariens-coups-de-coeur-en-belgique? | Nos 7 restaurants végétariens coups de coeur en Belgique

https://www.startpage.com/do/dsearch?query=lo0+interface&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.startpage.com/do/dsearch?query=amaranth&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.startpage.com/do/dsearch?query=nextjs&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.startpage.com/do/dsearch?query=baud+rate&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results

https://www.parlor.io/blog/customer-feedback-tools/ | 15 Best Customer Feedback Tools for 2021 (By Use Case) - Parlor
https://martinfowler.com/bliki/CanaryRelease.html | CanaryRelease
https://bitnine.net/blog-postgresql/postgresql-internals-jsonb-type-and-its-indexes/?ckattempt=1 | PostgreSQL internals: JSONB type and its indexes ⋆ Bitnine Global Inc.
https://www.redhat.com/en/topics/devops/what-is-blue-green-deployment | What is blue green deployment?
https://argoproj.github.io/ | Home | Argo
https://news.ycombinator.com/item?id=29390483 | Event Sourcing Is Hard (2019) | Hacker News
https://martinfowler.com/bliki/CQRS.html | CQRS
https://stackoverflow.com/questions/56728979/event-sourcing-why-a-dedicated-event-store | apache kafka - Event sourcing - why a dedicated event store? - Stack Overflow
https://softwareengineering.stackexchange.com/questions/400329/cqrs-how-can-a-command-properly-validate-when-queries-are-required | event sourcing - CQRS - How can a command properly validate when queries are required? - Software Engineering Stack Exchange
https://linuxize.com/post/bash-redirect-stderr-stdout/ | How to Redirect stderr to stdout in Bash | Linuxize
https://docs.docker.com/search/?q=docker%20compose%20file | Docs search | Docker Documentation
https://stackoverflow.com/questions/30137135/confused-about-docker-t-option-to-allocate-a-pseudo-tty | Confused about Docker -t option to Allocate a pseudo-TTY - Stack Overflow
https://en.wikipedia.org/wiki/Device_driver | Device driver - Wikipedia
https://unix.stackexchange.com/questions/4126/what-is-the-exact-difference-between-a-terminal-a-shell-a-tty-and-a-con | What is the exact difference between a 'terminal', a 'shell', a 'tty' and a 'console'? - Unix & Linux Stack Exchange
http://www.linusakesson.net/programming/tty/ | The TTY demystified
https://en.wikipedia.org/wiki/Linux_kernel | Linux kernel - Wikipedia
https://en.wikipedia.org/wiki/Mainframe_computer | Mainframe computer - Wikipedia
https://en.wikipedia.org/wiki/Teletype_Model_33 | Teletype Model 33 - Wikipedia

https://www.exberliner.com/ | Exberliner - Berlin in English since 2002
https://www.advocate.com/sexy-beast/2016/5/25/17-tips-happier-healthier-bottoming#media-gallery-media-19 | 17 Tips for Happier, Healthier Bottoming
https://stackoverflow.com/questions/********/how-can-i-know-if-my-computer-is-behind-nat | networking - How can I know if my computer is behind NAT? - Stack Overflow
https://apple.stackexchange.com/questions/20547/how-do-i-find-my-ip-address-from-the-command-line | terminal - How do I find my IP Address from the command line? - Ask Different
https://library.netapp.com/ecmdocs/ECMP1155586/html/GUID-BA9AD6B9-B994-4F41-B5A0-C22071BAB2A4.html | Viewing the routing table from the command-line interface
https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing | Classless Inter-Domain Routing - Wikipedia
https://cidr.xyz/ | CIDR.xyz
https://www.amazon.fr/Computer-Networking-Global-James-Kurose-dp-**********/dp/**********/ref=dp_ob_title_bk | Computer Networking, Global Edition : Kurose, James, Ross, Keith: Amazon.fr: Livres
https://en.wikipedia.org/wiki/Loopback | Loopback - Wikipedia

https://www.amazon.com/exec/obidos/ASIN/**********/ref%3Dnosim/themichaelupdate | Vegetables: Recipes and Techniques from the World's Premier Culinary College: The Culinary Institute of America: *************: Amazon.com: Books
http://library.lol/main/F765A10E8438A71F14DD0BC18AA82277 | library.lol/main/F765A10E8438A71F14DD0BC18AA82277
https://fr.zalando.be/element-fluky-casquette-cream-gold-el854q01a-e11.html | Element FLUKY - Casquette - cream gold/jaune - ZALANDO.BE
https://www.amazon.fr/s?k=casquette+playstation&i=clothing&sprefix=casquette+plasy%2Cclothing%2C94&ref=nb_sb_ss_sc_1_14 | Amazon.fr : casquette playstation
https://en.wikipedia.org/wiki/Demoscene | Demoscene - Wikipedia
https://www.amazon.com/Japanese-Vegetable-Cooking-Asako-Tohata/dp/4889960694 | Japanese Vegetable Cooking: Tohata M.D., Asako: 9784889960693: Amazon.com: Books
https://www.amazon.com/Kansha-Celebrating-Japans-Vegetarian-Traditions/dp/1580089550/ref=pd_bxgy_img_2/132-4336203-8026731?pd_rd_w=oh4Ox&pf_rd_p=6b3eefea-7b16-43e9-bc45-2e332cbf99da&pf_rd_r=2V3R7KPJPD5AAMDWCRJK&pd_rd_r=b601a980-bf87-42be-b777-7520f25be054&pd_rd_wg=25IIK&pd_rd_i=1580089550&psc=1 | Kansha: Celebrating Japan's Vegan and Vegetarian Traditions [A Cookbook]: Andoh, Elizabeth: 8601406505384: Amazon.com: Books
https://en.wikipedia.org/wiki/Blanching_(cooking) | Blanching (cooking) - Wikipedia
https://en.wikipedia.org/wiki/Japanese_rice | Japanese rice - Wikipedia
https://fr.wikipedia.org/wiki/Riz_blanc | Riz blanc — Wikipédia
https://cuisine-japonaise.com/sitemaps/ | sitemap | cuisine-japonaise.com
https://www.sasasa.be/cours/cours-de-chant/ | Cours de chant pour débutants (& perfectionnement) - Bruxelles - SASASA : Sasasa

https://stackoverflow.com/questions/8296170/what-is-a-pid-file-and-what-does-it-contain | linux - What is a .pid file and what does it contain? - Stack Overflow
https://hevodata.com/learn/airflow-etl-guide/ | Understanding Airflow ETL: 2 Easy Methods
https://gtoonstra.github.io/etl-with-airflow/principles.html | ETL principles — ETL Best Practices with Airflow v1.8
https://www.acm.org/publications/digital-library | ACM Digital Library
https://dl.acm.org/toc/cacm/2022/65/4 | CACM: Vol 65, No 4
https://www.fivetran.com/extract-load | Product Features | ETL & BI Tool | Fivetran

https://www.ruby-lang.org/en/news/2019/12/12/separation-of-positional-and-keyword-arguments-in-ruby-3-0/ | Separation of positional and keyword arguments in Ruby 3.0
http://www.carrotmuseum.co.uk/babycarrot.html#full | The True Story of Baby Carrots - Origin and Evolution
https://smallstep.com/blog/use-ssh-certificates/ | If You're Not Using SSH Certificates You're Doing SSH Wrong | Smallstep Blog
https://www.youtube.com/watch?v=tZafawk3arc | Shoulder Exercises Ranked (BEST TO WORST!) - YouTube

https://www.etsy.com/shop/blibloop?ref=simple-shop-header-name&listing_id=844098264&search_query=zelda+pin&source=aw&utm_source=affiliate_window&utm_medium=affiliate&utm_campaign=us_location_buyer&utm_content=249521&awc=6220_1647958764_1ba4cb4628e1fd8d5ea994f0cb535f65&utm_term=0 | blibloop | Etsy

https://stackoverflow.com/questions/48546124/what-is-linux-equivalent-of-host-docker-internal | What is linux equivalent of "host.docker.internal" - Stack Overflow
https://docs.docker.com/network/network-tutorial-standalone/ | Networking with standalone containers | Docker Documentation
https://forums.docker.com/t/option-network-mode-host-in-docker-compose-file-not-working-as-expected/51682 | Option network_mode: host in docker compose file not working as expected - Open Source Projects / Compose - Docker Community Forums
https://docs.docker.com/network/host/ | Use host networking | Docker Documentation
https://ddev.com/ddev-live/how-to-deploy-from-a-branch-commit-or-tag/ | How to deploy from a branch, commit, or tag | DDEV
https://www.docker.com/blog/understanding-docker-networking-drivers-use-cases/ | Docker Networking Drivers - Details and Use Cases | Docker Blog
https://developers.redhat.com/blog/2018/10/22/introduction-to-linux-interfaces-for-virtual-networking | Introduction to Linux interfaces for virtual networking | Red Hat Developer
https://en.wikipedia.org/wiki/Network_bridge | Network bridge - Wikipedia

https://ruudvanasseldonk.com/2022/03/20/please-put-units-in-names | Please put units in names
https://www.youtube.com/watch?v=wF-f_AdCxl0 | Building a Nerf Dart Missile Defense System - YouTube
https://www.urbandictionary.com/define.php?term=Liege | Urban Dictionary: Liege

https://www.youtube.com/watch?v=zSM4ZyVe8xs | Introduction to Snowplow - Yali Sassoon - Snowplow Helsinki Meetup #1 - YouTube
https://pytorch.org/ | PyTorch
https://stackoverflow.com/questions/28555859/why-puma-rails-server-only-accepts-localhost3000-rather-than-127-0-0-13000 | Why Puma rails server only accepts localhost:3000 rather than 127.0.0.1:3000 - Stack Overflow

https://stackoverflow.com/questions/61584077/fork-a-child-process-and-capture-any-stdout-and-stderr | ruby - Fork a child process, and capture any stdout and stderr? - Stack Overflow
https://stackoverflow.com/questions/48546124/what-is-linux-equivalent-of-host-docker-internal | What is linux equivalent of "host.docker.internal" - Stack Overflow
http://johnsalvatier.org/blog/2017/reality-has-a-surprising-amount-of-detail | Reality has a surprising amount of detail

https://stream.sooner.be/home/<USER>

http://www.bioinfomaster.ulb.ac.be/MBM-FR/description/description.html | Description du Master
https://en.wikipedia.org/wiki/Balkanization | Balkanization - Wikipedia
https://developer.mozilla.org/en-US/docs/Tools/Debugger/UI_Tour#source_pane | UI Tour - Firefox Developer Tools | MDN
https://www.gentlydownthe.stream/#/23 | Gently Down the Stream
https://softwareengineering.stackexchange.com/questions/186889/why-was-python-written-with-the-gil | multithreading - Why Was Python Written with the GIL? - Software Engineering Stack Exchange
https://www.startpage.com/do/dsearch?query=catch+22&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results

https://egghead.io/lessons/react-create-a-user-interface-with-vanilla-javascript-and-dom | Create a User Interface with Vanilla JavaScript and DOM | egghead.io
https://learnbyexample.github.io/vim_reference/Introduction.html | Introduction - Vim Reference Guide

https://ramitsurana.github.io/awesome-kubernetes/learning-resources/books/ | Books - Awesome Kubernetes
https://news.ycombinator.com/item?id=23211325 | Common mistakes using Kubernetes | Hacker News
https://news.ycombinator.com/item?id=28050750 | NSA Kubernetes Hardening Guidance [pdf] | Hacker News
https://en.wikipedia.org/wiki/HTTP_cookie | HTTP cookie - Wikipedia

https://www.educative.io/blog/top-react-mistakes#folder | Top 10 mistakes to avoid when using React
https://www.scanofthemonth.com/about | Combine curiosity and a CT Scanner—the results are endless.
https://www.techradar.com/news/amazons-new-role-playing-game-can-help-you-build-your-aws-skills | Amazon's new role-playing game can help you build your AWS skills | TechRadar
https://github.com/ramnes/awesome-mongodb#python | ramnes/awesome-mongodb: A curated list of awesome MongoDB resources, libraries, tools and applications

https://airflow.apache.org/docs/apache-airflow/stable/concepts/overview.html | Architecture Overview — Airflow Documentation
https://tldrlegal.com/ | TLDRLegal - Software Licenses Explained in Plain English
https://fossa.com/ | FOSSA | Scalable Open Source Management
https://www.wired.co.uk/article/taiwan-democracy-social-media | Taiwan is making democracy work again. It's time we paid attention | WIRED UK

https://pytorch-geometric.readthedocs.io/en/latest/ | PyG Documentation — pytorch_geometric 2.0.5 documentation
https://github.com/facebookresearch/Kats | facebookresearch/Kats: Kats, a kit to analyze time series data, a lightweight, easy-to-use, generalizable, and extendable framework to perform time series analysis, from understanding the key statistics and characteristics, detecting change points and anomalies, to forecasting future trends.
https://vowpalwabbit.org/ | Vowpal Wabbit
https://tailwindcss.com/ | Tailwind CSS - Rapidly build modern websites without ever leaving your HTML.
https://headlessui.dev/react/combobox | Headless UI – Unstyled, fully accessible UI components
https://lifelines.readthedocs.io/en/latest/ | lifelines — lifelines 0.27.0 documentation
https://github.com/Microsoft/dowhy | microsoft/dowhy: DoWhy is a Python library for causal inference that supports explicit modeling and testing of causal assumptions. DoWhy is based on a unified language for causal inference, combining causal graphical models and potential outcomes frameworks.
https://blog.logrocket.com/guide-to-react-usereducer-hook/ | The ultimate guide to the React useReducer Hook - LogRocket Blog
https://www.prefect.io/ | Prefect - The New Standard in Dataflow Automation - Prefect
https://github.com/remotemobprogramming/mob | remotemobprogramming/mob: Tool for smooth git handover.
https://k8slens.dev/ | Lens | The Kubernetes IDE
https://developer.contrastsecurity.com/docs/getting-started/where-do-i-start/ | DevSec with Contrast | Contrast DevSec
https://argoproj.github.io/ | Home | Argo
http://xtdb-website.s3-website.eu-west-1.amazonaws.com/ | XTDB
https://airflow.apache.org/ | Apache Airflow
https://github.com/milvus-io/milvus | milvus-io/milvus: An open-source vector database for scalable similarity search and AI applications.
https://github.com/kubernetes-sigs/external-dns | kubernetes-sigs/external-dns: Configure external DNS servers (AWS Route53, Google CloudDNS and others) for Kubernetes Ingresses and Services
https://wandb.ai/site | Weights & Biases – Developer tools for ML
https://backstage.io/demos | Backstage Software Catalog and Developer Platform · An open platform for building developer portals
https://www.thoughtworks.com/radar/techniques/recreating-esb-antipatterns-with-kafka | Recreating ESB antipatterns with Kafka | Technology Radar | Thoughtworks
https://nx.dev/ | Nx: Smart, Fast and Extensible Build System
https://mlflow.org/docs/latest/quickstart.html | Quickstart — MLflow 1.24.0 documentation
https://martinfowler.com/bliki/TolerantReader.html | TolerantReader
https://towardsdatascience.com/contextual-bandits-and-reinforcement-learning-6bdfeaece72a | Contextual Bandits and Reinforcement Learning | by Pavel Surmenok | Towards Data Science
https://en.wikipedia.org/wiki/Reinforcement_learning | Reinforcement learning - Wikipedia

https://scottaaronson.blog/?p=40 | Shtetl-Optimized » Blog Archive » Umeshisms

https://www.kiehls.be/fr/creme-de-corps-soy-milk---honey-whipped-body-butter/915.html | Creme De Corps Whipped Body Butter | Kiehl's
https://christianheilmann.com/2021/11/01/developer-tools-secrets-that-shouldnt-be-secrets/ | Developer Tools secrets that shouldn’t be secrets | Christian Heilmann
https://www.sitepoint.com/understanding-es6-modules/ | Understanding ES6 Modules - SitePoint
https://docs.pi-hole.net/ | Overview of Pi-hole - Pi-hole documentation

https://hotwired.dev/ | HTML Over The Wire | Hotwire
https://www.bruzz.be/eenvoudig-nederlands | Eenvoudig Nederlands | BRUZZ
http://sequel.jeremyevans.net/ | Sequel: The Database Toolkit for Ruby

https://treyhunner.com/2019/05/python-builtins-worth-learning/ | Python built-in functions to know - Trey Hunner
https://github.com/cookiecutter/cookiecutter-django | cookiecutter/cookiecutter-django: Cookiecutter Django is a framework for jumpstarting production-ready Django projects quickly.

https://en.wikipedia.org/wiki/Analysis_of_variance | Analysis of variance - Wikipedia
https://en.wikipedia.org/wiki/SOLID | SOLID - Wikipedia
https://en.wikipedia.org/wiki/Referential_transparency | Referential transparency - Wikipedia
https://unix.stackexchange.com/questions/259193/what-is-a-block-device | What is a block device? - Unix & Linux Stack Exchange

https://stackoverflow.com/questions/35110146/can-anyone-explain-docker-sock | Can anyone explain docker.sock - Stack Overflow
https://flaviocopes.com/how-to-inspect-javascript-object/ | How to inspect a JavaScript object
https://github.com/containerd/containerd | containerd/containerd: An open and reliable container runtime
https://github.com/opencontainers/runc | opencontainers/runc: CLI tool for spawning and running containers according to the OCI specification
https://docs.docker.com/engine/context/working-with-contexts/ | Docker Context | Docker Documentation

https://kubernetes.io/blog/2016/06/illustrated-childrens-guide-to-kubernetes/ | kubernetes.io/blog/2016/06/illustrated-childrens-guide-to-kubernetes/
https://aws.amazon.com/training/learn-about/machine-learning/?th=tile&tile=learnabout | Machine Learning (ML) - Digital and Classroom Training | AWS
https://www.sqltutorial.org/sql-rollup/ | SQL ROLLUP

https://www.postgresql.org/docs/9.3/protocol-flow.html#AEN99807 | PostgreSQL: Documentation: 9.3: Message Flow
https://github.com/jwt/ruby-jwt | jwt/ruby-jwt: A ruby implementation of the RFC 7519 OAuth JSON Web Token (JWT) standard.
https://github.com/wagoodman/dive | wagoodman/dive: A tool for exploring each layer in a docker image

https://stackoverflow.com/questions/24319662/from-inside-of-a-docker-container-how-do-i-connect-to-the-localhost-of-the-mach | nginx - From inside of a Docker container, how do I connect to the localhost of the machine? - Stack Overflow
https://stackoverflow.com/questions/29395452/php-connection-failed-sqlstatehy000-2002-connection-refused | mysql - PHP Connection failed: SQLSTATE[HY000] [2002] Connection refused - Stack Overflow
https://en.wikipedia.org/wiki/Gateway_(telecommunications) | Gateway (telecommunications) - Wikipedia
https://superuser.com/questions/124453/how-can-i-scan-the-local-network-for-connected-devices-mac-os | macos - How can I scan the local network for connected devices? (Mac OS) - Super User
https://mario.fandom.com/wiki/Sarasaland | Sarasaland | MarioWiki | Fandom
https://en.wikipedia.org/wiki/Super_Nintendo_World | Super Nintendo World - Wikipedia

https://learning.edx.org/course/course-v1:EPFLx+MF201x+1T2018/block-v1:EPFLx+MF201x+1T2018+type@sequential+block@3f8215699efc4337b92f99758f36710a/block-v1:EPFLx+MF201x+1T2018+type@vertical+block@1cdbfecaa3ee4b5b8b7dd9d3fa84249d | 1.2 Les gaz | Semaine 1: Propriétés physiques des fluides | Mécanique des Fluides | edX
https://github.com/thbar/kiba | thbar/kiba: Data processing & ETL framework for Ruby
https://aws.amazon.com/blogs/architecture/hybrid-cloud-architectures-using-self-hosted-apache-kafka-and-aws-glue/ | Hybrid Cloud Architectures Using Self-hosted Apache Kafka and AWS Glue | AWS Architecture Blog
https://martinfowler.com/eaaDev/EventSourcing.html | Event Sourcing

https://stackoverflow.com/questions/24319662/from-inside-of-a-docker-container-how-do-i-connect-to-the-localhost-of-the-mach | nginx - From inside of a Docker container, how do I connect to the localhost of the machine? - Stack Overflow
http://blog.notdot.net/2010/07/Damn-Cool-Algorithms-Levenshtein-Automata | Damn Cool Algorithms: Levenshtein Automata - Nick's Blog
https://helm.sh/ | Helm

https://stackoverflow.com/questions/12057104/rails-eav-model-with-multiple-value-types | database - Rails - EAV model with multiple value types? - Stack Overflow
https://en.wikipedia.org/wiki/Entity%E2%80%93attribute%E2%80%93value_model | Entity–attribute–value model - Wikipedia
https://www.startpage.com/do/dsearch?query=cbor+vs+messagepack&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://news.ycombinator.com/item?id=14067747 | Indeed. MessagePack should be replaced with CBOR in new protocols, as CBOR is a ... | Hacker News
https://batect.dev/docs/getting-started/tutorial | Tutorial | Batect
https://www.startpage.com/do/dsearch?query=the+go+script&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results

https://fr.wikipedia.org/wiki/Udon | Udon — Wikipédia
https://fr.wikipedia.org/wiki/Bl%C3%A9_dur#Pain | Blé dur — Wikipédia

http://cbor.io/ | CBOR — Concise Binary Object Representation | Overview
https://www.devops-research.com/performance.html?leadtime=4&deployfreq=6&ttr=5&chgfail=6&industry=other | DORA DevOps Quick Check
https://cloud.google.com/devops/ | What is DevOps? Research and Solutions  |  Google Cloud
https://csrc.nist.gov/publications/detail/sp/800-207/final | SP 800-207, Zero Trust Architecture | CSRC
https://cloud.google.com/docs/security/beyondprod | BeyondProd: A new approach to cloud-native security  |  Documentation  |  Google Cloud
https://www.thoughtworks.com/radar/tools/open-policy-agent-opa | Open Policy Agent (OPA) | Technology Radar | Thoughtworks
https://www.openpolicyagent.org/docs/latest/kubernetes-tutorial/ | Open Policy Agent | Tutorial: Ingress Validation
https://github.com/npryce/adr-tools | npryce/adr-tools: Command-line tools for working with Architecture Decision Records
https://acoup.blog/2022/03/03/collections-how-the-weak-can-win-a-primer-on-protracted-war/ | Collections: How the Weak Can Win – A Primer on Protracted War – A Collection of Unmitigated Pedantry
https://acoup.blog/2019/08/16/collections-this-isnt-sparta-part-i-spartan-school/ | Collections: This. Isn’t. Sparta. Part I: Spartan School – A Collection of Unmitigated Pedantry
https://msgpack.org/ | MessagePack: It's like JSON. but fast and small.

https://en.wikipedia.org/wiki/Lightweight_Directory_Access_Protocol | Lightweight Directory Access Protocol - Wikipedia
https://context.reverso.net/traduction/anglais-francais/not+a+walk+in+the+park | not a walk in - Traduction en français - exemples anglais | Reverso Context
https://en.wikipedia.org/wiki/Disaster_recovery#How_RTO_and_RPO_values_affect_computer_system_design | Disaster recovery - Wikipedia
https://www.thoughtworks.com/radar/techniques/social-code-analysis | Social code analysis | Technology Radar | Thoughtworks

https://ruby-doc.org/core-3.1.1/Hash.html#method-i-fetch | Class: Hash (Ruby 3.1.1)
https://twitter.com/proflhunter?lang=en | Larry Hunter (@ProfLHunter) / Twitter
https://martinfowler.com/articles/data-mesh-principles.html#CorePrinciplesAndLogicalArchitectureOfDataMesh | Data Mesh Principles and Logical Architecture
https://vercel.com/ | Develop. Preview. Ship. For the best frontend teams – Vercel
https://www.thefreedictionary.com/prospect | Prospect - definition of prospect by The Free Dictionary
https://en.wikipedia.org/wiki/List_of_column-oriented_DBMSes | List of column-oriented DBMSes - Wikipedia
https://geeko.lesoir.be/2022/03/02/dead-by-daylight-va-etre-decline-en-un-jeu-de-plateau/ | Dead By Daylight va être décliné en un jeu de plateau - Geeko
https://stackoverflow.com/questions/8817500/how-to-make-sure-that-a-certain-port-is-not-occupied-by-any-other-process | windows - How to make sure that a certain Port is not occupied by any other process - Stack Overflow
https://stackoverflow.com/questions/11302705/will-a-firewall-block-local-tcp-communication-between-processes | windows - Will a firewall block local TCP communication between processes? - Stack Overflow
https://www.howtogeek.com/225487/what-is-the-difference-between-127.0.0.1-and-0.0.0.0/ | What is the Difference Between 127.0.0.1 and 0.0.0.0?

https://osteriabolognese.eu/ | Osteria Bolognese
http://www.pholaumi.be/fr/ | PhoLauMi - Restaurant Vietnamien à Bruxelles - Cuisine Vietnamienne à Bruxelles | Restaurant Vietnamien à Bruxelles - Cuisine Vietnamienne à Bruxelles
https://www.brusselskitchen.com/osteria-romana/bruxelles/restaurant | Osteria Romana | Brussels' Kitchen
https://www.brusselskitchen.com/lauberge-des-maieurs/bruxelles/restaurant | L’Auberge des Maieurs | Brussels' Kitchen

https://www.defmacro.org/2014/10/03/engman.html | 44 engineering management lessons | defmacro
https://www.thefreedictionary.com/loophole | Loophole - definition of loophole by The Free Dictionary
https://www.chathamhouse.org/2021/05/myths-and-misconceptions-debate-russia/myth-15-its-all-about-putin-russia-manually-run | Myth 16: ‘What comes after Putin must be better than Putin’ | Chatham House – International Affairs Think Tank
https://bundler.io/v2.3/guides/using_bundler_in_applications.html#installing-gems---bundle-install | Bundler: How to manage application dependencies with Bundler
https://docs.docker.com/engine/scan/ | Vulnerability scanning for Docker local images | Docker Documentation
https://github.com/oldboyxx/jira_clone/blob/master/client/src/App/index.jsx | jira_clone/index.jsx at master · oldboyxx/jira_clone

https://beam.apache.org/documentation/runners/capability-matrix/ | Apache Beam Capability Matrix
https://twitter.com/martinkl?lang=en | Martin Kleppmann (@martinkl) / Twitter
http://rubykoans.com/ | Learn Ruby with the Edgecase Ruby Koans
https://blog.samaltman.com/the-days-are-long-but-the-decades-are-short | The days are long but the decades are short - Sam Altman

https://www.startpage.com/do/dsearch?query=metaphors+we+live+by&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://fr.wikipedia.org/wiki/Joseph_Staline#Mort_et_fun%C3%A9railles | Joseph Staline — Wikipédia
https://solar.lowtechmagazine.com/fr/2015/02/heating-people-not-spaces.html | Avoir chaud à l’ancienne : chauffer les personnes et non les espaces | LOW←TECH MAGAZINE
https://solar.lowtechmagazine.com/2014/09/circulating-fans-air-conditioning/ | The Revenge of the Circulating Fan | LOW←TECH MAGAZINE
https://solar.lowtechmagazine.com/2015/03/radiant-and-conductive-heating-systems/ | Radiant <span class="amp">&</span> Conductive Heating Systems | LOW←TECH MAGAZINE
https://solar.lowtechmagazine.com/low-tech-solutions.html | Low-tech solutions | LOW←TECH MAGAZINE

https://dev.to/docker/make-your-containers-better-smaller-and-more-secure-using-dockerslim-1ai8?mkt_tok=NzkwLVNTQi0zNzUAAAGCxidrA0KoMI7wPA2WLuUGrZIeYFIyPXyFv4tKb5JaOx9N9aaKMZJwE3HL-FJqtCoqjNdnoMYBpjQ7bDkMdvFVgnuqE7zV0Rr9JCzhVZR8 | Make Your Containers Better, Smaller and More Secure using DockerSlim and Trivy - DEV Community
https://www.getdbt.com/ | dbt™ - Transform data in your warehouse
https://opentelemetry.io/ | OpenTelemetry
https://openlineage.io/ | Home | OpenLineage
https://acoup.blog/book-recommendation-list/ | Book Recommendation List – A Collection of Unmitigated Pedantry
https://fleetdm.com/handbook/security#google-workspace-security | Security | Fleet handbook
https://www.youtube.com/watch?v=Ftg8fjY_YWU | Christian Heimes - File descriptors, Unix sockets and other POSIX wizardry - PyCon 2016 - YouTube
https://stackoverflow.com/questions/31045575/how-to-trace-system-calls-of-a-program-in-mac-os-x | macos - How to trace system calls of a program in Mac OS X? - Stack Overflow
https://dev.to/captainsafia/say-this-five-times-fast-strace-ptrace-dtrace-dtruss-3e1b | Say this five times fast: strace, ptrace, dtrace, dtruss - DEV Community
http://web.archive.org/web/20200103161748/http://osxbook.com/book/bonus/ancient/procfs/ | /proc on Mac OS X

https://www.metacritic.com/music/texas-moon-ep/khruangbin | Texas Moon [EP] by Khruangbin Reviews and Tracks - Metacritic
https://www.lesoir.be/426468/article/2022-02-25/certains-lestent-desormais-leurs-couvertures-pour-dormir-mieux? | Certains «lestent» désormais leurs couvertures pour dormir mieux - Le Soir
https://sosoir.lesoir.be/les-9-cantines-preferees-de-nos-chefs-etoiles | Les 9 cantines préférées de nos chefs étoilés
https://www.anaconda.com/state-of-data-science-2021 | Anaconda | State of Data Science 2021
https://www.go-fair.org/fair-principles/ | FAIR Principles - GO FAIR
https://martinfowler.com/bliki/DDD_Aggregate.html | DDD_Aggregate
https://martinfowler.com/bliki/ComputationalNotebook.html | ComputationalNotebook
https://www.dataengineering.academy/overview | Pipeline Academy - Overview - Data Engineering Coding Bootcamp in Berlin — Pipeline Data Engineering Academy

https://news.ycombinator.com/item?id=30447186 | Dwarf Fortress Steam Updates – Release Roadmap | Hacker News
https://www.gog.com/game/caves_of_qud | -10% Caves of Qud on GOG.com
https://www.domainlanguage.com/ddd/ | DDD Resources - Domain Language
https://martinfowler.com/eaaDev/DomainEvent.html | Domain Event

https://stackoverflow.com/questions/10456174/oauth-how-to-test-with-local-urls | localhost - OAuth: how to test with local URLs? - Stack Overflow
https://github.com/brunopulis/awesome-a11y/blob/master/topics/books.md | awesome-a11y/books.md at master · brunopulis/awesome-a11y

https://fossbytes.com/10-best-simulation-games/ | Top 10 Best Simulation Games To Play In 2022 - Fossbytes
https://www.google.com/search?client=firefox-b-d&tbm=lcl&q=Un+posto+al+sole&rflfq=1&num=20&stick=H4sIAAAAAAAAABVQu1EEUQybC2AIGY4h2GhL8F92EYQUcMFFx9wyHBHlUAF1UQXayO_JsmT54X45QhQ-06piaBOVWo7m1lKlkSoS2gDBFO-EdA4KFsPW8qzu6cK5iOmudp0hWpUyhhQYPFPbfXnqGqAJ6RAshFCUNZOCxT-73r0cA6MIC7ggrV0EnJYo7azp9JgwbfqYOllNyN28pMt2lCtlilHTUpNcY9Ac8SGpRyDOBXcqxyGAokOKodlcjm3VSHgUlzUwWe7UsVDJNnM-hacCVZvqkBkJmjqz7OfTGuOpMByV9NLdqpiKAZx8dQzRWJ6KJsNz0amM2YKarS3qFVyyUofG_Xs4_B1eXs_X2_m6fm_nC8u2Xdbr6fT5c_f4dl0_ttvXtp7e19v2fv4HcnkBqdkBAAA&sa=X&ved=2ahUKEwjXzZ2toJb2AhXinFwKHe-GCxAQ63UoAXoECDYQAg&biw=1440&bih=781&dpr=1#rlfi=hd:;si:7017399811027820106,l,ChBVbiBwb3N0byBhbCBzb2xlSI7YnKWZqoCACFokEAAQARACEAMYARgCGAMiEHVuIHBvc3RvIGFsIHNvbGUyAml0kgEQcGl6emFfcmVzdGF1cmFudKoBGBABKhQiEHVuIHBvc3RvIGFsIHNvbGUoBw,y,gCz8zqRac90;mv:[[50.8653927,4.4191369],[50.828338200000005,4.3344292]] | Un posto al sole - Google Zoeken

https://pixeldetracking.com/fr/google-tag-manager-server-side-tagging | Google Tag Manager, la nouvelle arme anti adblock | Pixel de tracking
https://docs.stimulusreflex.com/ | Welcome - StimulusReflex
https://docs.celeryproject.org/en/stable/getting-started/backends-and-brokers/index.html | Backends and Brokers — Celery 5.2.3 documentation

https://authy.com/blog/how-the-authy-two-factor-backups-work/ | How Authy 2FA Backups Work - Authy
https://www.adithyabalaji.com/productivity/2022/02/01/Sleep.html | Get Better Sleep—Anecdata and Sleep Tech | Adithya Balaji
https://www.startpage.com/do/dsearch?query=droit+de+cit%C3%A9&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://cloudacademy.com/resource/recommended-reading-for-dp-100-exam/ | Recommended Reading for DP-100 Exam - Cloud Academy
https://stackoverflow.com/questions/430182/is-c-strongly-typed | strong typing - Is C strongly typed? - Stack Overflow
https://www.nytimes.com/wirecutter/reviews/the-best-adjustable-dumbbells/ | The Best Adjustable Dumbbells for 2022 | Reviews by Wirecutter
https://www.bloomberg.com/news/articles/2019-03-20/amsterdam-says-newly-built-homes-aren-t-for-renting-out | Amsterdam Says Newly Built Homes Aren't for Renting Out - Bloomberg
https://www.interdb.jp/pg/ | The Internals of PostgreSQL : Introduction
https://relishapp.com/rspec/rspec-core/docs/command-line/order | `--order` option - Command line - RSpec Core - RSpec - Relish
https://www.raptitude.com/2022/02/what-i-learned-during-my-three-days-offline/ | What I Learned During My Three Days Offline
https://stackoverflow.com/questions/3737740/is-there-a-better-way-to-run-a-command-n-times-in-bash | loops - Is there a better way to run a command N times in bash? - Stack Overflow
https://docs.gitlab.com/ee/topics/git/numerous_undo_possibilities_in_git/index.html#undo-staged-local-changes-without-modifying-history | Undo possibilities in Git | GitLab

https://www.hopono-shop.com/en/ | hopono - Concept Decoration store, gift items, watches, jewelery, glasses - hopono
https://www.nintendo.com/nintendo-direct/02-09-2022/ | Nintendo Direct - 2.09.2022
https://www.metacritic.com/game/xbox-series-x/olliolli-world | OlliOlli World for Xbox Series X Reviews - Metacritic
https://aryanvij02.medium.com/push-ups-with-python-mediapipe-open-a544bd9b4351 | Push-Ups with Python! (mediapipe + OpenCV) | by Aryan Vij | Feb, 2022 | Medium

https://blog.the-pans.com/wrong/ | Don't point out something wrong immediately
https://github.com/vasturiano/react-force-graph/blob/master/example/highlight/index.html | react-force-graph/index.html at master · vasturiano/react-force-graph
https://github.com/vasturiano/react-force-graph/issues/293 | Link Opacity Gradients · Issue #293 · vasturiano/react-force-graph
https://github.com/vasturiano/3d-force-graph/blob/master/example/gradient-links/index.html | 3d-force-graph/index.html at master · vasturiano/3d-force-graph

https://observablehq.com/explore | Explore

https://cheatsheetseries.owasp.org/ | Introduction - OWASP Cheat Sheet Series
https://testing.googleblog.com/ | Google Testing Blog
https://opencollective.com/how-it-works | Open Collective - Make your community sustainable. Collect and spend money transparently.
https://www.amazon.com/Explore-Increase-Confidence-Exploratory-Testing/dp/1937785025 | Explore It!: Reduce Risk and Increase Confidence with Exploratory Testing: Hendrickson, Elisabeth: 9781937785024: Amazon.com: Books
https://2021.stateofjs.com/en-US/ | The State of JS 2021
https://monzo.com/tone-of-voice/ | Monzo – Tone of Voice

https://www.youtube.com/watch?v=PhiXo5CWjYU | Sliming in TDD (Fake it 'til you make it) | Code Walks 024 - YouTube
http://sequel.jeremyevans.net/ | Sequel: The Database Toolkit for Ruby

https://developer.mozilla.org/fr/docs/Web/HTTP/CORS | Cross-origin resource sharing (CORS) - HTTP | MDN
https://tools.ietf.org/id/draft-cavage-http-signatures-01.html | HTTP Signatures
https://blog.awaxman.com/what-is-the-difference-between-a-block-a-proc-and-a-lambda-in-ruby | What is the Difference Between a Block, a Proc, and a Lambda in Ruby?
https://dannorth.net/introducing-bdd/ | Introducing BDD - Dan North & Associates Ltd
https://webuild.envato.com/blog/making-the-most-of-bdd-part-1/ | Making the Most of BDD, Part 1 - We build Envato

https://stackoverflow.com/questions/47064090/rails-postgres-migration-why-am-i-receiving-the-error-pgundefinedfunction | postgresql - Rails + Postgres migration - why am I receiving the error "PG::UndefinedFunction: ERROR: function gen_random_uuid() does not exist"? - Stack Overflow
https://stackoverflow.com/questions/16611226/how-to-install-postgres-extensions-at-database-creation | ruby on rails - How to install Postgres extensions at database creation? - Stack Overflow
https://github.com/zipmark/rspec_api_documentation | zipmark/rspec_api_documentation: Automatically generate API documentation from RSpec
https://github.com/zipmark/rspec_api_documentation/blob/master/lib/tasks/docs.rake | rspec_api_documentation/docs.rake at master · zipmark/rspec_api_documentation
https://json-schema.org/learn/file-system.html | Modeling a file system with JSON Schema | JSON Schema
https://www.seancdavis.com/posts/4-ways-to-pass-arguments-to-a-rake-task/ | 4 Ways to Pass Arguments to a Rake Task | Sean C Davis

https://devdocs.io/rails~7.0/activerecord/recordinvalid | Ruby on Rails 7.0 / ActiveRecord::RecordInvalid — DevDocs
https://stackoverflow.com/questions/51557377/ruby-loop-through-each-element-in-an-array-n-times | Ruby loop through each element in an array n times - Stack Overflow

https://www.startpage.com/do/dsearch?query=linux+administration&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.youtube.com/watch?v=wsh64rjnRas | Linux System Administration Full Course - YouTube
https://www.youtube.com/c/GeeksLesson/playlists | Geek's Lesson - YouTube
https://www.bruzz.be/economie/nederlands-meest-geoefende-taal-op-gratis-talenplatform-brulingua-2022-02-12 | Nederlands meest geoefende taal op gratis talenplatform Brulingua | BRUZZ

https://fr.wikipedia.org/wiki/Cuisine_japonaise | Cuisine japonaise — Wikipédia
https://www.startpage.com/do/dsearch?query=wagashi+bruxelles&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.rtbf.be/article/et-si-on-tentait-les-patisseries-japonaises-10574525 | Et si on tentait les pâtisseries japonaises ?
https://www.tagawa.eu/table-kitchenware/ | TABLE & KITCHENWARE - TAGAWA
https://www.youtube.com/watch?v=jD8n2CKEWtA | 27 GARDENING HACKS YOU'LL WANT TO KNOW - YouTube
https://cognitivemedium.com/srs-mathematics | Using spaced repetition systems to see through a piece of mathematics
https://apple.stackexchange.com/questions/206887/macos-x-iptables | mac - MacOS X - iptables? - Ask Different

https://paulbutler.org/2022/what-does-it-mean-to-listen-on-a-port/ | Paul Butler – What does it mean to listen on a port?
https://www.thediff.co/p/the-factorio-mindset | The Factorio Mindset - The Diff

https://github.com/magnusvk/counter_culture | magnusvk/counter_culture: Turbo-charged counter caches for your Rails app.
https://apidock.com/rails/Enumerable/index_by | index_by (Enumerable) - APIdock
https://docs.google.com/presentation/d/1-07bYoKThDciyVtZFS04KWX6PdvVUPw-bF2soAYyKZM/edit#slide=id.g1132e26828f_0_708 | ISO Traning: Secure Development Policy - Google Slides
https://portswigger.net/research/top-10-web-hacking-techniques-of-2021 | Top 10 web hacking techniques of 2021 | PortSwigger Research

https://en.wikipedia.org/wiki/The_Salvation_Army#Stance_on_LGBT_issues | The Salvation Army - Wikipedia
https://stackify.com/rack-mini-profiler-a-complete-guide-on-rails-performance/ | Rack Mini Profiler: A Complete Guide on Rails Performance – Stackify

https://www.amazon.com/Systems-Performance-Brendan-Gregg/dp/0136820158/ref=sr_1_2?crid=3TPHSIRSU0FZY&keywords=The+Art+of+Computer+Systems+Performance+Analysis&qid=1644433526&s=books&sprefix=the+art+of+computer+systems+performance+analysis%2Cstripbooks-intl-ship%2C150&sr=1-2 | Systems Performance (Addison-Wesley Professional Computing Series): Gregg, Brendan: 9780136820154: Amazon.com: Books
http://www.monk.be/ | Monk bar - BXL

https://shivamarora.medium.com/a-guide-to-manage-your-environment-variables-in-a-better-way-using-direnv-2c1cd475c8e | A guide to manage your environment variables in a better way using direnv | by Shivam Arora | Medium
https://docs.google.com/document/d/13RGW1xM-yVmMHW5Zn1Yyifj356ucbxzU/edit | A.14 Secure Development Policy.docx - Google Docs
https://owasp.org/ | OWASP Foundation | Open Source Foundation for Application Security

https://flexboxfroggy.com/ | Flexbox Froggy - A game for learning CSS flexbox
https://codepip.com/games/grid-garden/ | Grid Garden - A game for learning CSS grid
https://codepip.com/games/ | Games | Codepip

https://dev.to/molly/rake-task-enhance-method-explained-3bo0 | Rake::Task .enhance() Method Explained - DEV Community
https://stackoverflow.com/questions/18104923/fatal-ref-head-is-not-a-symbolic-ref-during-interactive-git-rebase | "fatal: ref HEAD is not a symbolic ref" during interactive git rebase - Stack Overflow

https://www.youtube.com/watch?v=VhLEmrt1tjA | How to Fix a Tight Lower Back in 30 SECONDS - YouTube

https://www.startpage.com/do/dsearch?query=verbal+fluency+exercises&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
http://foodcoop.film/see-the-film/ | See the film - Food Coop the film - Lardux films
https://vimeo.com/ondemand/foodcoopthefilm | Watch Food Coop Online | Vimeo On Demand on Vimeo
https://stackoverflow.com/questions/14824551/rendering-json-in-controller | ruby on rails - Rendering JSON in controller - Stack Overflow
https://blog.bitsrc.io/common-react-hooks-mistakes-every-developer-should-avoid-defd47d09d8c | Common React Hooks Mistakes You Should Avoid | by Piumi Liyana Gunawardhana | Bits and Pieces
https://reactjs.org/docs/hooks-effect.html | Using the Effect Hook – React
https://blog.neuronation.com/en/study-shows-language-exercises-can-be-effective-and-fun/#influence_on_verbal_fluency | Study shows: Language exercises can be effective and fun | NeuroNation
https://en.wikipedia.org/wiki/Free_variables_and_bound_variables | Free variables and bound variables - Wikipedia

https://www.rabbitmq.com/tutorials/amqp-concepts.html | AMQP 0-9-1 Model Explained — RabbitMQ
https://kapeli.com/dash | Dash for macOS - API Documentation Browser, Snippet Manager - Kapeli
https://langfren.cvosemper.be/en/languages/english-courses.php | English courses at CVO Semper
https://www.google.be/maps/place/Cairngorms+National+Park/@55.8640335,2.5138724,5z/data=!4m12!1m6!3m5!1s0x4887c79a2099c0f7:0x469a1eebe54c0a58!2sEdinburgh+Castle!8m2!3d55.9485947!4d-3.1999135!3m4!1s0x4885f016397fbcc9:0xceaa2fb994f58b34!8m2!3d57.0491341!4d-3.5616302 | Cairngorms National Park - Google Maps
https://reactjs.org/docs/hooks-intro.html | Introducing Hooks – React
https://reactjs.org/docs/hooks-effect.html | Using the Effect Hook – React
https://guides.rubyonrails.org/7_0_release_notes.html | Ruby on Rails 7.0 Release Notes — Ruby on Rails Guides
https://guides.rubyonrails.org/routing.html | Rails Routing from the Outside In — Ruby on Rails Guides
https://en.wikipedia.org/wiki/Internationalization_and_localization | Internationalization and localization - Wikipedia
https://github.com/jaredpalmer/formik/blob/master/packages/formik/src/Field.tsx | formik/Field.tsx at master · jaredpalmer/formik
https://reactjs.org/docs/hooks-reference.html#usememo | Hooks API Reference – React
https://devdocs.io/react/higher-order-components | React / Higher-Order Components — DevDocs
https://gist.github.com/alexander-cit/76004ec6334095fe2f20b849d026acbf | reset.sh

https://www.startpage.com/do/dsearch?query=think+faster+when+talking&cat=web&pl=ext-ff&language=english&extVersion=1.3.0 | Startpage Search results
https://www.youtube.com/watch?v=slivVd9hQlU | How To Think FAST and Talk SMART - Verbal Fluency - YouTube

https://stackoverflow.com/questions/4904096/whats-the-difference-between-unit-functional-acceptance-and-integration-test | testing - What's the difference between unit, functional, acceptance, and integration tests? - Stack Overflow
https://martinfowler.com/articles/practical-test-pyramid.html#acceptance | The Practical Test Pyramid
https://stackoverflow.com/questions/57792900/python-region-folding-syntax | Python region folding syntax - Stack Overflow

https://www.postgresql.org/docs/9.1/ddl-schemas.html | PostgreSQL: Documentation: 9.1: Schemas
https://medium.com/reflektive-engineering/from-service-objects-to-interactors-db7d2bb7dfd9 | From Service Objects to Interactors | by Jared Rader | reflektive-engineering | Medium
https://www.diagrams.net/ | Diagram Software and Flowchart Maker
https://www.tldraw.com/ | tldraw

https://stackoverflow.com/questions/816071/prototype-based-vs-class-based-inheritance | javascript - prototype based vs. class based inheritance - Stack Overflow
https://softwareengineering.stackexchange.com/questions/121778/is-duck-typing-a-subset-of-polymorphism | object oriented - Is duck typing a subset of polymorphism - Software Engineering Stack Exchange
https://stackoverflow.com/questions/11502433/what-is-the-difference-between-polymorphism-and-duck-typing | oop - What is the difference between polymorphism and duck typing? - Stack Overflow
https://dev.to/caicindy87/rendering-json-in-a-rails-api-25fd | Rendering JSON in a Rails API - DEV Community
https://stackoverflow.com/questions/14824551/rendering-json-in-controller | ruby on rails - Rendering JSON in controller - Stack Overflow

https://cpldcpu.wordpress.com/2022/01/23/controlling-rgb-leds-with-only-the-powerlines-anatomy-of-a-christmas-light-string/ | Controlling RGB LEDs With Only the Powerlines: Anatomy of a Christmas Light String – Tim's Blog
https://en.wikipedia.org/wiki/Rubber_duck_debugging | Rubber duck debugging - Wikipedia
https://blog.skylight.io/the-lifecycle-of-a-request/ | The Lifecycle of a Rails Request
https://longliveruby.com/articles/rails-request-cycle | Rails request cycle explained
https://ns1.com/blog/decoding-dig-output | Decoding DIG Output
https://github.com/rack/rack/wiki/(tutorial)-rackup-howto | (tutorial) rackup howto · rack/rack Wiki
https://en.wikipedia.org/wiki/Common_Gateway_Interface | Common Gateway Interface - Wikipedia
https://learnwithdaniel.com/2015/01/apache-puma-via-reverse-proxy/ | Rails Server with Apache + Puma (via reverse proxy) | Learn with Daniel - A learner diary
https://www.python.org/dev/peps/pep-3333/#preface-for-readers-of-pep-333 | PEP 3333 -- Python Web Server Gateway Interface v1.0.1 | Python.org
https://en.wikipedia.org/wiki/FastCGI | FastCGI - Wikipedia
https://en.wikipedia.org/wiki/Rack_(web_server_interface) | Rack (web server interface) - Wikipedia
https://prograils.com/ruby-methods-differences-load-require-include-extend#load | Ruby Methods: differences between load, require, include and extend in Ruby. | Prograils
https://javascript.info/class-inheritance | Class inheritance
https://stackoverflow.com/questions/10383535/in-ruby-whats-the-relationship-between-new-and-initialize-how-to-return-n | constructor - In Ruby, what's the relationship between 'new' and 'initialize'? How to return nil while initializing? - Stack Overflow
https://blog.appsignal.com/2018/08/07/ruby-magic-changing-the-way-ruby-creates-objects.html | Changing the Way Ruby Creates Objects | AppSignal Blog
https://makandracards.com/makandra/15157-ruby-finding-where-a-method-is-defined | Ruby: Finding where a method is defined - makandra dev
https://stackoverflow.com/questions/38289634/why-does-the-ruby-module-kernel-exist | oop - Why does the Ruby module Kernel exist? - Stack Overflow

https://www.metacritic.com/movie/the-tragedy-of-macbeth | The Tragedy of Macbeth Reviews - Metacritic

https://doyou.world/ | Do!!You!!! - Charlie Bones – DO!!YOU!!!WORLD
https://stackoverflow.com/questions/30064937/writing-my-own-cat-function-in-c | Writing my own Cat function in C - Stack Overflow
https://www.liveabout.com/cardinal-signs-aries-cancer-libra-capricorn-206724 | The Meaning of the Cardinal Signs in Astrology

https://en.wikipedia.org/wiki/SOA_record | SOA record - Wikipedia
https://stackoverflow.com/questions/31806431/rails-validation-knowing-which-field-is-invalid | Rails validation, knowing which field is invalid? - Stack Overflow
https://stackoverflow.com/questions/42510002/docker-how-to-clear-the-logs-properly-for-a-docker-container | Docker: How to clear the logs properly for a Docker container? - Stack Overflow
https://sudo-bmitch.github.io/presentations/dc2019/tips-and-tricks-of-the-captains.html#5 | Tips and Tricks From A Docker Captain - Brandon Mitchell
https://www.steveonstuff.com/2022/01/27/no-such-thing-as-clean-code | There’s No Such Thing as Clean Code
https://stackoverflow.com/questions/10567430/check-if-an-array-is-subset-of-another-array-in-ruby | Check if an array is subset of another array in Ruby - Stack Overflow
https://snippets.aktagon.com/snippets/925-where-not-exists-in-activerecord-rails | WHERE NOT EXISTS in ActiveRecord / Rails
https://www.postgresqltutorial.com/postgresql-exists/ | PostgreSQL EXISTS By Practical Examples
https://www.postgresql.org/docs/8.1/functions-subquery.html | PostgreSQL: Documentation: 8.1: Subquery Expressions

https://visualgo.net/en | visualising data structures and algorithms through animation - VisuAlgo
https://www.bookandsword.com/2021/05/08/how-much-did-a-tunic-cost-in-the-roman-empire/ | How Much did a Tunic Cost in the Roman Empire? – Book and Sword
http://infolab.stanford.edu/~ullman/focs.html | Aho/Ullman Foundations of Computer Science
https://blog.trailofbits.com/2022/01/26/part-1-the-life-of-an-optimization-barrier/ | Part 1: The life of an optimization barrier | Trail of Bits Blog
https://sinews.siam.org/Details-Page/origami-and-the-structure-of-materials | Origami and the Structure of Materials
https://softwareengineering.stackexchange.com/questions/143659/what-was-the-most-used-programming-language-before-c-was-created | history - What was the most used programming language before C was created? - Software Engineering Stack Exchange
https://www.cs.auckland.ac.nz/~paul/C/Mac/ | Developing C programs on MacOS
https://gustedt.wordpress.com/ | Jens Gustedt's Blog

https://www.efp.be/faq/ | FAQ - Foire aux questions de l'efp
https://radimrehurek.com/gensim/auto_examples/core/run_similarity_queries.html | Similarity Queries — gensim
https://arxiv.org/abs/1607.04606 | [1607.04606] Enriching Word Vectors with Subword Information

https://stackoverflow.com/questions/21138207/activerecordstatementinvalid-pg-infailedsqltransaction | ruby on rails - ActiveRecord::StatementInvalid: PG InFailedSqlTransaction - Stack Overflow
https://stackoverflow.com/questions/38763007/how-to-use-spacy-lemmatizer-to-get-a-word-into-basic-form | python - how to use spacy lemmatizer to get a word into basic form - Stack Overflow
https://stackoverflow.com/questions/23381538/efficient-way-to-return-select-columns-from-rails-4-activerecord-nested-query | sql - Efficient way to return select columns from Rails 4 ActiveRecord nested query - Stack Overflow
https://www.csail.mit.edu/research/computer-aided-programming | Computer-Aided Programming | MIT CSAIL
https://stackoverflow.com/questions/6997141/rails-has-many-through-with-polymorphic-association-will-this-work | activerecord - Rails: has_many through with polymorphic association - will this work? - Stack Overflow
https://tomdebruijn.com/posts/ruby-rspec-instance-variables/ | When not to use instance variables in RSpec | Tom de Bruijn
https://en.wikipedia.org/wiki/Rack_(web_server_interface) | Rack (web server interface) - Wikipedia
https://www.codewithjason.com/add-rails-application-nginx-server/ | How to add a Rails application to an nginx server - Code with Jason
https://www.rubyguides.com/2019/08/puma-app-server/ | Why Do We Need Application Servers in Ruby? (Like Puma)
https://stackoverflow.com/questions/50516699/why-do-i-need-nginx-with-puma | ruby on rails - Why do I need Nginx with Puma? - Stack Overflow
https://stackoverflow.com/questions/6230904/whats-the-difference-between-rack-app-vs-rails-app | What's the difference between rack app vs. rails app? - Stack Overflow

https://cirw.in/blog/constant-lookup | Everything you ever wanted to know about constant lookup in Ruby

https://blog.arkency.com/2014/04/mastering-rails-validations-contexts/ | → Mastering Rails Validations: Contexts | Arkency Blog
https://stackoverflow.com/questions/6210572/how-to-replace-a-hash-key-with-another-key | ruby on rails - How to replace a hash key with another key - Stack Overflow
https://superuser.com/questions/162999/how-to-find-files-with-certain-text-in-the-terminal/163002 | macos - How to find files with certain text in the Terminal - Super User

https://stackoverflow.com/questions/34624754/how-it-works-belongs-to-user-dependent-destroy | ruby on rails - How it works - `belongs_to :user, dependent: :destroy` - Stack Overflow
https://www.bigbinary.com/blog/Rails-5-supports-bi-directional-destroy-dependency | Rails 5 supports bi-directional destroy dependency | BigBinary Blog
https://stevepolito.design/blog/create-dependent-associations-in-factorybot/ | Create Dependent Associations in FactoryBot
https://www.youtube.com/watch?v=-ZIytGINZ6c | Funny Fitness Challenge TikTok Compilation. The Fitness Wizard - YouTube

https://theworsethingsgettheharderifight.tumblr.com/post/673593715848282112 | The Harder I Fight, The More I Love You
https://techspot.zzzeek.org/2015/02/15/asynchronous-python-and-databases/ | zzzeek : Asynchronous Python and Databases
https://lvl99catpriest.tumblr.com/ | Worse In Person

https://www.kuleuven.be/mooc/index.html | MOOCs at KU Leuven – MOOC

https://fr.wikipedia.org/wiki/Test_de_Noble | Test de Noble — Wikipédia
https://www.youtube.com/watch?v=UeHt4GXeyVA | Is FOAM ROLLING Worth It? - YouTube
https://fs.blog/spacing-effect/ | The Spacing Effect: How to Improve Learning and Maximize Retention - Farnam Street
https://www.upbuild.io/blog/leadership-as-a-type-b-introvert/ | Leadership As a Type B Introvert | UpBuild
https://publiclab.org/w//spectrometry | 🎈 Public Lab: Spectrometry
https://jackschaedler.github.io/handwriting-recognition/ | GRAIL Text Recognizer
https://kedro.readthedocs.io/en/stable/ | Welcome to Kedro’s documentation! — Kedro 0.17.6 documentation

https://ieeexplore.ieee.org/abstract/document/8563218 | dhSegment: A Generic Deep-Learning Approach for Document Segmentation | IEEE Conference Publication | IEEE Xplore
https://fairyonice.github.io/Frequency-analysis-of-images-from-scratch.html | Frequency analysis of images from scratch
https://github.com/ReactiveX/rxjs | ReactiveX/rxjs: A reactive programming library for JavaScript

https://x-team.com/blog/code-streaming-channels/ | 14 Code Streaming Channels to Make You a Better Programmer
https://www.learnwithjason.dev/let-s-learn-react | Let's Learn React! · Learn With Jason
https://www.mozilla.org/en-US/firefox/developer/ | Firefox Developer Edition
https://studies.helsinki.fi/courses/cur/otm-6adcac18-c8b8-4771-8710-187dac70dfc8/Elements_of_AI_Building_AI_Intermediate | Elements of AI: Building AI - Intermediate, Open Uni: Elements of AI: Building AI - Intermediate
https://buildingai.elementsofai.com/ | Course Overview - Building AI
https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/ETag | ETag - HTTP | MDN
https://github.innominds.com/mit-nlp/MITIE | GitHub - mit-nlp/MITIE: MITIE: library and tools for information extraction
https://ieeexplore.ieee.org/abstract/document/8089448 | Automatic Software Repair: A Survey | IEEE Journals & Magazine | IEEE Xplore

https://stackoverflow.com/questions/32305821/rails4-migration-adding-null-false-to-an-add-reference-method | ruby on rails - Rails4 migration- adding null: false to an add_reference method? - Stack Overflow
https://www.geeksforgeeks.org/sql-using-clause/ | SQL | USING Clause - GeeksforGeeks
https://blog.jetbrains.com/ruby/2020/11/rubymine-2020-3-eap7/#git_staging_support | RubyMine 2020.3 EAP7: Improved Terminal, Git Staging Support, and More | The RubyMine Blog
https://api.rubyonrails.org/classes/ActiveRecord/NestedAttributes/ClassMethods.html | ActiveRecord::NestedAttributes::ClassMethods
https://augustl.com/blog/2008/procs_blocks_and_anonymous_functions/ | Procs, blocks and anonymous functions (August Lilleaas' blog)
https://www.amazon.science/blog/using-computer-vision-to-weed-out-product-catalogue-errors | Using computer vision to weed out product catalogue errors - Amazon Science
https://github.com/icalendar/icalendar | icalendar/icalendar: icalendar.rb main repository
https://www.atlassian.com/blog/git/************************-or-rebase | Git team workflows: merge or rebase? - Work Life by Atlassian
https://medium.com/@dan_abramov/smart-and-dumb-components-7ca2f9a7c7d0 | Presentational and Container Components | by Dan Abramov | Medium
https://github.com/enaqx/awesome-react | enaqx/awesome-react: A collection of awesome things regarding React ecosystem
https://flatlogic.com/blog/best-react-open-source-projects/#six | Best React Open Source Projects - Flatlogic Blog
https://en.wikipedia.org/wiki/TypeScript#Generics | TypeScript - Wikipedia
https://2020.stateofjs.com/en-US/technologies/ | State of JS 2020: Technologies

https://alexey-shepelev.medium.com/data-migrations-in-ruby-on-rails-8ddf22f9c800 | Data migrations in Ruby on Rails. In the world of programming and IT… | by Alexey Shepelev | Medium
https://stackoverflow.com/questions/452859/inserting-multiple-rows-in-a-single-sql-query | sql server - Inserting multiple rows in a single SQL query? - Stack Overflow
https://commandercoriander.net/blog/2014/11/09/a-multiline-string-cheatsheet-for-ruby/ | Cmdr Coriander
https://www.rubydoc.info/gems/rubocop-rails/2.9.1/RuboCop/Cop/Rails/SquishedSQLHeredocs | Class: RuboCop::Cop::Rails::SquishedSQLHeredocs — Documentation for rubocop-rails (2.9.1)
https://stackoverflow.com/questions/21850995/association-for-polymorphic-belongs-to-of-a-particular-type | ruby on rails - Association for polymorphic belongs_to of a particular type - Stack Overflow
https://stackoverflow.com/questions/9090204/rails-migration-set-current-date-as-default-value/22132081 | Rails migration set current date as default value - Stack Overflow

https://news.ycombinator.com/item?id=29978036 | I Spent Hundreds of Hours Working in VR | Hacker News
https://datatracker.ietf.org/doc/html/rfc5545#section-2 | rfc5545
https://github.com/thrivesmart/ics-rails-example/blob/master/ical.rb | ics-rails-example/ical.rb at master · thrivesmart/ics-rails-example
https://www.ruby-forum.com/t/publishing-an-ical-calendar/115365 | Publishing an ical calendar? - Rails - Ruby-Forum
https://news.ycombinator.com/item?id=29995152 | Ask HN: Those making $500/month on side projects in 2022 – Show and tell | Hacker News
https://www.patreon.com/fivem | Cfx.re/The CitizenFX Collective is creating FiveM for GTA V | Patreon

https://en.wikipedia.org/wiki/Hue | Hue - Wikipedia
https://www.youtube.com/watch?v=rcTw5CrhCYg&list=PL-9x0_FO_lgmDWZ825tJylJ-e7xplDCOF | How to Get Started with Natural Language Processing in Tensorflow 2 - YouTube
https://en.wikipedia.org/wiki/Planar_graph | Planar graph - Wikipedia
https://docs.google.com/document/d/1lIf2vxiecsQWJ-iS4vXWKosfg18LWhJbM5dyaA_kN2w/edit#heading=h.rt43dpbbv0kw | Considering multiple cloud providers - Google Docs

https://xcelab.net/rm/statistical-rethinking/ | Statistical Rethinking | Richard McElreath
https://www.efp.be/formations/chef-d-entreprise/efp-2/graphic-designer.html | Graphic designer
https://web.dev/learn/css/typography/ | Text and typography

https://www.datasciencemadesimple.com/return-first-n-character-of-the-column-in-postgresql/ | Return First ‘N’ character of the column in Postgresql - DataScience Made Simple
https://www.postgresql.org/docs/9.1/functions-comparison.html | PostgreSQL: Documentation: 9.1: Comparison Operators
https://www.postgresql.org/docs/14/functions-matching.html | PostgreSQL: Documentation: 14: 9.7. Pattern Matching
https://www.postgresql.org/docs/current/functions-conditional.html#FUNCTIONS-COALESCE-NVL-IFNULL | PostgreSQL: Documentation: 14: 9.18. Conditional Expressions
https://fr.wikipedia.org/wiki/Peinture_(mati%C3%A8re)#Solvants_et_diluants | Peinture (matière) — Wikipédia

https://www.postgresql.org/docs/9.1/functions-string.html | PostgreSQL: Documentation: 9.1: String Functions and Operators
https://stackoverflow.com/questions/985295/can-you-define-literal-tables-in-sql | mysql - Can you define "literal" tables in SQL? - Stack Overflow
https://stackoverflow.com/questions/23320945/postgresql-select-if-string-contains | sql - Postgresql SELECT if string contains - Stack Overflow
https://www.postgresql.org/docs/8.3/tutorial-join.html | PostgreSQL: Documentation: 8.3: Joins Between Tables
https://www.postgresql.org/docs/8.3/sql-createtable.html | PostgreSQL: Documentation: 8.3: CREATE TABLE
https://www.postgresql.org/docs/8.3/sql-createview.html | PostgreSQL: Documentation: 8.3: CREATE VIEW
https://blog.sigplan.org/2022/01/13/provably-space-efficient-parallel-functional-programming/ | Provably Space-Efficient Parallel Functional Programming | SIGPLAN Blog
https://stackoverflow.com/questions/9253244/sql-having-vs-where/9253267 | SQL - HAVING vs. WHERE - Stack Overflow
https://www.postgresql.org/docs/14/queries-table-expressions.html#QUERIES-GROUPING-SETS | PostgreSQL: Documentation: 14: 7.2. Table Expressions
https://www.postgresql.org/docs/14/functions-subquery.html | PostgreSQL: Documentation: 14: 9.23. Subquery Expressions

https://www.quora.com/How-do-I-drill-level-holes-in-a-wall | How to drill level holes in a wall - Quora

https://man7.org/linux/man-pages/man1/perf-top.1.html | perf-top(1) - Linux manual page
https://stackoverflow.com/questions/30028017/group-by-date-and-reduce-an-array-to-create-a-new-one | ruby - Group by date and reduce an array to create a new one - Stack Overflow
https://stackoverflow.com/questions/4351390/how-do-i-check-an-array-for-duplicates | ruby - How do I check an array for duplicates? - Stack Overflow
https://github.com/ffi/ffi | ffi/ffi: Ruby FFI
https://goteleport.com/blog/security-hardening-ssh-bastion-best-practices/ | SSH Bastion host best practices: How to Build and Deploy a Security-Hardened SSH Bastion Host | Teleport
https://blog.frankmtaylor.com/2021/10/21/a-small-guide-for-naming-stuff-in-front-end-code/#the-foundations-for-a-guide | A Small Guide for Naming Stuff in Front-end Code – Frank M Taylor
https://diy.stackexchange.com/questions/216226/tool-to-help-precision-drill-4-holes-in-a-wall | Tool to help precision drill 4 holes in a wall? - Home Improvement Stack Exchange
https://diy.stackexchange.com/?tab=month | Hottest Questions This Month - Home Improvement Stack Exchange
https://www.instructables.com/contest/SDnewyear/ | New Year, New Skill Student Design Challenge - Instructables
https://towardsdatascience.com/beyond-predictive-models-the-causal-story-behind-hotel-booking-cancellations-d29e8558cbaf | Beyond Predictive Models: The Causal Story Behind Hotel Booking Cancellations | by Siddharth Dixit | Towards Data Science
https://mitpress.mit.edu/books/elements-causal-inference#:~:text=Elements%20of%20Causal%20Inference%20is,data%20to%20understand%20the%20world. | Elements of Causal Inference | The MIT Press
https://datatracker.ietf.org/doc/html/rfc6749 | rfc6749
https://auth0.com/learn/how-auth0-uses-identity-industry-standards/ | How Auth0 Uses Identity Industry Standards - Auth0

https://cffi.readthedocs.io/en/latest/ | CFFI documentation — CFFI 1.15.0 documentation
https://tldp.org/HOWTO/Program-Library-HOWTO/shared-libraries.html | Shared Libraries
https://en.wikipedia.org/wiki/Scope_(computer_science)#Lexical_scope | Scope (computer science) - Wikipedia

https://www.tiktok.com/@calebrownn/video/7033587108321365295?is_copy_url=1&is_from_webapp=v1 | Cale Brown (@calebrownn) TikTok | Watch Cale Brown's Newest TikTok Videos

https://stackoverflow.blog/2021/12/31/700000-lines-of-code-20-years-and-one-developer-how-dwarf-fortress-is-built/ | 700,000 lines of code, 20 years, and one developer: How Dwarf Fortress is built - Stack Overflow Blog
https://if50.substack.com/p/the-end-of-a-journey | The End of a Journey - by Aaron A. Reed
https://github.com/dwmkerr/hacker-laws | dwmkerr/hacker-laws: 💻📖 Laws, Theories, Principles and Patterns that developers will find useful. #hackerlaws
https://scottaaronson.blog/?p=6183 | Shtetl-Optimized » Blog Archive » Book Review: “Viral” by Alina Chan and Matt Ridley
https://jvns.ca/blog/2017/06/28/notes-on-bpf---ebpf/ | Notes on BPF & eBPF
https://eduardosasso.co/blog/how-i-built-a-wfh-shed/ | How I built a WFH Shed
https://graphitemaster.github.io/aau/ | Almost Always Unsigned | Almost Always Unsigned
https://www.ietf.org/archive/id/draft-ietf-httpbis-safe-method-w-body-02.html | The HTTP QUERY Method
https://mxstbr.com/thoughts/margin/ | Margin considered harmful
https://www.tamriel-rebuilt.org/content/tamriel-rebuilt-roadmap | Tamriel Rebuilt Roadmap | Tamriel Rebuilt
https://davnicwil.com/tips-for-making-writing-more-fun/ | David Nicholas Williams
https://www.kooslooijesteijn.net/blog/web3 | A not so gentle intro to web3 | Koos Looijesteijn
http://www.paulgraham.com/venturecapital.html | A Unified Theory of VC Suckage
https://www.science.org/content/blog-post/things-i-won-t-work-dioxygen-difluoride | Things I Won't Work With: Dioxygen Difluoride | Science | AAAS
https://arxiv.org/abs/2112.14714 | [2112.14714] Automated Code Optimization with E-Graphs
https://drive.google.com/file/d/1ssS_zBQWDEXMENc2_aqqPGxGkEEU5Ea3/view | RadioAstronomyWithRaspberry-2022Jan01-rev1.pdf - Google Drive
https://www.bytesizedengineering.com/projects/openwheel | The Openwheel - A DIY Onewheel XR Self Balancing Skateboard — byte sized
https://www.raycast.com/blog/no-code-reviews-by-default/ | No code reviews by default
https://jeffhuang.com/best_paper_awards/# | Best Paper Awards in Computer Science
https://www.jetpens.com/blog/The-41-Best-Pens-for-2022-Gel-Ballpoint-Rollerball-and-Fountain-Pens/pt/974 | The 41 Best Pens for 2022: Gel, Ballpoint, Rollerball, and Fountain Pens | JetPens
https://goteleport.com/blog/5-ssh-best-practices/ | 5 Best Practices for Securing SSH | Teleport
https://til.simonwillison.net/python/init-subclass | __init_subclass__ | Simon Willison’s TILs
https://www.onebag.com/ | Leisure / Business Travel Packing List - Travel Light (One Bag)!
https://treefrog-editor.com/ | Treefrog Editor
https://constructionphysics.substack.com/p/how-to-design-a-house-to-last-for | How to design a house to last for 1000 years (part III)
https://github.com/kevinthew/linuxgems | kevinthew/linuxgems: A succinct cheat sheet for newbie linux coders and system administrators, documenting some of the more obscure and useful gems of linux lore. Intended to be viewed in emacs org-mode, or VimOrganizer, though any text editor will suffice.
https://news.ycombinator.com/item?id=29793770 | Ask HN: Resources for “Learning” Manufacturing? | Hacker News
https://sudhir.io/uuids-ulids | sudhir.io/uuids-ulids
https://nginx.org/en/docs/control.html#upgrade | Controlling nginx
https://contains.dev/blog/optimizing-docker-image-size | Optimizing Docker image size and why it matters - contains.dev
http://beej.us/guide/ | Beej's Guides
http://pi.math.cornell.edu/~web6140/TopTenAlgorithms/JPEG.html | JPEG: Image compression algorithm
https://copyconstruct.medium.com/know-how-your-org-works-or-how-to-become-a-more-effective-engineer-1a3287d1f58d | know how your org works (or how to become a more effective engineer) | by Cindy Sridharan | Jan, 2022 | Medium
https://www.antenna-theory.com/ | The Antenna Theory Website
https://www.onegreenplanet.org/lifestyle/10-edible-plants-that-grow-in-the-shade/ | 10 Edible Plants That Grow in the Shade - One Green Planet
https://blog.crunchydata.com/blog/five-tips-for-a-healthier-postgres-database-in-the-new-year | Five Tips For a Healthier Postgres Database in the New Year
https://github.com/BoltzmannEntropy/interviews.ai | BoltzmannEntropy/interviews.ai: It is my belief that you, the postgraduate students and job-seekers for whom the book is primarily meant will benefit from reading it; however, it is my hope that even the most experienced researchers will find it fascinating as well.
https://notes.nicfab.it/post/xmpp/xmpp/ | XMPP: the secure communication protocol that respects privacy | NicFab Notes
https://jvns.ca/blog/2022/01/11/how-to-find-a-domain-s-authoritative-nameserver/ | How to find a domain's authoritative nameservers
https://ably.com/resources/ebooks/websocket-handbook | The WebSocket Handbook | Ably Realtime
https://terrytao.wordpress.com/2010/10/21/245a-problem-solving-strategies/ | 245A: Problem solving strategies | What's new
https://github.com/synercys/annotated_latex_equations | synercys/annotated_latex_equations: Examples of how to create colorful, annotated equations in Latex using Tikz.
https://www.joshwcomeau.com/css/make-beautiful-gradients/ | Make Beautiful Gradients in CSS, with linear-gradient, radial-gradient, or conic-gradient.
https://staceyoniot.com/the-next-big-wi-fi-standard-is-for-sensing-not-communication/ | The next big Wi-Fi standard is for sensing, not communication - Stacey on IoT | Internet of Things news and analysis
https://samenright.com/2022/01/09/disambiguating-the-observable-universe/ | Disambiguating the ‘Observable Universe’ – Sam Enright
https://scrapeops.io/blog/the-state-of-web-scraping-2022/ | The State of Web Scraping 2022 | ScrapeOps
https://www.tomshardware.com/news/ultraram-implemented-in-silicon-for-first-time | UltraRAM Breakthrough Brings New Memory and Storage Tech to Silicon | Tom's Hardware
https://blog.honzamrazek.cz/2021/06/making-nice-looking-and-interactive-diagrams-for-your-pcbs/ | Making nice-looking and interactive diagrams for your PCBs – mind.dump()
https://goughlui.com/2016/12/19/great-aa-alkaline-battery-test-pt-1-battery-testing-fundamentals/ | Great AA Alkaline Battery Test – Pt 1: Battery Testing Fundamentals | Gough's Tech Zone

https://discourse.julialang.org/t/state-of-machine-learning-in-julia/74385 | State of machine learning in Julia - Specific Domains / Machine Learning - JuliaLang

https://learning.postman.com/docs/sending-requests/requests/ | Building requests | Postman Learning Center
https://learning.postman.com/docs/sending-requests/capturing-request-data/capturing-http-requests/ | Capturing HTTP requests | Postman Learning Center
https://spacy.io/usage/spacy-101 | spaCy 101: Everything you need to know · spaCy Usage Documentation
https://github.com/aztek/awesome-self-reference | aztek/awesome-self-reference: A curated list of examples of self-reference in art, science, and technology
https://betterexplained.com/articles/intuitive-convolution/ | Intuitive Guide to Convolution – BetterExplained
https://mypy.readthedocs.io/en/stable/cheat_sheet_py3.html | Type hints cheat sheet (Python 3) — Mypy 0.931 documentation
https://stackoverflow.com/questions/38987/how-do-i-merge-two-dictionaries-in-a-single-expression-take-union-of-dictionari | python - How do I merge two dictionaries in a single expression (take union of dictionaries)? - Stack Overflow
https://towardsdatascience.com/merge-dictionaries-in-python-d4e9ce137374 | 5 Ways to Merge Dictionaries in Python | by Jimit Dholakia | Towards Data Science
https://docs.python.org/3/library/itertools.html#itertools.groupby | itertools — Functions creating iterators for efficient looping — Python 3.10.1 documentation
https://stackoverflow.com/questions/58584413/black-formatter-ignore-specific-multi-line-code | python - Black formatter - Ignore specific multi-line code - Stack Overflow
https://stackoverflow.com/questions/4020131/rails-db-migration-how-to-drop-a-table | database - Rails DB Migration - How To Drop a Table? - Stack Overflow

https://www.healthline.com/health/fitness-exercise/jawline-exercises#takeaway | Jawline Exercises: 5 Moves for Definition

https://citizen-engagement.eu/participation_activities | Participation Activities - Citizen Engagement Activities
https://en.wikipedia.org/wiki/Green_threads | Green threads - Wikipedia
https://github.com/typesense/typesense#quick-start | typesense/typesense: Fast, typo tolerant, fuzzy search engine for building delightful search experiences ⚡ 🔍 ✨ An Open Source alternative to Algolia and an Easier-to-Use alternative to ElasticSearch.
https://typesense.org/typesense-vs-algolia-vs-elasticsearch-vs-meilisearch/ | Typesense vs Algolia vs Elasticsearch vs Meilisearch Comparison
https://typesense.org/docs/guide/? | Typesense Guide | Typesense
https://livebook.manning.com/book/microservices-patterns/chapter-7/104 | Chapter 7. Implementing queries in a microservice architecture - Microservices Patterns
https://ubuntu.com/blog/guide-to-ml-model-serving | A guide to ML model serving | Ubuntu
https://towardsdatascience.com/serve-your-machine-learning-models-with-a-simple-python-server-5a72d005e0ae | Serve Your Machine Learning Models With A Simple Python Server | by Or Levi | Towards Data Science
https://github.com/louismullie/graph-rank | louismullie/graph-rank: Ruby implementation of the PageRank and TextRank algorithms.
https://github.com/david-mccullars/text_rank | david-mccullars/text_rank: Ruby implementation of TextRank solution to ranked keyword extraction
https://cloudacademy.com/quiz/exam/2663747/results/ | Exam Session - Pre-Test: AWS Cloud Architect — Starter
https://cloudacademy.com/quiz/38024/ | Pre-Test: AWS Cloud Architect — Proficient
https://youfeellikeshit.com/index.html | you feel like shit

https://github.com/markets/awesome-ruby | markets/awesome-ruby: A collection of awesome Ruby libraries, tools, frameworks and software
https://www.scrapingbee.com/blog/best-ruby-http-clients/ | The Best Ruby HTTP clients for 2021
https://gitlab.com/gitlab-org/gitlab/-/issues/29196 | Consider removing HTTParty from our codebase (#29196) · Issues · GitLab.org / GitLab · GitLab
https://github.com/httprb/http | httprb/http: HTTP (The Gem! a.k.a. http.rb) - a fast Ruby HTTP client with a chainable API, streaming support, and timeouts
https://honeyryderchuck.gitlab.io/httpx/rdoc/ | HTTPX: An HTTP client library for ruby
https://news.ycombinator.com/item?id=23283551 | Ask HN: Best practices (and examples) for designing client libraries for APIs? | Hacker News
https://github.com/fintoc-com/fintoc-python/blob/master/fintoc/mixins/resource_mixin.py | fintoc-python/resource_mixin.py at master · fintoc-com/fintoc-python
https://www.rubydoc.info/stdlib/net/Net/HTTPResponse#error!-instance_method | Class: Net::HTTPResponse — Documentation for net (3.0.2)
https://conf.reactjs.org/stage | React Conf 2021
https://github.com/rails/rails/issues/43472 | Rails standardized error reporting interface · Issue #43472 · rails/rails
https://mdxjs.com/ | Markdown for the component era | MDX
https://stackoverflow.com/questions/36058453/git-recover-intermediate-commits-after-squash | recovery - Git - recover intermediate commits after squash - Stack Overflow
https://stackoverflow.com/questions/3893278/ruby-kind-of-vs-instance-of-vs-is-a | inheritance - Ruby: kind_of? vs. instance_of? vs. is_a? - Stack Overflow
https://fr.wikipedia.org/wiki/Thread_(informatique) | Thread (informatique) — Wikipédia
https://about.gitlab.com/blog/2020/07/08/migrating-to-puma-on-gitlab/ | How we migrated application servers from Unicorn to Puma | GitLab
https://gitlab.com/gitlab-org/gitlab-foss/-/issues/3592#note_2805965 | Multithreaded application server (#3592) · Issues · GitLab.org / GitLab FOSS · GitLab
https://about.gitlab.com/releases/2020/05/22/gitlab-13-0-released/#reduced-memory-consumption-of-gitlab-with-puma | GitLab 13.0 released with Gitaly Clusters, Epic Hierarchy on Roadmaps, and Auto Deploy to ECS | GitLab

https://til.simonwillison.net/python/init-subclass | __init_subclass__ | Simon Willison’s TILs
https://jeffhuang.com/best_paper_awards/# | Best Paper Awards in Computer Science
https://www.jetpens.com/blog/The-Best-Technical-Drawing-Pens/pt/436 | The Best Technical Drawing Pens | JetPens
https://dl.acm.org/doi/10.1145/1926385.1926423 | Automating string processing in spreadsheets using input-output examples | Proceedings of the 38th annual ACM SIGPLAN-SIGACT symposium on Principles of programming languages

https://stackoverflow.com/questions/19688718/append-string-if-not-already-present/42296618 | ruby - Append string if not already present - Stack Overflow
https://www.generacodice.com/en/articolo/490008/HTML-to-Plain-Text-with-Ruby | HTML to Plain Text with Ruby? - Genera Codice

https://www.andreagrandi.it/2018/10/16/using-ipdb-with-python-37-breakpoint/ | Andrea Grandi – Using ipdb with Python 3.7.x breakpoint
https://falcon.readthedocs.io/en/stable/user/tutorial-asgi.html#testing-our-application | Tutorial (ASGI) — Falcon 3.0.1 documentation
https://docs.python.org/dev/library/unittest.mock.html#quick-guide | unittest.mock — mock object library — Python 3.11.0a3 documentation
https://stackoverflow.com/questions/3829742/assert-that-a-method-was-called-in-a-python-unit-test | Assert that a method was called in a Python unit test - Stack Overflow
https://stackoverflow.com/questions/25057383/patch-decorator-is-not-compatible-with-pytest-fixture | python - @Patch decorator is not compatible with pytest fixture - Stack Overflow
https://pretagteam.com/question/pytest-and-why-avoid-init-file | Pytest and why avoid init file - Pretag

https://en.wikipedia.org/wiki/Repeated_measures_design#Repeated_measures_ANOVA | Repeated measures design - Wikipedia
https://ux.stackexchange.com/questions/60026/when-is-it-appropriate-to-add-the-html5-autofocus-attribute-to-a-page | accessibility - When is it appropriate to add the HTML5 "autofocus" attribute to a page? - User Experience Stack Exchange
https://www.geneanet.org/connexion/?from=view_limit_redirect&url=https://gw.geneanet.org/gtasset?lang%3Dfr%26pz%3Djean%2Bmarc%2Bchristian%26nz%3Dtasset%26p%3Djean%2Bgilbert%26n%3Ddessy | Me connecter à Geneanet - Geneanet
https://amir.rachum.com/blog/2017/07/28/python-entry-points/ | Python Entry Points Explained
https://www.pnas.org/content/99/12/7821 | Community structure in social and biological networks | PNAS

https://scholar.google.com/citations?hl=en&user=lbmBY4AAAAAJ&view_op=list_works&alert_preview_top_rm=2 | ‪Jure Demsar‬ - ‪Google Scholar‬
https://github.com/sherbold/autorank | sherbold/autorank
https://hausetutorials.netlify.app/posts/2020-05-17-reshape-python-pandas-dataframe-from-long-to-wide-with-pivottable/ | Data science: Reshape Python pandas dataframe from long to wide with pivot_table

https://stackoverflow.com/questions/16579085/how-can-i-verify-if-one-list-is-a-subset-of-another | python - How can I verify if one list is a subset of another? - Stack Overflow
https://www.one-tab.com/page/KdEjEnOBR8aQh64yi83wQA | OneTab shared tabs

https://ankane.org/scaling-the-monolith#boot-times-&-memory | Scaling the Monolith
https://dpmc.govt.nz/our-programmes/policy-project/policy-methods-toolbox/community-engagement | Community engagement | Department of the Prime Minister and Cabinet (DPMC)
https://github.com/rant-lang/rant | rant-lang/rant: Rant - High-level procedural templating language
https://fr.wikipedia.org/wiki/Sauce_Worcestershire | Sauce Worcestershire — Wikipédia
https://www.metacritic.com/pictures/best-albums-of-2021/22 | The 40 Best Albums of 2021: BONUS: Biggest disappointments - Metacritic
https://www.space.com/best-star-projectors | Best star projectors: Indoor views of the night sky | Space
https://www.amazon.fr/Sega-Toys-Plan%C3%A9tarium-personnel-Homestar/dp/B00AL250ES/ref=sr_1_4?__mk_fr_FR=%C3%85M%C3%85%C5%BD%C3%95%C3%91&crid=2J8R8BLKO3RDC&keywords=sega+toys+homestar+flux+home+planetarium+star+projector&qid=1640356611&sprefix=flux+star%2Caps%2C200&sr=8-4 | Sega Toys Planétarium Personnel Homestar Pro/Star Theatre, Noir : Amazon.fr: Bébé et Puériculture
https://drive.google.com/file/d/18H2GeUSgTbjY_ZDKSF0hjqiNI14F3nh1/view | Inclusive citizen participation.pptx - Google Drive

https://stackoverflow.com/questions/34620469/safely-assign-value-to-nested-hash-using-hashdig-or-lonely-operator | ruby - Safely assign value to nested hash using Hash#dig or Lonely operator(&.) - Stack Overflow
https://embed.plnkr.co/7QipwWDjaFRfWbPrhpRd/ | bootstrap list group items w/ remove button (manual mouseenter/mouseleave) - Plunker
https://stackoverflow.com/questions/53231667/bundler-you-must-use-bundler-2-or-greater-with-this-lockfile | ruby on rails - Bundler: You must use Bundler 2 or greater with this lockfile - Stack Overflow
https://fr.wikipedia.org/wiki/Sauce_au_poivre | Sauce au poivre — Wikipédia
https://www.marmiton.org/recettes/recette_sauce-au-poivre-toute-simple_28438.aspx | Sauce au poivre toute simple : recette de Sauce au poivre toute simple

https://www.startpage.com/do/dsearch?query=dovetails&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.metacritic.com/tv/agents-of-chaos/season-1 | Agents of Chaos - Season 1 Reviews - Metacritic

https://docs.google.com/document/d/1M3IibDPgn6KO7boGX9ysgzmBEnbnIZXYHc1NGXHWVW4/edit# | Zencity Analysis - Google Docs
https://envisio.com/blog/8-local-government-public-dashboard-examples/ | 8 Local Government Public Dashboard Examples - Envisio

https://gist.github.com/elierotenberg/1f43013fd971dde68326430a19e3c6c8 | Idiomatic Data Fetching using React Hooks
https://stackoverflow.com/questions/53070970/infinite-loop-in-useeffect | reactjs - Infinite loop in useEffect - Stack Overflow
https://overreacted.io/ | Overreacted — A blog by Dan Abramov
https://stackoverflow.com/questions/55808749/use-object-in-useeffect-2nd-param-without-having-to-stringify-it-to-json | javascript - use object in useEffect 2nd param without having to stringify it to JSON - Stack Overflow
https://stackoverflow.com/questions/56963708/how-to-use-import-in-node-repl | node.js - How to use "import" in node REPL - Stack Overflow
https://react-tutorial.app/app.html?id=329 | Uppercase a string | React Tutorial
https://stackoverflow.com/questions/17826380/what-is-difference-between-functional-and-imperative-programming-languages | oop - What is difference between functional and imperative programming languages? - Stack Overflow
https://cs.stackexchange.com/questions/18570/isnt-functional-programming-just-imperative-programming-in-disguise | Isn't Functional Programming just Imperative Programming in disguise? - Computer Science Stack Exchange

https://dmitripavlutin.com/react-useeffect-explanation/ | A Simple Explanation of React.useEffect()
https://dmitripavlutin.com/react-useeffect-infinite-loop/ | How to Solve the Infinite Loop of React.useEffect()
https://stackoverflow.com/questions/14848274/git-log-to-get-commits-only-for-a-specific-branch | Git log to get commits only for a specific branch - Stack Overflow

http://www.johncostella.com/magic/ | The magic kernel

https://www.bmj.com/content/375/bmj.n2635/rr-80 | Open letter from The BMJ to Mark Zuckerberg | The BMJ
https://www.youtube.com/watch?v=ri9uaeU3JNc | Coudre une écharpe - YouTube
https://www.youtube.com/watch?v=mUHZAKBEH18 | Echarpe Homme au crochet - L'écharpe Léo - - YouTube

https://www.climbinganchors.com.au/hangboard-training/ | Community Healthy Climbers Hangboard Training
https://uphillathlete.com/rock-climbing-training-arcing/ | Rock Climbing Training: ARCing — Uphill Athlete

https://www.abigailsee.com/2017/08/30/four-deep-learning-trends-from-acl-2017-part-1.html#word_emb | Four deep learning trends from ACL 2017 | Abigail See
https://2021.emnlp.org/ | EMNLP 2021
https://www.pinecone.io/learn/vector-database/ | What is a Vector Database? | Pinecone
https://milvus.io/docs/v2.0.0/hybridsearch.md | Conduct a Hybrid Search · Open Source Vector Database designed for AI applications
https://lucene.apache.org/ | Apache Lucene - Welcome to Apache Lucene

https://martinfowler.com/microservices/ | Microservices Guide
https://docs.mongodb.com/manual/text-search/ | Text Search — MongoDB Manual
https://fr.wikipedia.org/wiki/Apache_Avro | Apache Avro — Wikipédia

https://www.reddit.com/r/devops/comments/c58q2b/eli5_enterprise_service_bus_rabbitmq_azure/ | Problem loading page
https://www.quora.com/Is-RabbitMQ-an-ESB | Is RabbitMQ an ESB? - Quora
about:blank | core.ac.uk/download/pdf/55828317.pdf
http://www.aclweb.org/cgi-sys/suspendedpage.cgi | aclweb.org/anthology/I13-1062

https://htck.github.io/bayeux/#!/ | Historic Tale Construction Kit - Bayeux

https://www.edx.org/learn/urban-planning | Learn Urban Planning with Online Courses | edX
https://en.wikipedia.org/wiki/Haiku | Haiku - Wikipedia
https://traditionalkyoto.com/culture/kintsugi/ | Kintsugi – Art of Repair | Traditional Kyoto
https://kintsugiacademy.be/fr/kintsugi/ | Kintsugi – Kintsugi Academy
https://www.coursehero.com/lit/The-Dispossessed/characters/ | The Dispossessed Characters | Course Hero
https://www.edx.org/course/data-analysis-in-social-scienceassessing-your-know | Data Analysis in Social Science—Assessing Your Knowledge | edX

https://fr.wikipedia.org/wiki/Test_de_Wilcoxon-Mann-Whitney | Test de Wilcoxon-Mann-Whitney — Wikipédia
https://www.toptal.com/product-managers/agile/scrum-team-size | Too Big to Scale – A Guide to Optimal Scrum Team Size | Toptal
https://stackoverflow.com/questions/62056294/github-folders-have-a-white-arrow-on-them | git - GitHub folders have a white arrow on them - Stack Overflow

https://www.gfx.dev/python-for-feature-film | Python For Feature Film
https://www.metacritic.com/feature/film-awards-and-nominations-scorecard | 2021-22 Film Awards and Nominations - Metacritic
https://www.youtube.com/watch?v=T3CsGbtifZI | The Making of The Legend of Zelda: Breath of the Wild Video – Story and Characters - YouTube

https://blog.sequin.io/events-not-webhooks | Give me /events, not webhooks - Sequin

https://medium.com/podiihq/ruby-parameters-c178fdcd1f4e | Ruby Required, Default and Optional Parameters | by Getty Orawo | podiihq | Medium
https://rails.rubystyle.guide/ | The Rails Style Guide
https://www.beardresource.com/category/grooming/ | Category - Grooming - Beard Resource

https://git-scm.com/docs/git-interpret-trailers | Git - git-interpret-trailers Documentation
http://mitrev.net/ruby/2015/08/28/benchmarking-ruby/ | Benchmarking Ruby – Georgi Mitrev
https://samsaffron.com/archive/2018/02/16/reducing-string-duplication-in-ruby | Reducing String duplication in Ruby
https://edgeguides.rubyonrails.org/active_record_validations.html | Active Record Validations — Ruby on Rails Guides
https://www.tiktok.com/@ghosthoney? | tyler (@ghosthoney) Official TikTok | Watch tyler's Newest TikTok Videos
https://vsupalov.com/cleaning-up-after-docker/ | Cleaning Up After Docker · vsupalov.com

https://coggle.it/diagram/WcVFEYue8QAB6o4o/t/testprof | TestProf
https://crypt.codemancers.com/posts/2018-07-29-leveraging-pundit/ | Using Pundit for authorization in Rails - recipes and best practices
https://www.startpage.com/sp/search | Startpage Search results
https://www.startpage.com/do/dsearch?query=stoeffer&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.startpage.com/do/dsearch?query=chouke&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.startpage.com/do/dsearch?query=almost+equal+symbol&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.startpage.com/do/dsearch?query=concerns+rails&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche

https://haystack.deepset.ai/usage/languages | Haystack Docs
https://cofca.org/2021/ | 2021 - COFCA
https://stackoverflow.com/questions/4658269/ruby-send-vs-send | syntax - Ruby send vs __send__ - Stack Overflow
https://apidock.com/ruby/Kernel/__callee__#1341--callee-and-method-both-return-symbol-when-originally-defined-not-current | __callee__ (Kernel) - APIdock
https://docs.gitlab.com/ee/development/testing_guide/testing_migrations_guide.html | Testing Rails migrations at GitLab | GitLab

https://en.wikipedia.org/wiki/BBC%27s_100_Greatest_Films_of_the_21st_Century | BBC's 100 Greatest Films of the 21st Century - Wikipedia
https://www.bbc.com/culture/article/20161219-bbc-cultures-best-of-2016 | BBC Culture’s best of 2016 - BBC Culture

https://www.redhat.com/fr/topics/devops/what-is-blue-green-deployment | Qu'est-ce que le déploiement bleu/vert ?
https://tvl.fyi/blog/rewriting-nix | TVL's blog: Tvix: We are rewriting Nix
https://en.wikipedia.org/wiki/Nim_(programming_language) | Nim (programming language) - Wikipedia
https://fr.wikipedia.org/wiki/Probl%C3%A8me_du_sac_%C3%A0_dos | Problème du sac à dos — Wikipédia
https://en.wikipedia.org/wiki/Subset_sum_problem | Subset sum problem - Wikipedia
https://fr.wikipedia.org/wiki/Th%C3%A9orie_de_la_complexit%C3%A9_(informatique_th%C3%A9orique) | Théorie de la complexité (informatique théorique) — Wikipédia
https://news.ycombinator.com/item?id=18298389 | The History and Concept of Computability (1996) [pdf] | Hacker News
https://www.semanticscholar.org/paper/The-Foundations-of-Computability-Theory-Robic/bb2c764211682e0bd2c8c6f326065d854024794f | The Foundations of Computability Theory | Semantic Scholar

https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/doc/howto/local_network.md#create-loopback-interface | doc/howto/local_network.md · main · GitLab.org / GitLab Development Kit · GitLab
https://en.wikipedia.org/wiki/Private_network#Private_IPv4_addresses | Private network - Wikipedia
https://www.juniper.net/documentation/en_US/junos/topics/concept/interface-security-loopback-understanding.html | Understanding the Loopback Interface - TechLibrary - Juniper Networks
https://www.frontiersin.org/articles/10.3389/frma.2018.00019/full | Frontiers | The Termolator: Terminology Recognition Based on Chunking, Statistical and Search-Based Scores | Research Metrics and Analytics
https://www.topito.com/top-shopping-meilleurs-modeles-crochet-telecharger | Top 40+ meilleurs modèles au crochet à télécharger | Topito

https://softwareengineering.stackexchange.com/questions/433982/best-practice-for-populating-objects-in-python | Best Practice for Populating Objects in Python - Software Engineering Stack Exchange
https://stackoverflow.com/questions/53897228/how-to-efficiently-encode-sequence-of-word-to-sequence-of-integers | python 3.x - how to efficiently encode sequence of word to sequence of integers - Stack Overflow
https://stackoverflow.com/questions/57211183/make-spacy-rule-based-matching-operators-greedy | python - Make SpaCy rule-based matching operators greedy - Stack Overflow
https://medium.com/mlearning-ai/10-popular-keyword-extraction-algorithms-in-natural-language-processing-8975ada5750c | 10 Popular Keyword Extraction Algorithms in Natural Language Processing | by Prakhar Mishra | MLearning.ai | Medium

https://stackoverflow.com/questions/19016553/add-subdomain-to-localhost-url | http - Add subdomain to localhost URL - Stack Overflow
https://www.reddit.com/r/rails/comments/n0rkzh/using_threadnew/ | Using Thread.new : rails
https://www.reddit.com/r/rails/comments/ejlt9z/concurrent_ruby_vs_parallel_has_anyone_tried_both/ | Concurrent Ruby vs Parallel? Has anyone tried both? : rails
https://go.dev/blog/codelab-share | Share Memory By Communicating - go.dev
https://idratherbewriting.com/learnapidoc/ | Documenting APIs: A guide for technical writers and engineers | Documenting APIs

https://stackoverflow.com/questions/18418936/rabbitmq-and-relationship-between-channel-and-connection | java - RabbitMQ and relationship between channel and connection - Stack Overflow
https://wiki.openstack.org/wiki/OpsGuide-Maintenance-RabbitMQ | OpsGuide-Maintenance-RabbitMQ - OpenStack
https://developers.redhat.com/articles/2021/11/03/how-use-service-binding-rabbitmq | How to use service binding with RabbitMQ | Red Hat Developer
http://patshaughnessy.net/2014/11/11/discovering-the-computer-science-behind-postgres-indexes | Discovering the Computer Science Behind Postgres Indexes - Pat Shaughnessy

https://blog.saeloun.com/2020/04/08/heredoc-in-ruby-and-rails.html | Heredocs and how to use them in Ruby and Rails | Saeloun Blog
https://stoplight.io/studio/ | Stoplight Studio | OpenAPI Design, Planning & Modeling Tool
https://relishapp.com/rspec/rspec-rails/docs/matchers/activerecord-relation-match-array | ActiveRecord::Relation match array - Matchers - RSpec Rails - RSpec - Relish

https://datacarpentry.org/lessons/ | Lessons
https://github.com/ossu/data-science | ossu/data-science: Path to a free self-taught education in Data Science!
https://fr.wikipedia.org/wiki/Cycle_de_l%27Ekumen | Cycle de l'Ekumen — Wikipédia

https://stackoverflow.com/questions/21210811/in-rspec-what-is-the-difference-between-a-message-expectation-receive-and-a-t | ruby on rails - In RSpec, what is the difference between a message expectation (receive) and a test spy (have_received)? - Stack Overflow
https://www.ombulabs.com/blog/rspec/ruby/spy-vs-double-vs-instance-double.html | Spy vs Double vs Instance Double - The Lean Software Boutique
https://manabouttown.tv/blogs/words-and-images/fashion_editorial_sonny_hall | Man About Town Fashion Editorial Sonny Hall - MAN ABOUT TOWN

https://www.thoughtworks.com/radar/techniques/hotwire | Hotwire | Technology Radar | Thoughtworks

https://scholar.google.com/scholar?cites=16310947660404610933&as_sdt=2005&sciodt=0,5&hl=en | Wiseman: Methodological bias in public opinion surveys - Google Scholar
https://martinfowler.com/books/refactoring.html | Refactoring
https://zaiste.net/posts/rails-activerecord-updating-attributes-object-fields/ | Rails ActiveRecord: Updating attributes (object fields)

https://harpers.org/archive/1932/10/in-praise-of-idleness/ | In Praise of Idleness
https://en.wikipedia.org/wiki/Design_Patterns | Design Patterns - Wikipedia
https://aws.amazon.com/certification/certified-machine-learning-specialty/?ch=tile&tile=getstarted | AWS Certified Machine Learning - Specialty
https://medium.com/@shubhpaliwal98/when-to-use-squash-merge-and-rebase-43da3571dcbe | When to use Squash, Merge, and Rebase? | by Shubham Paliwal | Medium
https://en.wikipedia.org/wiki/Relational_algebra#Set_operators | Relational algebra - Wikipedia
https://stackoverflow.com/questions/45516540/query-for-exact-match-of-jsonb-column-in-rails-5-and-postgresql | Query for exact match of jsonb column in Rails 5 and PostgreSQL - Stack Overflow

https://mailcatcher.me/ | MailCatcher
https://www.the-angry-chef.com/blog/the-worlds-deadliest-thing | The World’s Deadliest Thing — Anthony Warner
https://2020.stateofjs.com/en-US/technologies/datalayer/ | State of JS 2020: Data Layer
https://svelte.dev/ | Svelte • Cybernetically enhanced web apps

https://blog.nona.digital/cleaning-up-commit-history-with-git-rebase/ | Cleaning up commit history with git rebase - Nona Blog
https://stackoverflow.com/questions/156767/whats-the-difference-between-an-argument-and-a-parameter | language agnostic - What's the difference between an argument and a parameter? - Stack Overflow
https://en.wikipedia.org/wiki/Environment_variable | Environment variable - Wikipedia
https://www.thefreedictionary.com/nonplused | Nonplused - definition of nonplused by The Free Dictionary
https://fr.wikipedia.org/wiki/Charlotte_Rampling | Charlotte Rampling — Wikipédia
https://martinfowler.com/articles/domain-oriented-observability.html | Domain-Oriented Observability
https://askubuntu.com/questions/354993/how-to-remove-lines-from-the-text-file-containing-specific-words-through-termina | How to remove lines from the text file containing specific words through terminal? - Ask Ubuntu

https://blog.getcensus.com/cascading-deletes-in-rails/ | Handling Slow Cascading Deletes In Rails
https://stackoverflow.com/questions/25521151/redirect-all-pages-except-1-to-a-single-temporary-unavailable-page-in-rails | Redirect all pages except 1 to a single Temporary Unavailable Page in Rails - Stack Overflow
https://blog.nona.digital/cleaning-up-commit-history-with-git-rebase/ | Cleaning up commit history with git rebase - Nona Blog

https://guides.rubyonrails.org/action_cable_overview.html | Action Cable Overview — Ruby on Rails Guides
https://english.stackexchange.com/questions/23986/in-detail-vs-in-details | word choice - "In detail" vs. "in details" - English Language & Usage Stack Exchange
https://fr.wikipedia.org/wiki/Apdex | Apdex — Wikipédia

https://nobaproject.com/ | Knowledge Evolved | Noba
https://byrnehobart.medium.com/writing-is-networking-for-introverts-5cac14ad4c77 | Writing is Networking for Introverts | by Byrne Hobart | Medium
https://bartoszmilewski.com/2021/02/16/functorio/ | Functorio |   Bartosz Milewski's Programming Cafe

https://stackoverflow.com/questions/16160199/how-to-quickly-undo-staged-and-unstaged-changes-in-git | How to quickly undo staged and unstaged changes in git? - Stack Overflow
https://jessicahagy.substack.com/p/the-many-gifts-of-awkward-silence | The many gifts of awkward SILENCE. - by Jessica Hagy - This Week's Top Ten
https://ashleyjanssen.com/the-introverts-guide-to-increasing-energy/ | The Introverts Guide to Increasing Energy

https://earthly.dev/blog/command-line-tools/ | 6 Command Line Tools for Productive Programmers - Earthly Blog
https://direnv.net/docs/hook.html | Setup | direnv
https://chris.beams.io/posts/git-commit/ | How to Write a Git Commit Message
https://docs.python.org/3/library/subprocess.html#subprocess.check_output | subprocess — Subprocess management — Python 3.10.0 documentation

https://api.rubyonrails.org/classes/ActiveSupport/CurrentAttributes.html | ActiveSupport::CurrentAttributes
https://luizkowalski.net/using-concurrent-ruby-in-a-rails-application/ | Using concurrent-ruby in a Rails application
https://www.speedshop.co/2020/05/11/the-ruby-gvl-and-scaling.html | The Practical Effects of the GVL on Scaling in Ruby
https://blog.saeloun.com/2020/11/18/rails-6.1-adds-support-for-destroying-dependent-associations-in-the-background.html | Rails 6.1 adds support for destroying dependent associations in the background | Saeloun Blog
https://stackoverflow.com/questions/6471451/map-a-rails-custom-sql-query-to-an-activerecord-model/6471497 | Map a Rails Custom SQL Query to an ActiveRecord Model - Stack Overflow
https://stackoverflow.com/questions/5437507/union-with-where-clause | sql - UNION with WHERE clause - Stack Overflow
https://stackoverflow.com/questions/2685151/is-it-possible-to-specify-a-sql-server-schema-name-in-a-rails-activerecord-migra | Is it possible to specify a SQL Server schema name in a Rails ActiveRecord migration? - Stack Overflow

https://overreacted.io/goodbye-clean-code/ | Goodbye, Clean Code — Overreacted
https://www.rubyguides.com/2018/01/ruby-string-methods/#How_to_Remove_the_Last_Character_From_a_String | Ruby String Methods (Ultimate Guide) - RubyGuides
https://stackoverflow.com/questions/53766844/can-i-force-the-execution-of-an-active-record-query-chain | ruby on rails - Can I force the execution of an active record query chain? - Stack Overflow
https://stackoverflow.com/questions/4968009/api-design-http-basic-authentication-vs-api-token | API Design: HTTP Basic Authentication vs API Token - Stack Overflow

https://fr.wikipedia.org/wiki/Trigger_warning_(psychologie) | Trigger warning (psychologie) — Wikipédia

https://stackoverflow.com/questions/16159021/rails-service-objects-vs-lib-classes | Rails service objects vs lib classes - Stack Overflow
https://livre.fnac.com/a169009/Bronislaw-Geremek-La-Potence-ou-la-pitie?oref=47502b0b-54bc-2105-5802-5a938b7a8b5f | La Potence ou la pitié L'Europe et les pauvres du Moyen Âge à nos jours - broché - Bronislaw Geremek, Joanna Arnold Moricet - Achat Livre | fnac
https://www.youtube.com/watch?v=wxtSKwODEXk | George Sand - Grand Ecrivain (1804-1876) - YouTube
https://www.seuil.com/ouvrage/george-sand-a-nohant-une-maison-d-artiste-michelle-perrot/9782020820769 | George Sand à Nohant. Une maison d'art... | Editions Seuil

https://stackoverflow.com/questions/34203825/how-to-use-x-www-form-urlencoded-in-rails | ruby - How to use x-www-form-urlencoded in rails - Stack Overflow

https://www.youtube.com/watch?v=PnIxfBZeel0 | Défi Une Semaine Végane I La Cuisine de Jean-Philippe - YouTube

https://stackoverflow.com/questions/24691483/passing-headers-and-query-params-in-httparty | ruby on rails - Passing headers and query params in HTTparty - Stack Overflow

https://stackoverflow.com/questions/109781/uniq-by-object-attribute-in-ruby | Uniq by object attribute in Ruby - Stack Overflow
https://stackoverflow.com/questions/50217304/copy-file-from-remote-docker-container | ssh - Copy file from remote docker container - Stack Overflow
https://stackoverflow.com/questions/11621037/suppressing-irb-output/11621080 | ruby on rails - suppressing IRB output? - Stack Overflow

https://docs.docker.com/compose/reference/down/ | docker-compose down | Docker Documentation
https://api.rubyonrails.org/classes/ActiveRecord/Locking/Pessimistic.html#method-i-with_lock | ActiveRecord::Locking::Pessimistic
https://www.cockroachlabs.com/docs/stable/select-for-update.html | SELECT FOR UPDATE | CockroachDB Docs
https://zepel.io/blog/scrum-master-vs-product-owner/ | Scrum Master vs Product Owner

https://docs.gitlab.com/ee/development/permissions.html#where-should-permissions-be-checked | GitLab permissions guide | GitLab
https://ciechanow.ski/curves-and-surfaces/ | Curves and Surfaces – Bartosz Ciechanowski
https://github.com/RestlessThinker/danger-pronto | RestlessThinker/danger-pronto: A Danger plugin to lint files through Pronto

https://nl.wikipedia.org/wiki/Bright_Brussels | Bright Brussels - Wikipedia
https://fr.wikipedia.org/wiki/Palais_des_Acad%C3%A9mies | Palais des Académies — Wikipédia
https://fr.wikipedia.org/wiki/%C3%89curies_royales_de_Bruxelles | Écuries royales de Bruxelles — Wikipédia

https://stackoverflow.com/questions/18848564/rails-how-to-sort-the-array-of-active-records-by-created-at/24272581 | sorting - Rails, how to sort the array of active records by created_at - Stack Overflow
https://stackoverflow.com/questions/62153907/const-defined-vs-defined-in-ruby | const_defined? vs. defined? in Ruby - Stack Overflow

https://www.zeste.ca/recettes/tarte-aux-tomates-confites-oignons-caramelises-feta-et-basilic | Tarte aux tomates confites, oignons caramélisés, feta et basilic | Zeste

https://noteflakes.com/articles/2021-10-20-explaining-ruby-fibers | Noteflakes: Explaining Ruby Fibers
https://brunosutic.com/blog/async-ruby | Async Ruby - Bruno Sutic
https://aeon.co/essays/how-the-anglophone-world-is-rediscovering-hegels-philosophy | How the Anglophone world is rediscovering Hegel’s philosophy | Aeon Essays
https://fundamentals-of-piano-practice.readthedocs.io/chapter1/ch1_procedures/index.html | II. Basic Procedures for Piano Practice — Fundamentals of Piano Practice
https://www.linternaute.fr/dictionnaire/fr/definition/vergogne/ | Vergogne : Définition simple et facile du dictionnaire

https://www.pluralsight.com/guides/playing-with-rspec-from-irb | How to Load and Test rspec from IRB | Pluralsight | Pluralsight
https://stackoverflow.com/questions/4861416/whats-the-difference-between-includes-and-joins-in-activerecord-query | ruby on rails - What's the difference between "includes" and "joins" in ActiveRecord query? - Stack Overflow

https://buttondown.email/hardwarethings/archive/hardware-things-im-betting-that-you-need-a-break/ | Hardware Things: I'm betting that you need a break • Buttondown
https://stackoverflow.com/questions/10911371/how-to-get-the-line-of-code-that-triggers-a-query | ruby on rails - How to get the line of code that triggers a query? - Stack Overflow
https://stackoverflow.com/questions/40036393/how-to-select-fields-from-joined-table-properly | ruby on rails - how to select fields from joined table properly? - Stack Overflow

https://medium.com/@marton.waszlavik/gdpr-for-engineers-what-is-personal-data-ce2b62d856c3 | GDPR for Engineers: What is Personal Data | by Márton Waszlavik | Medium
https://www.thefreedictionary.com/decommission | Decommission - definition of decommission by The Free Dictionary
https://gilesbowkett.com/blog/2021/08/15/fork-freshness-project-lifespans-in-the-ruby-ecosystem/ | gilesbowkett.com/blog/2021/08/15/fork-freshness-project-lifespans-in-the-ruby-ecosystem/
https://stackoverflow.com/questions/2325471/using-return-in-a-ruby-block | lambda - Using 'return' in a Ruby block - Stack Overflow
https://stackoverflow.com/questions/53641703/rails-scope-for-jsonb-hash-with-array-for-value | postgresql - Rails Scope for JSONB Hash with Array for Value - Stack Overflow
http://www.geometry.org/tex/conc/mathlearn.html | How to learn mathematics
https://goteleport.com/blog/ssh-tunneling-explained/ | What is SSH Tunneling, SSH Reverse Tunneling and SSH Port Forwarding? | Teleport

https://www.postgresql.org/docs/9.3/view-pg-locks.html | PostgreSQL: Documentation: 9.3: pg_locks

https://stackoverflow.com/questions/19225859/difference-between-core-and-processor | cpu - Difference between core and processor - Stack Overflow

https://www.loggly.com/ultimate-guide/using-journalctl/ | Using journalctl - The Ultimate Guide To Logging
https://docs.aws.amazon.com/autoscaling/ec2/userguide/as-manual-scaling.html | Manual scaling for Amazon EC2 Auto Scaling - Amazon EC2 Auto Scaling

https://thoughtbot.com/blog/activerecords-wherenot | ActiveRecord's where.not
https://stackoverflow.com/questions/2381718/rails-activerecord-date-between | Rails ActiveRecord date between - Stack Overflow
https://stackoverflow.com/questions/68717771/bulk-deleting-entries-from-a-model-efficiently | ruby on rails - Bulk deleting entries from a model efficiently - Stack Overflow

https://www.typescriptlang.org/docs/handbook/jsx.html | TypeScript: Documentation - JSX
https://github.com/pact-foundation/pact-ruby | pact-foundation/pact-ruby: Enables consumer driven contract testing, providing a mock service and DSL for the consumer project, and interaction playback and verification for the service provider project.
https://unix.stackexchange.com/questions/196009/location-of-the-crontab-file | cron - Location of the crontab file - Unix & Linux Stack Exchange
https://www.python.org/dev/peps/pep-0505/#built-in-maybe | PEP 505 -- None-aware operators | Python.org

https://stackoverflow.com/questions/22644648/rails-rspec-expect-method-inside-before-block | ruby - Rails, Rspec: "expect" method inside "before" block - Stack Overflow
https://www.testim.io/blog/end-to-end-testing-vs-integration-testing/ | End-to-End Testing vs Integration Testing - Testim Blog
https://axelhodler.medium.com/integration-tests-for-third-party-apis-dab67c52e352 | Integration Tests for Third Party APIs | by Axel Hodler | Medium

https://www.bruzz.be/fr/culture/eat-drink/chez-nous-brunch-ecolo-2021-10-15 | Chez Nous : brunch écolo | BRUZZ
https://stackoverflow.com/questions/4530440/rails-rspec-how-to-test-initialize-method | ruby - Rails / RSpec: How to test #initialize method? - Stack Overflow
https://www.exceptionalcreatures.com/guides/advanced-rescue-and-raise.html | Advanced Rescue & Raise - Exceptional Creatures

https://stackoverflow.com/questions/50598344/how-to-write-rspec-for-rails-jobs | How to write rspec for rails jobs - Stack Overflow
https://evertpot.com/jwt-is-a-bad-default/ | JWT should not be your default for sessions
https://www.honeybadger.io/blog/a-beginner-s-guide-to-exceptions-in-ruby/ | A Beginner's Guide to Exceptions in Ruby - Honeybadger Developer Blog
https://www.honeybadger.io/blog/ruby-custom-exceptions/ | Custom exceptions in Ruby - Honeybadger Developer Blog

https://stackoverflow.com/questions/17992054/is-my-server-overloaded | linux - Is my server overloaded? - Stack Overflow
https://askubuntu.com/questions/532845/what-is-system-load | What is system load? - Ask Ubuntu
https://stackoverflow.com/questions/28667474/ruby-rails-how-to-determine-if-module-is-included/28667632 | ruby/rails: How to determine if module is included? - Stack Overflow
https://guides.rubygems.org/make-your-own-gem/#adding-an-executable | Make your own gem - RubyGems Guides
https://gist.github.com/staltz/868e7e9bc2a7b8c1f754 | The introduction to Reactive Programming you've been missing
https://stackoverflow.com/questions/10729452/how-to-count-running-threads-in-ruby-server | multithreading - How to count running threads in ruby server - Stack Overflow
https://docs.python.org/3.11/whatsnew/changelog.html#changelog | Changelog — Python 3.11.0a0 documentation
https://blog.royalsloth.eu/posts/it-takes-a-phd-to-develop-that/ | It takes a PhD to develop that | RoyalSloth

https://www.simplethread.com/20-things-ive-learned-in-my-20-years-as-a-software-engineer/ | 20 Things I've Learned in my 20 Years as a Software Engineer - Simple Thread
https://rxjs.dev/guide/observable#pull-versus-push | RxJS - Observable

https://chrisbutner.github.io/ChessCoach/ | Overview | ChessCoach
https://mihaisplace.blog/2021/10/03/the-state-of-web-scraping-in-2021/ | The State Of Web Scraping in 2021 – Mihai's Blog

https://stackoverflow.com/questions/9491384/remove-order-from-activerecord-scope | ruby on rails - Remove order from ActiveRecord scope - Stack Overflow
https://stackoverflow.com/questions/43000732/how-does-rails-know-when-to-respond-with-304-status | http headers - How does Rails know when to respond with 304 status? - Stack Overflow
https://developer.mozilla.org/fr/docs/Web/HTTP/Headers/If-None-Match | If-None-Match - HTTP | MDN

https://stackoverflow.com/questions/41399788/use-rails-select-to-add-not-overwrite-selected-attributes | activerecord - Use Rails' select() to add (not overwrite) selected attributes? - Stack Overflow
https://stackoverflow.com/questions/31754820/rails-activerecord-where-exists-query | mysql - Rails ActiveRecord WHERE EXISTS query - Stack Overflow
https://stackoverflow.com/questions/21138207/activerecordstatementinvalid-pg-infailedsqltransaction | ruby on rails - ActiveRecord::StatementInvalid: PG InFailedSqlTransaction - Stack Overflow

https://thoughtbot.com/blog/using-arel-to-compose-sql-queries | Using Arel to Compose SQL Queries
https://www.rubyguides.com/2018/11/ruby-heredoc/ | How To Use Heredoc in Ruby - RubyGuides

https://tomdebruijn.com/posts/ruby-rspec-instance-variables/ | When not to use instance variables in RSpec | Tom de Bruijn
https://davidverhasselt.com/set-attributes-in-activerecord/ | Different Ways to Set Attributes in ActiveRecord (Rails 4)
https://stackoverflow.com/questions/8485002/difference-between-savefalse-and-savevalidate-false | ruby on rails - Difference between save(false) and save(:validate => false) - Stack Overflow
https://www.youtube.com/c/DriftingRuby/videos | Drifting Ruby - YouTube
https://pganalyze.com/blog/active-record-subqueries-rails | Advanced Active Record: Using Subqueries in Rails
https://pganalyze.com/blog/full-text-search-ruby-rails-postgres | Full Text Search in Milliseconds with Rails and PostgreSQL
https://stackoverflow.com/questions/27993979/activerecord-subquery-inner-join | ruby - ActiveRecord Subquery Inner Join - Stack Overflow

https://github.com/Casecommons/pg_search/issues/238#issuecomment-167964496 | `SELECT DISTINCT` triggers ActiveRecord::StatementInvalid in v1.0.0 · Issue #238 · Casecommons/pg_search
https://sql.sh/cours/jointures/inner-join | SQL INNER JOIN - SQL
https://stackoverflow.com/questions/5391564/how-to-use-distinct-and-order-by-in-same-select-statement | sql - How to use DISTINCT and ORDER BY in same SELECT statement? - Stack Overflow
https://stackoverflow.com/questions/25111426/rails-nested-joins-activerecord-with-conditions | sql - Rails Nested Joins Activerecord with conditions - Stack Overflow
https://stackoverflow.com/questions/39575398/rails-uniq-vs-distinct | activerecord - Rails: uniq vs. distinct - Stack Overflow
https://stackoverflow.com/questions/788984/getting-distinct-rows-from-a-left-outer-join/15065307 | sql - Getting distinct rows from a left outer join - Stack Overflow
https://stackoverflow.com/questions/11800680/rails-add-column-to-select-statement | mysql - Rails, add column to select statement - Stack Overflow
https://www.coraliecollignon.com/ruby/on/rails/2018/12/04/has_many_distinct.html | .distinct may not do what you think it does - Coralie Collignon’s blog
https://stackoverflow.com/questions/19181480/custom-select-on-join-table | sql - Custom select on join table - Stack Overflow

https://stackoverflow.com/questions/46616323/rails-remove-duplicates-after-ordering-a-join-table | Rails: remove duplicates after ordering a join table - Stack Overflow

https://www.keka.com/research-and-development-okr-examples | Research And Development OKR Examples | Keka
https://www.savio.io/blog/feature-request-software-tools-for-saas/ | 12 powerful feature request software tools for SaaS
https://docs.oracle.com/cd/E37670_01/E75728/html/ch06s01s06.html | 6.1.6 Recovering quorum in a swarm

https://news.ycombinator.com/item?id=21927473 | Hard deadlines are not user-first | Hacker News
https://www.brainyquote.com/quotes/dwight_d_eisenhower_164720 | Dwight D. Eisenhower - In preparing for battle I have...
https://www.oxfordreference.com/view/10.1093/acref/9780191826719.001.0001/q-oro-ed4-00004005 | Dwight D. Eisenhower - Oxford Reference
https://fr.wikipedia.org/wiki/Richard_Nixon | Richard Nixon — Wikipédia
https://en.wikipedia.org/wiki/Franklin_D._Roosevelt#Legacy | Franklin D. Roosevelt - Wikipedia
https://en.wikipedia.org/wiki/Fireside_chats | Fireside chats - Wikipedia
https://www.thriftbooks.com/w/the-essential-franklin-delano-roosevelt_franklin-d-roosevelt/1279183/#edition=3790652&idiq=3359821 | The Essential Franklin Delano Roosevelt book by Franklin D. Roosevelt

https://www.cnil.fr/en/sheet-ndeg1-identify-personal-data | Sheet n°1: Identify personal data | CNIL
https://stackoverflow.com/questions/18418936/rabbitmq-and-relationship-between-channel-and-connection | java - RabbitMQ and relationship between channel and connection - Stack Overflow
https://customer.cloudamqp.com/instance | Instances - CloudAMQP
https://stingray.rmq.cloudamqp.com/#/ | RabbitMQ Management
https://www.rabbitmq.com/networking.html | Networking and RabbitMQ — RabbitMQ

https://p403n1x87.github.io/spy-on-python-down-to-the-linux-kernel-level.html | The Hub of Heliopolis - Spy on Python down to the Linux kernel level
https://stackoverflow.com/questions/406294/left-join-vs-left-outer-join-in-sql-server | tsql - LEFT JOIN vs. LEFT OUTER JOIN in SQL Server - Stack Overflow

https://www.reddit.com/r/GripTraining/comments/pgkdzs/september_challenge_the_silver_bullet/ | September Challenge - The Silver Bullet : GripTraining
https://towardsdatascience.com/keyword-extraction-with-bert-724efca412ea | Keyword Extraction with BERT | Towards Data Science
https://stackoverflow.com/questions/3814738/how-can-i-get-sql-statement-created-by-activerecordfind-without-actually-execut | ruby on rails - How can I get SQL statement created by ActiveRecord#find without actually executing it? - Stack Overflow
https://theculturetrip.com/europe/the-netherlands/articles/why-is-the-hague-known-as-the-international-city-of-peace-and-justice/ | Why is The Hague Known as the International City of Peace and Justice?

https://relishapp.com/rspec/rspec-mocks/v/2-11/docs/stubbing-constants/stub-defined-constant | Stub Defined Constant - Stubbing constants - RSpec Mocks - RSpec - Relish
https://github.com/rails/spring/pull/623/files | Redirect STDERR to /dev/null during eager_preload by mvz · Pull Request #623 · rails/spring
https://www.nslookup.io/learning/dns-record-types/ | DNS Record Types — NsLookup.io
https://stackoverflow.com/questions/611906/http-post-with-url-query-parameters-good-idea-or-not | rest - HTTP POST with URL query parameters -- good idea or not? - Stack Overflow

https://mux.com/blog/you-either-die-an-mvp-or-live-long-enough-to-build-content-moderation/ | You either die an MVP or live long enough to build content moderation | Mux blog
https://fr.wikipedia.org/wiki/Domain_Name_System_Security_Extensions | Domain Name System Security Extensions — Wikipédia
https://docs.aws.amazon.com/Route53/latest/DeveloperGuide/dns-configuring-dnssec.html | Configuring DNSSEC signing in Amazon Route 53 - Amazon Route 53
https://docs.aws.amazon.com/Route53/latest/DeveloperGuide/route-53-concepts.html#route-53-concepts-reusable-delegation-set | Amazon Route 53 concepts - Amazon Route 53
https://serverfault.com/questions/530415/what-is-dns-delegation | networking - What is DNS Delegation? - Server Fault
https://en.wikipedia.org/wiki/List_of_DNS_record_types | List of DNS record types - Wikipedia

https://www.howtogeek.com/683023/why-does-unplugging-a-device-fix-so-many-problems/ | Why Does Unplugging a Device Fix So Many Problems?
https://fr.wikipedia.org/wiki/R%C3%A9volution_belge#La_Loi_fondamentale | Révolution belge — Wikipédia

https://stackoverflow.com/questions/6884408/how-to-show-sql-query-log-generated-by-a-rspec-test | ruby on rails - How to show SQL query log generated by a RSpec test? - Stack Overflow
https://github.com/peterc/whatlanguage | peterc/whatlanguage: A language detection library for Ruby that uses bloom filters for speed.

https://reactrouter.com/web/api/withRouter | React Router: Declarative Routing for React.js
https://stackoverflow.com/questions/2846283/what-are-the-rules-for-javascripts-automatic-semicolon-insertion-asi | What are the rules for JavaScript's automatic semicolon insertion (ASI)? - Stack Overflow
https://www.youtube.com/watch?v=x7cQ3mrcKaY | Pete Hunt: React: Rethinking best practices -- JSConf EU - YouTube
https://gist.github.com/gaearon/683e676101005de0add59e8bb345340c | Modern JavaScript in React Documentation
https://apidock.com/ruby/Object/eql%3F | eql? (Object) - APIdock
https://stackoverflow.com/questions/********/how-can-i-delete-one-element-from-an-array-by-value | ruby - How can I delete one element from an array by value - Stack Overflow
https://codereview.stackexchange.com/questions/25648/a-pattern-to-destructively-extract-items-from-an-array | ruby - A pattern to destructively extract items from an array - Code Review Stack Exchange
https://www.vlaamswoordenboek.be/definities/term/keikop | Het Vlaams woordenboek » keikop
https://stackoverflow.com/questions/52859751/most-efficient-way-to-find-order-of-magnitude-of-float-in-python | performance - Most efficient way to find order of magnitude of float in python - Stack Overflow
https://stackoverflow.com/questions/9540801/combine-two-activerecordrelation-objects/9540911 | ruby on rails - Combine two ActiveRecord::Relation objects - Stack Overflow

https://bundler.io/man/gemfile.5.html | Bundler: gemfile
https://stackoverflow.com/questions/4800721/ruby-what-does-require-false-in-gemfile-mean | bundler - Ruby: What does 'require: false' in Gemfile mean? - Stack Overflow
https://medium.com/@connorstack/understanding-ruby-load-require-gems-bundler-and-rails-autoloading-from-the-bottom-up-3b422902ca0 | Understanding ruby load, require, gems, bundler and rails autoloading from the bottom up | by cstack | Medium
https://www.loggly.com/blog/best-practices-for-logging-in-docker-swarm/ | Best Practices for Logging in Docker Swarm | Loggly
https://stackoverflow.com/questions/44110075/docker-stack-force-recreate-in-swarm-mode-using-compose-file | Docker stack "--force-recreate" in swarm mode using compose file - Stack Overflow
https://stackoverflow.com/questions/46125658/ruby-insert-object-into-existing-sorted-array-of-objects/46126133 | Ruby insert object into existing sorted array of objects - Stack Overflow
https://stackoverflow.com/questions/9025277/how-do-i-extract-a-sub-hash-from-a-hash | ruby - How do I extract a sub-hash from a hash? - Stack Overflow

https://apidock.com/rails/v6.1.3.1/ActionController/Instrumentation/process_action | process_action (ActionController::Instrumentation) - APIdock
https://www.postgresqltutorial.com/postgresql-json/ | PostgreSQL JSON Tutorial
https://docs.docker.com/engine/reference/commandline/container_kill/ | docker container kill | Docker Documentation
https://blog.bearer.sh/module-overrides-ruby-prepend-alias/ | Solving alias_method and prepend Conflicts in Our Ruby Agent
https://stackoverflow.com/questions/5177634/list-of-installed-gems | ruby - List of installed gems? - Stack Overflow
https://unix.stackexchange.com/questions/46715/piping-from-grep-to-awk-not-working | bash - Piping from grep to awk not working - Unix & Linux Stack Exchange
https://askubuntu.com/questions/7404/can-i-run-a-command-within-another-command | bash - Can I run a command WITHIN another command? - Ask Ubuntu

https://github.com/collectiveidea/interactor | collectiveidea/interactor: Interactor provides a common interface for performing complex user interactions.
https://github.com/brandon-rhodes/python-patterns | brandon-rhodes/python-patterns: Source code behind the python-patterns.guide site by Brandon Rhodes
https://github.com/faif/python-patterns/blob/master/patterns/behavioral/command.py | python-patterns/command.py at master · faif/python-patterns
https://stackoverflow.com/questions/36648382/interactors-vs-command | asp.net web api - Interactors VS Command - Stack Overflow
https://www.youtube.com/watch?v=WpkDN78P884 | Ruby Midwest 2011 - Keynote: Architecture the Lost Years by Robert Martin - YouTube
https://stackoverflow.com/questions/58668681/docker-view-the-logs-of-failed-crashed-container | Docker view the logs of failed / crashed container - Stack Overflow
https://unix.stackexchange.com/questions/26654/how-can-i-paste-overwriting-with-vim/26655 | vi - How can I paste (overwriting) with vim? - Unix & Linux Stack Exchange

about:blank | ics.uci.edu/~sabdujyo/papers/sigcomm21-cme.pdf
https://berthub.eu/articles/posts/useful-spy-books/ | Useful Spy Books - Bert Hubert's writings
https://www.justinweiss.com/articles/4-simple-memoization-patterns-in-ruby-and-one-gem/ | 4 simple memoization patterns in Ruby (and one gem) - Justin Weiss
https://english.stackexchange.com/questions/78403/acceptable-uses-for-associated-with-or-associated-to | prepositions - Acceptable uses for "associated with" or "associated to" - English Language & Usage Stack Exchange
https://relishapp.com/rspec/rspec-rails/docs/transactions | Transactions - RSpec Rails - RSpec - Relish
https://editorsmanual.com/ | The Editor's Manual | Grammar, usage, punctuation, and style resource for editors, writers, and learners of the English language.

https://javascript.info/ | The Modern JavaScript Tutorial
https://stackoverflow.com/questions/4151128/what-are-the-differences-between-numpy-arrays-and-matrices-which-one-should-i-u | python - What are the differences between numpy arrays and matrices? Which one should I use? - Stack Overflow
https://stackoverflow.com/questions/5076944/what-is-the-difference-between-null-and-undefined-in-javascript | What is the difference between null and undefined in JavaScript? - Stack Overflow
https://www.justinweiss.com/articles/fun-with-keyword-arguments/ | Fun with keyword arguments, hashes, and splats - Justin Weiss
https://www.thunderboltlabs.com/blog/2013/03/27/testing-pundit-policies-with-rspec/ | Thunder blog - Testing Pundit Policies with RSpec
https://briansunter.com/blog/five-minute-journal/ | Five Minute Journal - Daily Journal Techniques and Tips
https://stackoverflow.com/questions/39035813/rails-how-to-query-by-multiple-idsarray | mysql - Rails - How to query by multiple ids(array) - Stack Overflow
https://www.atlasobscura.com/articles/history-of-milk | Remembering When Only Barbarians Drank Milk - Gastro Obscura
https://repository.prace-ri.eu/git/help/development/testing_guide/best_practices.md | Best practices · Testing guide · Development · Help · GitLab
https://stackoverflow.com/questions/21263678/error-in-an-after-hook-pginfailedsqltransaction-from-rspec/24028963 | ruby on rails - Error in an after hook, PG::InFailedSqlTransaction from Rspec - Stack Overflow
https://stackoverflow.com/questions/48450584/why-raise-activerecordrollback-doesnt-remove-record-created-in-another-thread | ruby on rails - why raise ActiveRecord::Rollback doesn't remove record created in another thread - Stack Overflow
https://stackoverflow.com/questions/20102583/how-to-tell-if-already-within-a-database-transaction-in-ruby-on-rails | mysql - How to tell if already within a database transaction in ruby on rails? - Stack Overflow

https://stackoverflow.com/questions/8577223/ruby-get-the-file-being-executed/8577342 | Ruby - get the file being executed - Stack Overflow
https://blog.capsens.eu/rails-6-boot-sequence-d289b44d2e94 | Rails 6 boot sequence. Have you ever wondered how your Rails… | by Younes SERRAJ | Blog de Capsens
https://en.wikipedia.org/wiki/Spline_(mathematics) | Spline (mathematics) - Wikipedia
https://gravityandlevity.wordpress.com/ | Gravity and Levity | A blog about the big ideas in physics, plus a few other things

https://www.honeybadger.io/blog/ruby-rbs-type-annotation/ | Understanding RBS, Ruby's new Type Annotation System - Honeybadger Developer Blog
https://developer.squareup.com/blog/the-state-of-ruby-3-typing/ | The State of Ruby 3 Typing | Square Corner Blog
https://evilmartians.com/chronicles/climbing-steep-hills-or-adopting-ruby-types | Climbing Steep hills, or adopting Ruby 3 types with RBS — Martian Chronicles, Evil Martians’ team blog

https://www.masterincybersecurity.eu/ | Master in CyberSecurity
https://martinfowler.com/articles/consumerDrivenContracts.html | Consumer-Driven Contracts: A Service Evolution Pattern
https://stackoverflow.com/questions/3679200/rails-routes-limiting-the-available-formats-for-a-resource/******** | routing - Rails Routes - Limiting the available formats for a resource - Stack Overflow
http://underpop.online.fr/r/ruby/rails/tutorial/ruby-on-rails-6-3.html | Ruby Supporting Alternative Data Formats by MIME Type - Rails
https://stackoverflow.com/questions/1026069/how-do-i-make-the-first-letter-of-a-string-uppercase-in-javascript | How do I make the first letter of a string uppercase in JavaScript? - Stack Overflow
https://stackoverflow.com/questions/6326440/best-way-to-find-a-single-record-using-activerecord-3-arel | ruby on rails - Best way to find a single record using ActiveRecord 3 / Arel? - Stack Overflow
https://stackoverflow.com/questions/********/how-to-insert-an-array-in-the-middle-of-an-array/******** | ruby - How to insert an array in the middle of an array? - Stack Overflow

https://blog.noredink.com/post/658510851000713216/haskell-for-the-elm-enthusiast | NoRedInk – Haskell for the Elm Enthusiast
https://medium.com/zencity-engineering | Zencity Engineering – Medium
https://www.futurelearn.com/info/courses/energy-transition/0/steps/10227 | The waterbed effect
https://blog.acolyer.org/ | the morning paper | a random walk through Computer Science research, by Adrian Colyer

https://www.rabbitmq.com/tutorials/tutorial-five-python.html | RabbitMQ tutorial - Topics — RabbitMQ
https://www.rabbitmq.com/access-control.html#loopback-users | Authentication, Authorisation, Access Control — RabbitMQ

https://styled-components.com/docs | styled-components: Documentation
https://english.stackexchange.com/questions/6735/do-things-use-apostrophe-for-indicating-possessive | grammaticality - Do things use apostrophe for indicating possessive? - English Language & Usage Stack Exchange
https://www.rabbitmq.com/documentation.html | Documentation: Table of Contents — RabbitMQ
https://www.rabbitmq.com/tutorials/amqp-concepts.html | AMQP 0-9-1 Model Explained — RabbitMQ
https://www.rabbitmq.com/connections.html | Connections — RabbitMQ
https://reflectoring.io/amqp-request-response/ | Request/Response Pattern with Spring AMQP - Reflectoring

https://nn.labml.ai/adaptive_computation/ponder_net/index.html | PonderNet: Learning to Ponder
https://geometricdeeplearning.com/lectures/ | GDL Course
https://arxiv.org/abs/2104.13478 | [2104.13478] Geometric Deep Learning: Grids, Groups, Graphs, Geodesics, and Gauges
https://stackoverflow.com/questions/56765825/activejob-whats-the-difference-between-myjob-new-perform-and-myjob-perform-no | ruby on rails - ActiveJob - what's the difference between MyJob.new.perform and MyJob.perform_now? - Stack Overflow

https://news.ycombinator.com/item?id=28093063 | Surveys show Americans want more walkable cities | Hacker News
https://rubyinrails.com/2014/01/15/ruby-count-vs-length-vs-size/ | Ruby Count vs Length vs Size
https://ruby-doc.org/core-2.5.0/Struct.html | Class: Struct (Ruby 2.5.0)
https://stackoverflow.com/questions/2642182/how-to-sort-an-array-in-descending-order-in-ruby | sorting - How to sort an array in descending order in Ruby - Stack Overflow

http://valve.github.io/blog/2013/10/26/constant-resolution-in-ruby/ | Constant resolution in Ruby - valve's
https://stackoverflow.com/questions/13017501/ruby-mapping-an-array-to-hashmap | Ruby - mapping an array to hashmap - Stack Overflow
http://www.cs.columbia.edu/~tr/s20/s20.html | COMS 4995: Incentives in Computer Science (Spring 2020)
https://thegradient.pub/machine-learning-wont-solve-the-natural-language-understanding-challenge/ | Machine Learning Won't Solve Natural Language Understanding
https://listed.to/@mo/4733/the-top-shelf-principle | The Top Shelf Principle | Mo Bitar
https://news.ycombinator.com/item?id=28115438 | How Watches Work | Hacker News
https://docs.lightly.ai/ | Documentation — lightly 1.0.0 documentation
https://www.brendangregg.com/blog/2019-08-19/bpftrace.html | A thorough introduction to bpftrace
https://stackoverflow.com/questions/27471254/ruby-check-if-all-array-elements-are-equal/27471291 | Ruby: check if all array elements are equal - Stack Overflow
https://stackoverflow.com/questions/4351390/how-do-i-check-an-array-for-duplicates | ruby - How do I check an array for duplicates? - Stack Overflow
https://apidock.com/ruby/v2_6_3/Enumerable/group_by | group_by (Enumerable) - APIdock
https://apidock.com/ruby/Enumerable/reduce | reduce (Enumerable) - APIdock
https://stackoverflow.com/questions/5128200/how-to-count-identical-string-elements-in-a-ruby-array | How to count identical string elements in a Ruby array - Stack Overflow

https://www.ipcc.ch/assessment-report/ar6/?__cf_chl_jschl_tk__=pmd_a8a57e502c543e63b71b550040e2d650c6c393ed-1628507631-0-gqNtZGzNAeKjcnBszQgi | Sixth Assessment Report — IPCC
https://softwareengineering.stackexchange.com/questions/316208/http-status-code-for-still-processing | rest - HTTP Status Code for "Still Processing" - Software Engineering Stack Exchange
https://stackoverflow.com/questions/1801516/how-do-you-add-an-array-to-another-array-in-ruby-and-not-end-up-with-a-multi-dim?answertab=votes#tab-top | How do you add an array to another array in Ruby and not end up with a multi-dimensional result? - Stack Overflow
https://stackoverflow.com/questions/5678108/how-can-i-get-the-intersection-union-and-subset-of-arrays-in-ruby | How can I get the intersection, union, and subset of arrays in Ruby? - Stack Overflow

https://stackoverflow.com/questions/14431582/generate-all-possibles-combinations-of-an-array-with-a-length-within-a-given-ran | ruby - Generate all possibles combinations of an array with a length within a given range - Stack Overflow
https://devdocs.io/rails~6.0/activesupport/testing/filefixtures#method-i-file_fixture | Ruby on Rails 6.0 / ActiveSupport::Testing::FileFixtures#file_fixture — DevDocs
https://eev.ee/blog/2016/09/15/music-theory-for-nerds/ | Music theory for nerds / fuzzy notepad
http://alquilarenelcentro.lol/ | Dime cuánto cobras y te diré dónde vivir

https://stackoverflow.com/questions/33536207/converting-array-of-key-value-pairs-to-hash-in-ruby | Converting array of key value pairs to hash in Ruby - Stack Overflow
https://fr.wikipedia.org/wiki/Accessible_Rich_Internet_Applications | Accessible Rich Internet Applications — Wikipédia
https://www.iap2.org/mpage/Home | International Association for Public Participation
https://stackoverflow.com/questions/18760101/how-to-load-the-fixtures-from-an-engine | ruby on rails - How to load the fixtures from an Engine? - Stack Overflow
https://sosoir.lesoir.be/que-faire-avec-les-kids-quand-il-pleut | Que faire avec les kids quand il pleut ?

https://rxjs.dev/guide/overview | RxJS - Introduction
https://developers.redhat.com/blog/2017/06/30/5-things-to-know-about-reactive-programming | 5 Things to Know About Reactive Programming | Red Hat Developer
https://dl.acm.org/doi/abs/10.1145/2501654.2501666 | A survey on reactive programming | ACM Computing Surveys

https://stackoverflow.com/questions/42602731/can-i-run-an-intermediate-layer-of-a-docker-image | dockerfile - Can I run an intermediate layer of a Docker image? - Stack Overflow
https://stackoverflow.com/questions/65614378/getting-docker-build-to-show-ids-of-intermediate-containers | Getting docker build to show IDs of intermediate containers - Stack Overflow
https://windsock.io/explaining-docker-image-ids/ | Explaining Docker Image IDs
https://ubuntu.com/tutorials/how-to-verify-ubuntu#1-overview | How to verify your Ubuntu download | Ubuntu

https://dev.to/hlmerscher/speeding-up-rails-boot-time-ie9 | Speeding up Rails boot time - DEV Community
https://stackoverflow.com/questions/10301794/difference-between-rake-dbmigrate-dbreset-and-dbschemaload | ruby on rails - Difference between rake db:migrate db:reset and db:schema:load - Stack Overflow
https://nautil.us/issue/104/harmony/the-incredible-fig | The Incredible Fig - Issue 104: Harmony - Nautilus
https://stackoverflow.com/questions/3647685/how-to-rollback-a-specific-migration | ruby on rails - How to rollback a specific migration? - Stack Overflow

https://stackoverflow.com/questions/13506690/how-to-determine-if-rails-is-running-from-cli-console-or-as-server | rack - How to determine if Rails is running from CLI, console or as server? - Stack Overflow
https://stackoverflow.com/questions/12706834/rails-where-clause-over-two-tables | ruby - Rails where clause over two tables - Stack Overflow
https://stackoverflow.com/questions/3055339/read-contents-of-a-local-file-into-a-variable-in-rails | ruby - Read contents of a local file into a variable in Rails - Stack Overflow
https://stackoverflow.com/questions/38301957/difference-between-as-json-and-to-json-method-in-ruby | Difference between as_json and to_json method in Ruby - Stack Overflow
https://www.bbc.com/future/article/20210727-how-to-boost-biodiversity-and-attract-wildlife-to-your-home | Why we should build for wildlife as well as people - BBC Future
https://www.nonviolentcommunication.com/ | Nonviolent Communication (NVC) - PuddleDancer Press

https://ez.substack.com/p/the-remote-culture-war-has-begun | The Remote Culture War Has Begun, And Executives Are Pumping Out "Back To Work" Propaganda - by Ed Z - Ed Zitron's Where's Your Ed At
https://earthly.dev/blog/command-line-tools/ | 6 Command Line Tools for Productive Programmers - Earthly Blog

https://www.honeybadger.io/blog/understanding-the-ruby-exception-hierarchy/ | Understanding the Ruby Exception Hierarchy - Honeybadger Developer Blog
https://rails.devcamp.com/trails/ruby-programming/campsites/error-handling-in-ruby/guides/ruby-error-handling-best-practices | Ruby Error Handling Best Practices
https://apihandyman.io/api-design-tips-and-tricks-getting-creating-updating-or-deleting-multiple-resources-in-one-api-call/ | API Handyman | API Design Tips And Tricks - Getting, creating, updating or deleting multiple resources in one API call
https://blog.appsignal.com/2018/07/03/custom-exceptions-in-ruby.html | Custom Exceptions in Ruby | AppSignal Blog
https://airbrake.io/blog/ruby-exception-handling/runtimeerror | Ruby Exception Handling: RuntimeError
https://ruby-doc.org/core-2.5.0/StandardError.html | Class: StandardError (Ruby 2.5.0)
https://ruby-doc.org/core-2.5.1/Exception.html | Class: Exception (Ruby 2.5.1)
https://analytics.google.com/analytics/academy/course/5 | Google Tag Manager Fundamentals
https://musicforprogramming.net/?three | musicForProgramming("03: Datassette");
https://datassette.tumblr.com/ | Datassette's Audio-Visual Aesthetics Toilet
https://stackoverflow.com/questions/3893278/ruby-kind-of-vs-instance-of-vs-is-a | inheritance - Ruby: kind_of? vs. instance_of? vs. is_a? - Stack Overflow
https://stackoverflow.com/questions/49230085/how-to-use-activemodeleachvalidator-in-rails-5 | How to use ActiveModel::EachValidator in rails 5 - Stack Overflow
https://www.rubyguides.com/2018/06/rubys-method-arguments/ | Ruby's Powerful Method Arguments & How To Use Them Correctly
https://www.jetbrains.com/ruby/whatsnew/ | What’s New in RubyMine 2021.2
https://marcqualie.com/2015/08/postgresql-distinct-jsonb-keys | PostgreSQL: Distinct JSONB Keys

https://news.ycombinator.com/item?id=4431289 | How To Give (Negative) Feedback Effectively | Hacker News
https://www.forbes.com/sites/laurelfarrer/2021/04/14/5-tips-to-optimize-the-success-of-your-hybrid-return-to-office-plan/ | 5 Tips To Optimize The Success Of Your Hybrid Return To Office Plan
https://blog.dropbox.com/topics/work-culture/-virtual-first-toolkit--how-to-support-your-team | Virtual First Toolkit: How to support your team | Dropbox Blog
https://garbiczfestival.com/garbicz-festival/ | Garbicz Festival Garbicz Festival

https://www.newyorker.com/magazine/2021/07/26/the-german-experiment-that-placed-foster-children-with-pedophiles | The German Experiment That Placed Foster Children with Pedophiles | The New Yorker
https://wheleph.gitlab.io/posts/2016-07-13-tagging-using-docker-registry-http-api-v2/ | Tagging images using Docker Registry HTTP API v2 | wheleph's blog

https://www.rubytapas.com/2016/11/20/ruby-thread-local-variables/ | Understanding Ruby Thread-Local Variables – RubyTapas
https://alien-project.org/documentation.html | Documentation
https://influencair.be/map-brussels/ | Map Brussels
https://www.megaverse.info/ | Megaverse (ICML2021)
https://stackoverflow.com/questions/7964282/how-to-convert-json-to-a-ruby-hash | How to convert JSON to a Ruby hash - Stack Overflow

https://martinfowler.com/articles/continuousIntegration.html | Continuous Integration
https://twitter.com/billmarczak/status/1416801514685796352 | Bill Marczak on Twitter: "@AmnestyTech (1) @AmnestyTech saw an iOS 14.6 device hacked with a zero-click iMessage exploit to install Pegasus. We at @citizenlab also saw 14.6 device hacked with a zero-click iMessage exploit to install Pegasus. All this indicates that NSO Group can break into the latest iPhones." / Twitter
https://bundler.io/guides/updating_gems.html | Bundler: How to update gems with Bundler
https://serverfault.com/questions/530415/what-is-dns-delegation | networking - What is DNS Delegation? - Server Fault
https://news.ycombinator.com/item?id=27872596 | Zig cc: A drop-in replacement for GCC/Clang (2020) | Hacker News
https://www.jeuxvideo.com/news/1438841/metroid-dread-nintendo-refait-l-historique-de-la-licence-et-apporte-des-precisions.htm | Metroid Dread : Nintendo refait l'historique de la licence et apporte des précisions - jeuxvideo.com

https://stackoverflow.com/questions/33379287/gunicorn-cant-find-app-when-name-changed-from-application | python - Gunicorn can't find app when name changed from "application" - Stack Overflow
https://news.ycombinator.com/item?id=26631467 | Icecream: Never use print() to debug again in Python | Hacker News
https://stackoverflow.com/questions/42068572/sending-get-request-parameters-in-body | api - Sending GET request parameters in body - Stack Overflow
https://falcon.readthedocs.io/en/stable/api/request_and_response_wsgi.html#id1 | WSGI Request & Response — Falcon 3.0.1 documentation

https://guides.rubyonrails.org/generators.html | Creating and Customizing Rails Generators & Templates — Ruby on Rails Guides
https://www.asyncapi.com/docs/specifications/v2.0.0 | 2.0.0 | AsyncAPI Initiative for event-driven APIs
https://medium.com/zencity-engineering/creating-an-abstraction-layer-over-multiple-data-sources-6cb88606733f | Creating an Abstraction Layer Over Multiple Data Sources | by Lev Eidelman Nagar | Zencity Engineering | Jun, 2021 | Medium

https://stackoverflow.com/questions/44014784/how-can-i-test-perform-now-job-in-rails | minitest - How can I test `perform_now` job in Rails - Stack Overflow
https://stackoverflow.com/questions/54272609/how-to-test-that-a-class-is-called-in-a-controller-method-with-rspec | ruby on rails - How to test that a class is called in a controller method with RSpec - Stack Overflow
https://www.startpage.com/do/dsearch?query=thinking+notes&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche

https://blog.rabbitmq.com/posts/2021/07/rabbitmq-streams-overview/ | RabbitMQ Streams Overview | RabbitMQ - Blog
https://english.stackexchange.com/questions/3098/expressing-an-opinion-to-me-or-for-me | word choice - Expressing an opinion: to me or for me? - English Language & Usage Stack Exchange
https://falcon.readthedocs.io/en/stable/deploy/nginx-uwsgi.html | Deploying Falcon on Linux with NGINX and uWSGI — Falcon 3.0.1 documentation
https://falcon.readthedocs.io/en/stable/api/app.html#falcon.App.add_route | The App Class — Falcon 3.0.1 documentation
https://danlebrero.com/2021/06/30/cto-dairy-lucky-lotto-chaos-engineering-for-teams/ | CTO day 7: Lucky Lotto, chaos engineering but for teams
https://danlebrero.com/2017/04/06/documenting-your-architecture-wireshark-plantuml-and-a-repl/ | Documenting your architecture: Wireshark, PlantUML and a REPL to glue them all.

https://www.rubyguides.com/2016/02/ruby-procs-and-lambdas/ | Ruby Blocks, Procs & Lambdas - The Ultimate Guide!
https://stackoverflow.com/questions/10001583/how-to-check-the-database-name-that-activerecord-uses | ruby on rails - how to check the database name that ActiveRecord uses - Stack Overflow

https://blog.appsignal.com/2018/09/04/ruby-magic-closures-in-ruby-blocks-procs-and-lambdas.html | Closures in Ruby: Blocks, Procs and Lambdas | AppSignal Blog
https://www.youtube.com/watch?v=Pji6_0pbHFw | Why I chose Logseq over Roam, Obsidian and Athens - YouTube
https://www.quora.com/Is-it-normal-that-being-with-people-is-so-energy-draining-for-me | Is it normal that being with people is so energy draining for me? - Quora

https://mdigi.tools/image-text/ | Text on Image Writer - mdigi.tools
https://www.saleshacker.com/getting-past-gatekeeper/ | 6 Proven Tips For Getting Past The Gatekeeper | Sales Hacker
https://martinheinz.dev/blog/52 | Martin Heinz | Functools - The Power of Higher-Order Functions in Python

https://www.thegeekstuff.com/2011/10/grep-or-and-not-operators/ | 7 Linux Grep OR, Grep AND, Grep NOT Operator Examples
http://rubybunny.info/articles/exchanges.html#topic_exchanges | Working with RabbitMQ exchanges and publishing messages from Ruby with Bunny

https://no-color.org/ | NO_COLOR: disabling ANSI color output in various Unix commands
https://stackoverflow.com/questions/4818466/rails-find-records-that-are-not-present-another-join-table | activerecord - Rails - Find records that are not present another join table - Stack Overflow
https://stackify.com/rails-logger-and-rails-logging-best-practices/ | Rails Logger and Rails Logging Best Practices – Stackify
https://www.fieggen.com/shoelace/ianknot.htm | Ian's Shoelace Site – Ian Knot (Ian's Fast Shoelace Knot)
https://test-prof.evilmartians.io/#/recipes/any_fixture?id=dsl | AnyFixture - TestProf: Ruby tests profiling and optimization toolbox
https://mitelman.engineering/blog/python-best-practice/automating-python-best-practices-for-a-new-project/ | Python Best Practices for a New Project in 2021 - Alex Mitelman

https://www.sparkfun.com/news | Blog - SparkFun Electronics
https://principles.dev/p/make-the-invisible-visible/ | Principle - Make the invisible, visible
https://networkengineering.stackexchange.com/questions/39774/public-vs-private-ip-addresses | Public vs Private IP addresses - Network Engineering Stack Exchange

https://apidock.com/rails/ActiveRecord/Base/exists%3F/class | exists? (ActiveRecord::Base) - APIdock
https://semaphoreci.com/blog/2017/03/14/faster-rails-how-to-check-if-a-record-exists.html | Faster Rails: How to Check if a Record Exists - Semaphore
https://stackoverflow.com/questions/41641113/how-to-get-multiple-values-of-a-record-with-map | ruby on rails - How to get multiple values of a record with map - Stack Overflow
https://relishapp.com/rspec/rspec-mocks/docs/configuring-responses/returning-a-value | Returning a value - Configuring responses - RSpec Mocks - RSpec - Relish

https://stackoverflow.com/questions/7749416/check-whether-a-variable-is-a-string-in-ruby | idioms - Check whether a variable is a string in Ruby - Stack Overflow
https://news.ycombinator.com/item?id=27585711 | Kats: One stop shop for time series analysis in Python | Hacker News
https://www.yubico.com/be/product/yubikey-5c-nano/ | USB-C YubiKey 5C Nano Two Factor Security Key | Yubico

https://www.learnersdictionary.com/qa/everybody-should-mind-his-her-their-own-business | Everybody should mind his/her/their(?) own business. | Ask The Editor | Learner's Dictionary
http://rubybunny.info/articles/exchanges.html#topic_exchanges | Working with RabbitMQ exchanges and publishing messages from Ruby with Bunny
https://dl.acm.org/doi/10.1145/3308558.3314123 | InfraNodus: Generating Insight Using Text Network Analysis | The World Wide Web Conference

https://lesoreillescurieuses.com/2020/11/06/oscar-anton-october-pack/ | Oscar Anton - October Pack - Les Oreilles Curieuses
https://fr.wikipedia.org/wiki/Juggernaut#Origine_de_la_l%C3%A9gende_du_juggernaut | Juggernaut — Wikipédia

https://www.milkshakefestival.com/news | Milkshake Festival

https://stackoverflow.com/questions/6964678/habtm-polymorphic-relationship | ruby on rails 3 - HABTM Polymorphic Relationship - Stack Overflow
https://stackoverflow.com/questions/951686/is-a-one-column-table-good-design | sql - Is a one column table good design? - Stack Overflow
https://www.informit.com/articles/article.aspx?p=2220311&seqNum=6 | 9.6 Polymorphic has_many Relationships | Advanced Active Record in Rails 4 | InformIT
https://stackoverflow.com/questions/1449459/how-do-i-make-a-column-unique-and-index-it-in-a-ruby-on-rails-migration | How do I make a column unique and index it in a Ruby on Rails migration? - Stack Overflow
https://vitalik.ca/general/2021/06/18/verkle.html | Verkle trees
https://api.rubyonrails.org/v5.1.7/classes/ActiveRecord/ConnectionAdapters/SchemaStatements.html#method-i-add_foreign_key | ActiveRecord::ConnectionAdapters::SchemaStatements

https://letsencrypt.org/fr/ | Let's Encrypt - Certificats SSL/TLS gratuits
https://www.ansible.com/integrations/infrastructure | Infrastructure Automation with Ansible
https://en.wikipedia.org/wiki/Continuous_integration#History | Continuous integration - Wikipedia
http://blog.dornea.nu/2021/06/13/note-taking-in-2021/ | Note taking in 2021 - blog.dornea.nu

https://dba.stackexchange.com/questions/193371/which-is-more-efficient-for-searches-on-json-data-in-postgres-gin-or-multiple-i | postgresql - Which is more efficient for searches on JSON data in Postgres: GIN or multiple indexed columns? - Database Administrators Stack Exchange
https://stackoverflow.com/questions/5443740/how-do-i-handle-too-long-index-names-in-a-ruby-on-rails-activerecord-migration | How do I handle too long index names in a Ruby on Rails ActiveRecord migration? - Stack Overflow
https://stackoverflow.com/questions/1449459/how-do-i-make-a-column-unique-and-index-it-in-a-ruby-on-rails-migration | How do I make a column unique and index it in a Ruby on Rails migration? - Stack Overflow
https://wasmer.io/posts/wasmer-2.0 | Wasmer 2.0, Its a big deal!
https://factorio.com/blog/post/fff-366 | Friday Facts #366 - The only way to go fast, is to go well! | Factorio
https://stackoverflow.com/questions/11486027/merge-results-from-two-has-many-associations-with-the-same-model | ruby on rails - Merge results from two has_many associations with the same model - Stack Overflow

https://stackoverflow.com/questions/23450108/how-to-start-bunny-thread-in-rails-and-thin | rabbitmq - How to start bunny thread in Rails and Thin - Stack Overflow
https://www.renehersecycles.com/12-myths-in-cycling-1-wider-tires-are-slower/ | Myths in Cycling (1): Wider Tires Are Slower – Rene Herse Cycles
https://docs.microsoft.com/en-us/azure/architecture/patterns/async-request-reply | Asynchronous Request-Reply Pattern - Azure Architecture Center | Microsoft Docs
https://www.enterpriseintegrationpatterns.com/patterns/messaging/CorrelationIdentifier.html | Enterprise Integration Patterns - Correlation Identifier
https://www.aclweb.org/anthology/W04-3252/ | TextRank: Bringing Order into Text - ACL Anthology
https://docs.celeryproject.org/en/stable/reference/celery.result.html#celery.result.AsyncResult | celery.result — Celery 5.1.0 documentation
http://www.pythondoc.com/celery-3.1.11/getting-started/introduction.html | Introduction to Celery — Celery 3.1.11 documentation
https://www.youtube.com/playlist?list=PLLHTzKZzVU9e6xUfG10TkTWApKSZCzuBI | NYU Deep Learning SP21 - YouTube
https://arxiv.org/abs/2005.14165 | [2005.14165] Language Models are Few-Shot Learners
https://drive.google.com/file/d/1Q7LtZyIS1f3TfeTGll3aDtWygh3GAfCb/view | 001-intro.pdf - Google Drive

https://www.pnas.org/content/99/12/7821 | Community structure in social and biological networks | PNAS
https://www.startpage.com/do/dsearch?query=YAKE%21+Keyword+Extraction+from+Single+Documents+using+Multiple+Local+Features.+&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche

https://github.com/chrxh/alien | chrxh/alien: ALiEn is a GPU-accelerated artificial life simulation program.
https://keathley.io/blog/good-and-bad-elixir.html | Good and Bad Elixir
https://atcold.github.io/NYU-DLSP21/en/week12/12-1/ | Low Resource Machine Translation (Part 1) · Deep Learning
https://github.com/syndbg/shell-in-a-nutshell/blob/master/GUIDE.md | shell-in-a-nutshell/GUIDE.md at master · syndbg/shell-in-a-nutshell
https://wiki.bash-hackers.org/bash4 | Bash 4 - a rough overview [Bash Hackers Wiki]

https://stackoverflow.com/questions/18060735/after-touch-callback-is-fired-multiple-times | ruby on rails - after_touch callback is fired multiple times - Stack Overflow
https://apidock.com/rails/v6.0.0/ActiveRecord/Persistence/ClassMethods/upsert_all | upsert_all (ActiveRecord::Persistence::ClassMethods) - APIdock
https://apidock.com/rails/v6.0.0/ActiveRecord/Persistence/ClassMethods/insert_all | insert_all (ActiveRecord::Persistence::ClassMethods) - APIdock
https://github.com/paper-trail-gem/paper_trail | paper-trail-gem/paper_trail: Track changes to your rails models
https://stackoverflow.com/questions/1543614/combination-of-two-arrays-in-ruby | Combination of two arrays in Ruby - Stack Overflow
https://victorrotariu.com/2021/06/better-than-microdosing-walks-in-nature/ | Better than microdosing: walks in nature - Victor Rotariu
https://www.sciencedirect.com/science/article/abs/pii/S0020019015000381?via%3Dihub | A note on the PageRank of undirected graphs - ScienceDirect
https://www.hillelwayne.com/post/using-formal-methods/ | Using Formal Methods at Work • Hillel Wayne
https://github.com/susam/lab/tree/master/web/sockets | lab/web/sockets at master · susam/lab
https://shielddigitaldesign.com/posts/2021/fast-antenna/ | The Quickest Antenna Design of the Year
https://www.economist.com/international/2021/06/04/a-backlash-against-gender-ideology-is-starting-in-universities | A backlash against gender ideology is starting in universities | The Economist
http://paulgraham.com/own.html | A Project of One's Own
https://nlpprogress.com/ | Tracking Progress in Natural Language Processing | NLP-progress
http://veredshwartz.blogspot.com/ | Probably Approximately a Scientific Blog
https://nlpoverview.com/#a-word-embeddings | Modern Deep Learning Techniques Applied to Natural Language Processing by Authors
https://docs.docker.com/engine/swarm/secrets/#examples | Manage sensitive data with Docker secrets | Docker Documentation
https://docs.docker.com/engine/swarm/configs/ | Store configuration data using Docker Configs | Docker Documentation

https://stackoverflow.com/questions/62595436/rails-routes-order-of-nested-scopes | Rails routes: Order of nested scopes - Stack Overflow
https://stackoverflow.com/questions/32928476/are-there-destroy-all-delete-all-in-rails | Are there destroy_all!/delete_all! in Rails? - Stack Overflow

https://datatracker.ietf.org/doc/html/rfc7231#section-4.3.5 | rfc7231
https://english.stackexchange.com/questions/141884/which-is-a-better-and-commonly-used-word-bulk-or-batch | expressions - Which is a better and commonly used word, Bulk or Batch? - English Language & Usage Stack Exchange
https://stackoverflow.com/questions/21863326/delete-multiple-records-using-rest | backbone.js - Delete multiple records using REST - Stack Overflow
https://www.joelonsoftware.com/2021/06/02/kinda-a-big-announcement/ | Kinda a big announcement – Joel on Software
https://brendangregg.com/blog/2021-06-04/an-unbelievable-demo.html | An Unbelievable Demo
https://test-prof.evilmartians.io/#/recipes/let_it_be | let_it_be Helper - TestProf: Ruby tests profiling and optimization toolbox
https://tomdebruijn.com/posts/ruby-rspec-instance-variables/ | When not to use instance variables in RSpec | Tom de Bruijn
https://stackoverflow.com/questions/7079383/can-you-join-another-table-that-isnt-an-association-in-arel | ruby on rails - Can you join another table that isn't an association in ARel? - Stack Overflow
https://engineering.ramp.com/elixir-at-ramp/ | Elixir at Ramp 🧪 | Ramp Engineering Blog
https://stackoverflow.com/questions/23097834/rails-user-joins-not-in-active-record | sql - Rails User.joins.not(...) in Active Record? - Stack Overflow
https://stackoverflow.com/questions/7923674/active-record-get-the-second-third-item-in-a-database-without-id | ruby on rails - Active Record - Get the second, third.. item in a database (without ID) - Stack Overflow
https://stackoverflow.com/questions/7161821/how-to-grep-a-continuous-stream | linux - How to 'grep' a continuous stream? - Stack Overflow

https://docs.rubocop.org/rubocop/configuration.html | Configuration :: RuboCop Docs
https://obsproject.com/ | Open Broadcaster Software | OBS
https://github.com/se2p/pynguin | se2p/pynguin: The PYthoN General UnIt Test geNerator is a test-generation tool for Python
https://www.spellingenzo.nl/taalgebruik-het-verschil-tussen-proficiat-en-gefeliciteerd/ | Taalgebruik: Het verschil tussen ‘Proficiat’ en ‘Gefeliciteerd’ – Spelling & Zo

https://apidock.com/rails/v4.0.2/ActiveRecord/Relation/find_or_initialize_by | find_or_initialize_by (ActiveRecord::Relation) - APIdock
https://makandracards.com/jan-bussieck/33361-active-record-find_or_create_by-with-additional-attributes | Active Record: find_or_create_by with additional attributes - 9elements's deck
https://stackoverflow.com/questions/50341737/how-do-i-find-update-or-create-an-object-in-rails-in-1-call/50342150 | ruby - How do I find & update or create an object in Rails in 1 call? - Stack Overflow
https://fr.forvo.com/word/suggestion/#en | Prononciation de suggestion : Comment prononcer suggestion en Anglais, Français, Interlingua, Danois, Suédois, Allemand
https://stackoverflow.com/questions/9608624/how-to-force-an-rspec-test-to-fail | ruby on rails - How to force an RSpec test to fail? - Stack Overflow
https://stackoverflow.com/questions/328525/rails-how-can-i-set-default-values-in-activerecord | Rails: How can I set default values in ActiveRecord? - Stack Overflow

https://stackoverflow.com/questions/7297338/how-to-add-records-to-has-many-through-association-in-rails | activerecord - how to add records to has_many :through association in rails - Stack Overflow
https://stackoverflow.com/questions/5130847/running-multiple-commands-in-one-line-in-shell | bash - Running multiple commands in one line in shell - Stack Overflow
https://api.rubyonrails.org/v6.1.3.1/classes/ActiveRecord/FinderMethods.html#method-i-find | ActiveRecord::FinderMethods
https://discuss.jsonapi.org/t/body-of-sucessful-relationship-update-response/1370 | Body of sucessful relationship update response - JSON API
https://stackoverflow.com/questions/19690687/check-if-multi-insert-transaction-is-successful-or-not | ruby on rails - Check if multi-insert transaction is successful or not - Stack Overflow
https://api.rubyonrails.org/v6.1.3.2/classes/ActiveRecord/RecordInvalid.html | ActiveRecord::RecordInvalid
https://github.com/rubocop/rspec-style-guide | rubocop/rspec-style-guide: Best practices for writing your specs!
https://www.betterspecs.org/#single | Better Specs. Testing Guidelines for Developers.
https://rajesh38.wordpress.com/2014/11/06/how-to-pluck-an-associated-models-attribute-in-rails/ | How to pluck an associated model’s attribute in Rails | Rajesh Paul

https://askubuntu.com/questions/25599/get-wget-output-to-a-variable | scripts - Get wget output to a variable - Ask Ubuntu
http://mywiki.wooledge.org/BashGuide | BashGuide - Greg's Wiki
https://wiki.bash-hackers.org/ | The Bash Hackers Wiki [Bash Hackers Wiki]

https://developer.mozilla.org/fr/docs/Web/HTTP/Status/502 | 502 Bad Gateway - HTTP | MDN
https://tldp.org/LDP/abs/html/io-redirection.html | I/O Redirection
https://stackoverflow.com/questions/637827/redirect-stderr-and-stdout-in-bash | shell - Redirect stderr and stdout in Bash - Stack Overflow
https://stackoverflow.com/questions/16497317/piping-both-stdout-and-stderr-in-bash | Piping both stdout and stderr in bash? - Stack Overflow
http://nginx.org/en/docs/http/ngx_http_log_module.html | Module ngx_http_log_module
https://developer.mozilla.org/fr/docs/Web/HTTP/Headers/Referer | Referer - HTTP | MDN

https://www.startpage.com/do/dsearch?query=how+to+be+witty&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.startpage.com/do/dsearch?query=equivalent+of+repr+ruby&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.startpage.com/do/dsearch?query=teletype&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.startpage.com/do/dsearch?query=shell+tty+console&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche

https://www.startpage.com/do/dsearch?query=gaslighting&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.startpage.com/do/dsearch?query=aspect+nlp&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche
https://www.startpage.com/do/dsearch?query=chain+bash+commands&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage Résultats de la recherche

https://unix.stackexchange.com/questions/128975/why-doesnt-grep-using-pipe-work-here | shell - Why doesn't grep using pipe work here? - Unix & Linux Stack Exchange
https://engage.cityoflancasterpa.com/en/projects/cso-control-plan | Project • CleanIt! Lancaster's Combined Sewer Overflow Co...

https://www.jetbrains.com/help/idea/openapi.html | OpenAPI | IntelliJ IDEA
https://apidock.com/rails/ActiveRecord/Validations/ClassMethods/validates_uniqueness_of#893-Does-not-work-with-polymorphic-relations | validates_uniqueness_of (ActiveRecord::Validations::ClassMethods) - APIdock
https://gitlab.com/gitlab-org/gitlab/-/blob/master/app/services/web_hook_service.rb | app/services/web_hook_service.rb · master · GitLab.org / GitLab · GitLab

https://stackoverflow.com/questions/7297338/how-to-add-records-to-has-many-through-association-in-rails | activerecord - how to add records to has_many :through association in rails - Stack Overflow
https://stackoverflow.com/questions/5130847/running-multiple-commands-in-one-line-in-shell | bash - Running multiple commands in one line in shell - Stack Overflow
https://datatracker.ietf.org/doc/html/rfc7231 | rfc7231
https://stackoverflow.com/questions/19690687/check-if-multi-insert-transaction-is-successful-or-not | ruby on rails - Check if multi-insert transaction is successful or not - Stack Overflow

https://www.kickstarter.com/projects/upperstory/spintronics-build-mechanical-circuits | Spintronics: Build mechanical circuits by Paul Boswell — Kickstarter
https://web.dev/learn/css/box-model/ | Box Model
https://code.visualstudio.com/docs/python/jupyter-support | Working with Jupyter Notebooks in Visual Studio Code
https://news.ycombinator.com/item?id=27273752 | EU bans Belarusian airlines from European skies | Hacker News
https://techcrunch.com/2018/12/09/jira-is-an-antipattern/?guccounter=1&guce_referrer=aHR0cHM6Ly9uZXdzLnljb21iaW5hdG9yLmNvbS8&guce_referrer_sig=AQAAAIUk25dCjadSW9NVLsIubT60bL_vvHG4jJF8C-qg97qkNpEcHXFZ5Aw_BzIf-TvVeqtv4ARvUThY7bjzJf_OUspO8hZK4KoH4boVTfXmO8phmSggDGjrb16IeLCiRD4eGsPVe6QmEbFfCiu63EIJc9RWb5S9U1Zk3tPA_U6JRB-A | JIRA is an antipattern | TechCrunch
https://www.thoughtworks.com/podcasts/20-years-agile | 20 years of agile | ThoughtWorks
https://hbr.org/2013/03/the-delicate-art-of-giving-fee | The Delicate Art of Giving Feedback

https://news.ycombinator.com/item?id=24383180 | Ask HN: Suggestions for books about API design? | Hacker News
https://apisyouwonthate.com/books/build-apis-you-wont-hate | Build APIs You Won't Hate | APIs You Won't Hate - A community that cares about API design and development.
https://www.ics.uci.edu/~fielding/pubs/dissertation/rest_arch_style.htm | Fielding Dissertation: CHAPTER 5: Representational State Transfer (REST)
https://restful-api-design.readthedocs.io/en/latest/intro.html | Introduction
https://datatracker.ietf.org/doc/html/rfc7807 | rfc7807
https://www.rfc-editor.org/rfc/inline-errata/rfc3986.html | rfc3986
https://stackoverflow.com/questions/19646989/is-using-a-verb-in-url-fundamentally-incompatible-with-rest | Is using a verb in URL fundamentally incompatible with REST? - Stack Overflow
https://discuss.jsonapi.org/t/create-update-relationships-with-attributes/1701 | Create/update relationships with attributes - JSON API
https://themarkup.org/news/2021/04/13/how-facebooks-ad-system-lets-companies-talk-out-of-both-sides-of-their-mouths | How Facebook’s Ad System Lets Companies Talk Out of Both Sides of Their Mouths – The Markup

https://pandas.pydata.org/docs/reference/api/pandas.get_dummies.html | pandas.get_dummies — pandas 1.2.4 documentation
https://news.ycombinator.com/item?id=27219759 | Ask HN: Favorite purchases of last two years? | Hacker News
https://www.750g.com/arancini-boulettes-de-riz-farcies-siciliennes-r91734.htm | Recette - Arancini, boulettes de riz farcies siciliennes en vidéo
https://jaketrent.com/post/jsonapi-errors-serializer-in-rails | A JSON API Errors Serializer in Rails | Jake Trent
https://github.com/rails-api/active_model_serializers/blob/v0.10.10/docs/general/getting_started.md | active_model_serializers/getting_started.md at v0.10.10 · rails-api/active_model_serializers

https://docs.mongodb.com/manual/core/journaling/ | Journaling — MongoDB Manual
https://docs.mongodb.com/manual/reference/parameters/#diagnostic-parameters | MongoDB Server Parameters — MongoDB Manual
https://english.stackexchange.com/questions/132453/how-does-the-phrase-is-something-the-matter-make-sense | grammar - How does the phrase "Is something the matter?" make sense? - English Language & Usage Stack Exchange
https://idioms.thefreedictionary.com/ship+of+the+desert | Ship of the desert - Idioms by The Free Dictionary
https://docs.google.com/document/d/1AtVXT7VZ_jelOT4y8Skv3YE_GGNjIV_879PKEX1FDHU/edit | Evaluation - Supervised Topic Classification VS Custom Zeroshot Classification - Google Docs
https://wandb.ai/site | Weights & Biases – Developer tools for ML
https://builtin.com/data-science/pytorch-vs-tensorflow | Pytorch vs. Tensorflow: Deep Learning Frameworks 2021 | Built In
https://gardening.cals.cornell.edu/ | Learn, Garden & Reflect with Cornell Garden-Based Learning

https://stackoverflow.com/questions/34424154/rails-validate-uniqueness-of-two-columns-together | Rails: validate uniqueness of two columns (together) - Stack Overflow
https://askubuntu.com/questions/334994/which-one-is-better-using-or-to-execute-multiple-commands-in-one-line | bash - Which one is better: using ; or && to execute multiple commands in one line? - Ask Ubuntu

https://stackoverflow.com/questions/********/how-can-i-delete-one-element-from-an-array-by-value | ruby - How can I delete one element from an array by value - Stack Overflow
http://**************:8505/ | zero_shot_learning_remote · Streamlit
https://www.youtube.com/results?search_query=daryl+davis | daryl davis - YouTube
https://apidock.com/rails/ActiveRecord/Persistence/touch | touch (ActiveRecord::Persistence) - APIdock
https://stackoverflow.com/questions/18568706/check-number-of-arguments-passed-to-a-bash-script | parameter passing - Check number of arguments passed to a Bash script - Stack Overflow

https://stackoverflow.com/questions/10846875/move-or-undo-last-git-commit-to-unstaged-area | Move (or "Undo") last git commit to unstaged area - Stack Overflow
https://stackoverflow.com/questions/47064090/rails-postgres-migration-why-am-i-receiving-the-error-pgundefinedfunction | postgresql - Rails + Postgres migration - why am I receiving the error "PG::UndefinedFunction: ERROR: function gen_random_uuid() does not exist"? - Stack Overflow
https://stackoverflow.com/questions/45069569/how-to-list-schemas-in-postgres-from-rails-console | postgresql - How to list schemas in postgres from rails console? - Stack Overflow
https://stackoverflow.com/questions/7614215/managing-conflict-in-schema-rb-created-by-git-operation | ruby on rails - Managing conflict in schema.rb created by Git operation - Stack Overflow
https://stackoverflow.com/questions/39148163/pundit-authorization-in-index | ruby on rails - Pundit authorization in index - Stack Overflow
https://git-scm.com/book/en/v2/Customizing-Git-Git-Hooks | Git - Git Hooks
https://stackoverflow.com/questions/63663562/use-of-pre-commit-with-pycharm | python - Use of pre-commit with PyCharm - Stack Overflow
https://stackoverflow.com/questions/20580041/write-a-spec-to-assert-that-a-certain-record-has-been-deleted | ruby on rails - Write a spec to assert that a certain record has been deleted - Stack Overflow
https://stackoverflow.com/questions/3943554/how-to-do-find-with-includes-in-rails-3 | How to do find() with includes() in Rails 3 - Stack Overflow
https://stackoverflow.com/questions/16050686/getting-activerecordunknownattributeerror-unknown-attribute-email-confirmat/27495256 | ruby on rails - Getting "ActiveRecord::UnknownAttributeError: unknown attribute: email_confirmation" Error with rspec - Stack Overflow
https://jeffkreeftmeijer.com/activerecord-destroyed/ | Checking if an ActiveRecord model instance was destroyed
about:blank | stackoverflow.com/questions/10239235/are-there-any-languages-that-compile-to-bash
https://spacy.io/ | spaCy · Industrial-strength Natural Language Processing in Python

https://python-poetry.org/docs/pyproject/#extras | The pyproject.toml file | Documentation | Poetry - Python dependency management and packaging made easy.
https://stackoverflow.com/questions/53835198/integrating-python-poetry-with-docker | Integrating Python Poetry with Docker - Stack Overflow
https://github.com/wemake-services/wemake-django-template/blob/master/%7B%7Bcookiecutter.project_name%7D%7D/docker/django/Dockerfile | wemake-django-template/Dockerfile at master · wemake-services/wemake-django-template
https://github.com/python-poetry/poetry/discussions/1879 | Document docker poetry best practices · Discussion #1879 · python-poetry/poetry
https://github.com/michael0liver/python-poetry-docker-example/blob/master/docker/Dockerfile | python-poetry-docker-example/Dockerfile at master · michael0liver/python-poetry-docker-example
https://stackoverflow.com/questions/45594707/what-is-pips-no-cache-dir-good-for | python - What is pip's `--no-cache-dir` good for? - Stack Overflow
https://medium.com/containers-101/docker-anti-patterns-ad2a1fcd5ce1 | Docker anti-patterns. Container usage is exploding. Even if… | by Codefresh | Container Hub | Medium
https://blog.pyston.org/2021/05/05/pyston-v2-2-faster-and-open-source/ | Pyston v2.2: faster and open source | The Pyston Blog
https://medium.com/plotly/nlp-visualisations-for-clear-immediate-insights-into-text-data-and-outputs-9ebfab168d5b | NLP visualizations for clear, immediate insights into text data and outputs | by JP Hwang | Plotly | Medium
https://github.com/vinta/awesome-python#data-visualization | vinta/awesome-python: A curated list of awesome Python frameworks, libraries, software and resources
https://github.com/python-poetry/poetry/issues/648#issuecomment-633302719 | How do I uninstall packages that have been removed from pyproject.toml? · Issue #648 · python-poetry/poetry
https://stackoverflow.com/questions/31936481/how-to-add-foreign-key-in-rails-migration-with-different-table-name | How to add foreign key in rails migration with different table name - Stack Overflow
https://stackoverflow.com/questions/13694654/specifying-column-name-in-a-references-migration/22384289 | ruby on rails - Specifying column name in a "references" migration - Stack Overflow
https://thoughtbot.com/blog/validation-database-constraint-or-both | Validation, Database Constraint, or Both?

https://tools.ietf.org/html/rfc7159#page-4 | RFC 7159 - The JavaScript Object Notation (JSON) Data Interchange Format
https://github.com/jtoy/cld | jtoy/cld: compact language detection in ruby
https://towardsdatascience.com/benchmarking-language-detection-for-nlp-8250ea8b67c | Benchmarking Language Detection for NLP | by Jenny Lee | Towards Data Science
https://github.com/Mimino666/langdetect | Mimino666/langdetect: Port of Google's language-detection library to Python.
https://www.arte.tv/fr/videos/080116-004-A/les-grands-mythes-l-iliade-4-10/ | Les grands mythes - L'Iliade (4/10) - Le sang de la déesse - Regarder le documentaire complet | ARTE
https://fr.wikipedia.org/wiki/Achille | Achille — Wikipédia
https://fr.wikipedia.org/wiki/N%C3%A9r%C3%A9e | Nérée — Wikipédia

https://bigbinary.com/blog/backtick-system-exec-in-ruby | Executing commands in ruby | BigBinary Blog
https://fr.wikipedia.org/wiki/La_Louve_(supermarch%C3%A9) | La Louve (supermarché) — Wikipédia
https://www.aclweb.org/anthology/2020.acl-demos.2/ | TextBrewer: An Open-Source Knowledge Distillation Toolkit for Natural Language Processing - ACL Anthology
https://www.researchgate.net/publication/290169681_Automatic_keyword_extraction_from_documents_using_conditional_random_fields | (PDF) Automatic keyword extraction from documents using conditional random fields
https://towardsdatascience.com/extracting-keyphrases-from-text-rake-and-gensim-in-python-eefd0fad582f | Extracting Keyphrases from Text: RAKE and Gensim in Python | by Nikita Saxena | Towards Data Science

https://github.com/varkor/quiver | GitHub - varkor/quiver: A modern commutative diagram editor for the web.
https://socialpronow.com/blog/how-to-be-witty/ | 25 Tips to be Witty (If You’re Not a Quick Thinker)

https://davidverhasselt.com/set-attributes-in-activerecord/ | Different Ways to Set Attributes in ActiveRecord (Rails 4)
https://www.matuzo.at/blog/html-boilerplate/ | My current HTML boilerplate - Manuel Matuzović
https://arxiv.org/abs/2005.14165 | [2005.14165] Language Models are Few-Shot Learners
https://www.slant.co/?filter=top | Top Question - Slant
https://docs.google.com/presentation/d/1vo4d6MiXe7boRtPpWEqiyq_ZcnooPvApZQWa1nelTQ4/edit#slide=id.gd3981e8778_0_34 | CitizenLab art collection - Google Slides

https://news.ycombinator.com/item?id=26934577 | Has UML died without anyone noticing? | Hacker News
https://api.rubyonrails.org/v6.1.3.1/classes/ActiveRecord/Callbacks/ClassMethods.html#method-i-after_update | ActiveRecord::Callbacks::ClassMethods
https://api.rubyonrails.org/classes/ActiveRecord/AttributeMethods/Dirty.html | ActiveRecord::AttributeMethods::Dirty
https://stackoverflow.com/questions/24314584/run-a-callback-only-if-an-attribute-has-changed-in-rails | Run a callback only if an attribute has changed in Rails - Stack Overflow
https://news.mit.edu/2020/study-compares-scooter-sharing-bike-sharing-1005 | Comparing the benefits of scooter-sharing vs. bike-sharing | MIT News | Massachusetts Institute of Technology
https://cheats.rs/ | Rust Language Cheat Sheet

https://stackoverflow.com/questions/14214033/how-rspec-array-should-include-another-array/14214641 | testing - How rspec array should include? another array - Stack Overflow
https://www.joelonsoftware.com/2000/04/06/things-you-should-never-do-part-i/ | Things You Should Never Do, Part I – Joel on Software
https://huggingface.co/zero-shot/ | app · Streamlit
https://towardsdatascience.com/zero-shot-text-classification-with-hugging-face-7f533ba83cd6 | Zero-Shot Text Classification with Hugging Face | by Andrej Baranovskij | Towards Data Science
https://en.wikipedia.org/wiki/Topic_model | Topic model - Wikipedia
http://docs.bigartm.org/en/stable/tutorials/python_tutorial.html | Python Tutorial — BigARTM 1.0 documentation
https://paperswithcode.com/task/topic-models | Topic Models | Papers With Code
https://radimrehurek.com/gensim/auto_examples/core/run_core_concepts.html#core-concepts-model | Core Concepts — gensim
https://www.newseye.eu/blog/news/multilingual-dynamic-topic-modelling/ | NewsEye: On Multilingual Dynamic Topic Modeling
https://www.extremetech.com/computing/322120-apples-m1-positioning-mocks-every-x86-cpu-amd-and-intel-have-ever-launched | Apple's M1 Positioning Mocks the Entire x86 Business Model - ExtremeTech
https://www.bricklink.com/v2/catalog/catalogitem.page?id=194123#T=S&O={%22iconly%22:0} | BrickLink - Set 10280-1 : Lego Flower Bouquet [Botanical Collection] - BrickLink Reference Catalog

https://github.com/lwe/simple_enum | lwe/simple_enum: Simple enum-like field support for ActiveModel (including validations and i18n)
https://medium.com/@diegocasmo/using-postgres-enum-type-in-rails-799db99117ff | Using Postgres Enum Type in Rails | by Diego Castillo | Medium
https://corpus-analysis.com/tag/topic%20models#list | Tools for Corpus Linguistics
https://docs.gitlab.com/ee/development/feature_flags/controls.html | Feature flag controls | GitLab
https://news.ycombinator.com/item?id=19682451 | Post-surgical deaths in Scotland drop by a third, attributed to a checklist | Hacker News

https://stackoverflow.com/questions/30969435/where-is-the-docker-daemon-log | logging - Where is the Docker daemon log? - Stack Overflow
https://stackoverflow.com/questions/24314584/run-a-callback-only-if-an-attribute-has-changed-in-rails | Run a callback only if an attribute has changed in Rails - Stack Overflow
https://docs.docker.com/engine/reference/commandline/swarm_leave/ | docker swarm leave | Docker Documentation

https://stackoverflow.com/questions/11461625/reverse-the-order-of-characters-in-a-string/11461628 | bash - reverse the order of characters in a string - Stack Overflow
https://stackoverflow.com/questions/17368067/length-of-string-in-bash | variables - Length of string in bash - Stack Overflow
https://github.com/koalaman/shellcheck/wiki/SC2086 | SC2086 · koalaman/shellcheck Wiki
https://unix.stackexchange.com/questions/232384/argument-string-to-integer-in-bash/232386 | Argument string to integer in bash - Unix & Linux Stack Exchange
https://tldp.org/LDP/Bash-Beginners-Guide/html/ | Bash Guide for Beginners
https://github.com/anordal/shellharden/blob/master/how_to_do_things_safely_in_bash.md | shellharden/how_to_do_things_safely_in_bash.md at master · anordal/shellharden
https://unix.stackexchange.com/questions/4126/what-is-the-exact-difference-between-a-terminal-a-shell-a-tty-and-a-con | What is the exact difference between a 'terminal', a 'shell', a 'tty' and a 'console'? - Unix & Linux Stack Exchange
http://www.linusakesson.net/programming/tty/ | The TTY demystified

https://github.com/keon/awesome-nlp#books | keon/awesome-nlp: A curated list of resources dedicated to Natural Language Processing (NLP)
https://github.com/vinta/awesome-python/blob/master/README.md | awesome-python/README.md at master · vinta/awesome-python
https://github.com/omarsar/nlp_overview | omarsar/nlp_overview: Overview of Modern Deep Learning Techniques Applied to Natural Language Processing
https://stackoverflow.blog/2020/04/06/a-practical-guide-to-writing-technical-specs/ | A practical guide to writing technical specs - Stack Overflow Blog
https://gist.github.com/lyoshenka/8251914 | Search Git commit history for a string and see the diffs

https://solr.apache.org/ | Welcome to Apache Solr - Apache Solr
https://tech.channable.com/posts/2021-04-09-nix-is-the-ultimate-devops-toolkit.html | Channable - Nix is the ultimate DevOps toolkit
https://stackoverflow.com/questions/48588739/rspec-how-to-pass-a-let-variable-as-a-parameter-to-shared-examples/48598876 | ruby on rails - RSpec: How to pass a "let" variable as a parameter to shared examples - Stack Overflow
https://computationalthinking.mit.edu/Spring21/cheatsheets/ | Introduction to Computational Thinking
https://idioms.thefreedictionary.com/up+in+the+air | Up in the air - Idioms by The Free Dictionary

https://thoughtbot.com/blog/use-factory-girls-build-stubbed-for-a-faster-test | Use Factory Girl's build_stubbed for a Faster Test Suite
https://www.rubypigeon.com/posts/rspec-expectations-cheat-sheet/ | RSpec Expectations Cheat Sheet
https://stackoverflow.com/questions/6065450/is-there-a-way-to-conditionally-add-to-an-array-in-one-line/6065858 | ruby on rails - Is there a way to conditionally add to an array in one line? - Stack Overflow
https://www.cybertec-postgresql.com/en/stale-statistics-cause-table-bloat/ | How a bad network configuration can cause table bloat - Cybertec

https://github.com/bats-core/bats-core | bats-core/bats-core: Bash Automated Testing System
https://research.exercism.io/ | Exercism
https://rspec.rubystyle.guide/ | The RSpec Style Guide
https://test-prof.evilmartians.io/#/profilers/ruby_prof | RubyProf Integration - TestProf: Ruby tests profiling and optimization toolbox
https://gitlab.com/gitlab-org/gitlab/-/blob/master/spec/factories_spec.rb | spec/factories_spec.rb · master · GitLab.org / GitLab · GitLab
https://thoughtbot.com/blog/lets-not | Let's Not
https://about.gitlab.com/handbook/engineering/quality/test-engineering/#test-heuristics | Test Engineering | GitLab
https://www.developsense.com/blog/2012/04/heuristics-for-understanding-heuristics/ | Heuristics for Understanding Heuristics « Developsense Blog
https://stackoverflow.com/questions/11996124/is-it-impossible-to-use-guard-with-rubymine | ruby on rails - Is it impossible to use Guard with RubyMine? - Stack Overflow
https://evilmartians.com/chronicles/testprof-2-factory-therapy-for-your-ruby-tests-rspec-minitest | TestProf II: Factory therapy for your Ruby tests — Martian Chronicles, Evil Martians’ team blog
https://stackoverflow.com/questions/5918606/disable-a-group-of-tests-in-rspec | ruby - Disable a group of tests in rspec? - Stack Overflow
https://stackoverflow.com/questions/20939175/quoting-class-name-in-rspec | ruby on rails - Quoting Class Name in RSpec - Stack Overflow
https://relishapp.com/rspec/rspec-rails/docs/directory-structure | Directory Structure - RSpec Rails - RSpec - Relish
https://nrakochy.github.io/rspec/pending/2015/04/17/Add-Skip-Comment-To-Rspec/ | Add Comment to Pending Skip Statement in Rspec – Remember the Judgment of Thamus
https://stackoverflow.com/questions/11503558/how-to-undefine-class-in-ruby | How to undefine class in Ruby? - Stack Overflow

https://stackoverflow.com/questions/61935684/is-it-possible-to-docker-build-a-multi-staged-image-in-parallel | Is it possible to docker build a multi-staged image in parallel? - Stack Overflow
https://anonymousplanet.org/ | The Hitchhiker’s Guide to Online Anonymity | How I learned to start worrying and love privacy
https://securitytxt.org/ | security.txt: Proposed standard for defining security policies

https://idioms.thefreedictionary.com/bestow+on+(someone) | Bestow on (someone) - Idioms by The Free Dictionary
https://stackoverflow.com/questions/18906350/unset-an-environment-variable-for-a-single-command | bash - Unset an environment variable for a single command - Stack Overflow
https://stackoverflow.com/questions/19537645/get-environment-variable-value-in-dockerfile | docker - Get environment variable value in Dockerfile - Stack Overflow
https://idioms.thefreedictionary.com/what+gives | What gives - Idioms by The Free Dictionary
https://www.hillebrand.com/media/publication/where-are-all-the-containers-the-global-shortage-explained | Where are all the containers? The global shortage explained
https://stackoverflow.com/questions/4722423/how-to-merge-two-branches-with-different-directory-hierarchies-in-git | How to merge two branches with different directory hierarchies in git? - Stack Overflow
https://www.redhat.com/en/topics/automation/what-is-provisioning | What is provisioning?
https://rtyley.github.io/bfg-repo-cleaner/ | BFG Repo-Cleaner by rtyley

https://stackoverflow.com/questions/6085518/what-is-the-easiest-way-to-push-an-element-to-the-beginning-of-the-array/6085543 | ruby - What is the easiest way to push an element to the beginning of the array? - Stack Overflow
https://askubuntu.com/questions/73160/how-do-i-find-the-amount-of-free-space-on-my-hard-drive | disk usage - How do I find the amount of free space on my hard drive? - Ask Ubuntu
https://stackoverflow.com/questions/24429949/device-vs-partition-vs-file-system-vs-volume-how-do-these-concepts-relate-to-ea | filesystems - Device vs Partition vs File System vs Volume: how do these concepts relate to each other, accurately - Stack Overflow
https://www.google.com/search?q=apt-get+autoremove&client=firefox-b-d&biw=1440&bih=793&ei=wZtYYIeyCM2RkwW56ZHgCQ&oq=apt-get+auto&gs_lcp=Cgdnd3Mtd2l6EAMYADIFCAAQkQIyAggAMgIIADICCAAyAggAMgIIADICCAAyAggAMgIIADICCAA6BwgAEEcQsAM6CggAELEDEIMBEEM6CAgAELEDEIMBOgUIABCxAzoICC4QsQMQgwE6CAguEMcBEKMCOg4ILhCxAxCDARDHARCjAjoECAAQQzoKCC4QsQMQgwEQQzoLCC4QsQMQxwEQrwE6DQguELEDEMcBEKMCEENQyGVYrH9gwIkBaARwAngAgAHIAYgBzAiSAQU1LjUuM5gBAKABAaoBB2d3cy13aXrIAQjAAQE&sclient=gws-wiz | apt-get autoremove - Google Search
https://askubuntu.com/questions/527410/what-is-the-advantage-of-using-sudo-apt-get-autoremove-over-a-cleaner-app | cleanup - What is the advantage of using sudo apt-get autoremove over a cleaner app? - Ask Ubuntu
https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/recognize-expanded-volume-linux.html | Extend a Linux file system after resizing a volume - Amazon Elastic Compute Cloud
https://docs.gitlab.com/ee/development/ee_features.html#separation-of-ee-code | Guidelines for implementing Enterprise Edition features | GitLab
https://stackoverflow.com/questions/40742078/relation-passed-to-or-must-be-structurally-compatible-incompatible-values-r | sql - Relation passed to #or must be structurally compatible. Incompatible values: [:references] - Stack Overflow
https://stackoverflow.com/questions/6334537/rails-where-clause-on-model-association | Rails where() clause on model association? - Stack Overflow

https://www.thedrive.com/news/39427/heres-why-the-new-usps-mail-trucks-look-so-weird | Here’s Why the New USPS Mail Trucks Look So Weird | The Drive
https://raphlinus.github.io/rust/graphics/gpu/2019/05/08/modern-2d.html | 2D Graphics on Modern GPU | Raph Levien’s blog
https://stackoverflow.com/questions/60184498/dummy-activerecord-model-in-rspec | ruby on rails - Dummy ActiveRecord model in RSpec - Stack Overflow
https://thoughtbot.com/blog/rails-dont-know-testing | Rails Don't Know Testing
https://commonreader.substack.com/p/let-there-be-more-biographies-of | Let there be more biographies of failures - The Common Reader
https://www.thefreedictionary.com/flushed | Flushed - definition of flushed by The Free Dictionary
https://www.tecmint.com/set-linux-process-priority-using-nice-and-renice-commands/ | How to Set Linux Process Priority Using nice and renice Commands

https://tushartuteja.medium.com/demystifying-rails-migrations-53abcf3a7ddd | Demystifying Rails Migrations. When I picked up rails almost 4 years… | by Tushar Tuteja | Medium
https://docs.gitlab.com/ee/development/migration_style_guide.html#migration-style-guide | Migration Style Guide | GitLab
https://github.com/apple/swift-evolution/blob/main/proposals/0306-actors.md | swift-evolution/0306-actors.md at main · apple/swift-evolution
https://jamesward.com/2021/03/16/the-modern-java-platform-2021-edition/ | The Modern Java Platform - 2021 Edition - James Ward
https://bytes.yingw787.com/posts/2019/02/02/concurrency_with_python_actor_models/ | Concurrency with Python: Actor Models > Ying Wang

https://www.dwell.com/ | Modern living, home design ideas, inspiration, and advice. - Dwell
https://stackoverflow.com/questions/26265480/rails-validation-inclusion-error-not-included-in-list | ruby - Rails validation inclusion error 'not included in list' - Stack Overflow
https://stackoverflow.com/questions/7586813/fake-an-active-record-model-without-db | ruby - Fake an active record model without db - Stack Overflow
https://dev.to/prathamesh/rspec-let-and-before-order-dependent-behavior-4gp6 | RSpec let! and before order dependent behavior - DEV Community
https://ruby-doc.org/core-2.4.1/Array.html#method-i-index | Class: Array (Ruby 2.4.1)
https://www.netmeister.org/blog/software-engineering-laws.html | 10 Software Engineering Laws Everybody Loves to Ignore
https://v2.docusaurus.io/ | Build optimized websites quickly, focus on your content | Docusaurus

https://stackoverflow.com/questions/66610247/why-does-my-antlr-grammar-give-me-an-error | antlr4 - Why does my antlr grammar give me an error? - Stack Overflow
https://fr.wiktionary.org/w/index.php?title=Sp%C3%A9cial:Recherche&limit=500&offset=0&profile=default&search=bruxellois&advancedSearch-current=%7B%7D&ns0=1&ns100=1&ns106=1&ns110=1&searchToken=az1thyr3w028h8zyxw4g7x33f | Résultats de recherche pour « bruxellois » — Wiktionnaire
https://fr.wiktionary.org/wiki/b%C3%A2tard | bâtard — Wiktionnaire
https://www.antlr.org/ | ANTLR

https://apidock.com/rails/v4.0.2/ActiveRecord/Relation/find_or_create_by | find_or_create_by (ActiveRecord::Relation) - APIdock
https://apidock.com/rails/ActiveRecord/Persistence/create_or_update | create_or_update (ActiveRecord::Persistence) - APIdock
https://stackoverflow.com/questions/9322353/factory-girl-create-that-bypasses-my-model-validation | ruby on rails - Factory-girl create that bypasses my model validation - Stack Overflow

https://polyformproject.org/ | Polyform Project – simple, standard, plain-language software licenses
https://juliacomputing.com/media/2021/03/darpa-ditto/ | Julia Computing Receives DARPA Award to Accelerate Electronics Simulation by 1,000x - Julia Computing

https://medium.com/@jeremy_96642/module-extend-understanding-ruby-singleton-classes-9dea718c80f2 | Module#extend: Understanding Ruby Singleton Classes | by Jem Zornow | Medium
https://stackoverflow.com/questions/4662722/extending-a-ruby-module-in-another-module-including-the-module-methods | Extending a Ruby module in another module, including the module methods - Stack Overflow
https://batsov.com/articles/2014/02/17/the-elements-of-style-in-ruby-number-13-length-vs-size-vs-count/ | The Elements of Style in Ruby #13: length vs size vs count · (think)
https://bigbinary.com/blog/alias-vs-alias-method | alias vs alias_method | BigBinary Blog
https://news.ycombinator.com/item?id=24567711 | Ask HN: Is hydrogen likely to be a major source of power in the next 10 years? | Hacker News
https://evilmartians.com/chronicles/testprof-2-factory-therapy-for-your-ruby-tests-rspec-minitest | TestProf II: Factory therapy for your Ruby tests — Martian Chronicles, Evil Martians’ team blog
https://gitlab.com/gitlab-org/gitlab/-/blob/da1dab64d3ef5d842bc89ea3b9e3db2b38a264f1/doc/development/testing_guide/best_practices.md | doc/development/testing_guide/best_practices.md · da1dab64d3ef5d842bc89ea3b9e3db2b38a264f1 · GitLab.org / GitLab · GitLab
https://test-prof.evilmartians.io/#/profilers/factory_doctor?id=configuration | Factory Doctor - TestProf: Ruby tests profiling and optimization toolbox
https://git-scm.com/docs/gitignore | Git - gitignore Documentation
https://apidock.com/rails/Rails/Railtie/Configuration/to_prepare | to_prepare (Rails::Railtie::Configuration) - APIdock
https://stackoverflow.com/questions/5103860/what-does-this-rails-engine-code-mean-config-to-prepare-methodactivate-to-p | spree - What does this Rails Engine code mean: config.to_prepare &method(:activate).to_proc - Stack Overflow
https://ruby-doc.org/stdlib-2.5.3/libdoc/benchmark/rdoc/Benchmark.html | Module: Benchmark (Ruby 2.5.3)
https://stackoverflow.com/questions/********/whats-the-difference-between-rspecs-subject-and-let-when-should-they-be-used | ruby - What's the difference between RSpec's subject and let? When should they be used or not? - Stack Overflow
https://github.com/rspec/rspec-mocks | rspec/rspec-mocks: RSpec's 'test double' framework, with support for stubbing and mocking
https://stackoverflow.com/questions/********/change-the-name-of-parent-parent-id-parameter-in-routing-resources-for-rails4 | ruby on rails 4 - Change the name of parent :parent_id parameter in Routing resources for Rails4 - Stack Overflow
https://stackoverflow.com/questions/7588870/engine-routes-in-application-controller | ruby on rails 3 - Engine routes in Application Controller - Stack Overflow
https://guides.rubyonrails.org/autoloading_and_reloading_constants.html | Autoloading and Reloading Constants (Zeitwerk Mode) — Ruby on Rails Guides
https://security.stackexchange.com/questions/36737/terminology-authentication-vs-verification | Terminology: authentication vs verification - Information Security Stack Exchange
https://gist.github.com/HeroicEric/9935542 | An example of how to create a RSpec test helper

https://www.postgresql.org/docs/13/tutorial-createdb.html | PostgreSQL: Documentation: 13: 1.3. Creating a Database
https://www.datadoghq.com/blog/postgresql-vacuum-monitoring/#last-time-autovacuum-ran | Monitoring PostgreSQL VACUUM Processes | Datadog
https://dba.stackexchange.com/questions/108697/how-to-write-a-query-that-counts-rows-by-date | mysql - How to write a query that counts rows by date? - Database Administrators Stack Exchange
https://idioms.thefreedictionary.com/Going+Crazy | Going Crazy - Idioms by The Free Dictionary
https://chrisvoncsefalvay.com/2021/03/07/julia-a-post-mortem/ | Erreur de chargement de la page
https://stackoverflow.com/questions/7923674/active-record-get-the-second-third-item-in-a-database-without-id | ruby on rails - Active Record - Get the second, third.. item in a database (without ID) - Stack Overflow

https://www.createdbypete.com/2017/09/26/dynamic-omniauth-provider-setup.html | Dynamic OmniAuth Provider Setup 🔐 | Peter Rhoades
http://blog.railsrumble.com/2010/10/08/intridea-omniauth/ | Rails Rumble - Separating Authentication and Identity with OmniAuth
https://realpython.com/python-sockets/ | Socket Programming in Python (Guide) – Real Python
https://www.rubydoc.info/github/elabs/pundit/Pundit/NotAuthorizedError#reason-instance_method | Exception: Pundit::NotAuthorizedError — Documentation for elabs/pundit (master)
https://stackoverflow.com/questions/61232740/create-a-union-all-query-when-the-columns-are-in-different-order | sql - Create a UNION ALL query when the columns are in different order - Stack Overflow
https://stackoverflow.com/questions/3479551/how-to-get-an-array-with-column-names-of-a-table | ruby on rails - How to get an array with column names of a table - Stack Overflow
https://intellij-support.jetbrains.com/hc/en-us/community/posts/206587735-Problem-integrating-with-JIRA-tasks | Problem integrating with JIRA tasks – IDEs Support (IntelliJ Platform) | JetBrains
https://stackoverflow.com/questions/46945728/ruby-code-in-gemfile-isnt-executed | rack - Ruby code in Gemfile isn't executed - Stack Overflow
https://stackoverflow.com/questions/50527194/how-to-have-multiple-gemfiles-for-a-project | ruby on rails - How to have multiple Gemfiles for a project? - Stack Overflow
https://stackoverflow.com/questions/19693842/what-is-the-correct-way-to-add-a-staging-group-to-my-gemfile/22561193 | ruby on rails - What is the Correct Way To Add a :staging group to My Gemfile - Stack Overflow
https://bundler.io/guides/groups.html | Bundler: How to manage groups of gems
https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/runtime.rb | lib/gitlab/runtime.rb · master · GitLab.org / GitLab · GitLab
https://github.com/kentcdodds/cross-env/issues/257 | cross-env is "finished" (now in maintenance mode) · Issue #257 · kentcdodds/cross-env
https://webpack.js.org/configuration/ | Configuration | webpack
https://www.flightradar24.com/blog/troll-research-station-how-to-operate-an-airport-in-antarctica/ | Troll Research Station: how to operate an airport in Antarctica | Flightradar24 Blog
https://elixir-lang.org/ | The Elixir programming language
https://hub.docker.com/_/elixir | elixir
https://mikeyhogarth.wordpress.com/2011/11/07/ruby-sigils/ | Ruby Sigils | Mikey Hogarth
https://stackoverflow.com/questions/7674685/whats-exactly-happening-in-infinite-nested-lists | python - What's exactly happening in infinite nested lists? - Stack Overflow
https://kevinlynagh.com/rust-zig/ | kevinlynagh.com/rust-zig/
https://ziglang.org/ | Home ⚡ Zig Programming Language
https://stackoverflow.com/questions/824562/does-ruby-perform-tail-call-optimization | functional programming - Does Ruby perform Tail Call Optimization? - Stack Overflow
https://www.cbc.ca/news/canada/winter-exercise-lungs-1.5936703 | Why winter exercise can be especially hard on the lungs | CBC News
https://manabouttown.tv/blogs/words-and-images/fashion_editorial_sonny_hall | Man About Town Fashion Editorial Sonny Hall - MAN ABOUT TOWN

https://chadaustin.me/2021/02/wired-sculpt/ | Microsoft Sculpt Wired Conversion Mod | Chad Austin
https://context.reverso.net/traduction/francais-anglais/o%C3%B9+elle+en+est | où elle en est - Traduction en anglais - exemples français | Reverso Context

https://docs.gitlab.com/ee/development/ee_features.html#use-self-descriptive-wrapper-methods | Guidelines for implementing Enterprise Edition features | GitLab
https://stopa.io/post/265 | An Intuition for Lisp Syntax
https://tekin.co.uk/2021/01/how-atomic-commits-make-you-a-better-coder | How focused commits make you a better coder | tekin.co.uk
https://www.startpage.com/do/dsearch?query=dialogues+hades&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://docs.gitlab.com/ee/development/reusing_abstractions.html#finders | Guidelines for reusing abstractions | GitLab
https://www.mendelowski.com/docs/ruby/self-vs-extend-self-vs-module-function/ | self vs extend self vs module_function - Lucas Mendelowski
https://monapollon.com/ | Mon Apollon - Décoration d'intérieur - le corps masculin à l'honneur
https://news.ycombinator.com/item?id=26281103 | Free Land – Living Off Grid With No Money | Hacker News
https://www.nationalreview.com/2017/10/aol-instant-messenger-eulogy-aim-social-media-millennials/ | AOL Instant Messenger Eulogy -- AIM, Social Media & Millennials | National Review
https://www.startpage.com/do/dsearch?query=append+if+key+present+hash+ruby&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://stackoverflow.com/questions/54724526/check-hash-if-key-exists-if-not-create-array-and-append-value | ruby - Check hash if key exists; if not, create array and append value - Stack Overflow
https://medium.com/risan/upgrade-your-ssh-key-to-ed25519-c6e8d60d3c54 | Upgrade Your SSH Key to Ed25519. If you’re a DevOps engineer or a web… | by Risan Bagja Pradana | risan | Medium
https://www.startpage.com/do/dsearch?query=upgrade+docker+engine&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://docs.docker.com/engine/install/ubuntu/#install-using-the-repository | Install Docker Engine on Ubuntu | Docker Documentation
https://www.startpage.com/do/dsearch?query=restart+docker+service+daemon&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://docs.docker.com/config/daemon/systemd/ | Control Docker with systemd | Docker Documentation
https://aem-goldex.citizenlab.co/fr-FR/ | Plateforme d'engagement citoyen de Agnico Eagle - Mine Goldex | CitizenLab
https://www.startpage.com/do/dsearch?query=journal+logs+system+prune&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://ma.ttias.be/clear-systemd-journal/ | Clear systemd journal
https://www.startpage.com/do/dsearch?query=sudo+apt+autoremove&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://www.google.com/search?client=firefox-b-d&q=%2Fdev%2Fnvme0n1p1 | /dev/nvme0n1p1 - Google Search
https://askubuntu.com/questions/932331/filesystem-shows-dev-nvme0n1p1-instead-of-dev-sda | boot - Filesystem shows /dev/nvme0n1p1 instead of /dev/sda - Ask Ubuntu
https://www.startpage.com/do/dsearch?query=xvda1+full&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://serverfault.com/questions/330532/xvda1-is-100-full-what-is-it-how-to-fix | linux - xvda1 is 100% full, What is it? how to fix? - Server Fault
https://www.startpage.com/do/dsearch?query=clear+space+used+by+a+container&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche

https://gitlab.com/gitlab-org/gitlab/-/issues/2500 | GitLab CE features to work with unlicensed EE instance (#2500) · Issues · GitLab.org / GitLab · GitLab

https://circle-production-customer-artifacts.s3.amazonaws.com/picard/59a58f31c9e77c0001a67bec/603790cc83cd70137c327805-0-build/artifacts/cypress/screenshots/idea_cards.ts/Idea%20cards%20without%20filter%20sidebar%20pagination%20--%20lets%20you%20load%20more%20ideas%20%28failed%29.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210225T125735Z&X-Amz-SignedHeaders=host&X-Amz-Expires=60&X-Amz-Credential=AKIAJR3Q6CR467H7Z55A%2F20210225%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=c581d0a3baccec317dde9bb6b5cfa6d6855aefe4007ebd62fc2d92bee9e291e9 | Idea cards without filter sidebar pagination -- lets you load more ideas (failed).png (PNG Image, 1280 × 720 pixels)
https://stackoverflow.com/questions/6839398/find-when-a-file-was-deleted-in-git/34755406 | Find when a file was deleted in Git - Stack Overflow

https://stackoverflow.com/questions/18289152/what-does-a-double-splat-operator-do/18289218#18289218 | ruby - What does a double * (splat) operator do - Stack Overflow
https://thoughtbot.com/upcase/videos/ruby-keyword-arguments | Ruby Keyword Arguments | Online Video Tutorial by thoughtbot
https://guides.rubyonrails.org/autoloading_and_reloading_constants.html | Autoloading and Reloading Constants (Zeitwerk Mode) — Ruby on Rails Guides
https://medium.com/@connorstack/understanding-ruby-load-require-gems-bundler-and-rails-autoloading-from-the-bottom-up-3b422902ca0 | Understanding ruby load, require, gems, bundler and rails autoloading from the bottom up | by cstack | Medium
https://en.wikipedia.org/wiki/Gigabyte | Gigabyte - Wikipedia
https://sentry.hq.citizenlab.co/organizations/citizenlab/issues/10454/?environment=staging&query=is%3Aunresolved | ActiveRecord::RecordNotFound: "Couldn't find AppConfiguration". Retry count: 4 (max: 15).

https://relishapp.com/rspec/rspec-core/v/3-8/docs/pending-and-skipped-examples/skip-examples | `skip` examples - Pending and skipped examples - RSpec Core - RSpec - Relish
https://stackoverflow.com/questions/5918606/disable-a-group-of-tests-in-rspec | ruby - Disable a group of tests in rspec? - Stack Overflow
https://gitlab.com/gitlab-org/gitlab/-/tree/master/ee/app/controllers | ee/app/controllers · master · GitLab.org / GitLab · GitLab
https://ruby-doc.org/stdlib-2.7.0/libdoc/set/rdoc/Set.html | Class: Set (Ruby 2.7.0)
https://stackoverflow.com/questions/19556296/whats-the-difference-between-include-examples-and-it-behaves-like | ruby - What's the difference between "include_examples" and "it_behaves_like"? - Stack Overflow
https://askubuntu.com/questions/433113/how-to-remove-temp-files-with-a-command-other-then-apt-get-autoremove | 12.04 - How to remove temp files with a command other then "apt-get autoremove"? - Ask Ubuntu

https://gist.github.com/damien-roche/351bf4e7991449714533 | A Primer on Ruby Method Lookup · GitHub
https://timnew.me/blog/2012/08/23/ruby-class-inheritance/ | Ruby Class Inheritance | ThoughtWorkshop

https://martinfowler.com/articles/evodb.html | Evolutionary Database Design
https://en.wikipedia.org/wiki/Message_authentication_code | Message authentication code - Wikipedia
https://frederic-hemberger.de/notes/kubernetes/manage-secrets-with-sops/ | Kubernetes: Manage secrets with SOPS | Frederic Hemberger
https://medium.com/mercos-engineering/secrets-as-a-code-with-mozilla-sops-and-aws-kms-d069c45ae1b9 | Secrets as a code with Mozilla SOPS and AWS KMS | by Gabriel Abdalla Cavalcante | mercos-engineering | Medium
https://blog.rebased.pl/2017/11/30/bindings-in-ruby-behind-the-magic-of-blocks.html | Bindings in Ruby – Behind the Magic of Blocks
https://gist.github.com/cflee/00c6a1b981de5bb812ee | Discoveries about Ruby Blocks, Procs and Lambdas
https://www.python.org/dev/peps/pep-0622/ | PEP 622 -- Structural Pattern Matching | Python.org
https://www.oreilly.com/library/view/head-first-design/9781492077992/ | Head First Design Patterns, 2nd Edition [Book]
https://gist.github.com/maxim | maxim’s gists
http://morningcoffee.io/interfaces-in-ruby.html | The Power of Interfaces in Ruby — by Igor Šarčević
https://docs.celeryproject.org/en/stable/getting-started/first-steps-with-celery.html#first-steps | First Steps with Celery — Celery 5.0.5 documentation
http://rubylearning.com/satishtalim/ruby_constants.html | Ruby Constants: Ruby Study Notes - Best Ruby Guide, Ruby Tutorial
https://dl.acm.org/magazines | ACM Magazine

https://www.thefreedictionary.com/All+Hell+Breaks+Loose | All Hell Breaks Loose - definition of All Hell Breaks Loose by The Free Dictionary

https://english.stackexchange.com/questions/13144/separated-versus-separate | word choice - "Separated" versus "separate" - English Language & Usage Stack Exchange
https://stackoverflow.com/questions/39239051/rs256-vs-hs256-whats-the-difference | jwt - RS256 vs HS256: What's the difference? - Stack Overflow
https://guides.rubyonrails.org/layouts_and_rendering.html#using-render | Layouts and Rendering in Rails — Ruby on Rails Guides
https://softwareengineering.stackexchange.com/questions/379575/how-do-you-write-unit-tests-for-code-with-difficult-to-predict-results | tdd - How do you write unit tests for code with difficult to predict results? - Software Engineering Stack Exchange
https://github.com/janfri/regtest | janfri/regtest: Simple regression testing with Ruby
https://gitlab.com/gitlab-org/gitlab/-/blob/master/ee/app/mailers/ee/notify.rb | ee/app/mailers/ee/notify.rb · master · GitLab.org / GitLab · GitLab
https://math.stackexchange.com/questions/864606/difference-between-%E2%89%88-%E2%89%83-and-%E2%89%85 | notation - Difference between "≈", "≃", and "≅" - Mathematics Stack Exchange
https://github.com/rails/rails/blob/main/activesupport/lib/active_support/configurable.rb | rails/configurable.rb at main · rails/rails

https://eggerapps.at/postico/ | Postico – a modern PostgreSQL client for the Mac
https://golangbyexample.com/understand-etc-paths-pathsd-mac/ | Understand /etc/paths file and /etc/paths.d directory on MAC – Welcome To Golang By Example
https://github.com/petl-developers/petl | petl-developers/petl: Python Extract Transform and Load Tables of Data

https://stackoverflow.com/questions/8650983/rails-update-model-attribute-without-invoking-callbacks | ruby - Rails: Update model attribute without invoking callbacks - Stack Overflow
https://scottbartell.com/2020/01/30/set-attributes-in-active-record-rails-6/ | Different Ways to Set Attributes in ActiveRecord (Rails 6)
https://stackoverflow.com/questions/21371734/how-to-save-a-model-without-running-callbacks-in-rails | How to save a model without running callbacks in Rails - Stack Overflow
https://stackoverflow.com/questions/60033/what-is-the-easiest-way-to-duplicate-an-activerecord-record | ruby on rails - What is the easiest way to duplicate an activerecord record? - Stack Overflow
http://localhost:5000/clusters/local/tenants | Action Controller: Exception caught
https://stackoverflow.com/questions/18718942/skipping-validation-on-create-method | ruby on rails - Skipping validation on create method - Stack Overflow
https://medium.com/@jelaniwoods/vcr-with-rspec-dead1de8245e | VCR with Rspec. Say you’re writing some tests and the… | by Jelani Woods | Medium

https://relishapp.com/rspec/rspec-mocks/v/3-0/docs/basics/null-object-doubles | Null object doubles - Basics - RSpec Mocks - RSpec - Relish
https://rspec.info/documentation/3.4/rspec-mocks/RSpec/Mocks/ArgumentMatchers.html | Module: RSpec::Mocks::ArgumentMatchers — Documentation by YARD *******
https://stackoverflow.com/questions/59541212/how-to-implement-complex-sql-queries-in-ruby-on-rails | How to implement complex SQL queries in Ruby on Rails? - Stack Overflow
https://stackoverflow.com/questions/1344232/how-can-i-see-the-sql-that-will-be-generated-by-a-given-activerecord-query-in-ru | How can I see the SQL that will be generated by a given ActiveRecord query in Ruby on Rails - Stack Overflow
http://joshfrankel.me/blog/a-journey-into-writing-union-queries-with-active-record/ | A Journey into Writing Union queries with Active Record | Development Simplified
https://stackoverflow.com/questions/14824453/rails-raw-sql-example | Rails raw SQL example - Stack Overflow
https://sql.sh/cours/union/union-all | SQL UNION ALL - SQL
https://commandercoriander.net/blog/2014/11/09/a-multiline-string-cheatsheet-for-ruby/ | Cmdr Coriander
https://bountify.co/size-of-an-object-in-ruby | Bountify | Size of an object in Ruby?
https://stackoverflow.com/questions/147969/is-it-idiomatic-ruby-to-add-an-assert-method-to-rubys-kernel-class | Is it idiomatic Ruby to add an assert( ) method to Ruby's Kernel class? - Stack Overflow
https://api.rubyonrails.org/classes/ActiveRecord/AttributeMethods/Serialization/ClassMethods.html | ActiveRecord::AttributeMethods::Serialization::ClassMethods
https://menuslate.gitbooks.io/activemodelserializers/content/rendering/adapter_options.html?q=%3Ajson | Adapter Options · ActiveModelSerializers
https://davidverhasselt.com/set-attributes-in-activerecord/ | Different Ways to Set Attributes in ActiveRecord (Rails 4)

https://www.rubyguides.com/2016/02/ruby-procs-and-lambdas/#What_is_a_Lambda | Ruby Blocks, Procs & Lambdas - The Ultimate Guide!
https://stackoverflow.com/questions/41384003/sql-union-all-a-large-number-of-tables | sql server - SQL- UNION ALL a large number of tables - Stack Overflow
https://en.wikipedia.org/wiki/Characterization_test | Characterization test - Wikipedia
https://laboratory-python.readthedocs.io/en/latest/ | Laboratory — Laboratory 1.0.2 documentation
https://github.com/github/scientist | github/scientist: A Ruby library for carefully refactoring critical paths.
https://stackoverflow.com/questions/2325471/using-return-in-a-ruby-block | lambda - Using 'return' in a Ruby block - Stack Overflow
https://github.com/minimaxir/big-list-of-naughty-strings | minimaxir/big-list-of-naughty-strings: The Big List of Naughty Strings is a list of strings which have a high probability of causing issues when used as user-input data.
https://stackoverflow.com/questions/10567430/check-if-an-array-is-subset-of-another-array-in-ruby | Check if an array is subset of another array in Ruby - Stack Overflow
https://stackoverflow.com/questions/26342570/rubocop-linelength-how-to-ignore-lines-with-comments | ruby - Rubocop Linelength: How to ignore lines with comments? - Stack Overflow

https://numpy.org/doc/1.20/release/1.20.0-notes.html | NumPy 1.20.0 Release Notes — NumPy v1.21.dev0 Manual
https://stackoverflow.com/questions/31967363/prevent-initializer-in-rake-tasks-and-rails-console | Prevent initializer in rake tasks and rails console - Stack Overflow
https://doc.bccnsoft.com/docs/rails-guides-3.2-en/configuring.html | Ruby on Rails Guides: Configuring Rails Applications
https://docs.gitlab.com/ee/development/rails_initializers.html | Rails initializers | GitLab
https://stackoverflow.com/questions/726690/what-killed-my-process-and-why | linux - What killed my process and why? - Stack Overflow
https://stackoverflow.com/questions/4110866/ruby-on-rails-where-to-define-global-constants | Ruby on Rails: Where to define global constants? - Stack Overflow
https://stackoverflow.com/questions/43000576/why-is-yaml-safe-load-failing-on-a-yaml-alias | ruby on rails - Why is YAML.safe_load failing on a YAML alias? - Stack Overflow
http://**************:8506/ | summarization_lab · Streamlit
https://cloud.google.com/solutions/machine-learning/building-real-time-embeddings-similarity-matching-system | Building a real-time embeddings similarity matching system  |  Solutions

https://english.stackexchange.com/questions/503303/clear-up-v-vs-clear-v | differences - clear up (v) vs. clear (v) - English Language & Usage Stack Exchange
https://emacs.stackexchange.com/questions/477/how-do-i-automatically-save-org-mode-buffers | saving - How do I automatically save org-mode buffers? - Emacs Stack Exchange
https://docs.docker.com/engine/reference/commandline/system_df/ | docker system df | Docker Documentation
https://stackoverflow.com/questions/35923282/rails-4-plugin-with-my-models-where-do-i-put-migrations | Rails 4 plugin with my models, where do I put migrations? - Stack Overflow
https://www.rubyguides.com/2017/07/ruby-constants/ | Everything You Need to Know About Ruby Constants
https://rubystyle.guide/#spaces-indentation | The Ruby Style Guide
https://superuser.openstack.org/ | Superuser, an Online Publication Covering Open Infrastructure
https://stackoverflow.com/questions/3723491/params-becomming-nil | ruby on rails - Params becomming nil - Stack Overflow
https://github.com/docker/for-win/issues/2194 | Docker compose can not start. Service network not found after restart docker · Issue #2194 · docker/for-win

https://github.com/jnunemaker/httparty/issues/242#issuecomment-25554086 | HTTParty::Response#ok? Is not reliable · Issue #242 · jnunemaker/httparty
https://github.com/HypothesisWorks/HypothesisWorks.github.io | HypothesisWorks/HypothesisWorks.github.io: Main hypothesis.works website
https://propertesting.com/book_shrinking.html | Shrinking
https://propertesting.com/book_state_machine_properties.html | State Machine Properties
https://www.jetbrains.com/help/idea/managing-tasks-and-context.html#work-with-tasks | Tasks and contexts—IntelliJ IDEA

https://stackoverflow.com/questions/27139937/creating-an-infinite-loop | ruby - Creating an infinite loop - Stack Overflow
https://haacked.com/archive/2019/06/03/suggested-changes/ | Suggesting Changes on GitHub | You’ve Been Haacked
https://stackoverflow.com/questions/6083219/activerecord-size-vs-count | ruby on rails - ActiveRecord: size vs count - Stack Overflow

https://www.jetbrains.com/pycharm/guide/playlists/42/ | 42 Tips and Tricks - PyCharm Guide
https://www.startpage.com/do/dsearch?query=bundle+binstubs+bundler+--force&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://stackoverflow.com/questions/49186959/your-bin-bundle-was-not-generated-by-bundler-so-this-binstub-cannot-run | ruby on rails - Your `bin/bundle` was not generated by Bundler, so this binstub cannot run - Stack Overflow
https://github.com/testdouble/standard | testdouble/standard: 🌟 Ruby Style Guide, with linter & automatic code fixer
https://blog.saeloun.com/2019/10/07/ruby-2-7-keyword-arguments-redesign.html | Ruby 2.7 deprecates automatic conversion from a hash to keyword arguments | Saeloun Blog
https://www.ruby-lang.org/en/news/2019/12/12/separation-of-positional-and-keyword-arguments-in-ruby-3-0/ | Separation of positional and keyword arguments in Ruby 3.0
https://github.com/rubygems/bundler/issues/6149 | 1.16.0 is intercepting binstub arguments and throwing 'unknown switches' error · Issue #6149 · rubygems/bundler

https://www.jetbrains.com/help/idea/configuring-third-party-tools.html | External tools—IntelliJ IDEA
https://www.jetbrains.com/help/ruby/using-macros-in-the-editor.html#reformat_on_save | Macros—RubyMine
https://www.jetbrains.com/help/idea/reader-mode.html | Reader mode—IntelliJ IDEA
https://devhints.io/tig | Tig cheatsheet
https://bigmachine.io/products/a-curious-moon/ | A Curious Moon | Big Machine
https://stackoverflow.com/questions/4870531/find-the-newest-record-in-rails-3 | ruby - Find the newest record in Rails 3 - Stack Overflow

https://davidverhasselt.com/set-attributes-in-activerecord/ | Different Ways to Set Attributes in ActiveRecord (Rails 4)
https://stackoverflow.com/questions/6770350/rails-update-attributes-without-save | ruby - Rails update_attributes without save? - Stack Overflow
https://intellij-support.jetbrains.com/hc/en-us/community/posts/360008283720-Docker-Interactive-Terminal- | Docker Interactive Terminal? – IDEs Support (IntelliJ Platform) | JetBrains
https://blog.jetbrains.com/idea/2020/07/run-ide-features-from-the-terminal/ | Run IDE Features From the Terminal | JetBrains Blog
https://blog.jetbrains.com/ruby/2017/05/10-rubymine-shorcuts-you-shouldnt-miss/ | 10 RubyMine Shorcuts you Shouldn't Miss | JetBrains Blog

https://uxdesign.cc/button-design-user-interface-components-series-85243b6736c7 | Button  Design — UI component series | UX Collective
https://uxdesign.cc/text-fields-forms-design-ui-components-series-2b32b2beebd0 | Text fields & Forms design — UI components series | by Taras Bakusevych | UX Collective

https://stackoverflow.com/questions/45069569/how-to-list-schemas-in-postgres-from-rails-console | postgresql - How to list schemas in postgres from rails console? - Stack Overflow
https://chagency.co.uk/blog/increasing-user-retention/dont-take-your-customers-hostages-facilitate-their-exit/ | Don't Hold Your Customers Hostages. Facilitate Their Exit - chagency_acumen

https://en.wikipedia.org/wiki/Exponential_backoff | Exponential backoff - Wikipedia

https://github.com/lantins/resque-retry | lantins/resque-retry: A resque plugin; provides retry, delay and exponential backoff support for resque jobs.
https://karolgalanciak.com/blog/2016/09/25/decoding-rails-magic-how-does-activejob-work/ | Decoding Rails Magic: How Does ActiveJob work?

https://docs.sentry.io/product/relay/operating-guidelines/#multi-core-cpu | Operating Guidelines | Sentry Documentation
https://relishapp.com/rspec/rspec-core/v/3-7/docs/configuration/zero-monkey-patching-mode | Zero monkey patching mode - Configuration - RSpec Core - RSpec - Relish
https://dev.to/gaetanm/check-if-a-file-was-uploaded-with-carrierwave-1h5l | Check If a File Was Uploaded with CarrierWave - DEV Community
https://apidock.com/rails/ActiveRecord/Base/attributes%3D | attributes= (ActiveRecord::Base) - APIdock
https://www.betterspecs.org/#stubbing | Better Specs. Testing Guidelines for Developers.

https://askubuntu.com/questions/932392/whats-exactly-the-point-of-the-sudo-command-in-terms-of-security | What's exactly the point of the sudo command, in terms of security? - Ask Ubuntu
https://stackoverflow.com/questions/14528883/how-do-i-keep-two-records-for-different-models-in-sync-without-falling-into-th | ruby on rails - How do I keep two records, for different models, in sync without falling into the callback trap? - Stack Overflow
https://stackoverflow.com/questions/1342761/how-to-skip-activerecord-callbacks | ruby on rails - How to skip ActiveRecord callbacks? - Stack Overflow
https://stackoverflow.com/questions/4928789/how-do-i-compare-two-hashes | ruby - How do I compare two hashes? - Stack Overflow
https://fr.wikipedia.org/wiki/Universally_unique_identifier | Universally unique identifier — Wikipédia
https://stackoverflow.com/questions/8301635/how-to-change-primary-id-of-a-record-in-rails | How to change primary ID of a record in Rails? - Stack Overflow
https://stackoverflow.com/questions/1117584/generating-guids-in-ruby | Generating Guids in Ruby - Stack Overflow
http://vaidehijoshi.github.io/blog/2015/08/25/unlocking-ruby-keywords-begin-end-ensure-rescue/ | Unlocking Ruby Keywords: Begin, End, Ensure, Rescue - Words and Code
https://stackoverflow.com/questions/38004148/another-git-process-seems-to-be-running-in-this-repository | Another git process seems to be running in this repository - Stack Overflow

https://www.creativemines.dev/blog/sentry-saga-part-3/ | Sentry: self-hosted installation using Docker – Creative Mines
https://en.wikipedia.org/wiki/Simple_Mail_Transfer_Protocol#Client_authentication | Simple Mail Transfer Protocol - Wikipedia
https://serverfault.com/questions/48428/how-to-send-emails-and-avoid-them-being-classified-as-spam | How to send emails and avoid them being classified as spam? - Server Fault
https://orgmode.org/guide/Export-Settings.html | Export Settings (Org Mode Compact Guide)
https://www.redhat.com/en/topics/automation/what-is-provisioning | What is provisioning?
https://devopsheaven.com/ssh/security/lastpass/devops/2018/06/13/ssh-lastpass-cli.html | How to store SSH keys in LastPass
https://guides.github.com/features/mastering-markdown/ | Mastering Markdown · GitHub Guides
https://docs.docker.com/engine/reference/commandline/port/ | docker port | Docker Documentation
https://www.cyberciti.biz/faq/unix-linux-check-if-port-is-in-use-command/ | How to check if port is in use on Linux or Unix - nixCraft
https://relishapp.com/rspec/rspec-mocks/v/3-10/docs/verifying-doubles/using-an-instance-double | Using an instance double - Verifying doubles - RSpec Mocks - RSpec - Relish
https://relishapp.com/rspec/rspec-mocks/v/3-2/docs/setting-constraints/matching-arguments | Matching arguments - Setting constraints - RSpec Mocks - RSpec - Relish
https://relishapp.com/rspec/rspec-expectations/docs/built-in-matchers/include-matcher | `include` matcher - Built in matchers - RSpec Expectations - RSpec - Relish
https://relishapp.com/rspec/rspec-expectations/docs/built-in-matchers | Built in matchers - RSpec Expectations - RSpec - Relish

https://dev.to/aakatev/deploy-ec2-instance-in-minutes-with-terraform-ip2 | [Terraform] Deploy EC2 Instance in Minutes - DEV Community
https://devops4solutions.com/provisioning-ec2-key-pairs-with-terraform/ | Provisioning EC2 key pairs with terraform - DevOps4Solutions DevOps4Solutions
https://docs.openstack.org/horizon/latest/user/configure-access-and-security-for-instances.html | OpenStack Docs: Configure access and security for instances
https://decorrespondent.nl/11955/kijken-deze-briljante-voorstelling-helpt-je-het-klimaatdebat-te-doorgronden-het-gaat-niet-over-schuld-maar-over-verantwoordelijkheid/1317548595-107a0219 | Kijken: Deze briljante voorstelling helpt je het klimaatdebat te doorgronden. Het gaat niet over schuld, maar over verantwoordelijkheid - De Correspondent
https://stackoverflow.com/questions/9270734/ssh-permissions-are-too-open-error | ssh "permissions are too open" error - Stack Overflow
https://stackoverflow.com/questions/791959/download-a-specific-tag-with-git | git clone - Download a specific tag with Git - Stack Overflow
https://news.ycombinator.com/item?id=20505565 | Ask HN: Book Recommendation for DevOps? | Hacker News
https://sre.google/sre-book/introduction/ | Google - Site Reliability Engineering
https://askubuntu.com/questions/932392/whats-exactly-the-point-of-the-sudo-command-in-terms-of-security | What's exactly the point of the sudo command, in terms of security? - Ask Ubuntu

https://howtosavetheworld.ca/2020/12/31/several-short-sentences-about-greenland-sharks/ | Several Short Sentences About… (Greenland) Sharks | how to save the world
https://ruby-doc.org/core-2.6.1/Method.html | Class: Method (Ruby 2.6.1)
http://andrewberls.com/blog/post/ruby-tips-calling-lambdas | Ruby tips: Calling lambdas | Andrew Berls
https://www.startpage.com/do/dsearch?query=Proc+method+classes&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://rubyreferences.github.io/rubyref/builtin/core/method-proc.html | Method and Proc - Ruby Reference
https://stackoverflow.com/questions/15773552/ruby-class-instance-variable-vs-class-variable | Ruby class instance variable vs. class variable - Stack Overflow
https://maximomussini.com/posts/ruby-class-variables/ | Class Instance Variables in Ruby · Máximo Mussini
https://www.ruby-forum.com/t/how-to-assign-an-element-to-a-hash-only-if-its-value-is-not-nil/204027 | How to assign an element to a hash only if its value is not nil? - Ruby - Ruby-Forum
https://zverok.github.io/blog/2018-01-24-yield_self.html | yield_self is more awesome than you could think
https://www.startpage.com/do/dsearch?query=python+with&cat=web&pl=ext-ff&language=francais&extVersion=1.3.0 | Startpage.com Résultats de la recherche
https://www.nme.com/features/nme-best-albums-of-the-year-2020-2835612 | The 50 best albums of 2020
https://www.nme.com/gaming-features/the-best-games-you-missed-in-2020-2846684 | The best games you missed in 2020 | NME

https://www.terraform.io/docs/providers/index.html | Provider Documentation - Terraform by HashiCorp
https://www.terraform.io/docs/configuration/providers.html | Provider Configuration - Configuration Language - Terraform by HashiCorp
https://stackoverflow.com/questions/46099329/error-with-keys-when-running-terraform-apply | Error with keys when running terraform apply - Stack Overflow
https://www.thegreatcodeadventure.com/rails-refactoring-part-iii-the-decorator-pattern/ | Rails Refactoring Part III: The Decorator Pattern

https://drewdevault.com/2020/12/12/Shell-literacy.html | Become shell literate
https://github.com/getsentry/onpremise/blob/20.12.1/docker-compose.yml | onpremise/docker-compose.yml at 20.12.1 · getsentry/onpremise
https://www.loggly.com/use-cases/ruby-on-rails-logging-best-practices/ | Ruby on Rails Logging Best Practices | Loggly
https://medium.com/@adnama.lin/mocking-and-doubles-and-stubs-oh-my-3f04efc69896 | Mocking, and Doubles, and Stubs, Oh My! | by Amanda Lin | Medium
https://martinfowler.com/articles/mocksArentStubs.html#TheDifferenceBetweenMocksAndStubs | Mocks Aren't Stubs
https://www.rubyguides.com/2018/10/rspec-mocks/ | How to Use RSpec Mocks (Step-By-Step Tutorial) - RubyGuides

https://www.google.com/search?newwindow=1&client=firefox-b-d&hl=en-US&q=Morphies+Law&stick=H4sIAAAAAAAAAONgFuLVT9c3NEw3qipMTspIUkLlailnJ1vpJ5el6yfn5xaUlqQWxZdlpqTmpyfmploVA1nliZXFi1h5fPOLCjIyU4sVfBLLd7AyAgBYf6fRVwAAAA&sa=X&ved=2ahUKEwifxs204pntAhWSyaQKHc6PAIkQzTooATAgegQIEBAC&biw=1440&bih=775 | Morphies Law - Google Search
https://www.youtube.com/channel/UCbWm0JYZTmfyrjvQEe2ZdVA | PARIS ASMR - YouTube
https://rxjs-dev.firebaseapp.com/guide/overview | RxJS - Introduction
https://emacsconf.org/2020/talks/09/ | EmacsConf - 2020 - talks - Orgmode - your life in plain text
https://devcenter.heroku.com/articles/procfile | The Procfile | Heroku Dev Center
https://fanlabel.com/music/2020s-disco-pop-revival/ | 2020's Disco Pop Revival | FanLabel Music Scene | Playlist
https://overfitti.ng-dis.co/archives/21822 | Overfitting Disco » 12 West Mix
https://en.wikipedia.org/wiki/Goodhart's_law | Goodhart's law - Wikipedia
https://www.ruby-lang.org/en/news/2020/09/25/ruby-3-0-0-preview1-released/?hn=t | Ruby 3.0.0 Preview 1 Released
https://speakerdeck.com/k_tsj/pattern-matching-new-feature-in-ruby-2-dot-7?slide=8 | Pattern matching - New feature in Ruby 2.7 - Speaker Deck
https://docs.google.com/document/d/16w3B84rVv7Okw7_FWS4gkQ1VjXRvmOJ94tLk7k4IxOA/edit | 2021 Engagement Roadmap - Google Docs
https://fr.wikipedia.org/wiki/Continuit%C3%A9_r%C3%A9troactive | Continuité rétroactive — Wikipédia

https://fr.wikipedia.org/wiki/Effet_Vavilov-Tcherenkov | Effet Vavilov-Tcherenkov — Wikipédia
https://schwad.github.io/ruby/rails/testing/2017/08/14/50-times-faster-rspec-loading.html | How I got RSpec to boot 50 times faster | Schwad
https://stackoverflow.com/questions/55237880/intellij-idea-find-all-deprecated-usages-in-the-project | Intellij Idea find all deprecated usages in the project - Stack Overflow
https://stackoverflow.com/questions/18923478/how-can-i-configure-rails-to-raise-an-error-when-it-hits-a-deprecation-warning | How can I configure Rails to raise an error when it hits a deprecation warning? - Stack Overflow

https://www.mmhmm.app/ | Clear, compelling communication for everyone | mmhmm
https://stackoverflow.com/questions/4713088/how-to-use-git-bisect | How to use git bisect? - Stack Overflow
https://stackoverflow.com/questions/2814315/rspec-and-stubbing-parameters-for-a-named-scope | ruby on rails - RSpec and stubbing parameters for a named scope - Stack Overflow
https://stackoverflow.com/questions/6227600/how-to-remove-a-key-from-hash-and-get-the-remaining-hash-in-ruby-rails | How to remove a key from Hash and get the remaining hash in Ruby/Rails? - Stack Overflow

https://fr.wikipedia.org/wiki/Cgroups | Cgroups — Wikipédia
https://thoughtbot.com/blog/data-migrations-in-rails | Data Migrations in Rails
https://kevinjalbert.com/defined_methods-in-rake-tasks-you-re-gonna-have-a-bad-time/ | Defined Methods in Rake Tasks; You're Gonna Have a Bad Time | Kevin Jalbert
https://stackoverflow.com/questions/10301794/difference-between-rake-dbmigrate-dbreset-and-dbschemaload | ruby on rails - Difference between rake db:migrate db:reset and db:schema:load - Stack Overflow
https://eklitzke.org/binding-on-port-zero | Binding On Port 0
https://davidverhasselt.com/set-attributes-in-activerecord/ | Different Ways to Set Attributes in ActiveRecord (Rails 4)
https://keepachangelog.com/en/1.0.0/ | Keep a Changelog
https://stackoverflow.com/questions/51621400/what-is-difference-between-release-notes-and-changelog | version control - What is difference between release notes and changelog? - Stack Overflow
https://superuser.com/questions/1237167/copy-the-output-of-the-last-command-in-iterm2 | macos - Copy the output of the last command in iTerm2 - Super User
https://stackoverflow.com/questions/28189560/is-there-a-shortcut-to-delete-the-currently-open-file-in-phpstorm | intellij idea - Is there a shortcut to delete the currently open file in PHPStorm? - Stack Overflow

https://stackoverflow.com/questions/18349887/rails4-how-to-permit-a-hash-with-dynamic-keys-in-params | ruby on rails - Rails4: How to permit a hash with dynamic keys in params? - Stack Overflow
https://stackoverflow.com/questions/5767222/rails-call-another-controller-action-from-a-controller | Rails: call another controller action from a controller - Stack Overflow
https://andycroll.com/ruby/use-a-deprecation-message/ | Use Active Support in Rails for deprecation messages - Andy Croll
https://stackoverflow.com/questions/29843430/warning-client-about-deprecated-rest-api | Warning client about deprecated REST API - Stack Overflow
https://stackoverflow.com/questions/750604/freeing-up-a-tcp-ip-port | linux - Freeing up a TCP/IP port? - Stack Overflow

https://www.regular-expressions.info/wordboundaries.html | Regex Tutorial - \b Word Boundaries

https://www.coralnodes.com/amazon-s3-alternatives/#h2_2 | 10 Best Amazon S3 Alternatives in 2020 | Cloud Object Storage Providers
https://www.zaproxy.org/getting-started/ | OWASP ZAP – Getting Started
https://docs.sentry.io/product/releases/ | Releases | Sentry Documentation
https://www.howtographql.com/basics/0-introduction/ | Learn GraphQL Fundamentals with Fullstack Tutorial
https://stackoverflow.com/questions/15823061/how-can-i-reorganize-an-existing-folder-hierarchy-with-carrierwave | ruby on rails 3 - How can I reorganize an existing folder hierarchy with CarrierWave? - Stack Overflow
https://stackoverflow.com/questions/9921085/whats-the-proper-way-to-copy-a-carrierwave-file-from-one-record-to-another | ruby on rails - What's the proper way to copy a carrierwave file from one record to another? - Stack Overflow
https://stackoverflow.com/questions/53368221/write-errors-to-log-file-in-ruby | logging - Write Errors to Log File in Ruby - Stack Overflow
https://blog.bigbinary.com/2017/11/28/ruby-2-5-added-delete_prefix-and-delete_suffix-methods.html | Ruby 2.5 added delete_prefix and delete_suffix methods | BigBinary Blog

https://desktop.arcgis.com/fr/arcmap/10.3/manage-data/using-sql-with-gdbs/what-is-an-srid.htm | Qu'est-ce qu'un SRID ?—Aide | ArcGIS Desktop
https://cutter.re/ | Cutter

http://www.viewsonicglobal.com/public/products_download/user_guide/Display/VG2455_VG2755/VG2455_VG2755_UG_ENG.pdf?pass | VG2455_VG2755_UG_ENG.pdf
https://tools.ietf.org/html/rfc7519 | RFC 7519 - JSON Web Token (JWT)
https://github.com/mbj/mutant/blob/master/docs/nomenclature.md | mutant/nomenclature.md at master · mbj/mutant
https://martinfowler.com/tags/2020.html | 2020
https://stackoverflow.com/questions/9658881/rails-select-unique-values-from-a-column | activerecord - Rails: select unique values from a column - Stack Overflow
https://gist.github.com/jamesyang124/9216074 | Ruby meta programming

https://airflow.apache.org/docs/stable/concepts.html | Concepts — Airflow Documentation
https://www.redhat.com/sysadmin/october-2020-recap | October 2020 top 10 sysadmin how-tos and tutorials | Enable Sysadmin
http://thomasmango.com/2011/09/02/getting-to-know-active-support-callbacks/ | Getting to Know ActiveSupport::Callbacks by Tom Mango

https://github.com/sh6khan/best-practices-rails#reduce-your-routes | sh6khan/best-practices-rails: Just a collection of some of the Rails best practices I have found over the past year since learning Rails
https://stackoverflow.com/questions/********/what-are-d-ts-files-for | typescript - What are *.d.ts files for? - Stack Overflow

https://stackoverflow.com/questions/5345757/in-rails-how-to-see-all-the-path-and-url-methods-added-by-railss-routing/5346350 | In Rails, how to see all the "path" and "url" methods added by Rails's routing? (update: using Rails console) - Stack Overflow

https://apidock.com/ruby/File/dirname/class | dirname (File) - APIdock
https://dev.to/szaszolak/extracting-rails-engine-by-example-vikings-social-media-4014 | Extracting Rails Engine by example - Vikings social media - DEV
https://www.synbioz.com/blog/tech/block-proc-lambda-ruby | Blocks, Proc et Lambda en Ruby
https://python-patterns.guide/ | Python Design Patterns
https://awesome-ruby.com/#-websocket | Awesome Ruby
https://scoutapm.com/blog/how-to-use-lambdas-in-ruby | How to Use Lambdas in Ruby | Scout APM Blog
http://morningcoffee.io/interfaces-in-ruby.html | The Power of Interfaces in Ruby — by Igor Šarčević
https://refactoring.guru/design-patterns/structural-patterns | Structural Design Patterns
https://ckeditor.com/docs/ckeditor5/latest/api/module_engine_model_schema-Schema.html | Class Schema (engine/model/schema~Schema) - CKEditor 5 API docs
https://stryker-mutator.io/docs/ | What is mutation testing? | Stryker Mutator
https://guides.rubyonrails.org/autoloading_and_reloading_constants.html#autoload-paths-and-eager-load-paths | Autoloading and Reloading Constants (Zeitwerk Mode) — Ruby on Rails Guides
https://bcourses.berkeley.edu/courses/1477171 | Software Engineering (Spring 2019)

https://flexport.engineering/approximating-prettier-for-ruby-with-rubocop-8b863bd64dc6 | Approximating “Prettier for Ruby” with RuboCop | by Max Heinritz | Flexport Engineering
https://stackoverflow.com/questions/11043450/vs-dot-vs-double-colon-for-calling-a-method | ruby - . vs :: (dot vs. double-colon) for calling a method - Stack Overflow
https://medium.com/@leo_hetsch/demystifying-singleton-classes-in-ruby-caf3fa4c9d91 | Diving into Ruby Singleton Classes | by Léonard Hetsch | Medium
https://stackoverflow.com/questions/2505067/class-self-idiom-in-ruby | metaclass - class << self idiom in Ruby - Stack Overflow
https://stackoverflow.com/questions/30367487/creating-a-hash-with-values-as-arrays-and-default-value-as-empty-array | ruby - Creating a Hash with values as arrays and default value as empty array - Stack Overflow

https://guides.rubyonrails.org/autoloading_and_reloading_constants.html#eager-loading | Autoloading and Reloading Constants (Zeitwerk Mode) — Ruby on Rails Guides
https://stackoverflow.com/questions/56402093/how-can-i-preload-concerns-in-a-rails-initializer-using-rails-6-zeitwerk | ruby - How can I preload concerns in a rails initializer using Rails 6/Zeitwerk? - Stack Overflow

https://stackoverflow.com/questions/3163641/get-a-class-by-name-in-ruby | Get a class by name in Ruby? - Stack Overflow
https://www.schneems.com/ | Schneems - Programming Practices, Performance, and Pedantry
https://www.jimmycuadra.com/posts/metaprogramming-ruby-class-eval-and-instance-eval/ | Metaprogramming Ruby: class_eval and instance_eval | Jimmy Cuadra
https://stackoverflow.com/questions/40946313/when-is-the-require-necessary-when-using-a-ruby-gem | rubygems - When is the 'require' necessary when using a ruby gem? - Stack Overflow
https://medium.com/rubycademy/requiring-a-file-or-library-in-ruby-29f99e5e2c6a | Requiring a file or library in Ruby | by Mehdi Farsi | RubyCademy | Medium
https://stackoverflow.com/questions/7036290/reading-docs-in-irb | ruby on rails - Reading docs in irb - Stack Overflow
https://codequizzes.wordpress.com/ | Ruby/Rails Programming | A fine WordPress.com site
https://www.justinweiss.com/articles/a-web-server-vs-an-app-server/ | A web server vs. an app server - Justin Weiss
http://juixe.com/techknow/index.php/2007/01/17/reopening-ruby-classes-2/ | Reopening Ruby Classes | Juixe Techknow
https://prograils.com/posts/ruby-methods-differences-load-require-include-extend | Ruby Methods: differences between load, require, include and extend in Ruby. | Prograils

https://stackoverflow.com/questions/5103860/what-does-this-rails-engine-code-mean-config-to-prepare-methodactivate-to-p | spree - What does this Rails Engine code mean: config.to_prepare &method(:activate).to_proc - Stack Overflow
https://fr.wikipedia.org/wiki/Chiffrement_homomorphe#Syst%C3%A8mes_totalement_homomorphes | Chiffrement homomorphe — Wikipédia
https://stackoverflow.com/questions/58856234/accessing-helpers-and-models-from-rails-engine-initializer | Accessing helpers and models from rails engine initializer - Stack Overflow
http://leahneukirchen.org/blog/archive/2007/02/introducing-rack.html | leah blogs: Introducing Rack
https://fr.wikipedia.org/wiki/Common_Gateway_Interface | Common Gateway Interface — Wikipédia
https://blog.capsens.eu/rails-6-boot-sequence-d289b44d2e94 | Rails 6 boot sequence. Have you ever wondered how your Rails… | by Younes SERRAJ | Blog de Capsens

http://maps.unomaha.edu/Peterson/gis/notes/MapProjCoord.html | Map Projections and Coordinate Systems
https://stackoverflow.com/questions/35631667/rename-existing-rails-model-and-add-namespace | activerecord - Rename existing rails model and add namespace - Stack Overflow
http://teotti.com/reengineer-legacy-rails-applications/ | Reengineer legacy Rails applications
http://teotti.com/component-based-rails-architecture-primer/ | A component based Rails architecture primer
https://blog.acolyer.org/2020/10/19/the-case-for-a-learned-sorting-algorithm/ | The case for a learned sorting algorithm | the morning paper

https://code.tutsplus.com/articles/rake-101--cms-26215 | Rake 101
https://gis.stackexchange.com/questions/279469/converting-shapefile-to-geojson-both-ogr-and-mapshaper-converters-give-errors | Converting Shapefile to GeoJSON. Both OGR and Mapshaper converters give errors - Geographic Information Systems Stack Exchange
https://stackoverflow.com/questions/2390017/ruby-on-rails-include-on-a-polymorphic-association-with-submodels | Ruby on Rails: :include on a polymorphic association with submodels - Stack Overflow
https://thoughtbot.com/upcase/clean-code | Write Clean Code | Refactoring, Rails Style Guide, and More
https://github.com/github/scientist | github/scientist: A Ruby library for carefully refactoring critical paths.
https://github.com/testdouble/suture | testdouble/suture: 🏥 A Ruby gem that helps you refactor your legacy code
https://medium.com/@gmalette/short-rant-about-activesupport-concern-312d9ffad88b | Short Rant about ActiveSupport::Concern | by Guillaume Malette | Medium
https://medium.com/ruby-on-rails/patterns-for-successful-rails-engines-a7dae3db6921 | Patterns for Successful Rails Engines | by Alessandro Desantis | Ruby on Rails | Medium

https://stackoverflow.com/questions/36151981/local-hostnames-for-docker-containers | Local hostnames for Docker containers - Stack Overflow
https://tools.ietf.org/html/rfc7807#page-3 | RFC 7807 - Problem Details for HTTP APIs
https://blog.newrelic.com/engineering/weird-ruby-2-rescue-interrupt-ensure/ | Weird Ruby: Rescue, Else, and Ensure

https://reactpatterns.com/ | React Patterns

https://github.com/sh6khan/best-practices-rails#reduce-your-routes | sh6khan/best-practices-rails: Just a collection of some of the Rails best practices I have found over the past year since learning Rails
https://stackoverflow.com/questions/29153928/restfully-fetching-by-attribute | rest - RESTfully Fetching by Attribute - Stack Overflow
https://letsencrypt.org/fr/docs/challenge-types/ | Types de défis - Let's Encrypt - Certificats SSL/TLS gratuits
https://stackoverflow.com/questions/4297975/find-resource-by-unique-attribute | api - find resource by unique attribute - Stack Overflow
https://medium.com/@apneadiving/why-i-do-not-use-strong-parameters-in-rails-e3bd07fcda1d | Why I do not use strong parameters in Rails | by benjamin roth | Medium
https://stackoverflow.com/questions/30009620/rails-4-require-and-permit-multiple | ruby - Rails 4 Require and Permit Multiple - Stack Overflow
https://stackoverflow.com/questions/38432270/rails-json-api-parameter-validation-error-responses | ruby - Rails JSON API parameter validation & error responses - Stack Overflow
https://stackoverflow.com/questions/29900796/ruby-on-rails-errorassociationrelation-is-not-an-activemodel-compatible-object | Ruby on Rails error:AssociationRelation is not an ActiveModel-compatible object. It must implement :to_partial_path - Stack Overflow
https://stackoverflow.com/questions/328525/rails-how-can-i-set-default-values-in-activerecord | Rails: How can I set default values in ActiveRecord? - Stack Overflow
https://dev.to/torianne02/protecting-your-api-keys-rails-355k | Protecting Your API Keys - Rails - DEV
https://stackoverflow.com/questions/29195489/rails-4-1-environment-variables-not-reloading | bash - Rails 4.1 environment variables not reloading - Stack Overflow

https://daringfireball.net/linked/2020/10/12/spotify-songshift | Daring Fireball: Spotify, Ever the Fans of Openness
https://apidock.com/rails/ActiveSupport/Dependencies/Loadable/require_dependency | require_dependency (ActiveSupport::Dependencies::Loadable) - APIdock
https://martinfowler.com/eaaCatalog/index.html | Catalog of Patterns of Enterprise Application Architecture
https://martinfowler.com/articles/micro-frontends.html | Micro Frontends

https://www.aosabook.org/en/asterisk.html | The Architecture of Open Source Applications: Asterisk
https://humanloop.com/ | Train and deploy NLP — Humanloop

https://stackoverflow.com/questions/22599172/cant-ping-aws-rds-endpoint | mysql - Can't ping AWS RDS endpoint - Stack Overflow
https://marker.io/blog/bug-report-template/#jira | 9 Bug Report Template Examples: Software Testing Workflows
https://docs.python.org/release/3.9.0/whatsnew/3.9.html | What’s New In Python 3.9 — Python 3.9.0 documentation

https://developer.mozilla.org/fr/docs/Web/JavaScript/Reference/Op%C3%A9rateurs/L_op%C3%A9rateur_this | L'opérateur this - JavaScript | MDN
http://gordonbrander.com/pattern/learn-to-learn/ | Learn to learn — Gordon Brander
https://stackoverflow.com/questions/6604749/what-reason-is-there-to-use-null-instead-of-undefined-in-javascript | What reason is there to use null instead of undefined in JavaScript? - Stack Overflow
https://www.vinaysahni.com/best-practices-for-a-pragmatic-restful-api | Best Practices for Designing a Pragmatic RESTful API | Vinay Sahni

https://github.com/semlinker/awesome-typescript | semlinker/awesome-typescript: A collection of awesome TypeScript resources for client-side and server-side development
https://stackoverflow.com/questions/57768114/postgresql-read-only-user | database - PostgreSQL: read only user - Stack Overflow
https://stackoverflow.com/questions/51272255/how-to-use-filereader-in-react | reactjs - How to use FileReader in React? - Stack Overflow
https://medium.com/@derek_dyer/ruby-simpledelegator-methods-c799e07ae108 | Ruby | SimpleDelegator Methods. There are a good number of posts about… | by Derek Dyer | Medium

https://react-bootstrap.github.io/components/jumbotron/ | react-bootstrap.github.io/components/list-group/
https://stackoverflow.com/questions/33973648/react-this-is-undefined-inside-a-component-function | javascript - React: "this" is undefined inside a component function - Stack Overflow
https://getbootstrap.com/docs/4.4/utilities/flex/ | Flex · Bootstrap
https://v4-alpha.getbootstrap.com/utilities/spacing/ | Spacing · Bootstrap
https://getbootstrap.com/docs/4.4/layout/utilities-for-layout/ | Utilities for layout · Bootstrap
https://lodash.com/docs/#find | Lodash Documentation
https://getbootstrap.com/docs/4.0/components/card/ | Cards · Bootstrap

https://stackoverflow.com/questions/28329382/understanding-unique-keys-for-array-children-in-react-js | javascript - Understanding unique keys for array children in React.js - Stack Overflow

https://blog.danslimmon.com/2019/07/15/do-nothing-scripting-the-key-to-gradual-automation/ | Do-nothing scripting: the key to gradual automation – Dan Slimmon
https://stackoverflow.com/questions/38117516/rest-api-sub-resources-data-to-return | restful architecture - REST API sub resources, data to return? - Stack Overflow
https://medium.com/@wajeeh.ahsan/rails-serialization-active-model-serializer-7af16ee9a466 | Rails Serialization (Active Model Serializer) | by Wajeeh Ahsan | Medium
https://medium.com/@raj.b.stretz/active-model-serializer-vs-fast-json-api-serializer-8338b939f01f | Active Model Serializer vs Fast JSON API Serializer | by Rajaa Boulassouak | Medium
https://stackoverflow.com/questions/19919780/receiving-post-data-in-rails-4-and-reading-request-body | ruby - Receiving POST data in Rails 4 and reading request.body - Stack Overflow
http://html5doctor.com/howto-subheadings/ | How to mark up subheadings, subtitles, alternative titles and taglines | HTML5 Doctor
https://stackoverflow.com/questions/154059/how-can-i-check-for-an-empty-undefined-null-string-in-javascript?page=1&tab=votes#tab-top | How can I check for an empty/undefined/null string in JavaScript? - Stack Overflow
https://stackoverflow.com/questions/1226714/how-to-get-the-browser-to-navigate-to-url-in-javascript | How to get the browser to navigate to URL in JavaScript - Stack Overflow
https://stackoverflow.com/questions/50193227/basic-react-form-submit-refreshes-entire-page | javascript - Basic React form submit refreshes entire page - Stack Overflow
https://gomakethings.com/converting-strings-to-numbers-with-vanilla-javascript/ | Converting strings to numbers with vanilla JavaScript | Go Make Things

https://serverfault.com/questions/744147/can-someone-using-the-same-dns-server-as-me-hijack-my-domains | Can someone using the same DNS server as me hijack my domains? - Server Fault
https://tools.ietf.org/html/rfc7946 | RFC 7946 - The GeoJSON Format
http://jfod.cnam.fr/brazil/brazil-18-Aug-09/html/build.html | The brazil Build system
https://www.nature.com/articles/s41586-020-2649-2 | Array programming with NumPy | Nature
https://stackoverflow.com/questions/17622106/variable-interpolation-in-the-shell | bash - Variable interpolation in the shell - Stack Overflow

https://github.com/donnemartin/awesome-aws | donnemartin/awesome-aws: A curated list of awesome Amazon Web Services (AWS) libraries, open source repos, guides, blogs, and other resources. Featuring the Fiery Meter of AWSome.
https://www.ssls.com/knowledgebase/how-to-install-an-ssl-certificate-on-a-nginx-server/ | How to install an SSL certificate on a NGINX server – HelpDesk | SSLs.com
https://dockerswarm.rocks/ | Docker Swarm Rocks
file:///Users/<USER>/Downloads/Nigel%20Poulton%20-%20Docker%20Deep%20Dive-Independently%20published%20(2018-02-11).pdf | Docker Deep Dive - Nigel Poulton - Docker Deep Dive-Independently published (2018-02-11).pdf
https://www.it-wars.com/posts/virtualisation/docker-swarm-cluster-aws-amazon-web-services/ | Déployer un Cluster Docker Swarm sur Amazon Web Services - AWS - ITwars | Vincent RABAH

https://superuser.com/questions/738817/how-do-i-convert-a-certificate-to-a-private-key | ssl - How do I convert a certificate to a private key? - Super User
https://stackoverflow.com/questions/42634626/postgresql-upgrade-on-amazon-rds-blocked-by-postgis-version | PostgreSQL upgrade on Amazon RDS blocked by PostGIS version - Stack Overflow
https://stackoverflow.com/questions/21799956/using-psql-how-do-i-list-extensions-installed-in-a-database/21799995 | postgresql - Using psql how do I list extensions installed in a database? - Stack Overflow

https://www.jetbrains.com/fr-fr/datagrip/features/executing.html | Exécution de requêtes : mode lecture seule, historique, plan d'exécution, journal SQL - Fonctionnalités | DataGrip
https://bertwagner.com/2019/08/06/5-things-you-need-to-know-when-reading-sql-server-execution-plans/ | 5 Things You Need To Know When Reading SQL Server Execution Plans - Data with Bert
http://ithare.com/oltp-db-optimizations-101-thinking-in-terms-of-execution-plans/ | OLTP DB Optimizations 101 - Thinking in Terms of Execution Plans - IT Hare on Soft.ware

https://www.networkcomputing.com/networking/six-benefits-ipv6 | Six Benefits Of IPv6 | Network Computing
https://www.storyblok.com/tp/rspec-api-documentation | How we use RSpec to automatically generate API documentations - Storyblok
https://blog.eq8.eu/article/memoization-caching-and-sql-optimization-in-ruby-on-rails.html | Memoization, Caching and SQL query optimization in Ruby on Rails

https://www.attejuvonen.fi/money-out-of-thin-air/ | Atte Juvonen - Banks create money out of thin air, but it's less impressive than it sounds
https://github.com/ssllabs/ssllabs-scan | ssllabs/ssllabs-scan: A command-line reference-implementation client for SSL Labs APIs, designed for automated and/or bulk testing.
https://www.howtoforge.com/ssl-perfect-forward-secrecy-in-nginx-webserver | Implementing SSL Perfect Forward Secrecy in NGINX Web-Server
https://support.rackspace.com/how-to/checking-linux-file-permissions-with-ls/ | Check Linux file permissions with ls
https://www.kinamo.be/fr/support/faq/commandes-openssl-utiles | OpenSSL - commandes utiles
https://learning.oreilly.com/library/view/beyond-the-twelve-factor/*************/ | Beyond the Twelve-Factor App
https://unix.stackexchange.com/questions/377891/copy-whole-folder-from-source-to-destination-and-remove-extra-files-or-folder-fr | linux - Copy whole folder from source to destination and remove extra files or folder from destination - Unix & Linux Stack Exchange

https://gist.github.com/iamatypeofwalrus/9237651 | Print stacktrace without raising and Exception in Ruby and/or Rails
https://stackify.com/rails-logger-and-rails-logging-best-practices/ | Rails Logger and Rails Logging Best Practices
http://www.r-5.org/files/books/computers/languages/ruby/rails/Mike_Clark-Advanced_Rails_Recipes-EN.pdf | Advanced Rails Recipes - Mike_Clark-Advanced_Rails_Recipes-EN.pdf

https://stackoverflow.com/questions/11563319/git-rebase-basics | git rebase basics - Stack Overflow
https://news.ycombinator.com/item?id=19877811 | Git rebase in depth | Hacker News
https://stackoverflow.com/questions/16329776/how-to-keep-a-git-branch-in-sync-with-master | How to keep a git branch in sync with master - Stack Overflow

https://benediktdeicke.com/2017/09/sending-webhooks-with-rails/ | Don't call us, we'll call you: Sending webhooks with Rails | Benedikt Deicke

https://gist.github.com/leonardofed/bbf6459ad154ad5215d354f3825435dc | A curated list of AWS resources to prepare for the AWS Certifications
https://www.python.org/dev/peps/pep-0544/ | PEP 544 -- Protocols: Structural subtyping (static duck typing) | Python.org
https://en.wikipedia.org/wiki/Structural_type_system | Structural type system - Wikipedia
https://www.rubyguides.com/2015/06/ruby-regex/ | Ruby Regular Expressions (Complete Tutorial)
https://fr.wikipedia.org/wiki/X.509 | X.509 — Wikipédia
https://stackoverflow.com/questions/1722181/how-to-determine-certificate-type-from-file | openssl - How to determine certificate type from file - Stack Overflow
https://news.ycombinator.com/item?id=23031628 | Mozilla SOPS with KMS and Git is underrated (2019) | Hacker News
https://blog.gruntwork.io/a-comprehensive-guide-to-managing-secrets-in-your-terraform-code-1d586955ace1#3073 | A comprehensive guide to managing secrets in your Terraform code | by Yevgeniy Brikman | Jul, 2020 | Gruntwork
https://serversforhackers.com/c/redirect-http-to-https-nginx | Redirect HTTP to HTTPS in Nginx | Servers for Hackers
http://www.plainenglish.co.uk/how-to-write-in-plain-english.html | How to write in plain English

https://www.alec.fyi/dorking-how-to-find-anything-on-the-internet.html#webpages | dorking (how to find anything on the Internet) - for your information
https://blog.danslimmon.com/2019/07/15/do-nothing-scripting-the-key-to-gradual-automation/ | Do-nothing scripting: the key to gradual automation – Dan Slimmon

https://github.com/amalucelli/nginx-config-reverse-proxy/blob/master/sites-available/ssl-example.com | nginx-config-reverse-proxy/ssl-example.com at master · amalucelli/nginx-config-reverse-proxy
https://www.linode.com/docs/web-servers/nginx/how-to-configure-nginx/ | How to Configure NGINX | Linode
https://superuser.com/questions/719672/how-to-send-an-http-request-for-a-certain-domain-name-to-a-specific-ip-address | dns - How to send an HTTP request for a certain domain name to a specific IP address? - Super User
https://superuser.com/questions/620121/what-is-the-difference-between-a-certificate-and-a-key-with-respect-to-ssl | What is the difference between a certificate and a key with respect to SSL? - Super User
https://code-maven.com/install-and-configure-nginx-using-ansible | Install and configure Nginx using Ansible

https://github.com/h5bp/server-configs-nginx | h5bp/server-configs-nginx: Nginx HTTP server boilerplate configs
https://serverfault.com/questions/527630/what-is-the-different-usages-for-sites-available-vs-the-conf-d-directory-for-ngi | linux - What is the different usages for sites-available vs the conf.d directory for nginx - Server Fault
https://www.keycdn.com/support/nginx-virtual-host | How to Create an Nginx Virtual Host (AKA Server Blocks) - KeyCDN Support
http://rubybunny.info/articles/getting_started.html | Getting Started with Ruby and RabbitMQ with Bunny

https://makandracards.com/makandra/32259-rspec-tagging-examples-and-example-groups | RSpec: Tagging examples and example groups - makandra dev
https://stackoverflow.com/questions/29952513/rspec-3-expect-object-do-something-to-not-raise-a-particular-kind-of-error | ruby - RSpec 3: expect object.do_something to NOT raise a particular kind of error - Stack Overflow
https://stackoverflow.com/questions/2249310/if-name-main-equivalent-in-ruby | python - `if __name__ == '__main__'` equivalent in Ruby - Stack Overflow
https://www.nginx.com/blog/using-packer-and-terraform-for-high-availability-of-nginx-plus-on-google-cloud-engine/ | Using Packer and Terraform for High Availability of NGINX Plus on Google Compute Engine - NGINX

https://serverfault.com/questions/327708/how-browsers-handle-multiple-ips | domain name system - How browsers handle multiple IPs - Server Fault
https://webmasters.stackexchange.com/questions/10927/using-multiple-a-records-for-my-domain-do-web-browsers-ever-try-more-than-one | dns - Using multiple A-records for my domain - do web browsers ever try more than one? - Webmasters Stack Exchange
https://medium.com/commutatus/how-to-configure-a-reverse-proxy-in-aws-b164de91176e | How to configure a reverse proxy in AWS? | by Chandu | Commutatus | Medium

https://fr.wikipedia.org/wiki/M%C3%A9thode_Borda | Méthode Borda — Wikipédia
https://fr.wikipedia.org/wiki/M%C3%A9thode_de_Condorcet | Méthode de Condorcet — Wikipédia
https://en.wikipedia.org/wiki/Approval_voting | Approval voting - Wikipedia

http://citeseerx.ist.psu.edu/viewdoc/download?doi=10.1.1.713.7073&rep=rep1&type=pdf | Fairness on the Web: Alternatives to the Power Law - download

https://www.baeldung.com/spring-boot-12-factor | Twelve-Factor Methodology in a Spring Boot Microservice | Baeldung
https://news.ycombinator.com/item?id=18172689 | 12 Factor CLI Apps | Hacker News
https://milianw.de/blog/heaptrack-a-heap-memory-profiler-for-linux.html | Heaptrack - A Heap Memory Profiler for Linux - Milian Wolff
https://samsaffron.com/archive/2019/10/08/debugging-unmanaged-and-hidden-memory-leaks-in-ruby | Debugging hidden memory leaks in Ruby
https://bl.ocks.org/wvengen/f1097651c238b2f7f11d | Ruby memory analysis over time - bl.ocks.org

https://www.thoughtworks.com/radar/techniques/lightweight-architecture-decision-records | Lightweight Architecture Decision Records | Technology Radar | ThoughtWorks
http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions | Blog | Documenting Architecture Decisions | Relevance
https://documentation.divio.com/ | The documentation system — Documentation system documentation
https://www.rubyguides.com/2017/01/read-binary-data/ | Packing & Unpacking: A Guide to Reading Binary Data in Ruby
https://stackoverflow.com/questions/626/when-to-use-lambda-when-to-use-proc-new | ruby - When to use lambda, when to use Proc.new? - Stack Overflow
