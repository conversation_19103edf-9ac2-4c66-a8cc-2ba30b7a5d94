We weren’t able to reproduce the DNS issue on our end, and our dashboard/metrics don’t show any clear signs of downtime around that time. If they’d like to dig deeper into it, they can try running a few DNS resolution commands on their end and reporting back the results.

1. Click the **Start menu** (Windows icon in the bottom-left corner)  
2. Type **"PowerShell"**, then click on **Windows PowerShell** when it appears  
3. In the blue PowerShell window that opens, type the following command and press Enter:
   ```
   Resolve-DnsName -Name uk -Type NS
   ```

4. Then, type this command and press Enter:
   ```
   Resolve-DnsName -Name co.uk -Type NS
   ```

5. Continue with the next level of the domain:
   ```
   Resolve-DnsName -Name nwl.co.uk -Type NS
   ```

6. Finally, check the full domain:
   ```
   Resolve-DnsName -Name communityhub.nwl.co.uk
   ```

Unfortunately, I can't fully guarantee that the commands are correct or will return the necessary information, since I'm not using Windows myself.

If they're on a Unix-based system, they could run the following command in the terminal:
```
dig +trace communityhub.nwl.co.uk
```
but that's probably unlikely.
