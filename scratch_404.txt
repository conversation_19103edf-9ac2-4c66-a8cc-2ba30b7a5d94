[TAN-2655] Remove "Rename" tooltip from `ReportTitle` component

The renaming mechanism stopped working when <PERSON><PERSON><PERSON> was replaced by the new Tooltip component in June. The issue is that Tooltip is stealing focus the focus from the input it wraps. The current implementation of Tooltip ties the tooltip visibility to its focus state, so there's no way to display it without removing the focus from the input.

We went for the easy solution and simply removed the tooltip because the current focus behavior of Tooltip is part of our recent accessibility improvements and we want to make sure that we don't regress on that.

