# General
- Run tools and rails command in docker instead of running it on the host. You can use: `docker compose exec web <cmd>`. If it fails because the web container is not running, you can use `docker compose run --rm web <cmd>` instead.
- Use `docker compose exec web <cmd>` preferentially instead of `docker compose run --rm web <cmd>` for better efficiency by using existing running containers.
- Keep things simple and to a bare minimum unless told otherwise.
- Avoid testing things in the Rails console when possible.
- You can use gh CLI tool directly (not in docker) to update PR descriptions.
- When the user's prompt starts with "TODO", extract the text that follows "TODO" and add it as a new item to the TODO section of the current PR description using `gh pr edit --body`. Before adding the item, rephrase it to be a clear, grammatically correct, and actionable task. (Maintain the original intent while improving clarity)
- To avoid an interactive pager when using `gh pr view` or similar, pipe the output to `cat`: `gh pr view | cat` shows the full content directly in the terminal.
- User prefers simplified method signatures when possible.
- User is interested in creating abstractions to simplify the creation of parameterized scopes in Rails models.
- User prefers using **params (double splat) instead of regular params for finder class method signatures.

# RSpec
- You can run multiple RSpec test files at once by specifying multiple file paths in a single command.
- Verify Rails association parameters (like 'source') through testing rather than assumptions.
- Association tests for models should be placed in the main model's spec file (e.g., idea_spec.rb) rather than in the join model's spec file.
- User prefers not to create specific binstubs for RSpec functionality like rspec-failures or rspec-all.
- User prefers splitting test cases with multiple assertions into separate, focused tests for better clarity and maintainability.

# Code Style
- User prefers data classes (like CopySummary) to have minimal methods and be designed as simple data containers for storing results.
- User prefers simpler, more concise documentation style rather than detailed explanations.
- User prefers refactoring participation method checks to use interface methods (like capability methods) instead of directly checking participation_method.class.method_str for better abstraction.
- User prefers method name 'supported_reaction_modes' over 'reaction_modes' for methods that return a subset of available reaction modes.
- User prefers interface-based design for reaction mode support using supported_reaction_modes method instead of hardcoded checks.

# ActiveJob
- Jobs::Tracker tracks progress of ActiveJob trees with total/progress/error counts as estimates or exact values depending on job implementation, and should be updated via TrackableJob methods rather than directly.
- Jobs::Tracker documentation should explain it tracks ActiveJob trees with total/progress/error counts as estimates or exact values depending on job implementation, and should be updated via TrackableJob methods rather than directly.
- Jobs::Tracker progress semantics changed: progress now represents total work done, error_count represents failed portion of progress, so error_count <= progress <= total (previously progress + error_count represented total work done).

# XLSX Exports
- XLSX exports should handle columns differently: XlsxService (bulk exports) should always include all columns for consistency, while InputSheetGenerator (phase-specific exports) should tailor columns to the participation method.
- User prefers tests that cover default/standard columns comprehensively rather than being specific to individual features like 'unsure' column or Common Ground.
- For XLSX exports, write comprehensive tests that check all columns, not just individual uncertain columns.
- For XLSX exports, write comprehensive tests covering all supported reaction columns.

# File Uploads
- In the unified file model implementation, a single upload should be able to associated with multiple resources (many-to-many relationship).
- The files API endpoint should be web_api/v1/files/... (direct path) rather than nested under container resources like /projects/{id}/files.
- User prefers hard delete (permanent deletion) over soft delete for file deletion endpoints by default.

# Anki Flashcards
- For Anki flashcard generation, exclude deck name from output format and include support for cloze deletion cards.
- User prefers cloze cards over basic question-answer cards when possible for Anki flashcard generation.
- User prefers table cards to use cloze format rather than traditional question-answer format for tabular data in Anki flashcards.

# API Tests
- For API acceptance tests, verify HTTP status codes, use appropriate test data setup with both positive and negative cases, and follow existing acceptance test patterns in the codebase rather than testing finder logic directly.
`