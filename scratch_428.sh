docker node ls | grep Down | awk '{print $1}' | xargs -n25 docker node rm

# same as above but only call `xargs -n25 docker node rm` if there are Down nodes
down_nodes=$(docker node ls | grep Down | awk '{print $1}')

down_nodes=$(docker node ls | grep Down | awk '{print $1}') &&  [ -n "$down_nodes" ] && echo "$down_nodes" | xargs -n25 docker node rm

# same in 2 lines with backslashes
down_nodes=$(docker node ls | grep Down | awk '{print $1}') && \
[ -n "$down_nodes" ] && echo "$down_nodes" | xargs -n25 docker node rm || echo "No down nodes found."

