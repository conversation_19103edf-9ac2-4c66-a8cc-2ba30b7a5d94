fields @timestamp, payload.tenant_host
| stats count(*) as requests by payload.tenant_host
| sort requests desc

# average requests duration per tenant (tenant_host must be present)

filter payload.tenant_host is not null
| stats avg(payload.duration) as avg_duration by payload.tenant_host
| sort avg_duration desc


stats avg(payload.duration_ms) as avg_duration by payload.tenant_host
| filter ispresent(payload.tenant_host)
| filter ispresent(payload.duration_ms)
| sort avg_duration desc

fields payload.tenant_host, payload.duration_ms
| limit 10

stats avg(duration_ms) as avg_duration by payload.tenant_host, payload.method
| filter ispresent(payload.tenant_host) and ispresent(payload.method)
| sort avg_duration desc

filter payload.tenant_host = 'praatmee.goeree-overflakkee.nl'

stats sum(duration_ms) as avg_duration by payload.tenant_host, payload.method, payload.action
| filter ispresent(payload.tenant_host) and ispresent(payload.method) and ispresent(payload.action)
| sort avg_duration desc


stats sum(duration_ms) as avg_duration by payload.tenant_host, name, payload.method, payload.action
| filter ispresent(payload.tenant_host) and ispresent(payload.method) and ispresent(payload.action) and ispresent(name)
| sort avg_duration desc


filter payload.tenant_host = 'praatmee.goeree-overflakkee.nl'
| filter name = 'WebApi::V1::GroupsController'
| filter payload.method = 'GET'
| sort duration_ms desc

# parse the params from @message
fields @timestamp, jsonParse(@message).payload.tenant_host
