# status of last command
echo $?


# using gh to get the pr between master and production (get both title and number)
gh pr list --base production --head master --state merged --json number,title --jq '.[0]'

gh pr list --state open --base master --head "TAN-4085-event-attendance-limit" --json url,title --jq '.[0]'


# get all titles
# jq '|' means that we want to apply the following command to each element of the array
# another shorter expression is: jq '.[].title'
gh pr list --state all --search "\"2025-07-03\" in:title"  --json title --jq '.[].title'