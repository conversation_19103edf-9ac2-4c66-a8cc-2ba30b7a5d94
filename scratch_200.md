In-product project copy bug. The copy fails for some ideation projects because of a bug in CustomField#title_multiloc.

In:
def title_multiloc
    if code == 'ideation_section1'
        project = resource.participation_context
        phase = TimelineService.new.current_or_last_can_contain_ideas_phase project
        input_term = phase&.input_term || Phase::DEFAULT_INPUT_TERM

      key = "custom_forms.categories.main_content.#{input_term}.title"
      MultilocService.new.i18n_to_multiloc key
    else
      super
    end
end

resource.participation_context returns a phase for some projects, while the current implementation expects a project (TimelineService.new.current_or_last_can_contain_ideas_phase fails if it receives a phase).

It's not clear to me if title_multiloc should be fixed to handle that case, or if the bug is actually that resource.participation_context should always return a project (for ideation).