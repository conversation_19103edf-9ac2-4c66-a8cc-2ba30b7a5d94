# {"controller":"AdminApi::ProjectsController","action":"template_export","params":{"project":{"anonymize_users":true,"include_ideas":true,"new_slug":"project-copy-test-ideation-2","shift_timestamps":0},"tenant_id":"9f772efa-16d4-449c-a49a-f4bc57797a10","id":"e3169bc5-6bb6-4c6b-955e-6c47f12d56dd"},"format":"JSON","method":"GET","path":"/admin_api/projects/e3169bc5-6bb6-4c6b-955e-6c47f12d56dd/template_export","status":200,"view_runtime":30.35,"db_runtime":292.99,"allocations":569506,"status_message":"OK"}}

project = Project.find("e3169bc5-6bb6-4c6b-955e-6c47f12d56dd")
options = { include_ideas: true, anonymize_users: true, new_slug: 'project-copy-test-ideation-2', shift_timestamps: 0 }
template = ProjectCopyService.new.export(project, **options)


ProjectCopyService.new.import(template)