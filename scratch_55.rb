pp = PricingPlan.find_by(name: 'essential')
pp.settings.except!("clustering")
pp.save!

# #<PricingPlan id: "a979fdaa-2b85-49d7-af51-c072d83edf1c", name: "standard", settings: {"core"=>{"allowed"=>true, "enabled"=>true}, "maps"=>{"allowed"=>true, "enabled"=>true}, "pages"=>{"allowed"=>true, "enabled"=>true}, "polls"=>{"allowed"=>true, "enabled"=>true}, "surveys"=>{"allowed"=>true, "enabled"=>true}, "widgets"=>{"allowed"=>true, "enabled"=>true}, "fragments"=>{"allowed"=>false, "enabled"=>false}, "redirects"=>{"allowed"=>false, "enabled"=>false}, "workshops"=>{"allowed"=>true, "enabled"=>true}, "clustering"=>{"allowed"=>false, "enabled"=>false}, "moderation"=>{"allowed"=>true, "enabled"=>true}, "custom_maps"=>{"allowed"=>true, "enabled"=>true}, "initiatives"=>{"allowed"=>true, "enabled"=>true}, "google_login"=>{"allowed"=>true, "enabled"=>false}, "smart_groups"=>{"allowed"=>true, "enabled"=>true}, "verification"=>{"allowed"=>false, "enabled"=>false}, "volunteering"=>{"allowed"=>true, "enabled"=>true}, "custom_topics"=>{"allowed"=>true, "enabled"=>true}, "events_widget"=>{"allowed"=>true, "enabled"=>false}, "similar_ideas"=>{"allowed"=>false, "enabled"=>false}, "azure_ad_login"=>{"allowed"=>false, "enabled"=>false}, "facebook_login"=>{"allowed"=>true, "enabled"=>false}, "manual_tagging"=>{"allowed"=>false, "enabled"=>false}, "password_login"=>{"allowed"=>true, "enabled"=>true}, "manual_emailing"=>{"allowed"=>true, "enabled"=>true}, "project_folders"=>{"allowed"=>true, "enabled"=>true}, "project_reports"=>{"allowed"=>true, "enabled"=>true}, "idea_custom_copy"=>{"allowed"=>true, "enabled"=>true}, "private_projects"=>{"allowed"=>true, "enabled"=>true}, "typeform_surveys"=>{"allowed"=>true, "enabled"=>true}, "automatic_tagging"=>{"allowed"=>false, "enabled"=>false}, "user_confirmation"=>{"allowed"=>true, "enabled"=>true}, "disable_downvoting"=>{"allowed"=>true, "enabled"=>true}, "idea_author_change"=>{"allowed"=>true, "enabled"=>true}, "idea_custom_fields"=>{"allowed"=>true, "enabled"=>true}, "user_custom_fields"=>{"allowed"=>true, "enabled"=>true}, "customizable_navbar"=>{"allowed"=>false, "enabled"=>false}, "franceconnect_login"=>{"allowed"=>false, "enabled"=>false}, "geographic_dashboard"=>{"allowed"=>false, "enabled"=>false}, "google_forms_surveys"=>{"allowed"=>true, "enabled"=>true}, "granular_permissions"=>{"allowed"=>true, "enabled"=>true}, "insights_manual_flow"=>{"allowed"=>true, "enabled"=>true}, "machine_translations"=>{"allowed"=>true, "enabled"=>false}, "surveymonkey_surveys"=>{"allowed"=>false, "enabled"=>false}, "admin_project_templates"=>{"allowed"=>true, "enabled"=>true}, "ideaflow_social_sharing"=>{"allowed"=>true, "enabled"=>true}, "participatory_budgeting"=>{"allowed"=>true, "enabled"=>true}, "integration_onze_stad_app"=>{"allowed"=>false, "enabled"=>false}, "automated_emailing_control"=>{"allowed"=>true, "enabled"=>true}, "customizable_homepage_banner"=>{"allowed"=>true, "enabled"=>true}, "initiativeflow_social_sharing"=>{"allowed"=>true, "enabled"=>true}}>