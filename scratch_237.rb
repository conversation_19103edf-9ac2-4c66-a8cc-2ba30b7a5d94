require 'net/http'
require 'uri'
require 'json'
require 'concurrent'

uri = URI.parse('https://demo.stg.citizenlab.co/web_api/v1/user_token')
request = Net::HTTP::Post.new(uri)
request.content_type = 'application/json'

request.body = JSON.dump({
                           'auth' => {
                             'email' => '<EMAIL>',
                             'password' => 'bad-password',
                             'remember_me' => false
                           }
                         })

pool = Concurrent::FixedThreadPool.new(25)

250.times do
  pool.post do
    response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
      http.request(request)
    end

    print '.'

    if response.code != '404'
      puts
      puts "[Thread##{Thread.current.object_id}] #{response.code}"
      puts "[Thread##{Thread.current.object_id}] #{response.body}"
    end
  end
end

pool.shutdown
pool.wait_for_termination
