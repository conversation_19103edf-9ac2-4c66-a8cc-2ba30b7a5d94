import http from 'http';

const client_id = "d921e6b2-7dee-4d88-8013-0699e7621e34";
const client_secret = "DZoTXklI21Gf4btwA62cDotcDA56roQIbYP8yrYOSo3K5vNJjbmRQJ5iVrEb75VrKXU";

const base_url = "https://demo.stg.citizenlab.co/api/v2";

// same using http
// const options = {
//     hostname: 'demo.stg.citizenlab.co',
//     path: '/api/v2/authenticate',
//     method: 'POST',
//     headers: {
//         'Content-Type': 'application/json'
//     }
// };

const https = require('https');

function getAccessToken() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'demo.stg.citizenlab.co',
            path: '/api/v2/authenticate',
            method: 'POST',
            headers: {'Content-Type': 'application/json'}
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    const accessToken = response.access_token; // Assuming the access token is in the response JSON
                    resolve(accessToken);
                } catch (error) {
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        // Optionally, you can send any request body if needed
        // req.write(JSON.stringify({ key: 'value' }));

        req.end();
    });
}

// Example usage
getAccessToken()
    .then((accessToken) => {
        console.log('Access Token:', accessToken);
        // Store the access token in a variable or use it as needed
    })
    .catch((error) => {
        console.error('Error getting access token:', error);
    });
