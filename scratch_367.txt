Meeting description: As we plan to divide our current Europe production cluster into smaller clusters, we want to discuss and think together to determine the best way to split it based on geographical regions or other criteria. In this discussion, it's important to consider the current market shares and future prospects of the different regions.

<PERSON>'s answer: Hey, async would be my preferred option, but I can do a short meeting if necessary. What are the questions that you have for me specifically?

Questions for Anne:
We are currently considering splitting the Europe cluster in 3. Ideally, the number of tenants per cluster should be balanced, at least in the short and mid-term (say the next 2-3 years).

- What do you think would be a good split by region? One current option on the table is: France-Belgium, DACH-Netherlands, and Nordics. Do you have any thoughts on this from a sales perspective? Does this align well with the current market and prospects?
- The previous option does not have a cluster for the South of Europe. Do you think that's okay, knowing that we could always add a cluster later on if needed?
- Are you aware of any constraints or regulations regarding the storage of data that could impact the choice of regions?
- Another option that we are contemplating is to have a cluster dedicated to demo (internal demos - not demo platform used by customers) and test platforms as they represent a big chunk of the Europe cluster (numbers coming...). That would leave us with 2 "production" clusters. Any thoughts on this option?


We're currently looking into splitting the Europe cluster into three separate clusters. Our goal is to keep the number of tenants roughly balanced across these clusters, at least in the short and mid-term (say the next 2-3 years).

Here are a few points for discussion:

What are your thoughts on how we should divide the regions? One option we’re considering is splitting them into France-Belgium, DACH-Netherlands, and the Nordics. Does this make sense from a sales perspective? Does it fit with our current market and future prospects?
We haven’t included a cluster for Southern Europe in this option. Do you think this is a problem, given that we could add a cluster later if needed?
Are there any regulations or constraints about data storage that might affect our choice of regions?
Another idea we’re exploring is creating a dedicated cluster for internal demos and testing platforms. This would leave us with two clusters specifically for production. What do you think of this approach?
Looking forward to your feedback!





We are currently considering splitting the Europe cluster into three. Ideally, the number of tenants per cluster should be roughly balanced, at least in the short and mid-term (say, the next 2-3 years).
- What do you think would be a good split by region? One current option on the table is: France-Belgium, DACH-Netherlands, and Nordics. Do you have any thoughts on this from a sales perspective? Does this align well with the current market and prospects?
- The previous option does not include a cluster for Southern Europe. Do you think that's okay, knowing that we could always add a cluster later if needed?
- Are you aware of any constraints or regulations about data storage that might affect our choice?
- Another option we are contemplating is having a cluster dedicated to internal demos (so, not including demos used by customers) and test platforms, as they represent a significant portion of the Europe cluster (numbers coming...). This would leave us with two "production" clusters. Any thoughts on this option? Any opinon on what could be those 2 production clusters in that case?
- Any other thoughts you'd like to share on this topic?

---

