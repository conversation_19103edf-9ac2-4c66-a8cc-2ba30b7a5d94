-    # Report any unhandled exception, and swallow it.
-    #
-    #  ErrorReporter.handle do
-    #    1 + '1'
-    #  end
-    #
-    def handle(error_class = StandardError)
-      yield
-    rescue error_class => e
-      report(e)
-      nil
+    # Do not swallow exceptions in test and development environments.
+    # Ideally, we should not rely on `Rails` constant since this file is in the lib folder.
+    if Rails.env.test? || Rails.env.development?
+      def handle(_error_class)
+        yield
+      end
+    else
+      # Report any unhandled exception, and swallow it.
+      #
+      #  ErrorReporter.handle do
+      #    1 + '1'
+      #  end
+      #
+      def handle(error_class = StandardError)
+        yield
+      rescue error_class => e
+        report(e)
+        nil
+      end


Commit:
[TAN-2248] Do not swallow exceptions in test and development environments with ErrorReporter.handle

Shorter:
