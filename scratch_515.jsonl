// response for common ground results
// it includes the lists of most popular ideas and most divisive ideas.
// Popular ideas are those with the most 'up' reactions.
// Divisive ideas are those where

{
    "data": {
        "type": "common_ground_results",
        "attributes": {
            "top_consensus_ideas": [
              {
                idea_id: "aadd62ad-646c-4351-bafd-3e0f72e68499",
                idea_title_multiloc: { "en": "Test" },
                reactions: {
                  "up": 10,
                  "down": 0,
                  "neutral": 0
                }
              }
            ],
            "top_controversial_ideas": [
              {

              }
            ]
        }
    }
}


// response for survey results
{
  "data": {
    "type": "survey_results",
    "attributes": {
      "results": [
        {
          "inputType": "page",
          "question": {},
          "description": {},
          "customFieldId": "d41ae542-69f6-4470-9cfb-88c6e38ec51e",
          "required": false,
          "grouped": false,
          "hidden": false,
          "totalResponseCount": 12,
          "questionResponseCount": 9,
          "pageNumber": 1,
          "questionNumber": null,
          "questionCategory": null,
          "logic": {}
        },
        {
          "inputType": "point",
          "question": {
            "en": "Test",
            "de-DE": "",
            "el-GR": "",
            "en-IE": "",
            "es-CL": "",
            "fr-BE": "",
            "nb-NO": "",
            "nl-BE": ""
          },
          "description": {},
          "customFieldId": "6a8e9f26-d1d9-4d5f-8b53-7f84b9e26f31",
          "required": false,
          "grouped": false,
          "hidden": false,
          "totalResponseCount": 12,
          "questionResponseCount": 5,
          "pageNumber": null,
          "questionNumber": 1,
          "questionCategory": null,
          "logic": {},
          "mapConfigId": null,
          "pointResponses": [
            {
              "answer": {
                "type": "Point",
                "coordinates": [
                  4.286797247362225,
                  50.85669387512888
                ]
              }
            },
            {
              "answer": {
                "type": "Point",
                "coordinates": [
                  4.3765908996581695,
                  50.84390524832017
                ]
              }
            },
            {
              "answer": {
                "type": "Point",
                "coordinates": [
                  3.7330323974610704,
                  50.41280166936964
                ]
              }
            },
            {
              "answer": {
                "type": "Point",
                "coordinates": [
                  4.361999682617173,
                  50.85550186319104
                ]
              }
            },
            {
              "answer": {
                "type": "Point",
                "coordinates": [
                  4.347236804199225,
                  50.87283721732156
                ]
              }
            }
          ]
        },
        {
          "inputType": "select",
          "question": {
            "en": "Your question",
            "de-DE": "Frage",
            "el-GR": "Ερώτηση δημοσκόπησης",
            "en-IE": "Your question",
            "es-CL": "Tu pregunta",
            "fr-BE": "Votre question",
            "nb-NO": "Ditt spørsmål",
            "nl-BE": "Jouw vraag"
          },
          "description": {
            "en": "\u003cp\u003eHello\u003c/p\u003e",
            "de-DE": "",
            "el-GR": "",
            "en-IE": "",
            "es-CL": "",
            "fr-BE": "",
            "nb-NO": "",
            "nl-BE": ""
          },
          "customFieldId": "c0d339a4-00c5-447c-939b-cee647637581",
          "required": false,
          "grouped": false,
          "hidden": false,
          "totalResponseCount": 12,
          "questionResponseCount": 9,
          "pageNumber": null,
          "questionNumber": 2,
          "questionCategory": null,
          "logic": {},
          "totalPickCount": 12,
          "answers": [
            {
              "answer": "option_1_bnp",
              "count": 5
            },
            {
              "answer": "option_2_s68",
              "count": 4
            },
            {
              "answer": null,
              "count": 3
            }
          ],
          "multilocs": {
            "answer": {
              "option_1_bnp": {
                "title_multiloc": {
                  "en": "Option 1",
                  "de-DE": "Option 1",
                  "el-GR": "Επιλογή",
                  "en-IE": "Option 1",
                  "es-CL": "Opción 1",
                  "fr-BE": "Option 1",
                  "nb-NO": "Alternativ 1",
                  "nl-BE": "Optie 1"
                }
              },
              "option_2_s68": {
                "title_multiloc": {
                  "en": "Option 2",
                  "de-DE": "Option 2",
                  "el-GR": "Επιλογή",
                  "en-IE": "Option 2",
                  "es-CL": "Opción 2",
                  "fr-BE": "Option 2",
                  "nb-NO": "Alternativ 2",
                  "nl-BE": "Optie 2"
                }
              }
            }
          }
        },
        {
          "inputType": "page",
          "question": {
            "en": "Empty page",
            "de-DE": "",
            "el-GR": "",
            "en-IE": "",
            "es-CL": "",
            "fr-BE": "",
            "nb-NO": "",
            "nl-BE": ""
          },
          "description": {},
          "customFieldId": "48f774a5-26dc-470a-9627-0c7f064035fb",
          "required": false,
          "grouped": false,
          "hidden": false,
          "totalResponseCount": 12,
          "questionResponseCount": 0,
          "pageNumber": 2,
          "questionNumber": null,
          "questionCategory": null,
          "logic": {}
        },
        {
          "inputType": "page",
          "question": {
            "en": "Another empty page! :)",
            "de-DE": "",
            "el-GR": "",
            "en-IE": "",
            "es-CL": "",
            "fr-BE": "",
            "nb-NO": "",
            "nl-BE": ""
          },
          "description": {},
          "customFieldId": "6d47d600-87bb-4953-801f-9c7b5527ba5e",
          "required": false,
          "grouped": false,
          "hidden": false,
          "totalResponseCount": 12,
          "questionResponseCount": 0,
          "pageNumber": 3,
          "questionNumber": null,
          "questionCategory": null,
          "logic": {}
        },
        {
          "inputType": "page",
          "question": {
            "en": "About you",
            "nl-BE": "Over jou",
            "fr-BE": "À propos de vous",
            "de-DE": "Über dich",
            "es-CL": "Sobre ti",
            "en-IE": "About you",
            "nb-NO": "Om deg",
            "el-GR": "Σχετικά με εσάς"
          },
          "description": {},
          "customFieldId": "d091e752-9824-4a1b-9f71-5f3d09881b5f",
          "required": false,
          "grouped": false,
          "hidden": false,
          "totalResponseCount": 12,
          "questionResponseCount": 1,
          "pageNumber": 4,
          "questionNumber": null,
          "questionCategory": null,
          "logic": {}
        },
        {
          "inputType": "select",
          "question": {
            "en": "Domicile",
            "mi": "Place of residence",
            "ar-MA": "مكان الإقامة",
            "ar-SA": "مكان الإقامة",
            "da-DK": "Lokalområde",
            "de-DE": "Wohnort",
            "en-CA": "Place of residence",
            "en-GB": "Place of residence",
            "es-CL": "Lugar de residencia",
            "es-ES": "Lugar de residencia",
            "fr-BE": "Localité",
            "fr-FR": "Localité",
            "hr-HR": "Mesto prebivališta",
            "hu-HU": "Place of residence",
            "it-IT": "Luogo di residenza",
            "kl-GL": "Najugaq",
            "lb-LU": "Wunnuert",
            "nb-NO": "Place of residence",
            "nl-BE": "Waar woon je?",
            "nl-NL": "Waar woon je?",
            "pl-PL": "Miejsce zamieszkania",
            "pt-BR": "Endereço",
            "ro-RO": "Domiciliu",
            "sr-SP": "Место пребивалишта",
            "sr-Latn": "Mesto prebivališta"
          },
          "description": {},
          "customFieldId": "2e68f600-4f19-4107-b921-a892ac63c887",
          "required": true,
          "grouped": false,
          "hidden": false,
          "totalResponseCount": 12,
          "questionResponseCount": 1,
          "pageNumber": null,
          "questionNumber": 3,
          "questionCategory": null,
          "logic": {},
          "totalPickCount": 12,
          "answers": [
            {
              "answer": null,
              "count": 11
            },
            {
              "answer": "f5d7184d-af38-4d77-956f-f7b3690423a1",
              "count": 1
            },
            {
              "answer": "04343f35-4ec2-4405-be36-054b4465d17b",
              "count": 0
            },
            {
              "answer": "95c74e3e-21ec-4e3a-9405-ca896800251e",
              "count": 0
            },
            {
              "answer": "8eb078ed-880c-46ac-8faa-d41bdf019c74",
              "count": 0
            },
            {
              "answer": "6db6d49a-c8b2-4277-8c39-2fda30cbee31",
              "count": 0
            },
            {
              "answer": "b2121ece-9e40-43af-99a1-79e494de19b9",
              "count": 0
            },
            {
              "answer": "a8ad933f-28e5-44f3-8351-1f1449a1c079",
              "count": 0
            },
            {
              "answer": "outside",
              "count": 0
            }
          ],
          "multilocs": {
            "answer": {
              "f5d7184d-af38-4d77-956f-f7b3690423a1": {
                "title_multiloc": {
                  "en": "Chrisshire",
                  "nl-BE": "Camrenside"
                }
              },
              "04343f35-4ec2-4405-be36-054b4465d17b": {
                "title_multiloc": {
                  "en": "East Keatonstad",
                  "nl-BE": "New Rubyside"
                }
              },
              "95c74e3e-21ec-4e3a-9405-ca896800251e": {
                "title_multiloc": {
                  "en": "New Daryl",
                  "nl-BE": "Geraldburgh"
                }
              },
              "8eb078ed-880c-46ac-8faa-d41bdf019c74": {
                "title_multiloc": {
                  "en": "South Cedrick",
                  "nl-BE": "O'Connerville"
                }
              },
              "6db6d49a-c8b2-4277-8c39-2fda30cbee31": {
                "title_multiloc": {
                  "en": "South Everettetown",
                  "nl-BE": "West Elnoraburgh"
                }
              },
              "b2121ece-9e40-43af-99a1-79e494de19b9": {
                "title_multiloc": {
                  "en": "Westbrook",
                  "nl-BE": "Westbroek"
                }
              },
              "a8ad933f-28e5-44f3-8351-1f1449a1c079": {
                "title_multiloc": {
                  "en": "Newington",
                  "de-DE": "Newington",
                  "el-GR": "Newington",
                  "en-IE": "Newington",
                  "es-CL": "Newington",
                  "fr-BE": "Newington",
                  "nb-NO": "Newington",
                  "nl-BE": "Newington"
                }
              },
              "outside": {
                "title_multiloc": {
                  "en": "Somewhere else",
                  "nl-BE": "Ergens anders",
                  "fr-BE": "Autre lieu",
                  "de-DE": "Anderer Ort",
                  "es-CL": "En algun otro lugar",
                  "en-IE": "Somewhere else",
                  "nb-NO": "Et annet sted",
                  "el-GR": "Κάπου αλλού"
                }
              }
            }
          }
        },
        {
          "inputType": "select",
          "question": {
            "en": "Education",
            "mi": "Education",
            "ar-MA": "التعليم",
            "ar-SA": "التعليم",
            "da-DK": "Uddannelse",
            "de-DE": "Bildungsweg",
            "en-CA": "Education",
            "en-GB": "Education",
            "es-CL": "Educación",
            "es-ES": "Educación",
            "fr-BE": "Éducation",
            "fr-FR": "Éducation",
            "hr-HR": "Obrazovanje",
            "hu-HU": "Education",
            "it-IT": "Educazione",
            "kl-GL": "Ilinniagaq",
            "lb-LU": "Formatioun",
            "nb-NO": "Education",
            "nl-BE": "Opleiding",
            "nl-NL": "Opleiding",
            "pl-PL": "Wykształcenie",
            "pt-BR": "Educação",
            "ro-RO": "Educație",
            "sr-SP": "Образовање",
            "sr-Latn": "Obrazovanje"
          },
          "description": {},
          "customFieldId": "1527de24-f93b-4fd2-b5f1-1ad81df83dbd",
          "required": true,
          "grouped": false,
          "hidden": false,
          "totalResponseCount": 12,
          "questionResponseCount": 1,
          "pageNumber": null,
          "questionNumber": 4,
          "questionCategory": null,
          "logic": {},
          "totalPickCount": 12,
          "answers": [
            {
              "answer": null,
              "count": 11
            },
            {
              "answer": "3",
              "count": 1
            },
            {
              "answer": "2",
              "count": 0
            },
            {
              "answer": "4",
              "count": 0
            },
            {
              "answer": "5",
              "count": 0
            },
            {
              "answer": "6",
              "count": 0
            },
            {
              "answer": "7",
              "count": 0
            },
            {
              "answer": "8",
              "count": 0
            }
          ],
          "multilocs": {
            "answer": {
              "2": {
                "title_multiloc": {
                  "en": "Lower secondary education",
                  "da-DK": "Lower secondary education",
                  "de-DE": "Lower secondary education",
                  "fr-BE": "Lower secondary education",
                  "nb-NO": "Lower secondary education",
                  "nl-BE": "Lower secondary education"
                }
              },
              "3": {
                "title_multiloc": {
                  "en": "Upper secondary education",
                  "da-DK": "Upper secondary education",
                  "de-DE": "Upper secondary education",
                  "fr-BE": "Upper secondary education",
                  "nb-NO": "Upper secondary education",
                  "nl-BE": "Upper secondary education"
                }
              },
              "4": {
                "title_multiloc": {
                  "en": "Post-secondary non-tertiary education",
                  "da-DK": "Post-secondary non-tertiary education",
                  "de-DE": "Post-secondary non-tertiary education",
                  "fr-BE": "Post-secondary non-tertiary education",
                  "nb-NO": "Post-secondary non-tertiary education",
                  "nl-BE": "Post-secondary non-tertiary education"
                }
              },
              "5": {
                "title_multiloc": {
                  "en": "Short-cycle tertiary education",
                  "da-DK": "Short-cycle tertiary education",
                  "de-DE": "Short-cycle tertiary education",
                  "fr-BE": "Short-cycle tertiary education",
                  "nb-NO": "Short-cycle tertiary education",
                  "nl-BE": "Short-cycle tertiary education"
                }
              },
              "6": {
                "title_multiloc": {
                  "en": "Bachelor or equivalent",
                  "da-DK": "Bachelor or equivalent",
                  "de-DE": "Bachelor or equivalent",
                  "fr-BE": "Bachelor or equivalent",
                  "nb-NO": "Bachelor or equivalent",
                  "nl-BE": "Bachelor or equivalent"
                }
              },
              "7": {
                "title_multiloc": {
                  "en": "Master or equivalent",
                  "da-DK": "Master or equivalent",
                  "de-DE": "Master or equivalent",
                  "fr-BE": "Master or equivalent",
                  "nb-NO": "Master or equivalent",
                  "nl-BE": "Master or equivalent"
                }
              },
              "8": {
                "title_multiloc": {
                  "en": "Doctoral or equivalent",
                  "da-DK": "Doctoral or equivalent",
                  "de-DE": "Doctoral or equivalent",
                  "fr-BE": "Doctoral or equivalent",
                  "nb-NO": "Doctoral or equivalent",
                  "nl-BE": "Doctoral or equivalent"
                }
              }
            }
          }
        }
      ],
      "totalSubmissions": 12
    }
  }
}

https://teams.live.com/l/invite/FBAxm6nde-1dhkbTwY