50000 + 23000 + 15000 + 37900 + 24600 = 150500
3000 * 12 * 3 = 100000
590000*1.24=731600

I just think it does not produce reliable numbers. Those dashboards are meant to show the history, but if we are using the model data, we are just showing a snapshot of the current state.

For instance, as I understand it, visitor tracking is GDPR compliant as long as the visitor token is not linked to a user id. Once it’s linked, that data becomes personal. However, even if it’s GDPR compliant, I’m not sure it abides by the cookie law.