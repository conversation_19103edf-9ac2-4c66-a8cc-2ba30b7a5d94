It’s a bit of an ill-posed question.
- What data are we talking about? Doest it include images and uploads?
- And what does “itself” mean here? Does AWS count as “itself”? The amount of data really depends on the representation and how it’s encoded.
The measure in GB of the amount of data stored in the application depends on the data format and encoding.


“The amount of data stored in the application depends on how it’s represented and encoded, so it’s hard to give an exact number in GB without more details.”

Or, if you want to keep it casual and clear:

“It’s tough to say exactly how many GB of information are stored since it depends on the data format and encoding.