; <<>> DiG 9.10.6 <<>> mathieuspalace.stg.citizenlab.co
;; global options: +cmd
### global options apply to all queries
;; Got answer:
;; ->>HEADER<<- opcode: QUERY, status: NOERROR, id: 13014
### ->>HEADER<<- contains the opcode, status, and id of the query
### the opcode is the type of query; possible values are:
### - QUERY: a standard query
### - IQUERY: an inverse query (= query by IP address)
### - STATUS: a status request (this is typically used when a client wants to know the status of a server)
### - NOTIFY: a request to update the server's zone data (this is typically used when a server wants to notify another server that its zone data has changed) zone data = DNS records for a domain
### - UPDATE: a request to update the server's zone data
### the status is the result of the query; possible values are:
### - NOERROR: the query was successful
### - FORMERR: the query was malformed
### - SERVFAIL: the server failed to process the query
### - NXDOMAIN: the domain name does not exist
### - NOTIMP: the server does not support the requested operation
### - REFUSED: the server refused to process the query
### - YXDOMAIN: the domain name exists, but no records are associated with it
### - YXRRSET: the domain name exists, but the record type does not exist
### - NXRRSET: the domain name exists, but the record type does not exist
### - NOTAUTH: the server is not authoritative for the domain
### - NOTZONE: the server is not authoritative for the zone
;; flags: qr rd ra; QUERY: 1, ANSWER: 5, AUTHORITY: 0, ADDITIONAL: 1

;; OPT PSEUDOSECTION:
; EDNS: version: 0, flags:; udp: 1232
;; QUESTION SECTION:
;mathieuspalace.stg.citizenlab.co. IN	A

;; ANSWER SECTION:
mathieuspalace.stg.citizenlab.co. 108 IN CNAME	d9pner0xhf5ap.cloudfront.net.
d9pner0xhf5ap.cloudfront.net. 60 IN	A	18.239.36.86
d9pner0xhf5ap.cloudfront.net. 60 IN	A	18.239.36.90
d9pner0xhf5ap.cloudfront.net. 60 IN	A	18.239.36.6
d9pner0xhf5ap.cloudfront.net. 60 IN	A	18.239.36.22

;; Query time: 41 msec
;; SERVER: 1.1.1.1#53(1.1.1.1)
;; WHEN: Wed Jun 26 12:54:21 CEST 2024
;; MSG SIZE  rcvd: 167

### IN stands for Internet; it is the class of the query
### other possible classes are CH (Chaosnet) and HS (Hesiod)
### Chaosnet is an early networking protocol. These days, it is mostly used for debugging and testing
### Hesiod is a system that provides a way to look up information in databases that are not DNS-compatible
### In other words, dig is not just for DNS queries; it can be used to query other types of databases as well
### In generic terms, dig is a tool for querying information from a data