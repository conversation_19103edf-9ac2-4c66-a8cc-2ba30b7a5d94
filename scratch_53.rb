
params = { during: [
  Time.zone.parse('2023-11-23T04:00:00.000Z'),
  Time.zone.parse('2023-12-31T23:59:59.999Z')
] }

EventsFinder.new(params, scope: Event, current_user: nil).find_records

start_datetime, end_datetime = params[:during]
start_datetime = "'; DROP TABLE records; --"
Event.where(
  '(start_at >= :start_datetime AND start_at < :end_datetime) OR (end_at > :start_datetime AND end_at <= :end_datetime)',
  start_datetime: start_datetime,
  end_datetime: end_datetime
).to_sql

Event.where(<<~SQL, start_datetime: start_datetime, end_datetime: end_datetime).to_a
  (start_at, end_at) OVERLAPS (:start_datetime, :end_datetime)
SQL