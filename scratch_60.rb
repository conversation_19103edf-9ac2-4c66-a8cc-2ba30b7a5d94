ics_string = "BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:CitizenLab\r\nCALSCALE:GREGORIAN\r\nBEGIN:VEVENT\r\nDTSTAMP:20230823T113406Z\r\nUID:0c2c61fa-0465-4428-912c-a20554ca2339\r\nDTSTART:20170501T200000\r\nDTEND:20170501T220000\r\nDESCRIPTION:<p>Be there and learn everything about our future!</p>\r\nGEO:50.8465574798584;4.351710319519043\r\nLOCATION:Atomiumsquare 1\\, 1020 Brussels\\, Belgium\\n(Sphere 1)\r\nSUMMARY:Info session\r\nEND:VEVENT\r\nEND:VCALENDAR\r\n"
ics2 = <<~ICS
  BEGIN:VCALENDAR
  VERSION:2.0
  PRODID:CitizenLab
  CALSCALE:GREGORIAN
  BEGIN:VEVENT
  DTSTAMP:20230823T113406Z
  UID:0c2c61fa-0465-4428-912c-a20554ca2339
  DTSTART:20170501T200000
  DTEND:20170501T220000
  DESCRIPTION:<p>Be there and learn everything about our future!</p>
  GEO:50.8465574798584;4.351710319519043
  LOCATION:Atomiumsquare 1\\, 1020 Brussels\\, Belgium\\n(Sphere 1)
  SUMMARY:Info session
  END:VEVENT
  END:VCALENDAR
ICS
ics2 = ics2.gsub("\n", "\r\n")

# replace the uid with a placeholder
dup = ics_string.gsub(/UID:[a-z0-9\-]+/, 'UID:UID_PLACEHOLDER')
dup = Regexp.escape(dup)
dup = dup.gsub('UID_PLACEHOLDER', '[a-z0-9\-]+')

regex = Regexp.new(dup)
expect(ics_string).to match(regex)

/BEGIN:VCALENDAR\r\nVERSION:2\.0\r\nPRODID:CitizenLab\r\nCALSCALE:GREGORIAN/\r\nBEGIN:VEVENT\r\nDTSTAMP:20230823T095841Z\r\nUID:.+\r\nDTSTART:20170501T200000\r\nDTEND:20170501T220000\r\nDESCRIPTION:<p>Be\ there\ and\ learn\ everything\ about\ our\ future!<\/p>\r\nGEO:50\.8465574798584;4\.351710319519043\r\nLOCATION:Atomiumsquare\ 1\\,\ 1020\ Brussels\\,\ Belgium\\n\(Sphere\ 1\)\r\nSUMMARY:Info\ session\r\nEND:VEVENT\r\nEND:VCALENDAR\r\n/
"BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:CitizenLab\r\nCALSCALE:GREGORIAN"\r\nBEGIN:VEVENT\r\nDTSTAMP:20230823T123253Z\r\nUID:0904682e-d0fb-45e2-bc0c-4163b54d4308\r\nDTSTART:20170501T200000\r\nDTEND:20170501T220000\r\nDESCRIPTION:<p>Be there and learn everything about our future!</p>\r\nGEO:50.8465574798584;4.351710319519043\r\nLOCATION:Atomiumsquare 1\\, 1020 Brussels\\, Belgium\\n(Sphere 1)\r\nSUMMARY:Info session\r\nEND:VEVENT\r\nEND:VCALENDAR\r\n"

expect("BEGIN:VCALENDAR\r\nVERSION:2.0\r\nPRODID:CitizenLab\r\nCALSCALE:GREGORIAN") to match /BEGIN:VCALENDAR\r\nVERSION:2\.0\r\nPRODID:CitizenLab\r\nCALSCALE:GREGORIAN/