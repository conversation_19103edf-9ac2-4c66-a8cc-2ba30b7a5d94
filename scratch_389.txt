Documentation
=============

When you try to allocate a bucket policy and it has "*" in the Principal, then it is required that "Block public access" setting should be disabled. Otherwise, you will get the error "After you or your AWS administrator have updated your permissions to allow the s3:PutBucketPolicy action, choose Save changes."

The problem in your original question was that you have put a policy which does not look like a bucket policy as there is no Principal in it.

You can very well put a bucket policy which has a specific ARN in Principal even without disabling "Block public access" setting.

============================================

"Principal" refers to the AWS account, IAM user, federated user, or assumed-role user that the statement in a policy is written about.


