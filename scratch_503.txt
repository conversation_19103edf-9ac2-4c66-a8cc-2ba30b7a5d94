Hello team! I’m still working on the API design. Here are some of the endpoints I’m considering. I'm not sure if they are the best approach, so I would love to hear your thoughts.

- `GET .../phase/:id/progress` (or `GET .../phase/:id/summary` / `GET .../phase/:id/common_ground/progress` )

```json
{
  data: {
    "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
    "type": "phase-progress",
    "attributes": {
      "num_ideas": 10,
      "num_ideas_reacted": 5 // "reacted" is provisional/not final
    },
    "relationships": {
      "next_idea": {
        "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
        "type": "idea"
      }
    }
  },
  included: [
    {
      "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
      "type": "idea",
      "attributes": {
        /* ... */
      }
    }
  ]
}
```

- `POST .../phase/:id/common_ground/reactions` with a body like:
```json
{
  "reaction": {
    'idea_id': "8f476eb9-c385-42a2-bd65-06aed088f138",
    'mode': "up" // or "down" or "agree/disagree/pass" or "-1/0/1"
  }
}
```
and the updated progress would be returned:
```json
{
  data: {
    "id": "8f476eb9-c385-42a2-bd65-06aed088f138",
    "type": "phase-progress",
    "attributes": {
      "num_ideas": 10,
      "num_ideas_reacted": 6
    },
    "relationships": {
      "next_idea": {
        "id": "7e98c3b2-4f8d-4a0e-9b5c-1f7d6e0a1f2b",
        "type": "idea"
      }
    }
  },
  included: [ /* ... */ ]
}
```

I'm not entirely sure about the second endpoint, as it's not very standard or RESTful. However, the idea is to allow voting and retrieving the next idea in a single step to ensure the vote is taken into account for before picking the next idea. The alternative would be to call:
- `POST .../ideas/:id/reactions`, wait for the response, and then
- `GET .../phase/:id/progress` (or its equivalent in the final spec) to get the next idea.

Let me know what you think and if you have better ideas.
