SELECT
    DATE_TRUNC('month', r.created_at) AS month,
    COUNT(*) FILTER (WHERE r.phase_id IS NOT NULL) AS phase_reports,
    COUNT(*) FILTER (WHERE r.phase_id IS NULL) AS global_reports,
    COUNT(*) AS total_reports,
    COUNT(*) FILTER (WHERE r.phase_id IS NULL AND (owner_id is NULL OR (users.email NOT LIKE '%@citizenlab.co' AND users.email NOT LIKE '%@govocal.com'))) as non_gv_global_reports,
    COUNT(*) FILTER (WHERE r.phase_id IS NULL AND owner_id is not NULL AND (users.email LIKE '%@citizenlab.co' OR users.email LIKE '%@govocal.com')) as gv_global_reports
FROM production_cl2_back_raw.cl2_raw_report_builder_reports r
         JOIN production_cl2_back_raw.cl2_raw_tenants t ON r.tenant_id = t.id
         LEFT JOIN production_cl2_back_raw.cl2_raw_users users ON r.owner_id = users.id
WHERE
    t.core_lifecycle_stage = 'active'
  AND t.report_builder_enabled = TRUE
  AND t.report_builder_allowed = TRUE
GROUP BY month
ORDER BY month ASC;