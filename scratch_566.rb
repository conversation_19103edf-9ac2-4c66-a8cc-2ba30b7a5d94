creds = Aws::Credentials.new(
  ENV['AWS_ACCESS_KEY_ID'],
  ENV['AWS_SECRET_ACCESS_KEY']
)


presigned_post = Aws::S3::PresignedPost.new(
  creds,
  'eu-central-1',
  'your-bucket-name',
  expires: 15.minutes.from_now
)

presigned_post.key('uploads/presigned/test-key')

  puts presigned_post.url
  puts presigned_post.fields


# same but with a policy to limit the size of the file to 50MB
presigned_post = Aws::S3::PresignedPost.new(
  creds,
  'eu-central-1',
  'your-bucket-name',
  expires: 15.minutes.from_now,
  content_length_range: 1..50.megabytes,
  key: 'uploads/presigned/test-key',
  content_type: 'application/pdf'
)

# =======
# Same but using a presigned put

s3_client = Aws::S3::Client.new(region: ENV.fetch('AWS_REGION'))
signer = Aws::S3::Presigner.new(client: s3_client)
put_url = signer.presigned_url(
  :put_object,
  bucket: 'your-bucket-name',
  key: 'uploads/presigned/test-key',
  expires_in: 15.minutes,
  content_length_range: 1..50.megabytes,
  content_type: 'application/pdf'
)

url = signer.presigned_url(
  :put_object,
  key: 'uploads/presigned/test-key',
  expires_in: 15.minutes,
  content_length_range: 1..50.megabytes,
  content_type: 'application/pdf'
)

puts url
