I'm not sure if this is the right level to solve this problem, as it would only address it at the backend level. Perhaps we should fix this at the level of the CloudFront distributions instead to cover the other origins (watch out: in the context of CloudFront, "origin" refers to the source of the content / server, not the "origin" in the CORS sense).

But the main question is: Do we need CORS and why? Looking at the git history, it appears to have been enabled across all environments at the same time. So, it does not appear to have been configured only for the dev environment at some point contrary to what <PERSON><PERSON> said (or there was a big mistake). In any case, if it is not necessary in production and staging, we should disable it altogether. Indeed, given our current local setup, CORS does not seem to be needed in development either since all requests are proxied through the front-end end (so they share the same origin).

Except for the widgets, I cannot think of any reason why we would need CORS.
