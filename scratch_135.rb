# Activities where action is created and item_type phase / count by item_id
Activity.where(action: 'started', item_type: 'Phase').group(:item_id).count.select { |_k, v| v > 1 }


activities = Activity.where(item_id: 'b47641bd-a5b1-446f-a988-10f97eca8642', action: 'started')
activities.pluck(:acted_at)

Phase.where(id: Activity.where(action: 'started', item_type: 'Phase').select(:item_id))

starting_phases = Phase.published.starting_on(Date.today)
phases_with_started_activities = Activity.where(item_id: starting_phases, action: 'started', acted_at: Date.today)
# exclude phases with started activities
phases = starting_phases.where.not(id: phases_with_started_activities.select(:item_id))


today = Date.today
start_range = (today-3.week)..(today + 1.week)
upcoming_phases = Phase.published.starting_on(start_range)

excluded_phases = Activity.where(item_id: upcoming_phases, action: 'upcoming', acted_at: start_range).select(:item_id)


# convert date to time in given timezone
timezone = 'Asia/Kamchatka'
start_date = Date.parse '2019-03-20'


def end_date(start_date)
  # parse if string
  start_date = Date.parse(start_date) if start_date.is_a? String
  start_date + 1.week
end


# Get timezone object
timezone = ActiveSupport::TimeZone['Asia/Kamchatka']