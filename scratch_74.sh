# serve open_api.json as html locally
docker run -t -i -p 8246:8080 -e SWAGGER_JSON=/api/open_api.json -v $PWD/api:/api swaggerapi/swagger-ui

# redoc build
npx redoc-cli build

# TRUNCATE the localhost.schema_migrations table
TRUNCATE TABLE localhost.schema_migrations;

# list all RUNNING tasks of docker service 'my-service'
docker service ps my-service | grep Running


# restart docker compose
docker-compose up -d --force-recreate
