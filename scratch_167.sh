ssh -L 5432:cl2-back-staging.cekjw1iai1hl.eu-central-1.rds.amazonaws.com:5432 ubuntu@*************
psql -h localhost -p 5432 -d cl2_back_staging -U postgres -W

# test TCP connectivity to cl2-back-staging.cekjw1iai1hl.eu-central-1.rds.amazonaws.com (port 5432)
telnet cl2-back-staging.cekjw1iai1hl.eu-central-1.rds.amazonaws.com 5432

# reload docker engine (systemctl)
sudo systemctl reload docker

sudo rm /root/.aws/credentials
sudo systemctl reload docker

docker stack deploy --compose-file docker-compose-production.yml cl2 --with-registry-auth
docker stack deploy --compose-file docker-compose-production-benelux.yml cl2-prd-bnlx-stack --with-registry-auth

