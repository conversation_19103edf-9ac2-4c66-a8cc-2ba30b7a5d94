
def matomo_site_id
  app_config = AppConfiguration.instance
  site_id = app_config.settings.dig('matomo', 'tenant_site_id')

  raise Analytics::ImportLatestMatomoDataJob::MatomoMisconfigurationError, <<~MSG if site_id.blank? || site_id == ENV['DEFAULT_MATOMO_TENANT_SITE_ID']
    Matomo site (= #{site_id.inspect}) for tenant '#{app_config.id}' is misconfigured.
  MSG

  site_id
end

def reimport(host, kwargs)
  Tenant.find_by(host: host).switch do
    timestamp = AppConfiguration.instance.created_at.to_i - 24 * 60 * 60
    Analytics::MatomoDataImporter.new.import(matomo_site_id, timestamp, **kwargs)
  end
end

reimport("jaiba1-demo.citizenlab.co", max_nb_batches: nil, batch_size: 200)


Tenant.pluck(:host).sort.map do |host|
  puts host
  reimport(host, max_nb_batches: nil, batch_size: 375)
  nil
rescue => e
  host
end.compact
