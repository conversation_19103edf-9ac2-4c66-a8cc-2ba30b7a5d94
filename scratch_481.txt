SELECT COUNT(*) FROM "users" WHERE "users"."id" IN (SELECT "users"."id" FROM "users" INNER JOIN "followers" ON "followers"."user_id" = "users"."id" WHERE "users"."id" IN (SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $1)) AND "users"."id" NOT IN (SELECT "users"."id" FROM "users" WHERE "users"."id" IN (SELECT "users"."id" FROM "users" INNER JOIN "followers" ON "followers"."user_id" = "users"."id" WHERE "users"."id" IN (SELECT "followers"."user_id" FROM "followe

