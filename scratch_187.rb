# Benchmark
# There are several ways to benchmark a block of code in Ruby:
# - Benchmark.measure: used to measure the time taken to execute a block of code
# - Benchmark.bm: used to measure and report the time taken to execute a block of code
# - Benchmark.bmbm: used to measure and report the time taken to execute a block of code, and then repeat the process to get a more accurate result
# - Benchmark.realtime
# - Benchmark.ms
# - Benchmark.ips
# - Benchmark.perf

# For comparison, it's better to use Benchmark.bm or Benchmark.bmbm, because they run the code multiple times and report the average time taken to execute the block of code.

require 'benchmark'

n = 10
report = Benchmark.bm do |x|
  x.report { n.times { service.available_templates } }
  x.report { n.times { service.internal_template_names } }
  x.report { n.times { service.external_template_names } }
end

puts report


def measure_time(&block)
  start = Time.now
  yield
  Time.now - start
end