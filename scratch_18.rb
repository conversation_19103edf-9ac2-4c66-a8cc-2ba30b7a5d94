class VerificationService

  @all_methods = Set[
    Methods::Cow.new,
    Methods::Bogus.new,
    OmniauthMethods::FranceConnect.new,
    Methods::IdCardLookup.new
  ]

  class << self
    attr_reader :all_methods
    def add_method(verification_method)
      @all_methods << verification_method
    end
  end

  # ...

  # for retro-compatibility, but IMO all_methods makes more sense as a class method in that case.
  def all_methods
    VerificationService.all_methods
  end
end
