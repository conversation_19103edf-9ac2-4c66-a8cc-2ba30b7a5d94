
bin/rspec ./spec/services/timeline_service_spec.rb:108
bin/rspec ./spec/services/timeline_service_spec.rb:113
bin/rspec ./spec/services/timeline_service_spec.rb:261

# use rspec to bisect the failure
bin/rspec --bisect engines/commercial/multi_tenancy/spec/services/multi_tenancy/templates/tenant_serializer_spec.rb spec/acceptance/notifications_spec.rb engines/free/email_campaigns/spec/mailers/event_registration_confirmation_mailer_spec.rb spec/services/survey_results_generator_service_spec.rb spec/policies/idea_comment_policy_spec.rb engines/commercial/id_bosa_fas/spec/requests/bosa_fas_verification_spec.rb engines/free/email_campaigns/spec/mailers/project_published_mailer_spec.rb spec/models/notification_spec.rb engines/free/email_campaigns/spec/mailers/status_change_on_idea_you_follow_mailer_spec.rb spec/acceptance/confirmations_spec.rb engines/commercial/multi_tenancy/spec/models/app_configuration_spec.rb engines/commercial/moderation/spec/acceptance/moderations_spec.rb spec/services/trending_idea_service_spec.rb engines/commercial/insights/spec/finders/insights/inputs_finder_spec.rb engines/commercial/analysis/spec/services/inputs_finder_spec.rb spec/services/timeline_service_spec.rb
