[TAN-2254] Fix copy of map_config in project copy

The project copy was (unnecessarily) copying config maps without mappable (mappable: nil) due to a faulty SQL query. This caused the number of such map configs to grow exponentially on some platforms.

-----


Question to people familiar with map configurations.

 In what cases does it make sense for a map configuration to have mappable set to nil? From what I could gather by looking at the code, it seems to be used when duplicating the configs (I found the `CustomMaps::WebApi::V1::MapConfigsController#duplicate_map_config_and_layers` action and the `IdeaCustomFieldsService#duplicate_all_fields`), but I'm not sure to understand the full scope of it. I assume it is supposed to be a temporary state, but how do we ensure that those configurations are eventually associated to a mappable object?

 Asking because a bug in the project copy caused these map configs to be duplicated exponentially, and now I have to determine how to identify and handle the bad copies.


 Question for those familiar with map configurations:

 In what scenarios does it make sense for a map configuration to have mappable set to nil? From what I can tell from the code, it seems to be used during the duplication of configs (I found the CustomMaps::WebApi::V1::MapConfigsController#duplicate_map_config_and_layers action and the IdeaCustomFieldsService#duplicate_all_fields method). However, I'm not sure I get the full picture. I guess it's meant to be a temporary state, but how do we ensure that these configurations eventually get associated with a mappable object?

 I'm asking because a bug in the project copy caused these map configs to be duplicated exponentially, and now I need to figure out how to identify and handly the excess copies.