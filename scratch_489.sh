aws-vault exec prd-admin -- aws cloudfront list-distributions --query "DistributionList.Items[?DefaultCacheBehavior.ResponseHeadersPolicyId=='eaab4381-ed33-4a86-88ca-d9558dc6cd63' || contains(CacheBehaviors.Items[*].ResponseHeadersPolicyId, 'eaab4381-ed33-4a86-88ca-d9558dc6cd63')].Id" --output text
aws-vault exec prd-admin -- aws cloudfront list-distributions --query "DistributionList.Items[?DefaultCacheBehavior.ResponseHeadersPolicyId=='eaab4381-ed33-4a86-88ca-d9558dc6cd63' || (CacheBehaviors!=null && contains(CacheBehaviors.Items[*].ResponseHeadersPolicyId, 'eaab4381-ed33-4a86-88ca-d9558dc6cd63'))]"