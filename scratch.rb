require "dry-struct"
require "dry-types"
require 'json'

module Types
  include Dry.Types()
end

class URL < Dry::Struct
  attribute :url, Types::String.optional
end

class ImageInfos < Dry::Struct
  attribute :url, Types::String.optional
  attribute :large, URL
  attribute :medium, URL
  attribute :small, URL
end

class T < Dry::Struct
  attribute :id, Types::String
  attribute :name, Types::String
  attribute :host, Types::String
  attribute :settings, Types::Hash
  attribute :updated_at, Types::JSON::DateTime
  attribute :created_at, Types::JSON::DateTime
  attribute :logo, ImageInfos
  attribute :header_bg, ImageInfos
  attribute :favicon, ImageInfos
  attribute :style, Types::Hash.default({})

  def self.from

  end
end



t = JSON.parse(File.read("./scratch_1.json"), symbolize_names: true)
tenant = T.new(t)

pp tenant.created_at
pp tenant.logo.large
pp tenant.to_h.to_json

