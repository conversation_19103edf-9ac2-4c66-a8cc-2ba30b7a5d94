
# 100 random files names
filenames = 1000.times.map do
  Faker::File.file_name.split('/').last
end

filenames.each do |filename|
  FactoryBot.create(:file, name: filename)
end

Faker::File.file_name(ext: 'pdf').split('/').last
Faker::File.file_name(ext: 'xlsx').split('/').last
Faker::File.file_name(ext: 'csv').split('/').last
Faker::File.file_name(ext: 'jpg').split('/').last
Faker::File.file_name(ext: 'png').split('/').last
Faker::File.file_name(ext: 'jpeg').split('/').last