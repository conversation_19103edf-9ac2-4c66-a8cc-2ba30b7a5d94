I will have to run a risky operation and reinitialize the europe cluster. Some operations fails with the following error:
> Error response from daemon: rpc error: code = Unknown desc = raft: raft message is too large and can't be sent

According to this issue, this could be due to an important node churn. I have a hard time believing that we hit the number of nodes mentioned in the issue, but in any case, it seems like the Cluster ojbect (whatever it is) "has grown larger than the maximum message size". Two solutions are proposed in the ticket:
- waiting (not fan of this option since i'm not sure of all the consequence this problem could have)
- decreasing the certificate expiration time, but this solution can only work before we hit the problem.


I need to run a risky operation and reinitialize the Europe cluster. Some operations are failing with the following error:
> Error response from daemon: rpc error: code = Unknown desc = raft: raft message is too large and can't be sent

According to this issue, it might be caused by a important node churn. I find it hard to believe we've hit the number of nodes mentioned in the issue, but in any case, it seems the Cluster object (whatever that is) "has grown larger than the maximum message size." Two solutions are proposed in the ticket:
- waiting (I'm not a fan of this option since I'm unsure of all the potential consequences of this problem)
- decreasing the certificate expiration time, but this solution only works if implemented before the problem occurs.