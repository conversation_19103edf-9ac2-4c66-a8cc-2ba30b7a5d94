In short, sharing the project at some point in time without exposing information that would prevent us from revoking the access to the project, without

The most promising alternative suggested is to introduce a new project status, **unlisted**, which wouldn’t have the same security guarantees or access restrictions as a shareable link. We discussed two variants:  a new status per se, or a new un-listed attribute that could be applied to any status.

We could agree on a satisfactory solution for the problem. Everyone will take some time to think about it and we'll meet again. Until then, we'll keep on writing our thoughts async here: