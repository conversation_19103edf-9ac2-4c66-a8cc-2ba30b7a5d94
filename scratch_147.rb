base64data = "IyEvYmluL2Jhc2gKCnN1ZG8gL2Jpbi9kZCBpZj0vZGV2L3plcm8gb2Y9L3Zhci9zd2FwLjEgYnM9MU0gY291bnQ9NDA5NgpzdWRvIC9zYmluL21rc3dhcCAvdmFyL3N3YXAuMQpzdWRvIGNobW9kIDYwMCAvdmFyL3N3YXAuMQpzdWRvIC9zYmluL3N3YXBvbiAvdmFyL3N3YXAuMQpzdWRvIGVjaG8gIi92YXIvc3dhcC4xICAgc3dhcCAgICBzd2FwICAgIGRlZmF1bHRzICAgICAgICAwICAgMCIgPj4gL2V0Yy9mc3RhYgoKc3VkbyBhcHQtZ2V0IC0teWVzIGluc3RhbGwgXAogICAgYXB0LXRyYW5zcG9ydC1odHRwcyBcCiAgICBjYS1jZXJ0aWZpY2F0ZXMgXAogICAgY3VybCBcCiAgICBzb2Z0d2FyZS1wcm9wZXJ0aWVzLWNvbW1vbgoKY3VybCAtZnNTTCBodHRwczovL2Rvd25sb2FkLmRvY2tlci5jb20vbGludXgvdWJ1bnR1L2dwZyB8IHN1ZG8gYXB0LWtleSBhZGQgLQoKc3VkbyBhZGQtYXB0LXJlcG9zaXRvcnkgXAogICAiZGViIFthcmNoPWFtZDY0XSBodHRwczovL2Rvd25sb2FkLmRvY2tlci5jb20vbGludXgvdWJ1bnR1IFwKICAgJChsc2JfcmVsZWFzZSAtY3MpIFwKICAgc3RhYmxlIgoKc3VkbyBhcHQtZ2V0IC15IHVwZGF0ZQoKc3VkbyBhcHQtZ2V0IC15IGluc3RhbGwgZG9ja2VyLWNlIGRvY2tlci1jZS1jbGkgY29udGFpbmVyZC5pbwoKc3VkbyBncm91cGFkZCBkb2NrZXIKc3VkbyB1c2VybW9kIC1hRyBkb2NrZXIgdWJ1bnR1CgplY2hvIC1lICd7XG4gICJsb2ctZHJpdmVyIjogImpzb24tZmlsZSIsXG4gICJsb2ctb3B0cyI6IHtcbiAgICAibWF4LXNpemUiOiAiMWciLFxuICAgICJtYXgtZmlsZSI6ICI1IlxuICB9XG59XG4nIHwgc3VkbyB0ZWUgL2V0Yy9kb2NrZXIvZGFlbW9uLmpzb24gPiAvZGV2L251bGwKCnN1ZG8gc2ggLWMgJ2VjaG8gIjAgNSAqICogMSBkb2NrZXIgc3lzdGVtIHBydW5lIC1mIiA+IC9ldGMvY3Jvbi5kL3BydW5lLWRvY2tlcicKc3VkbyBlY2hvICIiID4+IC9ob21lL3VidW50dS8uc3NoL2F1dGhvcml6ZWRfa2V5cwoKZG9ja2VyIHN3YXJtIGpvaW4gLS10b2tlbiBTV01US04tMS00cXUyN2FxZjRzbTJmdGg3ZnkwYnU2ZjBldWJ5ajhzMnAxODRldTY2OGliYzNzaTZ2Ny1kdmp4Y2IyaDVmYzZ1a2p0NGUyYzdmeTIwIDE3Mi4zMS4yNC4yMzQ6MjM3Nwo="

# decode base64 data
require 'base64'
puts Base64.decode64(base64data)

#!/bin/bash

sudo /bin/dd if=/dev/zero of=/var/swap.1 bs=1M count=4096
sudo /sbin/mkswap /var/swap.1
sudo chmod 600 /var/swap.1
sudo /sbin/swapon /var/swap.1
sudo echo "/var/swap.1   swap    swap    defaults        0   0" > /etc/fstab

sudo apt-get --yes install \
    apt-transport-https \
    ca-certificates \
    curl \
    software-properties-common

curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

sudo add-apt-repository \
   "deb [arch=amd64] https://download.docker.com/linux/ubuntu \
   $(lsb_release -cs) \
   stable"

sudo apt-get -y update

sudo apt-get -y install docker-ce docker-ce-cli containerd.io

sudo groupadd docker
sudo usermod -aG docker ubuntu

sudo echo "0 5 * * 1 docker system prune -f" >> /etc/cron.d/prune-docker

docker swarm join --token SWMTKN-1-4qu27aqf4sm2fth7fy0bu6f0eubyj8s2p184eu668ibc3si6v7-dvjxcb2h5fc6ukjt4e2c7fy20 172.31.24.234:2377