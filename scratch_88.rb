Analytics::ImportLatestMatomoDataJob.perform_for_all_tenants(max_nb_batches: nil)

Tenant.all.map do |tenant|
  tenant.switch do
    # Analytics::ImportLatestMatomoDataJob.perform_now(Tenant.current.id, max_nb_batches: nil, batch_size: 50)
    Analytics::FactVisit.count
  end
end.sum

res = Tenant.all.map do |tenant|
    Analytics::ImportLatestMatomoDataJob.perform_now(tenant.id, max_nb_batches: nil, batch_size: 50)
rescue => e
  tenant.host if e.is_a?(Matomo::Client::MatomoApiError)
end.compact