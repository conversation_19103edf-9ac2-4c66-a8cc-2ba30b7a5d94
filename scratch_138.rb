ser = MultiTenancy::Templates::TenantSerializer.new(Tenant.current)

result = {
  ProjectFolders::Folder => ser.send(:serialize_records, ProjectFolders::Folder),
  Project => ser.send(:serialize_records, Project),
  AdminPublication => ser.send(:serialize_records, AdminPublication),
}

dereferenced = result.each do |record_class, records|
  records.each do |id, attributes|
    attributes.transform_values! do |value|
      value.resolve(result)
    end
  end
end

dereferenced.transform_keys! { |klass| klass.name.snakecase }
dereferenced.deep_transform_keys!(&:to_s)
dereferenced.transform_values!(&:values)

File.write('dereferenced.yaml', dereferenced.to_yaml)


# ----------------------------------------

Tenant.find_by(host: 'example.org').destroy!
FactoryBot.create(:tenant, host: 'example.org')

tenant = Tenant.find_by!(host: "global-template.citizenlab.co")
tenant.switch!

ser = MultiTenancy::Templates::TenantSerializer.new(Tenant.current, uploads_full_urls: false)
template = nil
Pry::rescue { template = ser.run }
File.write('global_template.yml', template.to_yaml)

tenant = Tenant.find_by!(host: 'example.org')
tenant.switch!

service = MultiTenancy::Templates::TenantDeserializer.new
# template = YAML.load_file('global_template.yml')

Pry::rescue do
  service.deserialize(template)
end

h = {c: :a, d: :c, e: :f, f: :a}
h.sort do |(key1, value1), (key2, value2)|
  if key1 == value2
    -1
  elsif key2 == value1
    1
  else
    0
  end
end.to_h


# ----------------------------------------

models.map do |klass, records|
  [
    klass,
    records.flat_map do |_id, attributes|
      attributes.values.filter do |v|
        v.is_a?(MultiTenancy::Templates::Serializers::Core::Ref) && v.id
      end.map(&:klass)
    end.uniq
  ]
end.to_h


models[Permission].filter do |id,record_attributes|
  record_attributes.values.filter do |value|
    value.is_a?(MultiTenancy::Templates::Serializers::Core::Ref) && value.klass.nil?
  end.present?
end


begin

  rescue Exception => e
end