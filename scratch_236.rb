model_klass = User
multiloc_fields = model_klass.attribute_names.select { |f| f.end_with?('_multiloc') }

model_klass.transaction do
  model_klass.all.each do |record|
    multiloc_fields.each do |field|
      record[field]['en-IE'] ||= record[field]['en-GB'] || record[field]['en']
      record[field].compact!
    end

    record.save!
  end
end; nil

StaticPage.select do |page| page.title_multiloc['en-IE'].blank? end
Phase.select do |phase| phase.title_multiloc['en-IE'].blank? end
Topic.select do |topic| topic.title_multiloc['en-IE'].blank? end
CustomFieldOption.select do |topic| topic.title_multiloc['en-IE'].blank? end
EmailCampaigns::Campaigns::Manual.select do |topic| topic.subject_multiloc['en-IE'].blank? end