SELECT "activities"."item_id"
FROM "activities"
WHERE "activities"."item_id" IN (SELECT "phases"."id"
                                 FROM "phases"
                                          LEFT OUTER JOIN "projects"
                                                          ON "projects"."id" = "phases"."project_id"
                                          LEFT OUTER JOIN "admin_publications"
                                                          ON "admin_publications"."publication_type" =
                                                             'Project' AND
                                                             "admin_publications"."publication_id" =
                                                             "projects"."id"
                                          LEFT OUTER JOIN "admin_publications" "parents_admin_publications"
                                                          ON "parents_admin_publications"."id" =
                                                             "admin_publications"."parent_id"
                                 WHERE "admin_publications"."publication_status" =
                                       'published'
                                   AND ("parents_admin_publications"."publication_status" =
                                        'published' OR
                                        "admin_publications"."parent_id" IS NULL)
                                   AND "phases"."start_at" BETWEEN '2023-11-14' AND '2023-11-21')
  AND "activities"."action" = 'upcoming'
  AND "activities"."acted_at" BETWEEN '2023-11-14' AND '2023-11-21'