# print value of SECRET_TOKEN_TYPEFORM
cat cl2-deployment/.env-web | grep SECRET_TOKEN_TYPEFORM


# run the same command but through ssh (config = aws-prd-1)
ssh aws-prd-1 "cat cl2-deployment/.env-web | grep SECRET_TOKEN_TYPEFORM"

# same but for a list of servers: aws-prd-1, aws-can-1, aws-uk-1, aws-sam-1, aws-usw-1
servers=(aws-prd-1 aws-can-1 aws-uk-1 aws-sam-1 aws-usw-1)
for server in "${servers[@]}"; do
  echo -n "Server: $server: "
#  ssh $server "cat cl2-deployment/.env-web | grep SECRET_TOKEN_TYPEFORM"
  ssh $server "docker service ps cl2_web | grep Running"
done

# same but with a different variable name: DEFAULT_TYPEFORM_USER_TOKEN
servers=(aws-prd-1 aws-can-1 aws-uk-1 aws-sam-1 aws-usw-1)
variable_name=MATOMO_TOKEN_AUTH=
for server in "${servers[@]}"; do
  echo -n "Server $server: "
  ssh $server "cat cl2-deployment/.env-tenant-setup | grep $variable_name"
done

# transpose this on mac osx
ssh-keygen -t rsa -m PEM -N "" -f jwtRS256.key
jwt_rs256_private_key=$(sed -z 's/\n/\\\\n/g' jwtRS256.key)
openssl rsa -in jwtRS256.key -pubout -outform PEM -out jwtRS256.key.pub
jwt_rs256_public_key=$(sed -z 's/\n/\\\\n/g' jwtRS256.key.pub)

# mac version:
ssh-keygen -t rsa -m PEM -N "" -f jwtRS256.key
jwt_rs256_private_key=$(sed 's/$/\\n/' jwtRS256.key | tr -d '\n')
openssl rsa -in jwtRS256.key -pubout -outform PEM -out jwtRS256.key.pub
jwt_rs256_public_key=$(sed 's/$/\\n/' jwtRS256.key.pub | tr -d '\n')

# copy all the files in paris directory to cl2-deployment directory
cp paris/* cl2-deployment/


for server in "${servers[@]}"; do
  echo "Server $server: "
  ssh $server "cat cl2-deployment/secrets/google_cloud.json"
  echo ""
done


scp -r "aws-prd-1:~/cl2-deployment/secrets/*" "./paris-secrets"
scp -r paris-secret/* aws-paris-1:~/cl2-deployment/secrets

# create db and set up the schema
DISABLE_DATABASE_ENVIRONMENT_CHECK=1 bin/rails db:drop db:create db:schema:load


servers=(
  aws-frankfurt-1
  aws-can-1
  aws-uk-1
  aws-sam-1
  aws-usw-1
  aws-stockholm-1
  aws-paris-1
)



for server in "${servers[@]}"; do
  echo "Server $server: "
  ssh $server "sudo sh -c 'cat cl2-deployment/.env-web | grep AZURE_OPENAI_URI'"
  echo ""
done

for server in "${servers[@]}"; do
  echo "Server $server: "
  ssh $server "cat ~/.ssh/authorized_keys | cut -d ' ' -f 3 | cut -d '@' -f 1"
  echo ""
done | pbcopy


servers=(aws-frankfurt-1 aws-can-1 aws-uk-1 aws-sam-1 aws-usw-1 aws-stockholm-1 aws-paris-1)
for server in "${servers[@]}"; do
  echo -n "Server: $server: "
  ssh $server "cat cl2-deployment/.env-web | grep INTERCOM"
done
