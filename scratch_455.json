{"data": {"type": "json_forms_schema", "attributes": {"ui_schema_multiloc": {"en": {"type": "Categorization", "options": {"formId": "idea-form", "inputTerm": "idea"}, "elements": [{"type": "Control", "scope": "#/properties/", "label": "What is your idea?", "options": {"description": "<p><em>Pour déposer un projet dans le cadre du budget participatif 2025 de la ville de Bois-Guillaume, nous vous invitons à remplir formulaire ci-dessous. Nous attirons votre attention sur le fait qu’un projet doit respecter quelques règles, comme : être d’intérêt g<PERSON>, sur le territoire Bois-Guillaumais, ou encore concerner des dépenses d’investissement (un achat de mobilier urbain, de bien durable, d’équipement…), pour plus de précisions nous vous invitons à vous référer au règlement intérieur. Si des questions persistes, contactez-nous sur <a href=\"mailto:<EMAIL>\" target=\"_blank\" rel=\"noreferrer noopener nofollow\"><EMAIL></a>.</em></p>", "input_type": "section", "isAdminField": false, "hasRule": false}}, {"type": "VerticalLayout", "options": {"input_type": "text_multiloc", "render": "multiloc"}, "elements": [{"type": "Control", "scope": "#/properties/title_multiloc/properties/en", "label": "Title", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "isAdminField": false, "hasRule": false, "trim_on_blur": true, "locale": "en"}}, {"type": "Control", "scope": "#/properties/title_multiloc/properties/da-DK", "label": "Title", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "isAdminField": false, "hasRule": false, "trim_on_blur": true, "locale": "da-DK"}}, {"type": "Control", "scope": "#/properties/title_multiloc/properties/de-DE", "label": "Title", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "isAdminField": false, "hasRule": false, "trim_on_blur": true, "locale": "de-DE"}}, {"type": "Control", "scope": "#/properties/title_multiloc/properties/en-GB", "label": "Title", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "isAdminField": false, "hasRule": false, "trim_on_blur": true, "locale": "en-GB"}}, {"type": "Control", "scope": "#/properties/title_multiloc/properties/es-ES", "label": "Title", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "isAdminField": false, "hasRule": false, "trim_on_blur": true, "locale": "es-ES"}}, {"type": "Control", "scope": "#/properties/title_multiloc/properties/fr-FR", "label": "Title", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "isAdminField": false, "hasRule": false, "trim_on_blur": true, "locale": "fr-FR"}}, {"type": "Control", "scope": "#/properties/title_multiloc/properties/nl-BE", "label": "Title", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "isAdminField": false, "hasRule": false, "trim_on_blur": true, "locale": "nl-BE"}}, {"type": "Control", "scope": "#/properties/title_multiloc/properties/nl-NL", "label": "Title", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "isAdminField": false, "hasRule": false, "trim_on_blur": true, "locale": "nl-NL"}}]}, {"type": "VerticalLayout", "options": {"input_type": "html_multiloc", "render": "multiloc"}, "elements": [{"type": "Control", "scope": "#/properties/body_multiloc/properties/en", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "isAdminField": false, "hasRule": false, "render": "WYSIWYG", "locale": "en"}}, {"type": "Control", "scope": "#/properties/body_multiloc/properties/da-DK", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "isAdminField": false, "hasRule": false, "render": "WYSIWYG", "locale": "da-DK"}}, {"type": "Control", "scope": "#/properties/body_multiloc/properties/de-DE", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "isAdminField": false, "hasRule": false, "render": "WYSIWYG", "locale": "de-DE"}}, {"type": "Control", "scope": "#/properties/body_multiloc/properties/en-GB", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "isAdminField": false, "hasRule": false, "render": "WYSIWYG", "locale": "en-GB"}}, {"type": "Control", "scope": "#/properties/body_multiloc/properties/es-ES", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "isAdminField": false, "hasRule": false, "render": "WYSIWYG", "locale": "es-ES"}}, {"type": "Control", "scope": "#/properties/body_multiloc/properties/fr-FR", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "isAdminField": false, "hasRule": false, "render": "WYSIWYG", "locale": "fr-FR"}}, {"type": "Control", "scope": "#/properties/body_multiloc/properties/nl-BE", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "isAdminField": false, "hasRule": false, "render": "WYSIWYG", "locale": "nl-BE"}}, {"type": "Control", "scope": "#/properties/body_multiloc/properties/nl-NL", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "isAdminField": false, "hasRule": false, "render": "WYSIWYG", "locale": "nl-NL"}}]}, {"type": "Control", "scope": "#/properties/location_description", "label": "Location", "options": {"description": "<p>Indiquer ici la localisation de votre projet</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/selon_vous_a_combien_de_personnes_ce_projet_va_t_il_beneficier_438", "label": "<PERSON><PERSON><PERSON>vous, à combien de personnes ce projet va-t-il bénéficier ?", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/selon_vous_a_combien_s_eleve_le_montant_de_votre_projet_iuo", "label": "<PERSON><PERSON><PERSON>vous, à combien s'élève le montant de votre projet ?", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/deposez_vous_ce_projet_au_nom_d_un_collectif_d_habitant_oah", "label": "<PERSON><PERSON><PERSON><PERSON>-vous ce projet au nom d'un collectif d'habitant ?", "options": {"description": "", "input_type": "select", "isAdminField": false, "hasRule": false, "dropdown_layout": false, "enumNames": ["O<PERSON>", "Non"]}}, {"type": "Control", "scope": "#/properties/si_oui_merci_de_preciser_2vo", "label": "Si oui, merci de préciser :", "options": {"description": "", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/", "label": "Images and attachments", "options": {"description": "", "input_type": "section", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/idea_images_attributes", "label": "Images", "options": {"description": "<p>Nous vous invitons à illustrer votre projet en nous communiquant une image.</p>", "input_type": "image_files", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/idea_files_attributes", "label": "Attachments", "options": {"description": "<p>Si vous avez des documents complémentaires à nous fournir, merci de les télécharger ici</p>", "input_type": "files", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/", "label": "Details", "options": {"description": "", "input_type": "section", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/nom_nqr", "label": "Nom", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/prenom_eaf", "label": "Prénom", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/mail_nvb", "label": "Mail", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/telephone_68y", "label": "Téléphone", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/reglement_interieur_x49", "label": "Règlement intérieur ", "options": {"description": "", "input_type": "select", "isAdminField": false, "hasRule": false, "dropdown_layout": false, "enumNames": ["Je reconnais avoir pris connaissance du règlement intérieur du budget participatif que je m'engage à respecter."]}}, {"type": "Page", "options": {"input_type": "page", "id": "59f75ae8-1cc2-40a5-add5-a44087be89ba", "title": "Merci pour votre participation !", "description": "Votre contribution a été soumise avec succès.", "page_layout": "default", "map_config_id": null}, "elements": []}]}}}}}