- The problem is caused by an abnormally large number of `MapConfigs` in the DB.
- There are actually 2 tenants with a huge number of `MapConfigs`:
    - [28d767dc-0dda-42d8-92cb-a0fa51466ba3](https://engage.southwark.gov.uk) 1,769,484
    - [5d2fb6df-0412-49f9-a97f-72072cb2dde0](https://yourvoice.brighton-hove.gov.uk) 525,319

- MapConfigs were not all created at the same time. For:

```
CustomMaps::MapConfig.group("date(created_at)").count
=>
{Mon, 26 Feb 2024=>1,
 Wed, 20 Mar 2024=>1,
 Mon, 13 May 2024=>1,
 <PERSON><PERSON>, 14 May 2024=>3,
 Wed, 15 May 2024=>4,
 Thu, 16 May 2024=>1,
 Mon, 20 May 2024=>25,
 Wed, 22 May 2024=>32,
 Thu, 23 May 2024=>192,
 Fri, 24 May 2024=>256,
 Thu, 30 May 2024=>1,
 Thu, 06 Jun 2024=>1024,
 Fri, 07 Jun 2024=>1,
 Thu, 13 Jun 2024=>7680,
 <PERSON><PERSON>, 18 Jun 2024=>27649,
 Thu, 20 Jun 2024=>847873,
 <PERSON>, 24 Jun 2024=>4,
 <PERSON>e, 25 Jun 2024=>884736,
 Fri, 12 Jul 2024=>1}
```