# curl 'http://localhost:3000/web_api/v1/ideas/bd81edcb-bbc7-4ed9-a24c-5a2498f0589a/reactions' -X POST -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0' -H 'Accept: */*' -H 'Accept-Language: en-US,en;q=0.5' -H 'Accept-Encoding: gzip, deflate, br, zstd' -H 'Referer: http://localhost:3000/en/ideas/eos-fuga-quibusdam-id?phase_context=3ba9ed04-6a05-47ec-ade8-cbde6ec24287' -H 'Content-Type: application/json' -H 'Authorization: Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' -H 'Origin: http://localhost:3000' -H 'DNT: 1' -H 'Connection: keep-alive' -H 'Cookie: __profilin=p%3Dt; cl2_jwt=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' -H 'Sec-Fetch-Dest: empty' -H 'Sec-Fetch-Mode: cors' -H 'Sec-Fetch-Site: same-origin' -H 'Priority: u=4' -H 'Pragma: no-cache' -H 'Cache-Control: no-cache' --data-raw '{"user_id":"386d255e-2ff1-4192-8e50-b3022576be50","mode":"down"}'
POST http://localhost:3000/web_api/v1/ideas/bd81edcb-bbc7-4ed9-a24c-5a2498f0589a/reactions
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0
Accept: */*
Accept-Language: en-US,en;q=0.5
Accept-Encoding: gzip, deflate, br, zstd
Referer: http://localhost:3000/en/ideas/eos-fuga-quibusdam-id?phase_context=3ba9ed04-6a05-47ec-ade8-cbde6ec24287
Authorization: Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Origin: http://localhost:3000
DNT: 1
Connection: keep-alive
Cookie: __profilin=p%3Dt; cl2_jwt=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
Priority: u=4
Pragma: no-cache
Cache-Control: no-cache
Content-Type: application/json

{
  "user_id": "386d255e-2ff1-4192-8e50-b3022576be50",
  "mode": "down"
}

<> 2025-04-28T150840.201.json
<> 2025-04-28T150821.422.json

###

