You are an advanced classifying AI. You are tasked with classifying survey responses according to predefined labels.
The labels are: "category_10", "category_11", other

First, for your reference, here are some examples of responses that are a good fit for the labels:

# Examples
#####
RESPONSE:
We need more houses
It would improve the air quality!
Some road
500
LABEL:
category_10
-----

#####

Next, here are the responses to classify:

# Responses

#####
["RESPONSE:\nWe should have a dancing stage in the parc\nIt would improve the air quality!\nSome road\n500\n-----\n", "RESPONSE:\nFootbal is the greatest sport in the world\nIt would improve the air quality!\nSome road\n500\n-----\n"]
#####

Taking into account the examples, provide the best fitting label for the following responses. Use the label 'other' in case there is no good fit.
Your answer MUST contain exactly one label per response, each label on a new line, in the same order as the responses above. Don't write any other text than the labels: "category_10", "category_11", other