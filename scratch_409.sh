# get size of directory './uploads' (local directory, not a server)
# du -sh ./uploads
# ACL in postgres means Access Control List. This is a list of permissions attached to an object

pg_restore: while PROCESSING TOC:
pg_restore: from TOC entry 20091; 0 0 ACL TABLE activities postgres
pg_restore: error: could not execute query: ERROR:  role "read_access" does not exist
Command was: GRANT SELECT ON TABLE demo_stg_govocal_com.activities TO read_access;

# processing TOC means processing the Table of Contents. The Table of Contents is a list of all the objects that are being restored.
# In this case, the TOC entry 20091 is an ACL (Access Control List) for the table "activities". The error message is saying that the role "read_access" does not exist, which means that the permissions cannot be granted to that role. This could be due to a missing role in the database or a typo in the permissions script.

# find hosted zone by domain name
aws-vault exec prd-admin -- aws route53 list-hosted-zones-by-name --dns-name govocal.com

# find cloudfront distribution using the following tags:
#  cl2_zone_domain: govocal.com
aws-vault exec prd-admin -- aws cloudfront list-distributions --query "DistributionList.Items[?Tags.Items[?Key=='cl2_zone_domain' && Value=='govocal.com']]"

# git unstage file but keep changes
git reset HEAD -- .env-prd