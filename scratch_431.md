Hey everyone! Today, I kept working on the technical solution for the preview link. My current conclusions is that the decision we made last week to essentially mimic the behavior of the “published project in a draft folder” hack does not fundamentally simplify things up from a technical perspective. Unlisting a project while still making it accessible via a secret preview link is easy enough. However, revoking the access seems more complex.

The problem is still the same. A project is a composite resource that involves many different types of resources (e.g. phases, ideas, comments, votes, custom forms). When the project page is rendered, the front-end fetches all of these resources via independent API calls to the back-end and assembles the data into a single view. The main question is: How can the back-end distinguish between a request (for example, for a phase) that happens in the context of a normal project page and a request that happens in the context of a project preview? I may miss something, but I don't see any way around this if we want to make:
1. the preview link revocable
2. the project accessible only via the preview link and not by slug

Let's take a concrete example. Let's say we navigate to a project page at `demo.stg.govocal.com/en/projects/my-project`. The front-end requests, among other things, the project and the list of phases:
```
GET https://demo.stg.govocal.com/web_api/v1/projects/by_slug/my-project
GET https://demo.stg.govocal.com/web_api/v1/projects/4c323a7c-9010-4ce8-ab10-6d37a9b9f87e/phases
```

Now, if we access the preview at `demo.stg.govocal.com/en/projects/preview/a323ee0321d3b4fd3c57c748ed43344750f3b3b9` (or something like that), the front-end needs to make similar requests. The project requests could be replaced by something like this:
```
GET https://demo.stg.govocal.com/web_api/v1/projects/by-secret-token-whatever/a323ee0321d3b4fd3c57c748ed43344750f3b3b9
```
but it's not sufficient. The phase request should also be modified somehow. If it's not, it implies that the phases are necessarily made accessible to anyone, regardless of the project status or any sort of permissions, and the only secret that protects it is the project identifier, which is not a strong secret and cannot be revoked (not infeasible technically, but I would strongly oppose it). So, I'm still getting back to the same question: what should those API requests look like?

One option is to adapt them similarly to the project request. For example:
```
GET https://demo.stg.govocal.com/web_api/v1/projects/by-secret-token-whatever/a323ee0321d3b4fd3c57c748ed43344750f3b3b9/phases
```
but I think everyone would agree that it's not the way to go.

Other option:
```
GET https://demo.stg.govocal.com/web_api/v1/projects/4c323a7c-9010-4ce8-ab10-6d37a9b9f87e/phases?preview_token=a323ee0321d3b4fd3c57c748ed43344750f3b3b9
```
It's somewhot similar to the "assume_token" solution I proposed a few weeks ago (though less general). We still have to find a way to take this parameter into account in the back-end policies, but it should be doable. However, it would affect a lot of endpoints. Not as much as worried by the amount of work, but by the jump in complexity on both the front-end and the back-end.

What do you think? (= Mayday!)


It might not be the best approach to use the same status (published) for both unlisted projects for review and unlisted projects for limited