rspec './engines/commercial/id_id_card_lookup/spec/jobs/load_id_cards_job_spec.rb[1:1:1]' './engines/ee/multi_tenancy/spec/services/track_segment_service_spec.rb[1:1:1,1:2:1,1:3:1]'


bin/rspec --bisect spec/acceptance/initiatives_spec.rb spec/models/user_spec.rb spec/policies/idea_policy_spec.rb spec/models/idea_spec.rb spec/acceptance/admin_publications_spec.rb engines/commercial/granular_permissions/spec/services/participation_context_service_spec.rb engines/free/user_confirmation/spec/acceptance/user_confirmation/resend_codes_spec.rb spec/jobs/application_job_spec.rb spec/policies/idea_vote_policy_spec.rb engines/free/user_confirmation/spec/acceptance/user_confirmation/confirmations_spec.rb engines/commercial/idea_custom_fields/spec/acceptance/idea_custom_fields_spec.rb spec/acceptance/baskets_spec.rb spec/acceptance/initiative_comments_spec.rb engines/commercial/admin_api/spec/acceptance/projects_spec.rb spec/uploaders/header_bg_uploader_spec.rb engines/free/user_confirmation/spec/interactors/user_confirmation/send_confirmation_code_spec.rb spec/policies/initiative_vote_policy_spec.rb engines/commercial/idea_custom_fields/spec/policies/idea_custom_field_policy_spec.rb spec/acceptance/comment_votes_spec.rb engines/free/user_confirmation/spec/models/user_spec.rb engines/commercial/idea_custom_fields/spec/models/idea_spec.rb engines/commercial/project_folders/spec/services/user_role_service_spec.rb spec/models/basket_spec.rb engines/commercial/verification/spec/services/participation_context_service_spec.rb spec/policies/project_file_policy_spec.rb engines/commercial/smart_groups/spec/policies/idea_policy_spec.rb engines/commercial/machine_translations/spec/acceptance/machine_translations_spec.rb engines/free/user_confirmation/spec/interactors/user_confirmation/deliver_confirmation_code_spec.rb spec/policies/project_image_policy_spec.rb engines/commercial/project_management/spec/acceptance/moderators_spec.rb engines/free/polls/spec/acceptance/responses_spec.rb spec/acceptance/areas_spec.rb engines/commercial/project_management/spec/policies/idea_policy_spec.rb spec/services/side_fx_idea_spec.rb engines/commercial/project_management/spec/services/user_role_service_spec.rb engines/commercial/smart_groups/spec/rules/custom_field_number_spec.rb spec/acceptance/comment_spam_reports_spec.rb engines/commercial/idea_assignment/spec/services/side_fx_idea_spec.rb spec/acceptance/project_files_spec.rb engines/commercial/idea_assignment/spec/acceptance/moderators_spec.rb spec/services/side_fx_comment_spec.rb spec/services/avatars_service_spec.rb engines/commercial/smart_groups/spec/policies/project_policy_spec.rb spec/services/xlsx_export/value_visitor_spec.rb spec/requests/google_authentication_spec.rb engines/commercial/flag_inappropriate_content/spec/acceptance/moderations_spec.rb spec/acceptance/events_spec.rb engines/commercial/user_custom_fields/spec/acceptance/ref_distributions/categorical_distributions_spec.rb

# repeat next command 3 times
for run in {1..10}; do
  bin/rspec engines/commercial/user_custom_fields/spec/acceptance/ref_distributions/categorical_distributions_spec.rb
done

bin/rspec --bisect spec/acceptance/initiatives_spec.rb spec/models/user_spec.rb spec/policies/idea_policy_spec.rb spec/models/idea_spec.rb spec/acceptance/admin_publications_spec.rb engines/commercial/granular_permissions/spec/services/participation_context_service_spec.rb engines/free/user_confirmation/spec/acceptance/user_confirmation/resend_codes_spec.rb spec/jobs/application_job_spec.rb spec/policies/idea_vote_policy_spec.rb engines/free/user_confirmation/spec/acceptance/user_confirmation/confirmations_spec.rb engines/commercial/idea_custom_fields/spec/acceptance/idea_custom_fields_spec.rb spec/acceptance/baskets_spec.rb spec/acceptance/initiative_comments_spec.rb engines/commercial/admin_api/spec/acceptance/projects_spec.rb spec/uploaders/header_bg_uploader_spec.rb engines/free/user_confirmation/spec/interactors/user_confirmation/send_confirmation_code_spec.rb spec/policies/initiative_vote_policy_spec.rb engines/commercial/idea_custom_fields/spec/policies/idea_custom_field_policy_spec.rb spec/acceptance/comment_votes_spec.rb engines/free/user_confirmation/spec/models/user_spec.rb engines/commercial/idea_custom_fields/spec/models/idea_spec.rb engines/commercial/project_folders/spec/services/user_role_service_spec.rb spec/models/basket_spec.rb engines/commercial/verification/spec/services/participation_context_service_spec.rb spec/policies/project_file_policy_spec.rb engines/commercial/smart_groups/spec/policies/idea_policy_spec.rb engines/commercial/machine_translations/spec/acceptance/machine_translations_spec.rb engines/free/user_confirmation/spec/interactors/user_confirmation/deliver_confirmation_code_spec.rb spec/policies/project_image_policy_spec.rb engines/commercial/project_management/spec/acceptance/moderators_spec.rb engines/free/polls/spec/acceptance/responses_spec.rb spec/acceptance/areas_spec.rb engines/commercial/project_management/spec/policies/idea_policy_spec.rb spec/services/side_fx_idea_spec.rb engines/commercial/project_management/spec/services/user_role_service_spec.rb engines/commercial/smart_groups/spec/rules/custom_field_number_spec.rb spec/acceptance/comment_spam_reports_spec.rb engines/commercial/idea_assignment/spec/services/side_fx_idea_spec.rb spec/acceptance/project_files_spec.rb engines/commercial/idea_assignment/spec/acceptance/moderators_spec.rb spec/services/side_fx_comment_spec.rb spec/services/avatars_service_spec.rb engines/commercial/smart_groups/spec/policies/project_policy_spec.rb spec/services/xlsx_export/value_visitor_spec.rb spec/requests/google_authentication_spec.rb engines/commercial/flag_inappropriate_content/spec/acceptance/moderations_spec.rb spec/acceptance/events_spec.rb engines/commercial/user_custom_fields/spec/acceptance/ref_distributions/categorical_distributions_spec.rb

for i in `seq 50`; do
  bin/rspec './engines/commercial/user_custom_fields/spec/acceptance/user_custom_fields_spec.rb[1:2:1:1,1:2:2:1]';
  [[ ! $? = 0 ]] && break ;
done