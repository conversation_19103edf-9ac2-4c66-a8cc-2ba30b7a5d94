{"data": {"type": "survey_results", "attributes": {"results": [{"inputType": "page", "question": {"nl-NL": "<PERSON><PERSON> kern"}, "description": {}, "customFieldId": "65db42de-9ab5-4b0e-9fe8-64d39bba2343", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 39, "questionResponseCount": 38, "pageNumber": 1, "questionNumber": null, "logic": {}}, {"inputType": "select", "question": {"nl-NL": "Voor welke kern wilt u participeren?"}, "description": {}, "customFieldId": "a87dfb8b-7a33-4936-8f73-80c6c278bbae", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 39, "questionResponseCount": 38, "pageNumber": null, "questionNumber": 1, "logic": {"answer": {"aagtekerke_t7n": {"id": "0fab936b-b667-4273-bede-9d2bbab06de5", "nextPageNumber": 2, "numQuestionsSkipped": 0}, "biggekerke_ryh": {"id": "26141e2a-d0e4-47da-967e-8a99411e2d93", "nextPageNumber": 3, "numQuestionsSkipped": 12}, "domburg_68k": {"id": "9552e08a-7cec-4769-adbd-7dfac952a715", "nextPageNumber": 4, "numQuestionsSkipped": 24}, "gapinge_oyn": {"id": "b8b3644d-65ad-4281-9cac-bb89b35462b2", "nextPageNumber": 5, "numQuestionsSkipped": 36}, "grijpskerke_l8j": {"id": "0459062e-5f62-48b0-a64a-64c84185d08e", "nextPageNumber": 6, "numQuestionsSkipped": 48}, "koudekerke_ygb": {"id": "d862f25a-c52c-40f5-a207-91671fac48d3", "nextPageNumber": 7, "numQuestionsSkipped": 60}, "meliskerke_d8o": {"id": "82fc7353-5c87-4201-994b-007b980edbb9", "nextPageNumber": 8, "numQuestionsSkipped": 72}, "oostkapelle_g9z": {"id": "8071686b-8d8b-42e9-a933-e8332271e079", "nextPageNumber": 9, "numQuestionsSkipped": 84}, "serooskerke_cm9": {"id": "15dd90ae-20db-4a80-8e36-662d575d98d0", "nextPageNumber": 10, "numQuestionsSkipped": 96}, "veere_v04": {"id": "98f0262b-0775-43a4-942e-67f12ecbed1b", "nextPageNumber": 11, "numQuestionsSkipped": 108}, "vrouwenpolder_a9x": {"id": "92c0d202-0902-47f6-bbd1-424efdfedc93", "nextPageNumber": 12, "numQuestionsSkipped": 120}, "westkapelle_4a2": {"id": "678fde69-4868-49d6-bee1-727d92a321cc", "nextPageNumber": 13, "numQuestionsSkipped": 132}, "zoutelande_dq4": {"id": "11401413-8959-4f60-96e4-4c151d7fbbda", "nextPageNumber": 14, "numQuestionsSkipped": 144}}}, "totalPickCount": 39, "answers": [{"answer": "westkapelle_4a2", "count": 11}, {"answer": "oostkapelle_g9z", "count": 7}, {"answer": "domburg_68k", "count": 4}, {"answer": "koudekerke_ygb", "count": 4}, {"answer": "vrouwenpolder_a9x", "count": 3}, {"answer": "aagtekerke_t7n", "count": 2}, {"answer": "meliskerke_d8o", "count": 2}, {"answer": "biggekerke_ryh", "count": 1}, {"answer": "grijpskerke_l8j", "count": 1}, {"answer": "serooskerke_cm9", "count": 1}, {"answer": "veere_v04", "count": 1}, {"answer": "zoutelande_dq4", "count": 1}, {"answer": null, "count": 1}, {"answer": "<PERSON>e_oyn", "count": 0}], "multilocs": {"answer": {"aagtekerke_t7n": {"title_multiloc": {"nl-NL": "Aagtekerke"}}, "biggekerke_ryh": {"title_multiloc": {"nl-NL": "Biggekerke"}}, "domburg_68k": {"title_multiloc": {"nl-NL": "Domburg"}}, "gapinge_oyn": {"title_multiloc": {"nl-NL": "Gapinge"}}, "grijpskerke_l8j": {"title_multiloc": {"nl-NL": "Grijpskerke"}}, "koudekerke_ygb": {"title_multiloc": {"nl-NL": "Koudekerke"}}, "meliskerke_d8o": {"title_multiloc": {"nl-NL": "Meliskerke"}}, "oostkapelle_g9z": {"title_multiloc": {"nl-NL": "Oostkapelle"}}, "serooskerke_cm9": {"title_multiloc": {"nl-NL": "Serooskerke"}}, "veere_v04": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "vrouwenpolder_a9x": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "westkapelle_4a2": {"title_multiloc": {"nl-NL": "Westkapelle"}}, "zoutelande_dq4": {"title_multiloc": {"nl-NL": "Zoutelande"}}}}}, {"inputType": "page", "question": {"nl-NL": "Aagtekerke"}, "description": {}, "customFieldId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": 2, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 143}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern"}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "86c505c0-7786-4b9e-95b7-df9be48919ec", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 2, "logic": {}, "totalPickCount": 3, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 1}, {"answer": 5, "count": 1}, {"answer": null, "count": 1}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?"}, "description": {"nl-NL": ""}, "customFieldId": "0892c396-ea4d-43d0-8f10-cc7dcd630f89", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 3, "logic": {}, "totalPickCount": 4, "answers": [{"answer": null, "count": 1}, {"answer": "sportvoorzieningen_6of", "count": 1}, {"answer": "winkel_aanbod_kms", "count": 1}, {"answer": "woning_aanbod_2k8", "count": 0}, {"answer": "speelplekken_ksz", "count": 0}, {"answer": "groen_d6t", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"sportvoorzieningen_6of": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_kms": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_2k8": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_ksz": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_d6t": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "<PERSON><PERSON> en frequenter open<PERSON><PERSON> vervoer"}]}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten?  </p>"}, "customFieldId": "cd9cd2eb-c1d1-4855-b873-5aa7821aa05b", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 4, "logic": {}, "totalPickCount": 5, "answers": [{"answer": null, "count": 1}, {"answer": "horeca_y6b", "count": 1}, {"answer": "park_hqg", "count": 1}, {"answer": "pleinen_mqu", "count": 0}, {"answer": "speeltuin_8kw", "count": 0}, {"answer": "other", "count": 2}], "multilocs": {"answer": {"pleinen_mqu": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_y6b": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_hqg": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_8kw": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "Bootcamp park"}, {"answer": "<PERSON><PERSON> zou graag meer winkels zien en een café of lunchroom"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "b8cf47d0-41e9-4819-aa31-fdfdc4d9d49e", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 5, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_nja": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 1, "percentage": 0.5}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 0.5}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_j60": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 2, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_1g9": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 0.5}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.5}, {"answer": null, "count": 37}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist  in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "830ab17c-f274-4194-9bee-a71e58a2a187", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 6, "logic": {}, "textResponses": [{"answer": "Buiten sporttoestellen"}, {"answer": "<PERSON><PERSON> gro<PERSON> p<PERSON>en"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "2e63c96c-c270-4e76-bc14-bc009e530e5e", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 7, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_i3k": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 0.5}, {"answer": 2, "count": 1, "percentage": 0.5}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_bzt": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen. "}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 0.5}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 0.5}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fmz": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 1, "percentage": 0.5}, {"answer": 4, "count": 1, "percentage": 0.5}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_5hr": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden. "}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 1, "percentage": 0.5}, {"answer": 4, "count": 1, "percentage": 0.5}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "e4e7ca2e-bdbd-4509-b862-f43993767182", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 8, "logic": {}, "totalPickCount": 3, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 0}, {"answer": 7, "count": 0}, {"answer": 8, "count": 1}, {"answer": 9, "count": 0}, {"answer": 10, "count": 1}, {"answer": null, "count": 1}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "494c7715-3d9f-4dbb-850f-f2f282952f8d", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 9, "logic": {}, "average_rankings": {"alleenstaanden_a8r": "3.5", "gezinnen_ok7": "2.0", "sociale_huurders_8hy": "3.5", "luxe_woningzoekers_4p0": "4.0", "zzp_ers_met_werkruimte_in_woning_xqd": "7.0", "starters_k5g": "2.5", "senioren_sr9": "4.5"}, "rankings_counts": {"starters_k5g": {"1": 0, "2": 1, "3": 1, "4": 0, "5": 0}, "gezinnen_ok7": {"1": 1, "2": 0, "3": 1, "4": 0, "5": 0}, "senioren_sr9": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 1}, "alleenstaanden_a8r": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0}, "sociale_huurders_8hy": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 1}}, "multilocs": {"answer": {"starters_k5g": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_ok7": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_sr9": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_a8r": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_8hy": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "d7517cfa-6897-4fa8-b677-488b6fadd87e", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 10, "logic": {}, "average_rankings": {"andere_3pj": "5.0", "appartementen_tsi": "4.0", "hofjeswoningen_9ut": "4.0", "knarrenhof_d1c": "5.0", "rijwoningen_f86": "3.5", "seniorenwoningen_fu8": "3.5", "tiny_houses_8cs": "8.0", "twee_onder_een_kapwoning_whc": "3.5", "villa_8lr": "4.5"}, "rankings_counts": {"appartementen_tsi": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0, "6": 1, "7": 0}, "seniorenwoningen_fu8": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 0, "6": 0, "7": 0}, "tiny_houses_8cs": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1}, "villa_8lr": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "twee_onder_een_kapwoning_whc": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}, "rijwoningen_f86": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 1, "7": 0}, "knarrenhof_d1c": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 0, "7": 1}}, "multilocs": {"answer": {"appartementen_tsi": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_fu8": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_8cs": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_8lr": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_whc": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_f86": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_d1c": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "60ad481a-c217-41da-b391-eaff6b073215", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 11, "logic": {}, "totalPickCount": 6, "answers": [{"answer": null, "count": 1}, {"answer": "landelijk_6ze", "count": 2}, {"answer": "dorps_s6n", "count": 2}, {"answer": "duurzaam_e6i", "count": 1}, {"answer": "klassiek_3er", "count": 0}, {"answer": "in_het_groen_in6", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_6ze": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_s6n": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_e6i": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_3er": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_in6": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen"}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "ec56305a-074a-48fb-9440-9a4c92591b77", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 12, "logic": {}, "textResponses": [{"answer": "Uitbreiding mag niet ten koste gaan van het landelijke karakter van het dorp. Dus geen toeristische voorzieningen. Geen toeristen in particuliere woningen en geen vakantieparken in of bij het dorp. De landbouwgebieden moeten worden behouden om het karakter van Walcheren te behouden. Dus niet volbouwen met woningen (voor toeristen). Behoudt het groene gebied in het dorp"}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart "}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "f8380cb8-e2de-4fb0-859c-96c7b26cd6a9", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 13, "logic": {}, "mapConfigId": "796fc8ff-2c40-4826-a633-d5aefac8d48c", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.5039993418163515, 51.54715058457706]}}]}, {"inputType": "page", "question": {"nl-NL": "Biggekerke"}, "description": {}, "customFieldId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": 3, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 131}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern"}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "9a74b660-18cf-4599-b219-38244e4cbf5a", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 14, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 1}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?"}, "description": {"nl-NL": ""}, "customFieldId": "d2bf9ac9-e08f-4b09-aaa6-b5ff81dc56e7", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 15, "logic": {}, "totalPickCount": 2, "answers": [{"answer": null, "count": 0}, {"answer": "sportvoorzieningen_q39", "count": 1}, {"answer": "groen_vxw", "count": 1}, {"answer": "winkel_aanbod_l6w", "count": 0}, {"answer": "woning_aanbod_n82", "count": 0}, {"answer": "speelplekken_8xh", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_q39": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_l6w": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_n82": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_8xh": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_vxw": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "f7d73a80-e315-4888-81ac-be8a4f1c0ab4", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 16, "logic": {}, "totalPickCount": 3, "answers": [{"answer": null, "count": 0}, {"answer": "pleinen_b39", "count": 1}, {"answer": "park_s4o", "count": 1}, {"answer": "horeca_4ui", "count": 0}, {"answer": "speeltuin_nu7", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"pleinen_b39": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_4ui": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_s4o": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_nu7": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "eigen straat"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "baefb095-3973-4d7d-8400-a29bee19a249", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 17, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_1xe": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_4o0": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_jnz": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving  "}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "ab0aedac-c763-4e84-b801-8cc4b6744ba9", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 18, "logic": {}, "textResponses": [{"answer": "wandel<PERSON>en die droog blijven in herfst, winter, voorjaar"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "a3872614-fe9e-4901-864d-3849f6ec12c5", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 19, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_hg4": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_v5h": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen mijn eigen kern doen."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 1.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_o8d": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_du2": {"question": {"nl-NL": " Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "2a668fdc-1455-4674-84b3-5ebaa9dabbd5", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 20, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 0}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 1}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "b9b989f5-c633-4c36-9a6c-bf672d3d66b3", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 21, "logic": {}, "average_rankings": {"alleenstaanden_u7i": "2.0", "sociale_huurders_she": "4.0", "senioren_b19": "1.0", "gezinnen_0cm": "5.0", "starters_86v": "3.0"}, "rankings_counts": {"starters_86v": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0}, "gezinnen_0cm": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1}, "senioren_b19": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0}, "alleenstaanden_u7i": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0}, "sociale_huurders_she": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0}}, "multilocs": {"answer": {"starters_86v": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_0cm": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_b19": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_u7i": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_she": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "7cee1b8b-0e08-48c6-993c-a1cce2fc4484", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 22, "logic": {}, "average_rankings": {"tiny_houses_iya": "7.0", "appartementen_ech": "1.0", "knarrenhof_suc": "2.0", "villa_pvg": "6.0", "twee_onder_een_kapwoning_5ul": "5.0", "seniorenwoningen_i5b": "3.0", "rijwoningen_fdo": "4.0"}, "rankings_counts": {"appartementen_ech": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "seniorenwoningen_i5b": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0}, "tiny_houses_iya": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1}, "villa_pvg": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 1, "7": 0}, "twee_onder_een_kapwoning_5ul": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}, "rijwoningen_fdo": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0, "6": 0, "7": 0}, "knarrenhof_suc": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}}, "multilocs": {"answer": {"appartementen_ech": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_i5b": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_iya": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_pvg": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_5ul": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_fdo": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_suc": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen "}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "b2ee01b3-e2aa-40d7-a24a-748c4272b02f", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 23, "logic": {}, "totalPickCount": 3, "answers": [{"answer": null, "count": 0}, {"answer": "landelijk_86l", "count": 1}, {"answer": "dorps_9oe", "count": 1}, {"answer": "in_het_groen_9ir", "count": 1}, {"answer": "duurzaam_04y", "count": 0}, {"answer": "klassiek_a0d", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_86l": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_9oe": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_04y": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_a0d": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_9ir": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen "}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "603f3eb8-c3ad-4651-9612-c0886f760307", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 24, "logic": {}, "textResponses": []}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart "}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "4f1dcea9-94f0-4313-8fe1-9178238db3c4", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 25, "logic": {}, "mapConfigId": "*************-414a-938e-be1a68b453c6", "pointResponses": []}, {"inputType": "page", "question": {"nl-NL": "Domburg"}, "description": {}, "customFieldId": "ac015b59-accc-48ca-89f8-7b20165c8ee4", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": 4, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 119}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern"}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "f4bbe7e0-69dd-41e5-a305-e7b00d4ebc81", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 26, "logic": {}, "totalPickCount": 4, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 1}, {"answer": 3, "count": 2}, {"answer": 4, "count": 0}, {"answer": 5, "count": 1}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?"}, "description": {"nl-NL": ""}, "customFieldId": "31818aa9-6af6-4ce6-8f9c-07cd65300b1d", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 27, "logic": {}, "totalPickCount": 7, "answers": [{"answer": null, "count": 0}, {"answer": "woning_aanbod_vtl", "count": 3}, {"answer": "winkel_aanbod_c6p", "count": 1}, {"answer": "speelplekken_kv0", "count": 1}, {"answer": "groen_xpv", "count": 1}, {"answer": "sportvoorzieningen_hmb", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"sportvoorzieningen_hmb": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_c6p": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_vtl": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_kv0": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_xpv": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "alle soorten woningbouw"}]}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "fa453051-9b7e-4d62-ae3c-706ebdd2bf73", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 28, "logic": {}, "totalPickCount": 5, "answers": [{"answer": null, "count": 0}, {"answer": "horeca_3mu", "count": 3}, {"answer": "pleinen_3qz", "count": 0}, {"answer": "park_8c3", "count": 0}, {"answer": "speeltuin_slr", "count": 0}, {"answer": "other", "count": 2}], "multilocs": {"answer": {"pleinen_3qz": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_3mu": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_8c3": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_slr": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "Gewoon in het dorp. Op een bank."}, {"answer": "Op het bankje aan het begin van de straat. <PERSON><PERSON><PERSON> staat er maar 1 meer. <PERSON><PERSON> oudere mensen die in hun eigen buurt willen kletsen met buren."}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "e7d4549a-72f7-41a1-b265-9e9f184086a0", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 29, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_sf6": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 1, "percentage": 0.25}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 3, "percentage": 0.75}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_9ks": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 1, "percentage": 0.25}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 0.25}, {"answer": 2, "count": 1, "percentage": 0.25}, {"answer": 1, "count": 1, "percentage": 0.25}, {"answer": null, "count": 35}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_u69": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 0.25}, {"answer": 3, "count": 1, "percentage": 0.25}, {"answer": 2, "count": 2, "percentage": 0.5}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "df8302ff-bc35-47a4-afc1-3fa2b7b7eec6", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 30, "logic": {}, "textResponses": [{"answer": "<PERSON><PERSON> plekje waar je rustig kunt zitten."}, {"answer": "Zitbankjes voor ouderen"}, {"answer": "speelvoorzieningen kinderen"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "4dd63da5-97e6-40e7-8f98-97a6b09b10fc", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 31, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_9b6": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 1, "percentage": 0.25}, {"answer": 4, "count": 2, "percentage": 0.5}, {"answer": 3, "count": 1, "percentage": 0.25}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_w9b": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 2, "percentage": 0.5}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 0.25}, {"answer": 2, "count": 1, "percentage": 0.25}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_3b8": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 1, "percentage": 0.25}, {"answer": 4, "count": 1, "percentage": 0.25}, {"answer": 3, "count": 1, "percentage": 0.25}, {"answer": 2, "count": 1, "percentage": 0.25}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_f6y": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 2, "percentage": 0.5}, {"answer": 2, "count": 1, "percentage": 0.25}, {"answer": 1, "count": 1, "percentage": 0.25}, {"answer": null, "count": 35}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "73a02f68-3c7b-4556-aeab-20cc9ea9c427", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 32, "logic": {}, "totalPickCount": 4, "answers": [{"answer": 1, "count": 1}, {"answer": 2, "count": 1}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 2}, {"answer": 6, "count": 0}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 0}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften "}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "d7213980-dd30-4141-884b-6e46db7a0e6d", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 33, "logic": {}, "average_rankings": {"sociale_huurders_840": "3.75", "alleenstaanden_8ip": "3.75", "starters_w29": "1.5", "senioren_9t3": "2.75", "gezinnen_8br": "3.25"}, "rankings_counts": {"starters_w29": {"1": 2, "2": 2, "3": 0, "4": 0, "5": 0}, "gezinnen_8br": {"1": 1, "2": 0, "3": 1, "4": 1, "5": 1}, "senioren_9t3": {"1": 1, "2": 0, "3": 2, "4": 1, "5": 0}, "alleenstaanden_8ip": {"1": 0, "2": 1, "3": 0, "4": 2, "5": 1}, "sociale_huurders_840": {"1": 0, "2": 1, "3": 1, "4": 0, "5": 2}}, "multilocs": {"answer": {"starters_w29": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_8br": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_9t3": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_8ip": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_840": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "2ba46a87-c391-47c5-8b7e-b3bf5a4c5450", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 34, "logic": {}, "average_rankings": {"tiny_houses_fpr": "4.75", "twee_onder_een_kapwoning_wp9": "3.25", "seniorenwoningen_veq": "3.25", "rijwoningen_sd0": "2.25", "knarrenhof_3lt": "5.25", "appartementen_lgn": "4.25", "villa_2d3": "5.0"}, "rankings_counts": {"appartementen_lgn": {"1": 0, "2": 1, "3": 0, "4": 1, "5": 1, "6": 1, "7": 0}, "seniorenwoningen_veq": {"1": 1, "2": 1, "3": 0, "4": 1, "5": 0, "6": 1, "7": 0}, "tiny_houses_fpr": {"1": 0, "2": 0, "3": 2, "4": 0, "5": 0, "6": 1, "7": 1}, "villa_2d3": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 1, "6": 0, "7": 2}, "twee_onder_een_kapwoning_wp9": {"1": 0, "2": 1, "3": 1, "4": 2, "5": 0, "6": 0, "7": 0}, "rijwoningen_sd0": {"1": 2, "2": 1, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}, "knarrenhof_3lt": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 1, "6": 1, "7": 1}}, "multilocs": {"answer": {"appartementen_lgn": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_veq": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_fpr": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_2d3": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_wp9": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_sd0": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_3lt": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "ce81b17f-7b02-4e40-9d9d-2dcd8c8eb484", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 35, "logic": {}, "totalPickCount": 7, "answers": [{"answer": null, "count": 0}, {"answer": "dorps_wk9", "count": 4}, {"answer": "klassiek_bjq", "count": 1}, {"answer": "in_het_groen_ax9", "count": 1}, {"answer": "landelijk_zvr", "count": 0}, {"answer": "duurzaam_b9c", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"landelijk_zvr": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_wk9": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_b9c": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_bjq": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_ax9": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "<PERSON><PERSON> formaat woning."}]}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen "}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "121efe33-ff0a-46b5-80ab-055a46d324bb", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 36, "logic": {}, "textResponses": [{"answer": "De locatie voor het geplande KPN Hotel zou een prachtige inbreidingslocatie zijn als bouwplaats voor een combinatie starters-senioren appartementen of rijtjeswoningen."}, {"answer": "<PERSON><PERSON> een echte badstatus"}, {"answer": "<PERSON><PERSON> overma<PERSON>e woningen maar op \"dorps\"formaat. Da<PERSON><PERSON> passen er ook meer in een straat."}, {"answer": "<PERSON><PERSON> dit object want het staat al lang te koop en plan er een rijtje met woningen voor senioren"}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "226b4d32-7653-435c-a197-bbe426d12fa8", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 37, "logic": {}, "mapConfigId": "b3f52d1a-a689-4cf9-97f7-63ab3f69c6b2", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.500694272415221, 51.56273174827632]}}, {"answer": {"type": "Point", "coordinates": [3.497272576393128, 51.56678731647801]}}, {"answer": {"type": "Point", "coordinates": [3.493256565603942, 51.562346733452756]}}]}, {"inputType": "page", "question": {"nl-NL": "Gapinge"}, "description": {}, "customFieldId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": 5, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 107}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern"}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "dd533ae8-044f-4fa0-ad6e-adcb102d95e6", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 38, "logic": {}, "totalPickCount": 0, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?"}, "description": {"nl-NL": ""}, "customFieldId": "d62795cb-bc15-4236-a865-c94bcafd5923", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 39, "logic": {}, "totalPickCount": 0, "answers": [{"answer": null, "count": 0}, {"answer": "sportvoorzieningen_j19", "count": 0}, {"answer": "winkel_aanbod_z4c", "count": 0}, {"answer": "woning_aanbod_rns", "count": 0}, {"answer": "speelplekken_xus", "count": 0}, {"answer": "groen_8v0", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_j19": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_z4c": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_rns": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_xus": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_8v0": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "361add83-1ed1-466f-9925-bcb9bddbb602", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 40, "logic": {}, "totalPickCount": 0, "answers": [{"answer": null, "count": 0}, {"answer": "pleinen_5n3", "count": 0}, {"answer": "horeca_t1f", "count": 0}, {"answer": "park_vc7", "count": 0}, {"answer": "speeltuin_7z8", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"pleinen_5n3": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_t1f": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_vc7": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_7z8": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "c5cb65d4-e0a5-46a4-815a-481344ba1561", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 41, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_p1h": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 0, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 39}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_6f8": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 0, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 39}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_6ny": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 0, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 39}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "232fc931-e8e3-4f38-be06-35f05e2b26f7", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 42, "logic": {}, "textResponses": []}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "597a4e18-d75c-46f0-a083-6a9a68828127", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 43, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_vye": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 0, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 39}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_4hs": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 0, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 39}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_icg": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 0, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 39}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_9o7": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 0, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 39}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "093e9597-4110-408b-b9ee-84f77c43615d", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 44, "logic": {}, "totalPickCount": 0, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 0}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 0}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "bca6df5b-ef1e-4523-804d-a040240089a0", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 45, "logic": {}, "average_rankings": {}, "rankings_counts": {"starters_rgk": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "gezinnen_jb4": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "senioren_hdm": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "alleenstaanden_i91": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "sociale_huurders_5pu": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0}}, "multilocs": {"answer": {"starters_rgk": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_jb4": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_hdm": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_i91": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_5pu": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "4eb20c9b-6e78-462a-a99b-20f4c2ad65ea", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 46, "logic": {}, "average_rankings": {}, "rankings_counts": {"appartementen_276": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "seniorenwoningen_4k3": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "villa_c1k": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "twee_onder_een_kapwoning_p62": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "rijwoningen_6gx": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "knarrenhof_9ba": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}}, "multilocs": {"answer": {"appartementen_276": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_4k3": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "villa_c1k": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_p62": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_6gx": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_9ba": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "813dcf0f-5263-4951-96d8-138b30290ff8", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 47, "logic": {}, "totalPickCount": 0, "answers": [{"answer": null, "count": 0}, {"answer": "landelijk_n4m", "count": 0}, {"answer": "dorps_op9", "count": 0}, {"answer": "duurzaam_sny", "count": 0}, {"answer": "klassiek_9x8", "count": 0}, {"answer": "in_het_groen_sd9", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_n4m": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_op9": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_sny": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_9x8": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_sd9": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen "}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "82590e79-b2af-4228-be15-7244f22abe8a", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 48, "logic": {}, "textResponses": []}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "ceea6cdc-35ce-4032-bcf1-a1c71b55a14b", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 0, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 49, "logic": {}, "mapConfigId": "a5534191-102f-427e-b317-2ca566dc767d", "pointResponses": []}, {"inputType": "page", "question": {"nl-NL": "Grijpskerke"}, "description": {}, "customFieldId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": 6, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 95}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern"}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "63e95b7c-6e40-476b-b892-d3b40ad1153d", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 50, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 1}, {"answer": 5, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?"}, "description": {"nl-NL": ""}, "customFieldId": "cb8aacbb-0315-4593-beaf-7b98cea2bdf1", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 51, "logic": {}, "totalPickCount": 1, "answers": [{"answer": null, "count": 0}, {"answer": "winkel_aanbod_fec", "count": 1}, {"answer": "sportvoorzieningen_9i0", "count": 0}, {"answer": "woning_aanbod_6bd", "count": 0}, {"answer": "speelplekken_t7z", "count": 0}, {"answer": "groen_qyx", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_9i0": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_fec": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_6bd": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_t7z": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_qyx": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "dd15b59d-3f91-4c75-ae7f-571bf64fb04e", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 52, "logic": {}, "totalPickCount": 1, "answers": [{"answer": null, "count": 0}, {"answer": "horeca_ydj", "count": 1}, {"answer": "pleinen_02t", "count": 0}, {"answer": "park_2bq", "count": 0}, {"answer": "speeltuin_t1w", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"pleinen_02t": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_ydj": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_2bq": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_t1w": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "e7aae6a9-4023-4fc5-a675-9c7f4f24011c", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 53, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_sgk": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_lw9": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_9hk": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "3d5cc898-c4fc-4c97-ae2b-b81f6c951196", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 54, "logic": {}, "textResponses": []}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "9dc3b35d-8639-4cda-b883-784b1c3976e9", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 55, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_5nm": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_pd3": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 1.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fd8": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_mfw": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften "}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "378f93a1-cb2d-4d00-b018-b03558b4e2e5", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 56, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 1}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 0}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "0f77a271-61ef-4b09-84b6-0a76cff73cd6", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 57, "logic": {}, "average_rankings": {"senioren_lbm": "2.0", "alleenstaanden_uo5": "3.0", "starters_ifo": "1.0", "gezinnen_x0l": "5.0", "sociale_huurders_dob": "4.0"}, "rankings_counts": {"starters_ifo": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0}, "gezinnen_x0l": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1}, "senioren_lbm": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0}, "alleenstaanden_uo5": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0}, "sociale_huurders_dob": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0}}, "multilocs": {"answer": {"starters_ifo": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_x0l": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_lbm": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_uo5": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_dob": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften "}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "fad8ae23-0e6e-43bd-bf7d-6a554d2b0123", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 58, "logic": {}, "average_rankings": {"seniorenwoningen_kh6": "4.0", "knarrenhof_xce": "5.0", "tiny_houses_mq7": "7.0", "appartementen_ykv": "2.0", "villa_8le": "6.0", "rijwoningen_wr0": "1.0", "twee_onder_een_kapwoning_tno": "3.0"}, "rankings_counts": {"appartementen_ykv": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "seniorenwoningen_kh6": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0, "6": 0, "7": 0}, "tiny_houses_mq7": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1}, "villa_8le": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 1, "7": 0}, "twee_onder_een_kapwoning_tno": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0}, "rijwoningen_wr0": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "knarrenhof_xce": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}}, "multilocs": {"answer": {"appartementen_ykv": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_kh6": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_mq7": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_8le": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_tno": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_wr0": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_xce": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "e1ee4aac-6e1e-4461-9a09-d12e736e0b7c", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 59, "logic": {}, "totalPickCount": 3, "answers": [{"answer": null, "count": 0}, {"answer": "dorps_9na", "count": 1}, {"answer": "klassiek_vlh", "count": 1}, {"answer": "in_het_groen_1gl", "count": 1}, {"answer": "landelijk_kd2", "count": 0}, {"answer": "duurzaam_ztb", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_kd2": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_9na": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_ztb": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_vlh": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_1gl": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen"}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "f4df58be-db38-4fbd-a7c3-e6e51c55b0ec", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 60, "logic": {}, "textResponses": []}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "4e161b76-1ba5-4132-adae-3884c15e8d10", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 61, "logic": {}, "mapConfigId": "6eee7963-d0ff-4bd8-ab23-1e0e6fec438f", "pointResponses": []}, {"inputType": "page", "question": {"nl-NL": "Koudekerke"}, "description": {}, "customFieldId": "f809fbe0-dc41-4db3-8b00-fc6a5462941c", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": 7, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 83}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern "}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "77fd2175-4691-45e4-a5e5-0751b33a3b87", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 62, "logic": {}, "totalPickCount": 4, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 3}, {"answer": 5, "count": 1}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?"}, "description": {"nl-NL": ""}, "customFieldId": "70225d10-6886-4573-8a2d-ab2560c17052", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 63, "logic": {}, "totalPickCount": 8, "answers": [{"answer": null, "count": 0}, {"answer": "speelplekken_c82", "count": 3}, {"answer": "sportvoorzieningen_gpw", "count": 2}, {"answer": "winkel_aanbod_4gf", "count": 1}, {"answer": "woning_aanbod_mh3", "count": 1}, {"answer": "groen_rpi", "count": 1}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_gpw": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_4gf": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_mh3": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_c82": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_rpi": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "87391e7b-d499-4ed0-963f-47a5e4cea8bf", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 64, "logic": {}, "totalPickCount": 7, "answers": [{"answer": null, "count": 0}, {"answer": "horeca_cr1", "count": 2}, {"answer": "speeltuin_9hu", "count": 2}, {"answer": "pleinen_67v", "count": 1}, {"answer": "park_wky", "count": 0}, {"answer": "other", "count": 2}], "multilocs": {"answer": {"pleinen_67v": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_cr1": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_wky": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_9hu": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "Supermarkt en andere winkels"}, {"answer": "Winkelaanbod"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "589e2ec3-959b-4db9-9ff9-82ca3f90f4e4", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 65, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_65j": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 0.25}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 3, "percentage": 0.75}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_79j": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 4, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_v42": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 0.25}, {"answer": 3, "count": 2, "percentage": 0.5}, {"answer": 2, "count": 1, "percentage": 0.25}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "e5855680-244d-46be-9875-f717a838bd4a", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 66, "logic": {}, "textResponses": [{"answer": "<PERSON><PERSON><PERSON> baan"}, {"answer": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, fontein o.i.d."}, {"answer": "Voor de kinderen 1 echte volwaardige speeltuin en niet overal grasveldjes met 1 schommel."}, {"answer": "<PERSON><PERSON> bovenst<PERSON>de items"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "fa5221b4-876f-4fd4-b319-b094437368e8", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 67, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_ni1": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 3, "percentage": 0.75}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 0.25}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_oy0": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 1, "percentage": 0.25}, {"answer": 4, "count": 3, "percentage": 0.75}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_yot": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 3, "percentage": 0.75}, {"answer": 3, "count": 1, "percentage": 0.25}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_2xg": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 4, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 4, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 35}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "f114afb7-c50b-466b-a05d-2ad42c26fdf8", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 68, "logic": {}, "totalPickCount": 4, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 1}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 2}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 1}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften "}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "63847a06-be1f-4049-a7c1-8622d7820ab7", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 69, "logic": {}, "average_rankings": {"luxe_woningzoekers_bg4": "6.5", "sociale_huurders_yql": "2.75", "senioren_dr7": "3.0", "zzp_ers_met_werkruimte_in_woning_ity": "6.0", "gezinnen_cq8": "4.0", "alleenstaanden_b0p": "3.0", "starters_z2k": "2.5"}, "rankings_counts": {"starters_z2k": {"1": 1, "2": 1, "3": 1, "4": 1, "5": 0}, "gezinnen_cq8": {"1": 0, "2": 0, "3": 1, "4": 2, "5": 1}, "senioren_dr7": {"1": 2, "2": 0, "3": 0, "4": 1, "5": 0}, "alleenstaanden_b0p": {"1": 0, "2": 2, "3": 1, "4": 0, "5": 1}, "sociale_huurders_yql": {"1": 1, "2": 1, "3": 1, "4": 0, "5": 1}}, "multilocs": {"answer": {"starters_z2k": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_cq8": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_dr7": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_b0p": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_yql": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "abf8c8e6-22cd-4325-861b-1af18fb26177", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 70, "logic": {}, "average_rankings": {"rijwoningen_9p3": "3.0", "andere_1o0": "8.0", "knarrenhof_u9l": "5.0", "seniorenwoningen_98i": "2.0", "twee_onder_een_kapwoning_nwv": "4.5", "villa_vfg": "7.0", "appartementen_o7j": "2.5", "hofjeswoningen_15z": "4.0", "tiny_houses_s9c": "6.5"}, "rankings_counts": {"appartementen_o7j": {"1": 1, "2": 2, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}, "seniorenwoningen_98i": {"1": 1, "2": 2, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0}, "tiny_houses_s9c": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 2, "7": 2}, "villa_vfg": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1, "6": 1, "7": 0}, "twee_onder_een_kapwoning_nwv": {"1": 0, "2": 0, "3": 0, "4": 3, "5": 0, "6": 1, "7": 0}, "rijwoningen_9p3": {"1": 1, "2": 0, "3": 2, "4": 0, "5": 1, "6": 0, "7": 0}, "knarrenhof_u9l": {"1": 1, "2": 0, "3": 0, "4": 1, "5": 0, "6": 0, "7": 1}}, "multilocs": {"answer": {"appartementen_o7j": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_98i": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_s9c": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_vfg": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_nwv": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_9p3": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_u9l": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "35520a21-4329-4690-9c1c-4ee6ef4f6798", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 71, "logic": {}, "totalPickCount": 10, "answers": [{"answer": null, "count": 1}, {"answer": "dorps_tj3", "count": 4}, {"answer": "duurzaam_cyj", "count": 3}, {"answer": "in_het_groen_wfr", "count": 2}, {"answer": "landelijk_uyr", "count": 0}, {"answer": "klassiek_q71", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_uyr": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_tj3": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_cyj": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_q71": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_wfr": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen"}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "18b7afa9-8cdb-4a38-8052-ed2fe6a5ae45", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 72, "logic": {}, "textResponses": [{"answer": "Extra woning <PERSON><PERSON><PERSON><PERSON> met passende (luxe) voorzieningen voor senioren zal voor doorstroom zorgen waardoor er voor bijvoorbeeld voor gezinnen en starters extra woonruimte ontstaat op de plekken die senioren achter laten."}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "ef3d3f16-c59e-49cf-9b8c-01b8badd45ba", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 4, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 73, "logic": {}, "mapConfigId": "f83c3aa4-b12e-499d-9a2a-8eeed00f095a", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.5481444504060606, 51.48420620077323]}}, {"answer": {"type": "Point", "coordinates": [3.5644871904622994, 51.48043810075293]}}]}, {"inputType": "page", "question": {"nl-NL": "Meliskerke"}, "description": {}, "customFieldId": "68502174-2d4d-4565-b1b7-26450f1df28f", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": 8, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 71}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern "}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "c95c7f09-3a7d-48df-b409-fed0aa05abce", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 74, "logic": {}, "totalPickCount": 2, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 1}, {"answer": 5, "count": 1}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? "}, "description": {"nl-NL": ""}, "customFieldId": "830eac8b-ce6a-4b6f-b27f-31ece779cd3c", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 75, "logic": {}, "totalPickCount": 4, "answers": [{"answer": null, "count": 0}, {"answer": "woning_aanbod_51l", "count": 2}, {"answer": "speelplekken_128", "count": 1}, {"answer": "groen_nci", "count": 1}, {"answer": "sportvoorzieningen_iye", "count": 0}, {"answer": "winkel_aan<PERSON><PERSON>_sru", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_iye": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_sru": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_51l": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_128": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_nci": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "a6d3214d-2c0e-437b-98a7-6b7999dd1724", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 76, "logic": {}, "totalPickCount": 4, "answers": [{"answer": null, "count": 0}, {"answer": "pleinen_05m", "count": 1}, {"answer": "speeltuin_p6v", "count": 1}, {"answer": "horeca_tol", "count": 0}, {"answer": "park_jxi", "count": 0}, {"answer": "other", "count": 2}], "multilocs": {"answer": {"pleinen_05m": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_tol": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_jxi": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_p6v": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "Kerk"}, {"answer": "<PERSON><PERSON>"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "b52a7876-7048-4667-8d36-5d062cff29f8", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 77, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_yje": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 1, "percentage": 0.5}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 0.5}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_ve8": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 2, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_ido": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 0.5}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 0.5}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "0e4d9a50-0b54-42f9-b3d0-4fa36387e4cc", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 78, "logic": {}, "textResponses": [{"answer": "Park / openbaar groen"}, {"answer": "Woningbouw"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "8bf36ea0-65ad-4189-bc18-404fff8dc425", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 79, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_6x7": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 0.5}, {"answer": 3, "count": 1, "percentage": 0.5}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_xfv": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 2, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_5pu": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 0.5}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 0.5}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_x6c": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 2, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 2, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 37}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "813db444-b17a-4247-a945-e38d81dca10c", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 80, "logic": {}, "totalPickCount": 2, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 1}, {"answer": 3, "count": 1}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 0}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 0}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "27e3179d-a6cf-4f38-9c21-2472888348d5", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 81, "logic": {}, "average_rankings": {"sociale_huurders_aqf": "5.0", "starters_1jg": "2.0", "senioren_952": "1.5", "gezinnen_zhc": "3.0", "alleenstaanden_q5r": "3.5"}, "rankings_counts": {"starters_1jg": {"1": 1, "2": 0, "3": 1, "4": 0, "5": 0}, "gezinnen_zhc": {"1": 0, "2": 1, "3": 0, "4": 1, "5": 0}, "senioren_952": {"1": 1, "2": 1, "3": 0, "4": 0, "5": 0}, "alleenstaanden_q5r": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 0}, "sociale_huurders_aqf": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 2}}, "multilocs": {"answer": {"starters_1jg": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_zhc": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_952": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_q5r": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_aqf": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "b556c61b-d557-4853-8d72-61e9623ec04d", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 82, "logic": {}, "average_rankings": {"twee_onder_een_kapwoning_2vx": "2.0", "seniorenwoningen_lxj": "2.0", "villa_jzk": "3.5", "knarrenhof_2hw": "5.0", "appartementen_kcg": "6.0", "tiny_houses_iwz": "4.0", "rijwoningen_j4q": "5.5"}, "rankings_counts": {"appartementen_kcg": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 2, "7": 0}, "seniorenwoningen_lxj": {"1": 1, "2": 0, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0}, "tiny_houses_iwz": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1}, "villa_jzk": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 0, "6": 0, "7": 0}, "twee_onder_een_kapwoning_2vx": {"1": 0, "2": 2, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "rijwoningen_j4q": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0, "6": 0, "7": 1}, "knarrenhof_2hw": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 2, "6": 0, "7": 0}}, "multilocs": {"answer": {"appartementen_kcg": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_lxj": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_iwz": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_jzk": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_2vx": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_j4q": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_2hw": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen "}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "7bb78da6-8e45-4092-8856-10339f70b4f1", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 83, "logic": {}, "totalPickCount": 6, "answers": [{"answer": null, "count": 0}, {"answer": "landelijk_vfo", "count": 2}, {"answer": "duurzaam_tw8", "count": 2}, {"answer": "in_het_groen_fir", "count": 1}, {"answer": "dorps_nqk", "count": 0}, {"answer": "klassiek_7ru", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"landelijk_vfo": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_nqk": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_tw8": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_7ru": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_fir": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "St<PERSON>/hout bouw of andere diversiteit kan ook prima."}]}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen "}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "e9b4e3ec-7f0d-4dde-bc4d-b79fa8b3a511", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 84, "logic": {}, "textResponses": [{"answer": "Zorg voor geschikte bouwgrond. <PERSON><PERSON> men<PERSON> in Meliskerke willen in de kern zelf verhuizen maar kunnen niet. Maak een combi van bouwen mogelijk, zow<PERSON>  senioren, starters als doorstromers. Er zijn veel oudere mensen die in grote woningen wonen maar geen optie hebben om te verhuizen wegens gebrek aan seniorenwoningen. Stimuleer ook bijvoorbeeld een CPO, maar zorg dan wel voor bouwgrond. En een woonhof zou binnen de kern van Meliskerke ook goed passen in de gemeenschap.\n\nZorg dat onderstaande locatie geen belemmering vormt voor woningbouw. De grote (bos)tuin daar ligt er nu 'nutteloos'. <PERSON><PERSON><PERSON> met de eigenaren over samenwerking."}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "06822ce6-e17b-4fca-8349-cade5a58a0ff", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 2, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 85, "logic": {}, "mapConfigId": "c45e8479-6b25-47e3-a572-a0074bfdb1e9", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.506980513017421, 51.51284713144574]}}]}, {"inputType": "page", "question": {"nl-NL": "Oostkapelle"}, "description": {}, "customFieldId": "abc7d56a-cdfd-4932-83bb-b4005a7119a5", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": 9, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 59}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern "}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "a19f8a9e-0737-4b1b-9bb7-95235b3399a0", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 86, "logic": {}, "totalPickCount": 7, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 3}, {"answer": 5, "count": 4}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?"}, "description": {"nl-NL": ""}, "customFieldId": "fc680e71-7b13-401f-91fd-48f99bc47778", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 87, "logic": {}, "totalPickCount": 10, "answers": [{"answer": null, "count": 0}, {"answer": "woning_aanbod_lt7", "count": 6}, {"answer": "winkel_aan<PERSON>d_byj", "count": 1}, {"answer": "groen_49l", "count": 1}, {"answer": "sportvoorzieningen_fgj", "count": 0}, {"answer": "speelplekken_qlz", "count": 0}, {"answer": "other", "count": 2}], "multilocs": {"answer": {"sportvoorzieningen_fgj": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_byj": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_lt7": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_qlz": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_49l": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "<PERSON><PERSON>"}, {"answer": "Verkeersafwikkeling"}]}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "e4ab7051-1b71-4eb5-8d46-dee76552875b", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 88, "logic": {}, "totalPickCount": 10, "answers": [{"answer": null, "count": 0}, {"answer": "horeca_psh", "count": 3}, {"answer": "park_d50", "count": 1}, {"answer": "speeltuin_kcv", "count": 1}, {"answer": "pleinen_nix", "count": 0}, {"answer": "other", "count": 5}], "multilocs": {"answer": {"pleinen_nix": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_psh": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_d50": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_kcv": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "<PERSON><PERSON><PERSON> bu<PERSON>,"}, {"answer": "Gemeenschappelijke voorzieningen"}, {"answer": "Manteling en strand"}, {"answer": "Strand"}, {"answer": "Strand"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "54f62212-d174-40c0-9ddb-348c2a1e4fd4", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 89, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_3di": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 7, "answers": [{"answer": 5, "count": 3, "percentage": 0.42857142857142855}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 3, "percentage": 0.42857142857142855}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.14285714285714285}, {"answer": null, "count": 32}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_sg7": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 7, "answers": [{"answer": 5, "count": 4, "percentage": 0.5714285714285714}, {"answer": 4, "count": 2, "percentage": 0.2857142857142857}, {"answer": 3, "count": 1, "percentage": 0.14285714285714285}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 32}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_ohy": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 7, "answers": [{"answer": 5, "count": 3, "percentage": 0.42857142857142855}, {"answer": 4, "count": 2, "percentage": 0.2857142857142857}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 2, "percentage": 0.2857142857142857}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 32}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "983c235c-d789-493f-af61-9938409c48a5", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 90, "logic": {}, "textResponses": [{"answer": "Afvalbakken"}, {"answer": "<PERSON>en groot en gezellig evenementen gebouw. Kerk is te klein"}, {"answer": "<PERSON><PERSON><PERSON><PERSON>"}, {"answer": "Voldoende parkeerruimte. Terrasjes in de dorpskern. Minder focus op fietsers (e-bikes = gevaarlijk)"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "3966666e-62e5-44eb-a892-4fd90325cf9c", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 91, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_8e2": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 7, "answers": [{"answer": 5, "count": 2, "percentage": 0.2857142857142857}, {"answer": 4, "count": 4, "percentage": 0.5714285714285714}, {"answer": 3, "count": 1, "percentage": 0.14285714285714285}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 32}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_hlg": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 7, "answers": [{"answer": 5, "count": 2, "percentage": 0.2857142857142857}, {"answer": 4, "count": 5, "percentage": 0.7142857142857143}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 32}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_vtm": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 7, "answers": [{"answer": 5, "count": 2, "percentage": 0.2857142857142857}, {"answer": 4, "count": 4, "percentage": 0.5714285714285714}, {"answer": 3, "count": 1, "percentage": 0.14285714285714285}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 32}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_qjs": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 7, "answers": [{"answer": 5, "count": 2, "percentage": 0.2857142857142857}, {"answer": 4, "count": 2, "percentage": 0.2857142857142857}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 2, "percentage": 0.2857142857142857}, {"answer": 1, "count": 1, "percentage": 0.14285714285714285}, {"answer": null, "count": 32}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "d88877c4-9f8f-4802-805a-3fdce8c7c0ee", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 92, "logic": {}, "totalPickCount": 7, "answers": [{"answer": 1, "count": 3}, {"answer": 2, "count": 2}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 2}, {"answer": 6, "count": 0}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 0}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "0873a18b-32da-4b16-a426-3050815c93bc", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 93, "logic": {}, "average_rankings": {"luxe_woningzoekers_cwy": "7.0", "gezinnen_9cm": "4.0", "senioren_5t2": "3.0", "starters_qzf": "1.8571428571428571", "sociale_huurders_t7r": "3.7142857142857143", "zzp_ers_met_werkruimte_in_woning_4vw": "6.0", "alleenstaanden_hlb": "2.4285714285714286"}, "rankings_counts": {"starters_qzf": {"1": 3, "2": 2, "3": 2, "4": 0, "5": 0}, "gezinnen_9cm": {"1": 0, "2": 1, "3": 2, "4": 0, "5": 4}, "senioren_5t2": {"1": 2, "2": 1, "3": 0, "4": 3, "5": 1}, "alleenstaanden_hlb": {"1": 2, "2": 2, "3": 2, "4": 0, "5": 1}, "sociale_huurders_t7r": {"1": 0, "2": 1, "3": 1, "4": 4, "5": 1}}, "multilocs": {"answer": {"starters_qzf": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_9cm": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_5t2": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_hlb": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_t7r": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "47b12cd5-4f37-44fa-a2a3-4c374cb25ad0", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 94, "logic": {}, "average_rankings": {"twee_onder_een_kapwoning_rjb": "3.1428571428571429", "andere_1n5": "7.3333333333333333", "appartementen_g3i": "3.4285714285714286", "hofjeswoningen_nas": "4.6666666666666667", "seniorenwoningen_52s": "2.2857142857142857", "knarrenhof_ngv": "6.0", "villa_tmp": "7.2857142857142857", "tiny_houses_rlh": "5.1428571428571429", "rijwoningen_31f": "2.8571428571428571"}, "rankings_counts": {"appartementen_g3i": {"1": 2, "2": 1, "3": 0, "4": 2, "5": 1, "6": 0, "7": 1}, "seniorenwoningen_52s": {"1": 3, "2": 1, "3": 2, "4": 0, "5": 1, "6": 0, "7": 0}, "tiny_houses_rlh": {"1": 1, "2": 0, "3": 0, "4": 2, "5": 0, "6": 1, "7": 3}, "villa_tmp": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0, "6": 1, "7": 2}, "twee_onder_een_kapwoning_rjb": {"1": 1, "2": 2, "3": 1, "4": 1, "5": 2, "6": 0, "7": 0}, "rijwoningen_31f": {"1": 0, "2": 3, "3": 3, "4": 0, "5": 1, "6": 0, "7": 0}, "knarrenhof_ngv": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 4, "7": 1}}, "multilocs": {"answer": {"appartementen_g3i": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_52s": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_rlh": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_tmp": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_rjb": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_31f": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_ngv": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen "}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "ceef279c-700b-4616-80e5-b4a7f143ae63", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 95, "logic": {}, "totalPickCount": 17, "answers": [{"answer": null, "count": 0}, {"answer": "dorps_jvn", "count": 6}, {"answer": "in_het_groen_k2b", "count": 4}, {"answer": "duurzaam_4yn", "count": 3}, {"answer": "landelijk_p42", "count": 2}, {"answer": "klassiek_ep4", "count": 2}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_p42": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_jvn": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_4yn": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_ep4": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_k2b": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen"}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "83045022-a43c-4555-be82-9cdedb1023a2", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 96, "logic": {}, "textResponses": [{"answer": "<PERSON><PERSON><PERSON><PERSON> woonru<PERSON>te voor alleenstaanden, zowel jong als oud."}, {"answer": "<PERSON><PERSON> is in mijn doelgroep (alleenstaande starters) grote behoefte aan woningen dan wel appartementen die betaalbaar zijn voor één inkomen. De inbreiding Duynpoort is een hele fijn inbreiding, maar ook hier is de betaalbaarheid gericht op starters welke met een dubbel inkomen kunnen kopen."}, {"answer": "Ontbreken Levensloop bestendig woningen + woningen voor personen met een beperking."}, {"answer": "Wat nu woningen zijn moeten blijvend voor permanente bewoning zijn, niet veranderen naar vakantiewoning of onderverhuur. Bi<PERSON> woningen de voortuin ook voortuin laten en niet toestaan dat auto’s daarin geparkeerd kunnen worden. Maak voorziening voor voldoende parkeergelegenheid."}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "62944165-c13e-4473-8930-42afa3bd7433", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 7, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 97, "logic": {}, "mapConfigId": "768b38eb-790d-4a4e-995b-052609f49c81", "pointResponses": []}, {"inputType": "page", "question": {"nl-NL": "Serooskerke"}, "description": {}, "customFieldId": "6b4cf172-c925-43c0-8ad6-7c369b82fd5b", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": 10, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 47}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern"}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "f54c18bc-b1c2-4715-80c8-6482962f89c0", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 98, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 1}, {"answer": 5, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? "}, "description": {"nl-NL": ""}, "customFieldId": "5eb6507b-f8df-455e-85a4-913855072b1f", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 99, "logic": {}, "totalPickCount": 1, "answers": [{"answer": null, "count": 0}, {"answer": "woning_aanbod_4uc", "count": 1}, {"answer": "sportvoorzieningen_xce", "count": 0}, {"answer": "winkel_aanbod_1db", "count": 0}, {"answer": "speelplekken_k5l", "count": 0}, {"answer": "groen_s4y", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_xce": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_1db": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_4uc": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_k5l": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_s4y": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "96535bf9-7948-4eea-aa6c-d45aa50c17e0", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 100, "logic": {}, "totalPickCount": 1, "answers": [{"answer": null, "count": 0}, {"answer": "pleinen_7m2", "count": 0}, {"answer": "horeca_d21", "count": 0}, {"answer": "park_cvf", "count": 0}, {"answer": "speeltuin_ztv", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"pleinen_7m2": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_d21": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_cvf": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_ztv": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "winkels"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "4e36436f-d283-4fdf-b032-dceb7e8342cc", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 101, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_vto": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 1.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_rxl": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 1.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_sux": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "745035a9-2433-44d2-aa87-964cfe9eb99d", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 102, "logic": {}, "textResponses": []}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "764cfa1a-20c3-444f-b736-fed4d605cfe4", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 103, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_3wq": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_rzh": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_4hs": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_ois": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 1.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften "}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "541adaaa-b135-4af2-b329-2a0e772be067", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 104, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 1}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 0}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 0}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "96327efd-3958-49be-8af0-8657b57a2084", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 105, "logic": {}, "average_rankings": {"senioren_9ne": "3.0", "starters_hgd": "1.0", "gezinnen_m01": "5.0", "sociale_huurders_bhf": "4.0", "alleenstaanden_jgf": "2.0"}, "rankings_counts": {"starters_hgd": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0}, "gezinnen_m01": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1}, "senioren_9ne": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0}, "alleenstaanden_jgf": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0}, "sociale_huurders_bhf": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0}}, "multilocs": {"answer": {"starters_hgd": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_m01": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_9ne": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_jgf": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_bhf": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "fe842ec3-4c15-4fef-874d-bcfe5e7a027e", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 106, "logic": {}, "average_rankings": {"twee_onder_een_kapwoning_ygo": "3.0", "rijwoningen_cfs": "4.0", "knarrenhof_smt": "6.0", "appartementen_2zr": "5.0", "tiny_houses_98a": "2.0", "villa_v92": "7.0", "seniorenwoningen_0sd": "1.0"}, "rankings_counts": {"appartementen_2zr": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}, "seniorenwoningen_0sd": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "tiny_houses_98a": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "villa_v92": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1}, "twee_onder_een_kapwoning_ygo": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0}, "rijwoningen_cfs": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0, "6": 0, "7": 0}, "knarrenhof_smt": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 1, "7": 0}}, "multilocs": {"answer": {"appartementen_2zr": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_0sd": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_98a": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_v92": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "twee_onder_een_kapwoning_ygo": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_cfs": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_smt": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "37ba6efc-f46e-4f27-b154-d76b333a0ada", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 107, "logic": {}, "totalPickCount": 2, "answers": [{"answer": null, "count": 0}, {"answer": "dorps_f9q", "count": 1}, {"answer": "duurzaam_5k1", "count": 1}, {"answer": "landelijk_c7o", "count": 0}, {"answer": "klassiek_3c0", "count": 0}, {"answer": "in_het_groen_6ba", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_c7o": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_f9q": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_5k1": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_3c0": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_6ba": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen"}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "f0d22299-850a-46eb-aae3-76c1cf93c7b2", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 0, "pageNumber": null, "questionNumber": 108, "logic": {}, "textResponses": []}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "4b5d7d4a-10d7-4230-98cc-fdb37d70cc6b", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 109, "logic": {}, "mapConfigId": "10736e47-499d-4fdf-81a1-73445681843b", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.5994689530764523, 51.546127119677365]}}]}, {"inputType": "page", "question": {"nl-NL": "<PERSON><PERSON><PERSON>"}, "description": {}, "customFieldId": "498a9955-48c3-48e4-8155-538f86ff7dd9", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": 11, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 35}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern"}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "fe5b0edb-c706-418a-a26e-7f9f7b1d0aeb", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 110, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 1}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? "}, "description": {"nl-NL": ""}, "customFieldId": "dc01bb5c-fce6-41cd-8e44-36323afeb6f6", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 111, "logic": {}, "totalPickCount": 2, "answers": [{"answer": null, "count": 0}, {"answer": "winkel_aanbod_d20", "count": 1}, {"answer": "woning_aanbod_q2y", "count": 1}, {"answer": "sportvoorzieningen_ng2", "count": 0}, {"answer": "speelplekken_1lp", "count": 0}, {"answer": "groen_nu7", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_ng2": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_d20": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_q2y": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_1lp": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_nu7": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "2fe28fa0-f69d-4273-94b5-30c102550f32", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 112, "logic": {}, "totalPickCount": 2, "answers": [{"answer": null, "count": 0}, {"answer": "horeca_kyr", "count": 1}, {"answer": "pleinen_63z", "count": 0}, {"answer": "park_mp7", "count": 0}, {"answer": "speeltuin_94z", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"pleinen_63z": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_kyr": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_mp7": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_94z": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "op straat tijdens wandelingen"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "2194a787-ef06-45f5-ba58-c30a8f74f7c5", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 113, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_toe": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_3p6": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_1yg": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "7c1880b9-79b3-4cd8-9329-65326c8dcf86", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 114, "logic": {}, "textResponses": [{"answer": "zitplekken annex tafels en afvalbakken op populaire plaatsen"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "02def50b-48ce-4101-9c1c-02bce25d8e44", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 115, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_knq": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_pws": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_pvr": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_edl": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 1, "percentage": 1.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "934e6ba5-78fe-4831-b3d0-27c2cd943efb", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 116, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 1}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 0}, {"answer": 7, "count": 0}, {"answer": 8, "count": 0}, {"answer": 9, "count": 0}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "09ba5ee0-2584-4b1e-8ea9-72adee4a9671", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 117, "logic": {}, "average_rankings": {"starters_k31": "2.0", "sociale_huurders_04h": "3.0", "alleenstaanden_zy8": "4.0", "gezinnen_vd1": "5.0", "senioren_b70": "1.0"}, "rankings_counts": {"starters_k31": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0}, "gezinnen_vd1": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1}, "senioren_b70": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0}, "alleenstaanden_zy8": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0}, "sociale_huurders_04h": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0}}, "multilocs": {"answer": {"starters_k31": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_vd1": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_b70": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_zy8": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_04h": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "70b0b267-fa6a-41a1-8852-3fa1fae9da19", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 118, "logic": {}, "average_rankings": {"tiny_houses_m7n": "5.0", "twee_onder_een_kapwoning_f9k": "4.0", "villa_5b2": "6.0", "seniorenwoningen_egf": "2.0", "knarrenhof_26a": "1.0", "appartementen_y0b": "7.0", "rijwoningen_fm2": "3.0"}, "rankings_counts": {"appartementen_y0b": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1}, "seniorenwoningen_egf": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "tiny_houses_m7n": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}, "villa_5b2": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 1, "7": 0}, "twee_onder_een_kapwoning_f9k": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0, "6": 0, "7": 0}, "rijwoningen_fm2": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0}, "knarrenhof_26a": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}}, "multilocs": {"answer": {"appartementen_y0b": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_egf": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_m7n": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_5b2": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_f9k": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_fm2": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_26a": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "6f9d8f66-8fee-456b-a1ad-4c8d8a1ebf02", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 119, "logic": {}, "totalPickCount": 3, "answers": [{"answer": null, "count": 0}, {"answer": "dorps_cjz", "count": 1}, {"answer": "duurzaam_a20", "count": 1}, {"answer": "landelijk_jbl", "count": 0}, {"answer": "klassiek_p4z", "count": 0}, {"answer": "in_het_groen_d3k", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"landelijk_jbl": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_cjz": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_a20": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_p4z": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_d3k": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "modern met aandacht voor verleden"}]}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen"}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "b88e5324-6614-4221-90d9-de47aebc4cde", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 120, "logic": {}, "textResponses": [{"answer": "een knarrenhof mag ook woningen bevatten voor starters en alleenstaanden om daarmee een gezonde en sociale mix mogelijk te maken. Duurder en goedkoper segment passen ook in die opvatting."}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "5bf75b3d-39fb-4092-86c8-c5abd40d67d0", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 121, "logic": {}, "mapConfigId": "330f7285-bb7b-4085-b82c-f0cd890b7c8a", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.6702201269201904, 51.54911209782878]}}]}, {"inputType": "page", "question": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": {}, "customFieldId": "267e3ffc-b559-4f43-b946-6397c477a283", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": 12, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 23}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern "}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "475c36ca-8e06-48a6-a834-72997cecfbd0", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 122, "logic": {}, "totalPickCount": 3, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 1}, {"answer": 4, "count": 1}, {"answer": 5, "count": 1}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? "}, "description": {"nl-NL": ""}, "customFieldId": "1e748b4f-11ef-4fa7-8991-8bfea58b78e3", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 123, "logic": {}, "totalPickCount": 4, "answers": [{"answer": null, "count": 0}, {"answer": "woning_aanbod_y8c", "count": 2}, {"answer": "sportvoorzieningen_c72", "count": 1}, {"answer": "winkel_aanbod_oij", "count": 1}, {"answer": "speelplekken_tn9", "count": 0}, {"answer": "groen_dpk", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_c72": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_oij": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_y8c": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_tn9": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_dpk": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving "}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "5dca26c8-e354-4f6c-8739-a7ccc79b9c48", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 124, "logic": {}, "totalPickCount": 3, "answers": [{"answer": null, "count": 0}, {"answer": "horeca_f9y", "count": 2}, {"answer": "pleinen_6vh", "count": 0}, {"answer": "park_j8y", "count": 0}, {"answer": "speeltuin_6g3", "count": 0}, {"answer": "other", "count": 1}], "multilocs": {"answer": {"pleinen_6vh": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_f9y": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_j8y": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_6g3": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "69e2bb91-bfc8-4f29-9369-e439d310e928", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 125, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_12y": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 3, "answers": [{"answer": 5, "count": 1, "percentage": 0.3333333333333333}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 0.3333333333333333}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.3333333333333333}, {"answer": null, "count": 36}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_1e8": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 3, "answers": [{"answer": 5, "count": 1, "percentage": 0.3333333333333333}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 0.3333333333333333}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.3333333333333333}, {"answer": null, "count": 36}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_zt2": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 3, "answers": [{"answer": 5, "count": 1, "percentage": 0.3333333333333333}, {"answer": 4, "count": 1, "percentage": 0.3333333333333333}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.3333333333333333}, {"answer": null, "count": 36}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "c963d956-0878-4a96-9f71-fdc69f5ba1fe", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 2, "pageNumber": null, "questionNumber": 126, "logic": {}, "textResponses": [{"answer": "Goede weg om het centrum heen"}, {"answer": "<PERSON><PERSON><PERSON> parkje"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "71d524e5-9911-4a77-8fd4-758a9bc6ceb0", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 127, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_6ls": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 3, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 2, "percentage": 0.6666666666666666}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.3333333333333333}, {"answer": null, "count": 36}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_wik": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 3, "answers": [{"answer": 5, "count": 1, "percentage": 0.3333333333333333}, {"answer": 4, "count": 1, "percentage": 0.3333333333333333}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.3333333333333333}, {"answer": null, "count": 36}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_xfc": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 3, "answers": [{"answer": 5, "count": 1, "percentage": 0.3333333333333333}, {"answer": 4, "count": 1, "percentage": 0.3333333333333333}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.3333333333333333}, {"answer": null, "count": 36}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_eub": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 3, "answers": [{"answer": 5, "count": 1, "percentage": 0.3333333333333333}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 1, "percentage": 0.3333333333333333}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 0.3333333333333333}, {"answer": null, "count": 36}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "9d2cb4e0-64a9-4f39-a440-c5b10681bd05", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 128, "logic": {}, "totalPickCount": 3, "answers": [{"answer": 1, "count": 2}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 0}, {"answer": 5, "count": 0}, {"answer": 6, "count": 0}, {"answer": 7, "count": 1}, {"answer": 8, "count": 0}, {"answer": 9, "count": 0}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "7804ea0d-fe92-42b7-b0e1-59861ed6f6ef", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 129, "logic": {}, "average_rankings": {"senioren_2g0": "3.0", "sociale_huurders_kcv": "4.0", "starters_7k1": "1.0", "gezinnen_c86": "3.0", "alleenstaanden_42e": "4.0"}, "rankings_counts": {"starters_7k1": {"1": 3, "2": 0, "3": 0, "4": 0, "5": 0}, "gezinnen_c86": {"1": 0, "2": 2, "3": 0, "4": 0, "5": 1}, "senioren_2g0": {"1": 0, "2": 0, "3": 3, "4": 0, "5": 0}, "alleenstaanden_42e": {"1": 0, "2": 0, "3": 0, "4": 3, "5": 0}, "sociale_huurders_kcv": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 2}}, "multilocs": {"answer": {"starters_7k1": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_c86": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_2g0": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_42e": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_kcv": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "17c81053-183b-4b24-953b-8d5405ad8032", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 130, "logic": {}, "average_rankings": {"knarrenhof_ljo": "5.0", "tiny_houses_0a6": "5.3333333333333333", "twee_onder_een_kapwoning_hxr": "3.6666666666666667", "rijwoningen_oh9": "2.3333333333333333", "villa_q9s": "4.6666666666666667", "appartementen_d34": "4.3333333333333333", "seniorenwoningen_cj3": "2.6666666666666667"}, "rankings_counts": {"appartementen_d34": {"1": 0, "2": 0, "3": 1, "4": 1, "5": 0, "6": 1, "7": 0}, "seniorenwoningen_cj3": {"1": 1, "2": 0, "3": 1, "4": 1, "5": 0, "6": 0, "7": 0}, "tiny_houses_0a6": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 1, "6": 0, "7": 1}, "villa_q9s": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 1, "6": 0, "7": 1}, "twee_onder_een_kapwoning_hxr": {"1": 0, "2": 1, "3": 1, "4": 0, "5": 0, "6": 1, "7": 0}, "rijwoningen_oh9": {"1": 2, "2": 0, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}, "knarrenhof_ljo": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0, "6": 1, "7": 1}}, "multilocs": {"answer": {"appartementen_d34": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_cj3": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_0a6": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_q9s": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_hxr": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_oh9": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_ljo": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen "}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "fd50ccc8-0c8f-40ba-9957-43e8308d4521", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 131, "logic": {}, "totalPickCount": 9, "answers": [{"answer": null, "count": 0}, {"answer": "duurzaam_be7", "count": 3}, {"answer": "dorps_1uy", "count": 2}, {"answer": "in_het_groen_njl", "count": 2}, {"answer": "landelijk_0jt", "count": 1}, {"answer": "klassiek_pca", "count": 1}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_0jt": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_1uy": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_be7": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_pca": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_njl": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen "}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "11da5a27-1e0d-438f-8895-1a9ca6611c71", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 3, "pageNumber": null, "questionNumber": 132, "logic": {}, "textResponses": [{"answer": "Plan \"voetbalveld\"  perfect idee.  Kan m.i. verder uitgebreid worden met een 2e rij\nVoetbal kan ook op het veldje bij de te bouwen supermarkt"}, {"answer": "Voetbalveld volbouwen en samenwerking zoeken met de camping aan de andere kant van de weg zodat daar een mooi sportveld voor de dikke buuken gerealiseerd kan worden. Snelle actie is nodig! Ontwikkeling van ondernemers op de Dorpsdijk naast de Pizzaria opgang helpen. Ze willen wel maar zijn bang voor de ambtelijke molen."}, {"answer": "Woningen die zowel permanente als recreatieve woonbestemming hebben besch<PERSON>ar maken als vakantiewoning. Dit om leegstand van de woningen en lage bezetting van lokale winkel- en horeca gelegenheden te voorkomen."}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "0c78622b-a39d-41c2-83a8-ccb5ea4158ee", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 3, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 133, "logic": {}, "mapConfigId": "dd989380-de97-46d5-8943-dc2ba200d2cc", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.612176076755182, 51.57422528637416]}}]}, {"inputType": "page", "question": {"nl-NL": "Westkapelle"}, "description": {}, "customFieldId": "38abe218-9edb-4bf6-a4d5-2e1f5e152349", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": 13, "questionNumber": null, "logic": {"nextPageNumber": 15, "numQuestionsSkipped": 11}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern "}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "1c3db84b-b309-4c2d-b9fd-9884a9c1552d", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 134, "logic": {}, "totalPickCount": 11, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 1}, {"answer": 4, "count": 4}, {"answer": 5, "count": 6}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? "}, "description": {"nl-NL": ""}, "customFieldId": "3cbe657c-1708-4e27-a69a-ceb00282509f", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 135, "logic": {}, "totalPickCount": 18, "answers": [{"answer": null, "count": 0}, {"answer": "woning_aanbod_hvs", "count": 10}, {"answer": "winkel_aanbod_2yt", "count": 3}, {"answer": "sportvoorzieningen_xn4", "count": 2}, {"answer": "groen_94j", "count": 2}, {"answer": "speelplekken_vkt", "count": 1}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_xn4": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_2yt": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_hvs": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_vkt": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_94j": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "0f9aaa89-27af-423a-9ed5-3d8673184e8a", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 136, "logic": {}, "totalPickCount": 19, "answers": [{"answer": null, "count": 0}, {"answer": "horeca_d3k", "count": 10}, {"answer": "pleinen_u0i", "count": 2}, {"answer": "speeltuin_lk6", "count": 2}, {"answer": "park_eui", "count": 1}, {"answer": "other", "count": 4}], "multilocs": {"answer": {"pleinen_u0i": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_d3k": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_eui": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_lk6": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": [{"answer": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> thuis"}, {"answer": "Sportvelden"}, {"answer": "Winkels"}, {"answer": "verenigingsgebouw"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "2af1f314-fbcc-40ef-b869-6012842e86bf", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 137, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_94o": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 11, "answers": [{"answer": 5, "count": 1, "percentage": 0.09090909090909091}, {"answer": 4, "count": 6, "percentage": 0.5454545454545454}, {"answer": 3, "count": 3, "percentage": 0.2727272727272727}, {"answer": 2, "count": 1, "percentage": 0.09090909090909091}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 28}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_d63": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 11, "answers": [{"answer": 5, "count": 3, "percentage": 0.2727272727272727}, {"answer": 4, "count": 4, "percentage": 0.36363636363636365}, {"answer": 3, "count": 1, "percentage": 0.09090909090909091}, {"answer": 2, "count": 2, "percentage": 0.18181818181818182}, {"answer": 1, "count": 1, "percentage": 0.09090909090909091}, {"answer": null, "count": 28}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_01f": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 11, "answers": [{"answer": 5, "count": 1, "percentage": 0.09090909090909091}, {"answer": 4, "count": 3, "percentage": 0.2727272727272727}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 5, "percentage": 0.45454545454545453}, {"answer": 1, "count": 2, "percentage": 0.18181818181818182}, {"answer": null, "count": 28}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "92f01c41-12c3-4b38-8c87-af0709e0dcc0", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 7, "pageNumber": null, "questionNumber": 138, "logic": {}, "textResponses": [{"answer": "-"}, {"answer": "Buitensport toestellen"}, {"answer": "<PERSON><PERSON> levendig en aantrekkelijk marktplein met groen"}, {"answer": "<PERSON><PERSON><PERSON>"}, {"answer": "Kindvriedelijke opgeknapte heringerichte markt met afscher<PERSON> naar straat en duidelijke oversteekplaats zuidstraat."}, {"answer": "<PERSON><PERSON> zitje onder bomen met s<PERSON><PERSON><PERSON><PERSON>"}, {"answer": "film/theaterzaaltje"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten "}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "e983d77a-31e0-4d15-8fc1-7487021c1bec", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 139, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_d8j": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 11, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 7, "percentage": 0.6363636363636364}, {"answer": 3, "count": 1, "percentage": 0.09090909090909091}, {"answer": 2, "count": 3, "percentage": 0.2727272727272727}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 28}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_evm": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 11, "answers": [{"answer": 5, "count": 1, "percentage": 0.09090909090909091}, {"answer": 4, "count": 5, "percentage": 0.45454545454545453}, {"answer": 3, "count": 3, "percentage": 0.2727272727272727}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 2, "percentage": 0.18181818181818182}, {"answer": null, "count": 28}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fr9": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 11, "answers": [{"answer": 5, "count": 1, "percentage": 0.09090909090909091}, {"answer": 4, "count": 7, "percentage": 0.6363636363636364}, {"answer": 3, "count": 1, "percentage": 0.09090909090909091}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 2, "percentage": 0.18181818181818182}, {"answer": null, "count": 28}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_7ew": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 11, "answers": [{"answer": 5, "count": 3, "percentage": 0.2727272727272727}, {"answer": 4, "count": 2, "percentage": 0.18181818181818182}, {"answer": 3, "count": 3, "percentage": 0.2727272727272727}, {"answer": 2, "count": 2, "percentage": 0.18181818181818182}, {"answer": 1, "count": 1, "percentage": 0.09090909090909091}, {"answer": null, "count": 28}]}}}, {"inputType": "rating", "question": {"nl-NL": "Wonen en woonbehoeften "}, "description": {"nl-NL": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>"}, "customFieldId": "f6b41652-1a4a-4c73-b9cc-6d5529b70665", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 140, "logic": {}, "totalPickCount": 11, "answers": [{"answer": 1, "count": 3}, {"answer": 2, "count": 2}, {"answer": 3, "count": 2}, {"answer": 4, "count": 2}, {"answer": 5, "count": 0}, {"answer": 6, "count": 0}, {"answer": 7, "count": 1}, {"answer": 8, "count": 0}, {"answer": 9, "count": 1}, {"answer": 10, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}, "6": {"title_multiloc": {"nl-NL": "6"}}, "7": {"title_multiloc": {"nl-NL": "7"}}, "8": {"title_multiloc": {"nl-NL": "8"}}, "9": {"title_multiloc": {"nl-NL": "9"}}, "10": {"title_multiloc": {"nl-NL": "10"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "f100ac37-f3e1-4cd8-9010-9b43d5ea2dcd", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 141, "logic": {}, "average_rankings": {"gezinnen_c6z": "4.0", "starters_h6a": "1.4545454545454545", "senioren_ic6": "3.2727272727272727", "zzp_ers_met_werkruimte_in_woning_rmh": "5.5", "sociale_huurders_vpb": "3.8181818181818182", "luxe_woningzoekers_u4l": "7.0", "alleenstaanden_84k": "2.5454545454545455"}, "rankings_counts": {"starters_h6a": {"1": 6, "2": 5, "3": 0, "4": 0, "5": 0}, "gezinnen_c6z": {"1": 0, "2": 0, "3": 3, "4": 5, "5": 3}, "senioren_ic6": {"1": 2, "2": 2, "3": 1, "4": 3, "5": 3}, "alleenstaanden_84k": {"1": 2, "2": 3, "3": 4, "4": 2, "5": 0}, "sociale_huurders_vpb": {"1": 1, "2": 1, "3": 3, "4": 1, "5": 4}}, "multilocs": {"answer": {"starters_h6a": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_c6z": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_ic6": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_84k": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_vpb": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "ffa7da72-4a09-423a-b421-6a9e6703c7cb", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 142, "logic": {}, "average_rankings": {"villa_pxj": "5.1818181818181818", "hofjeswoningen_edi": "6.0", "appartementen_g9s": "3.3636363636363636", "knarrenhof_d5l": "5.1818181818181818", "tiny_houses_ikc": "5.5454545454545455", "rijwoningen_54f": "2.4545454545454545", "andere_rpi": "5.5", "seniorenwoningen_kl6": "3.5454545454545455", "twee_onder_een_kapwoning_j7q": "3.7272727272727273"}, "rankings_counts": {"appartementen_g9s": {"1": 3, "2": 2, "3": 0, "4": 1, "5": 4, "6": 1, "7": 0}, "seniorenwoningen_kl6": {"1": 2, "2": 1, "3": 2, "4": 2, "5": 3, "6": 1, "7": 0}, "tiny_houses_ikc": {"1": 1, "2": 1, "3": 1, "4": 0, "5": 1, "6": 1, "7": 5}, "villa_pxj": {"1": 1, "2": 1, "3": 1, "4": 1, "5": 0, "6": 4, "7": 2}, "twee_onder_een_kapwoning_j7q": {"1": 2, "2": 2, "3": 1, "4": 3, "5": 0, "6": 1, "7": 2}, "rijwoningen_54f": {"1": 2, "2": 3, "3": 5, "4": 1, "5": 0, "6": 0, "7": 0}, "knarrenhof_d5l": {"1": 0, "2": 1, "3": 0, "4": 3, "5": 2, "6": 3, "7": 1}}, "multilocs": {"answer": {"appartementen_g9s": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_kl6": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_ikc": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_pxj": {"title_multiloc": {"nl-NL": "Vrijtaand"}}, "twee_onder_een_kapwoning_j7q": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_54f": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_d5l": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "85e92a75-b30f-4262-8595-4a3a044ba8f6", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 11, "pageNumber": null, "questionNumber": 143, "logic": {}, "totalPickCount": 25, "answers": [{"answer": null, "count": 0}, {"answer": "dorps_r4a", "count": 9}, {"answer": "duurzaam_zop", "count": 6}, {"answer": "in_het_groen_xe1", "count": 4}, {"answer": "landelijk_218", "count": 3}, {"answer": "klassiek_f01", "count": 3}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_218": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_r4a": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_zop": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_f01": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_xe1": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen"}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "69961a89-5e4e-4d79-acee-45660245054c", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 5, "pageNumber": null, "questionNumber": 144, "logic": {}, "textResponses": [{"answer": "En plek voor iedere doelgroep"}, {"answer": "<PERSON><PERSON><PERSON> spoed maken met real<PERSON><PERSON>."}, {"answer": "Noordweg uitbreiden naar waardige nieuwe aanrijroute (geen rondweg) westkapelle om problemen bij kruispunt hogelicht te voorkomen en in te spelen op situaties bij noordwestroute."}, {"answer": "Voor het realiseren van 133 nieuwe woningen in Westkapelle (aantal door gemeente bepaald) zal er echt zo snel mogelijk aan de uitbreidingsplannen gewerkt moeten worden. Met alleen inbreidingslocaties komen we er nooit. De uitbreidinglocaties zijn ook opgenomen in de nieuwe gebiedsvisie én geadviseerd door stedenbouwkundigen. In de nieuwbouwplannen a.u.b. zoveel mogelijk rekening houden met alleenstaande kopers!! <PERSON><PERSON><PERSON> (jong)volwassenen als ouderen moeten individueel in staat zijn om een huis te kunnen kopen."}, {"answer": "meer en sneller betaalbare woningen (bijv plan vdWelle aan de Kerkeweg). woningen voor Westkapellaars en woningen betaalbaar houden"}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "03dc67d6-db74-492b-bc0d-9344e28100e1", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 11, "questionResponseCount": 4, "pageNumber": null, "questionNumber": 145, "logic": {}, "mapConfigId": "5080b82d-039b-4c58-aa22-e7a6e49ee108", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.4469261949985466, 51.53262744632137]}}, {"answer": {"type": "Point", "coordinates": [3.4462511019533086, 51.53207569907873]}}, {"answer": {"type": "Point", "coordinates": [3.446512348571415, 51.53162256696684]}}, {"answer": {"type": "Point", "coordinates": [3.448771266171025, 51.53472694088659]}}]}, {"inputType": "page", "question": {"nl-NL": "Zoutelande"}, "description": {}, "customFieldId": "2af1e421-d478-4540-843a-7046fee92561", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": 14, "questionNumber": null, "logic": {}}, {"inputType": "rating", "question": {"nl-NL": "Algemen<PERSON> v<PERSON> over uw kern"}, "description": {"nl-NL": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>"}, "customFieldId": "978d2ce3-bde9-48de-9973-02b0e754aed6", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 146, "logic": {}, "totalPickCount": 1, "answers": [{"answer": 1, "count": 0}, {"answer": 2, "count": 0}, {"answer": 3, "count": 0}, {"answer": 4, "count": 1}, {"answer": 5, "count": 0}, {"answer": null, "count": 0}], "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1"}}, "2": {"title_multiloc": {"nl-NL": "2"}}, "3": {"title_multiloc": {"nl-NL": "3"}}, "4": {"title_multiloc": {"nl-NL": "4"}}, "5": {"title_multiloc": {"nl-NL": "5"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?"}, "description": {"nl-NL": ""}, "customFieldId": "cdcc27e0-db26-4841-961b-e89fcb29fd51", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 147, "logic": {}, "totalPickCount": 2, "answers": [{"answer": null, "count": 0}, {"answer": "winkel_aanbod_y7a", "count": 1}, {"answer": "groen_g5k", "count": 1}, {"answer": "sportvoorzieningen_eub", "count": 0}, {"answer": "woning_aanbod_l9v", "count": 0}, {"answer": "speelplekken_7ge", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"sportvoorzieningen_eub": {"title_multiloc": {"nl-NL": "Sportvoorzieningen"}}, "winkel_aanbod_y7a": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}, "woning_aanbod_l9v": {"title_multiloc": {"nl-NL": "Woning a<PERSON><PERSON>d"}}, "speelplekken_7ge": {"title_multiloc": {"nl-NL": "Speelplekken"}}, "groen_g5k": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiselect", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>"}, "customFieldId": "2b8ef5e9-3f0f-431c-b8ec-8a4396a121c2", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 148, "logic": {}, "totalPickCount": 2, "answers": [{"answer": null, "count": 0}, {"answer": "horeca_d0c", "count": 1}, {"answer": "park_u9q", "count": 1}, {"answer": "pleinen_qk3", "count": 0}, {"answer": "speeltuin_mie", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"pleinen_qk3": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON>"}}, "horeca_d0c": {"title_multiloc": {"nl-NL": "Horeca"}}, "park_u9q": {"title_multiloc": {"nl-NL": "Park"}}, "speeltuin_mie": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>"}, "customFieldId": "eb31a9d8-044a-4ead-88ec-686d8916bbeb", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 149, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_lit": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_v1c": {"question": {"nl-NL": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_6rw": {"question": {"nl-NL": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 1.0}, {"answer": null, "count": 38}]}}}, {"inputType": "text", "question": {"nl-NL": "Publieke ruimte en leefomgeving"}, "description": {"nl-NL": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>"}, "customFieldId": "5347e072-91fa-4298-8dde-43dfc21113b3", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 150, "logic": {}, "textResponses": [{"answer": "<PERSON><PERSON>re bomen in Nieuwe Land"}]}, {"inputType": "matrix_linear_scale", "question": {"nl-NL": "Voorzieningen en dagelijkse activiteiten"}, "description": {"nl-NL": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>"}, "customFieldId": "b85f0d7b-74a5-40c8-974d-d8384b87d116", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 151, "logic": {}, "multilocs": {"answer": {"1": {"title_multiloc": {"nl-NL": "1 - <PERSON><PERSON> mee oneens"}}, "2": {"title_multiloc": {"nl-NL": "2 - <PERSON><PERSON>"}}, "3": {"title_multiloc": {"nl-NL": "3 - Neu<PERSON>al"}}, "4": {"title_multiloc": {"nl-NL": "4 - <PERSON><PERSON> <PERSON><PERSON>"}}, "5": {"title_multiloc": {"nl-NL": "5 - <PERSON><PERSON> mee eens"}}}}, "linear_scales": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_kwm": {"question": {"nl-NL": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 1, "percentage": 1.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_mvh": {"question": {"nl-NL": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 1.0}, {"answer": null, "count": 38}]}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_eqi": {"question": {"nl-NL": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 0, "percentage": 0.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 1, "percentage": 1.0}, {"answer": null, "count": 38}]}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_uc1": {"question": {"nl-NL": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}, "questionResponseCount": 1, "answers": [{"answer": 5, "count": 0, "percentage": 0.0}, {"answer": 4, "count": 1, "percentage": 1.0}, {"answer": 3, "count": 0, "percentage": 0.0}, {"answer": 2, "count": 0, "percentage": 0.0}, {"answer": 1, "count": 0, "percentage": 0.0}, {"answer": null, "count": 38}]}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>"}, "customFieldId": "0bef84da-948f-4d95-a745-26c4bf9fa708", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 152, "logic": {}, "average_rankings": {"sociale_huurders_yek": "2.0", "gezinnen_uma": "5.0", "starters_6on": "3.0", "alleenstaanden_02j": "1.0", "senioren_tl6": "4.0"}, "rankings_counts": {"starters_6on": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0}, "gezinnen_uma": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1}, "senioren_tl6": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0}, "alleenstaanden_02j": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0}, "sociale_huurders_yek": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0}}, "multilocs": {"answer": {"starters_6on": {"title_multiloc": {"nl-NL": "Starters"}}, "gezinnen_uma": {"title_multiloc": {"nl-NL": "Gezinnen"}}, "senioren_tl6": {"title_multiloc": {"nl-NL": "<PERSON>en"}}, "alleenstaanden_02j": {"title_multiloc": {"nl-NL": "Alleenstaanden"}}, "sociale_huurders_yek": {"title_multiloc": {"nl-NL": "<PERSON><PERSON> h<PERSON>"}}}}}, {"inputType": "ranking", "question": {"nl-NL": "Wonen en woonbehoeften"}, "description": {"nl-NL": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>"}, "customFieldId": "550480b3-f919-470d-bb37-02480d917a51", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 153, "logic": {}, "average_rankings": {"rijwoningen_80n": "3.0", "villa_852": "6.0", "seniorenwoningen_lbf": "1.0", "knarrenhof_zog": "7.0", "tiny_houses_jqx": "2.0", "twee_onder_een_kapwoning_1tx": "5.0", "appartementen_f3t": "4.0"}, "rankings_counts": {"appartementen_f3t": {"1": 0, "2": 0, "3": 0, "4": 1, "5": 0, "6": 0, "7": 0}, "seniorenwoningen_lbf": {"1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "tiny_houses_jqx": {"1": 0, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "villa_852": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 1, "7": 0}, "twee_onder_een_kapwoning_1tx": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 1, "6": 0, "7": 0}, "rijwoningen_80n": {"1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0}, "knarrenhof_zog": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1}}, "multilocs": {"answer": {"appartementen_f3t": {"title_multiloc": {"nl-NL": "Appartementen "}}, "seniorenwoningen_lbf": {"title_multiloc": {"nl-NL": "Seniorenwoningen"}}, "tiny_houses_jqx": {"title_multiloc": {"nl-NL": "Tiny houses "}}, "villa_852": {"title_multiloc": {"nl-NL": "Vrijstaand"}}, "twee_onder_een_kapwoning_1tx": {"title_multiloc": {"nl-NL": "<PERSON>we<PERSON>-onder-een-kapwoning"}}, "rijwoningen_80n": {"title_multiloc": {"nl-NL": "Rijwoningen"}}, "knarrenhof_zog": {"title_multiloc": {"nl-NL": "Knarrenhof"}}}}}, {"inputType": "multiselect", "question": {"nl-NL": "<PERSON>its<PERSON>ing woningen"}, "description": {"nl-NL": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>"}, "customFieldId": "58c61c8b-0f48-4496-b909-657eb7fb1e0f", "required": true, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 154, "logic": {}, "totalPickCount": 3, "answers": [{"answer": null, "count": 0}, {"answer": "landelijk_6k8", "count": 1}, {"answer": "dorps_vot", "count": 1}, {"answer": "in_het_groen_vxn", "count": 1}, {"answer": "duurzaam_98z", "count": 0}, {"answer": "klassiek_tv6", "count": 0}, {"answer": "other", "count": 0}], "multilocs": {"answer": {"landelijk_6k8": {"title_multiloc": {"nl-NL": "Landelijk"}}, "dorps_vot": {"title_multiloc": {"nl-NL": "Dorps"}}, "duurzaam_98z": {"title_multiloc": {"nl-NL": "<PERSON><PERSON><PERSON><PERSON>"}}, "klassiek_tv6": {"title_multiloc": {"nl-NL": "Klassiek"}}, "in_het_groen_vxn": {"title_multiloc": {"nl-NL": "In het groen"}}, "other": {"title_multiloc": {"nl-NL": "<PERSON><PERSON>"}}}}, "textResponses": []}, {"inputType": "multiline_text", "question": {"nl-NL": "Aanvullende opmerkingen"}, "description": {"nl-NL": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>"}, "customFieldId": "9ea82ed4-3227-4ab7-b5e7-22f395ee76f9", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 155, "logic": {}, "textResponses": [{"answer": "Behoud en uitbreiding van het park in De Tienden"}]}, {"inputType": "point", "question": {"nl-NL": "Aanvullende opmerkingen kaart"}, "description": {"nl-NL": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>"}, "customFieldId": "5e77dccf-1784-4dba-ad84-6cbb5e1337af", "required": false, "grouped": false, "hidden": false, "totalResponseCount": 1, "questionResponseCount": 1, "pageNumber": null, "questionNumber": 156, "logic": {}, "mapConfigId": "4833754d-dcc0-4ec6-a43c-deb93ad904d2", "pointResponses": [{"answer": {"type": "Point", "coordinates": [3.492952, 51.5017541]}}]}], "totalSubmissions": 39}}}