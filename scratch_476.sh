bin/rspec --bisect spec/acceptance/admin_native_survey_responses_spec.rb spec/acceptance/baskets_spec.rb spec/acceptance/event_images_spec.rb spec/acceptance/idea_images_spec.rb spec/acceptance/ideas_spec.rb spec/acceptance/notifications_spec.rb spec/acceptance/project_custom_fields_spec.rb spec/acceptance/projects_allowed_input_topics_spec.rb spec/acceptance/stats_comments_spec.rb spec/acceptance/events/attendances_spec.rb spec/finders/ideas_finder_spec.rb spec/jobs/generate_user_avatar_job_spec.rb spec/jobs/update_member_count_job_spec.rb spec/lib/verification_spec.rb spec/lib/participation_method/none_spec.rb spec/lib/voting_method/multiple_voting_spec.rb spec/models/app_configuration_spec.rb spec/models/custom_field_spec.rb spec/models/idea_spec.rb spec/models/phase_spec.rb spec/models/topic_spec.rb spec/models/notifications/invitation_to_cosponsor_idea_spec.rb spec/models/notifications/project_review_state_change_spec.rb spec/models/notifications/internal_comments/internal_comment_on_idea_you_commented_internally_on_spec.rb spec/policies/basket_policy_spec.rb spec/policies/idea_image_policy_spec.rb spec/policies/project_image_policy_spec.rb spec/requests/azure_active_directory_b2c_authentication_spec.rb