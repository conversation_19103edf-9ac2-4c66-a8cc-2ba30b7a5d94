# find key pair (aws) by name
aws ec2 describe-key-pairs --key-name ubuntu-xps13-keys
aws-vault exec prd-admin -- aws ec2 describe-key-pairs --key-name ubuntu-xps13-keys

# ubuntu = os
# xps13 = laptop

#{
#    "KeyPairs": [
#        {
#            "KeyPairId": "key-02187e4c4f275c3da",
#            "KeyFingerprint": "fe:70:7f:07:7b:91:c8:2a:2d:ce:5a:60:ce:5f:ac:f4:eb:aa:93:56",
#            "KeyName": "ubuntu-xps13-keys",
#            "KeyType": "rsa",
#            "Tags": [],
#            "CreateTime": "2017-02-20T11:41:30+00:00"
#        }
#    ]
#}

# in the aws console, it can be found in the EC2 dashboard, under Network & Security -> Key Pairs

# Make an IAM user in AWS with following policies: ` AmazonRDSFullAccess `, `AmazonEC2FullAccess`, `AmazonElastiCacheFullAccess`, `CloudFrontFullAccess`, `AmazonRoute53FullAccess`, `AmazonS3FullAccess `. Add `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` as environment variables in Terraform Cloud.

aws-vault exec prd-admin -- aws iam create-user --user-name terraform


# TODO:
# - update cl2_back_db_postgres_version and  cl2_workshops_db_postgres_version default to 16.3
# - update europe cluster to not skip the final db snapshot


# terraform plan & apply
terraform plan -out=plan.out
terraform apply plan.out

# or with tfvars
terraform plan -var-file=aws-public.tfvars-out=plan.out
terraform apply plan.out

# search git commit history (diff) for a specific string: "LB service account store log"
git log -S"LB service account store log"


aws-vault exec prd-admin -- terraform import 'module.aws_frankfurt[0].aws_s3_bucket_ownership_controls.log_store' cl2-logs-prod-frankfurt

