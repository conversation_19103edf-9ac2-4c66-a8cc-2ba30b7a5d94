layout = ContentBuilder::Layout.first

craftjs_state = Craftjs::State.new(layout.craftjs_state)
craftjs_state.nodes # => Array<CraftjsJson::Node>

# get node using idendifier
node = craftjs_state.node('node_1') # => Craftjs::Node
node.parent # => CraftjsJson::Node
node.nodes # => Array<CraftjsJson::Node>

node.delete # delete node from craftjs_json
node.replace(new_nodes)


node.to_h
# For example:
#   {
#     "type": {
#       "resolvedName": "Container"
#     },
#     "nodes": [
#       "¨"
#     ],
#     "props": {},
#     "custom": {},
#     "hidden": false,
#     "parent": "3h-Eizw2cD",
#     "isCanvas": true,
#     "displayName": "Container",
#     "linkedNodes": {}
#   }

class Craftjs::Node
  def initialize(resolved_name, state, &b)
    @state = state

    @resolved_name = resolved_name

    @nodes = []
    @props = {}
    @custom = {}
    @hidden = false
    @parent = nil
    @is_canvas = false
    @display_name = resolved_name # TODO: check if this is correct
    @linked_nodes = {}

    instance_eval(&b) if block_given? # instance_eval doc = https://apidock.com/ruby/BasicObject/instance_eval
  end
end

class Craftjs::State
end
