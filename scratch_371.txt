bundle exec rubocop -a app/concerns/filterer.rb app/controllers/web_api/v1/initiative_status_changes_controller.rb app/controllers/web_api/v1/stats_ideas_controller.rb app/jobs/log_activity_job.rb app/models/concerns/sluggable.rb app/models/permission.rb app/services/custom_field_params_service.rb app/services/events/side_fx_attendance_service.rb app/services/input_ui_schema_generator_service.rb app/services/permissions/initiative_permissions_service.rb app/services/permissions/project_permissions_service.rb app/services/settings_service.rb app/services/track_segment_service.rb config/initializers/application_controller_renderer.rb config/initializers/backtrace_silencers.rb config/initializers/inflections.rb engines/commercial/admin_api/app/controllers/admin_api/admin_api_controller.rb engines/commercial/analysis/app/controllers/analysis/web_api/v1/taggings_controller.rb engines/commercial/analysis/app/lib/analysis/nlp_cloud_helpers.rb engines/commercial/analytics/app/services/analytics/query_runner_service.rb engines/commercial/bulk_import_ideas/app/services/bulk_import_ideas/parsers/idea_base_file_parser.rb engines/commercial/bulk_import_ideas/app/services/bulk_import_ideas/parsers/idea_pdf_file_parser.rb engines/commercial/content_builder/lib/tasks/content_builder_tasks.rake engines/commercial/idea_assignment/spec/acceptance/users_spec.rb engines/commercial/idea_assignment/spec/mailers/idea_assigned_to_you_mailer_spec.rb engines/commercial/idea_assignment/spec/mailers/previews/email_campaigns/idea_assigned_to_you_mailer_preview.rb engines/commercial/idea_assignment/spec/services/idea_assignment/idea_assignment_service_spec.rb engines/commercial/multi_tenancy/app/services/multi_tenancy/templates/serializers/core.rb engines/commercial/multi_tenancy/app/services/multi_tenancy/templates/utils.rb engines/commercial/multi_tenancy/lib/tasks/core/fix_html_multiloc.rake engines/commercial/report_builder/app/services/report_builder/permissions/report_permissions_service.rb engines/commercial/report_builder/app/services/report_builder/side_fx_report_service.rb engines/commercial/verification/app/controllers/verification/patches/web_api/v1/user_custom_fields_controller.rb engines/free/email_campaigns/app/models/email_campaigns/campaign_email_command.rb engines/free/email_campaigns/app/models/email_campaigns/campaigns/admin_digest.rb engines/free/email_campaigns/app/models/email_campaigns/campaigns/moderator_digest.rb engines/free/email_campaigns/app/models/email_campaigns/campaigns/user_digest.rb engines/free/email_campaigns/spec/acceptance/consents_spec.rb engines/free/email_campaigns/spec/mailers/assignee_digest_mailer_spec.rb engines/free/email_campaigns/spec/mailers/initiative_assigned_to_you_mailer_spec.rb engines/free/email_campaigns/spec/mailers/previews/email_campaigns/assignee_digest_mailer_preview.rb engines/free/email_campaigns/spec/mailers/previews/email_campaigns/initiative_assigned_to_you_mailer_preview.rb engines/free/frontend/app/services/frontend/url_service.rb engines/free/polls/app/policies/polls/response_policy.rb lib/carrierwave/file_base64/base64_string_io.rb lib/pundit/not_authorized_error_with_reason.rb lib/tasks/single_use/20221013_add_missing_*****e.rake lib/tasks/single_use/20230131_migrate_flexible_input_forms.rake lib/tasks/single_use/20231212_migrate_homepage_craftjson.rake spec/lib/participation_method/ideation_spec.rb spec/lib/participation_method/proposals_spec.rb spec/services/mention_service_spec.rb