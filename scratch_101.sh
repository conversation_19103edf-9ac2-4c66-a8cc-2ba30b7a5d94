cp .env-production-benelux .env-web
cp .env-tenant-config-production .env-tenant-setup
cp .env-osa-production .env-osa
cp .env-widgets-production .env-widgets

rm .env-production-benelux .env-tenant-config-production .env-osa-production .env-widgets-production

# checkout to branch TAN-897/common-docker-compose-for-all-clusters
git checkout TAN-897/common-docker-compose-for-all-clusters

docker stack deploy --compose-file docker-compose-production-benelux.yml cl2-prd-bnlx-stack --with-registry-auth --prune

cp .env-production-benelux .env-prerender

# Remove the stack "nginx-prerender-proxy" from docker swarm
docker stack rm nginx-prerender-proxy
# Recover: docker stack deploy --compose-file nginx-prerender-proxy/docker-compose-production-benelux.yml nginx-prerender-proxy --with-registry-auth --prune


# Remove the

# Update the CircleCI config file to user docker-compose-production.yml instead of docker-compose-production-benelux.yml
# Remove unnecessary env files

# Docker swarm: prune networks that are not used by any stack
docker network prune
