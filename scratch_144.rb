admin_creator = AdminCreator.new(
  dry_run: !['production', 'staging'].include?(ENV.fetch("RACK_ENV")),
  cluster_cloudfront_id: ENV.fetch('CL2_CLUSTER_CLOUDFRONT_ID')
)

tenant_name = 'adrien-moderator-confirmation-test-11'
tenant_host = 'adrien-moderator-confirmation-test-11.citizenlab.co'
tenant_id = '8fc0578a-7e5f-4241-ab1c-283c05caa96a'
cluster = 'prd-can'

admin_creator.ensure_default_admin(tenant_name, tenant_host, tenant_id, cluster)
lp = admin_creator.instance_variable_get(:@lp)
lp.delete_credential(tenant_id)
