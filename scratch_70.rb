require 'jwt'

jwk = {
  "kty": "RSA",
  "kid": "GDeFBZrcfqxZ2CIPBYYMgjRhwnY=",
  "use": "sig",
  "x5t": "tSrE6n0UOizCaNTrIA_KyWi4SSQ",
  "x5c": [
    "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"
  ],
  "n": "urssM6BlGKux9zfTwMj-FLBJFmxik-GIPr1bIDrmz7BGkbbvrHlKhZ7Iw5kkyLirPYIbornX-Yei_RkWZ03excBFA86C96nBDWFjY49FWZbOK10A_dv9QO4lH-sEixJdWOdoM50X_L295-ZVZqcOHcB5_kOT_RPM97Eyh39ZsSByeJsiCDhpkxJR4WwBWJiRhJ-BesXXqdmbYDdAlNtK4VMamv1U9Ut0PuZG8GA1eGbXXs5ODk4gHNFScusqVyoiDjW490UfE0Ikje6R_LD2G-gdvWoJ0A7cj0vrpld1dONjq6GiqA_84ajrN7cGlC0hwQIic1TuqQ13wIn8wxGqAQ",
  "e": "AQAB",
  "alg": "RS256"
}

public_key = JWT::JWK::RSA.import(jwk).public_key

JSON::JWK