# find the first project that fails to be copied (exported and imported) correctly
project = Project.all.find do |project|
  Project.transaction do
    template = ProjectCopyService.new.export(project, include_ideas: true)
    template_yaml = template.to_yaml
    AdminApi::CopyProjectJob.perform_now(template_yaml, nil)
    raise ActiveRecord::Rollback
  end

  false
rescue StandardError => e
  puts "Failed to copy project #{project.id}: #{e.message}"
  true
end


project = Project.find("a0b0ac01-75eb-4712-a0b8-592ab4c5b1ea")
copy_service = ProjectCopyService.new
template = copy_service.export(project, include_ideas: true)
template['models']['follower'].size
# find followers whose serialization produces followable_type: nil
exported_ideas = project.ideas.published
followers = Follower.where(followable: project).or(Follower.where(followable: exported_ideas))


idea_followers = Follower.where(followable: exported_ideas)
idea_followers.map do |follower|
  copy_service.send(:lookup_ref, follower.followable, 'follower')
end