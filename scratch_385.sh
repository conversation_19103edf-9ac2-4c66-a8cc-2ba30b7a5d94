# copy all the files in arn:aws:s3:::govocal-cloudfront-access-logs in the current directory
aws-vault exec prd-admin -- aws s3 cp s3://govocal-cloudfront-access-logs . --recursive
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-08.9a2770bd.gz to ./E24N9GK07GTKIT.2024-08-08-08.9a2770bd.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-09.079909cd.gz to ./E24N9GK07GTKIT.2024-08-08-09.079909cd.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-09.1b7840a2.gz to ./E24N9GK07GTKIT.2024-08-08-09.1b7840a2.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-09.516f999e.gz to ./E24N9GK07GTKIT.2024-08-08-09.516f999e.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-09.2c7aa707.gz to ./E24N9GK07GTKIT.2024-08-08-09.2c7aa707.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-09.6d0d6077.gz to ./E24N9GK07GTKIT.2024-08-08-09.6d0d6077.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-08.79a39324.gz to ./E24N9GK07GTKIT.2024-08-08-08.79a39324.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-09.735aa92a.gz to ./E24N9GK07GTKIT.2024-08-08-09.735aa92a.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-09.823239a2.gz to ./E24N9GK07GTKIT.2024-08-08-09.823239a2.gz
#download: s3://govocal-cloudfront-access-logs/E24N9GK07GTKIT.2024-08-08-09.221f225d.gz to ./E24N9GK07GTKIT.2024-08-08-09.221f225d.gz

tail E24N9GK07GTKIT.2024-08-08-08.9a2770bd
# #Version: 1.0
# #Fields: date time x-edge-location sc-bytes c-ip cs-method cs(Host) cs-uri-stem sc-status cs(Referer) cs(User-Agent) cs-uri-query cs(Cookie) x-edge-result-type x-edge-request-id x-host-header cs-protocol cs-bytes time-taken x-forwarded-for ssl-protocol ssl-cipher x-edge-response-result-type cs-protocol-version fle-status fle-encrypted-fields c-port time-to-first-byte x-edge-detailed-result-type sc-content-type sc-content-len sc-range-start sc-range-end
# 2024-08-08	08:56:23	SCL51-P3	2839	***********	GET	d2oy3ts8qtqlds.cloudfront.net	/es-CL	200	-	Mozilla/5.0%20(compatible;%20PRTG%20Network%20Monitor%20(www.paessler.com);%20Windows)	-	-	Miss	Vm3w7JANGHz9ypYO4AOW0NumqnxVJHkkdsGb6WeIgJmaMfT6kdmUhg==	mopchile.citizenlab.co	https	180	0.565	-	TLSv1.3	TLS_AES_128_GCM_SHA256	Miss	HTTP/1.1	-	-	29903	0.565	Miss	text/html	1817	-	-

ipython

import pandas as pd
df = pd.read_csv('E24N9GK07GTKIT.2024-08-08-08.9a2770bd.gz', sep='\t', comment='#', compression='gzip')

# Logs version: 1.0
df.columns = ["date","time","x-edge-location","sc-bytes","c-ip","cs-method","cs(Host)","cs-uri-stem","sc-status","cs(Referer)","cs(User-Agent)","cs-uri-query","cs(Cookie)","x-edge-result-type","x-edge-request-id","x-host-header","cs-protocol","cs-bytes","time-taken","x-forwarded-for","ssl-protocol","ssl-cipher","x-edge-response-result-type","cs-protocol-version","fle-status","fle-encrypted-fields","c-port","time-to-first-byte","x-edge-detailed-result-type","sc-content-type","sc-content-len","sc-range-start","sc-range-end"]


# same but read all the files into a single dataframe
import glob
import os

files = glob.glob('*.gz')
columns = ["date","time","x-edge-location","sc-bytes","c-ip","cs-method","cs(Host)","cs-uri-stem","sc-status","cs(Referer)","cs(User-Agent)","cs-uri-query","cs(Cookie)","x-edge-result-type","x-edge-request-id","x-host-header","cs-protocol","cs-bytes","time-taken","x-forwarded-for","ssl-protocol","ssl-cipher","x-edge-response-result-type","cs-protocol-version","fle-status","fle-encrypted-fields","c-port","time-to-first-byte","x-edge-detailed-result-type","sc-content-type","sc-content-len","sc-range-start","sc-range-end"]
dfs = [pd.read_csv(f, sep='\t', comment='#', compression='gzip', names=columns) for f in files]

# stack the dataframes
df = pd.concat(dfs, axis=0)
# sort by date and time
df = df.sort_values(by=['date', 'time'])
# count x-host-header
df['x-host-header'].value_counts()

# conectados.citizenlab.co                 705
# san-esteban.citizenlab.co                604
# mopchile.citizenlab.co                   454
# sanisidroparticipa.citizenlab.co         324
# minvu.citizenlab.co                      287
# constitucion.citizenlab.co               156
# statkraftparticipa.citizenlab.co         116
# agendadigitalrd.citizenlab.co             86
# municipiosenpandemia.citizenlab.co        63
# dialogaciudadcndu.citizenlab.co           53
# san-vicente.citizenlab.co                 50
# esquerre.citizenlab.co                    29
# saneamentodebasenatural.citizenlab.co      4
# Name: x-host-header, dtype: int64
