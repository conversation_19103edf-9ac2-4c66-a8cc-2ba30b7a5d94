
Name: omniauth
Version: 1.9.1
CVE: CVE-2015-9284
GHSA: GHSA-ww4x-rwq6-qpgf
Criticality: High
URL: https://github.com/omniauth/omniauth/wiki/Resolving-CVE-2015-9284
Title: CSRF vulnerability in OmniAuth's request phase
Solution: update to '>= 2.0.0'

Name: omniauth
Version: 1.9.1
CVE: CVE-2020-36599
GHSA: GHSA-pm55-qfxr-h247
Criticality: Critical
URL: https://github.com/omniauth/omniauth/commit/43a396f181ef7d0ed2ec8291c939c95e3ed3ff00#diff-575abda9deb9b1a77bf534e898a923029b9a61e991d626db88dc6e8b34260aa2
Title: OmniAuth's `lib/omniauth/failure_endpoint.rb` does not escape `message_key` value
Solution: update to '~> 1.9.2', '>= 2.0.0'
