# list certificates whose domain is samen.dongen.nl and are not expired (aws)
aws-vault exec prd-admin -- aws acm list-certificates --query "CertificateSummaryList[?DomainName=='samen.dongen.nl' && NotAfter > '2020-01-01']"

# same but region is us-east-1
aws-vault exec prd-admin -- aws acm list-certificates --region us-east-1 --query "CertificateSummaryList[?DomainName=='samen.dongen.nl' && NotAfter > '2020-01-01']"

# list png files in cl2-front-production-benelux bucket
aws-vault exec prd-admin -- aws s3 ls s3://cl2-front-production-benelux --recursive --query "Contents[?contains(Key, '.png')].Key"

# terraform import aws_acm_certificate.cert


# replace acm certificate in terraform state
terraform state rm module.tenant_domain_90463923-5d20-4c50-b968-e27edea1b271.aws_acm_certificate.cert[0]
terraform import "module.tenant_domain_90463923-5d20-4c50-b968-e27edea1b271.aws_acm_certificate.cert[0]" arn:aws:acm:us-east-1:389841910643:certificate/f5b74b2c-8b84-4043-8cbb-89e10b99060e