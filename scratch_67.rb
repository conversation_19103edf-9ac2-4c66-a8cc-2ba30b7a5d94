{"setup"=>
  #<Proc:0x000055a0903e6288 /cl2_back/engines/commercial/id_bosa_fas/config/initializers/omniauth.rb:3 (lambda)>,
  "skip_info"=>false,
  "origin_param"=>"origin",
  "name"=>"bosa_fas",
  "client_options"=>
    {"identifier"=>nil,
      "secret"=>nil,
      "redirect_uri"=>nil,
      "scheme"=>"https",
      "host"=>nil,
      "port"=>443,
      "authorization_endpoint"=>"/authorize",
      "token_endpoint"=>"/token",
      "userinfo_endpoint"=>"/userinfo",
      "jwks_uri"=>"/jwk",
      "end_session_endpoint"=>nil},
  "issuer"=>nil,
  "discovery"=>false,
  "client_signing_alg"=>nil,
  "client_jwk_signing_key"=>nil,
  "client_x509_signing_key"=>nil,
  "scope"=>[:openid],
  "response_type"=>"code",
  "state"=>nil,
  "response_mode"=>nil,
  "display"=>nil,
  "prompt"=>nil,
  "hd"=>nil,
  "max_age"=>nil,
  "ui_locales"=>nil,
  "id_token_hint"=>nil,
  "acr_values"=>nil,
  "send_nonce"=>true,
  "send_scope_to_token_endpoint"=>true,
  "client_auth_method"=>nil,
  "post_logout_redirect_uri"=>nil,
  "extra_authorize_params"=>{},
  "allow_authorize_params"=>[],
  "uid_field"=>"sub"}