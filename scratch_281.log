2024-05-31 14:52:13.341707 D [16188:9800] [my-test] (1.364ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"deleted_at\" IS NULL AND \"public\".\"tenants\".\"creation_finalized_at\" IS NOT NULL", :allocations=>38, :cached=>nil}
2024-05-31 14:52:13.343482 D [16188:9800] [my-test] (1.471ms) ActiveRecord -- {:sql=>"SELECT id, name, host, logo, favicon, settings, created_at, updated_at, style FROM \"example_org\".app_configurations", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.351047 D [16188:9800] [my-test] (4.027ms) ActiveRecord -- ContentBuilder::Layout Load -- {:sql=>"SELECT \"content_builder_layouts\".* FROM \"content_builder_layouts\" WHERE NOT (code LIKE 'backup/%') AND \"content_builder_layouts\".\"id\" IN (SELECT \"content_builder_layouts\".\"id\" FROM \"content_builder_layouts\" CROSS JOIN jsonb_each(content_builder_layouts.craftjs_json) AS jsonb_each WHERE NOT (code LIKE 'backup/%') AND (jsonb_each.value->'type'->>'resolvedName' = 'AboutReportWidget'))", :allocations=>6, :cached=>nil}
2024-05-31 14:52:13.355809 D [16188:9800] [my-test] (2.113ms) ActiveRecord -- ReportBuilder::Report Load -- {:sql=>"SELECT \"report_builder_reports\".* FROM \"report_builder_reports\" WHERE \"report_builder_reports\".\"id\" IN ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46, $47, $48, $49, $50, $51)", :binds=>{:id=>["57725bb7-c783-4383-a72d-80f5c4d09388", "1c1bc458-9122-4459-8720-c2738835a12b", "23b4c917-7c54-412a-83bd-6b0de2bacf21", "aca08277-fe20-483b-bb06-4fbcb457b79f", "a2e37846-b58e-46c4-ae2a-4265b108d3b2", "983c50b6-76d7-4020-ad20-8ac8b37f4119", "115835cb-0acb-4ba0-b88a-274487aa0e6c", "33519dc3-36ef-40fc-9fa9-106cd0acb7da", "0693bca2-88b9-4c0b-b528-c868b8552dcb", "f2943991-904d-4d4f-ab07-12eb7ed65cfa", "39367ba2-bf2d-4e5b-8ac2-b4d1109303c5", "ad622a24-9ad7-44df-a7a3-c82666715963", "71c6365f-8255-4ef0-8136-d04c10402ab1", "1e695769-11d3-4292-b2c6-20b024f0b221", "13e81cb1-1962-42d7-adab-e7102508a2f8", "e1fc12e3-e5de-496d-b7af-d012cbdb4982", "901ccd3d-d5dd-411b-84b5-21f4067ebb18", "79ae2ea4-23dc-43ca-8ca3-ee7bb6313970", "5b1cc6c3-a049-4649-a9e2-3af3add2f43a", "19177f96-e302-4094-9067-515e7d476b79", "2ab7b179-8263-4ece-b42a-3d148446268d", "bf57e241-c5ed-48e3-bbf0-b1e789bf29ac", "b932cc21-6398-4ccf-a597-e3d1067fa2a7", "f910cc1a-025c-4019-af61-2872c046add7", "bd70e782-e59b-4c66-b024-a147259acad5", "a3d4df2d-f004-4d21-81f9-cd430a097cf0", "5c2f7eed-a2c2-4455-96ba-a922d092201d", "f0fd065e-bf68-4dc6-b78a-a9885f109679", "5c72bdfa-cb37-4add-9b4a-14c6019b6c72", "9f41e84a-33f5-492c-b98f-af73e9c056a5", "6e4ac887-1c59-483c-b58a-ad65a40ca18d", "ba88111b-3a7a-4188-b794-7d4b70802ad9", "9d5f5927-7e79-4e0b-9e5e-12c45c521453", "9672d93c-8a10-49b9-9b6d-0a7e8aaa4078", "47ec7822-f133-45c6-b79e-c62f41ecfe72", "0c115267-50cd-4ac2-b143-75bc863e3e38", "c8524e9d-d28a-42fc-b96f-02b9314ecd85", "9025ed0e-4c23-4b6c-8935-a4aa1b8d443e", "ea552849-70b5-44df-b143-c56c7ca24afc", "f106ade8-b41d-45c4-ac7e-83f82444c3f5", "5adbae7f-466e-489e-a824-8db56cae7cf4", "35ce217e-9b34-4574-85a8-6db3e54d9144", "2bbb5b98-b974-48b9-b166-4bc1ca9f68ef", "cc8ff0d2-64de-41a3-86b0-484f1f09e0ba", "c16a4f1d-be5b-409d-a346-90fbd4e57c7a", "1db82651-e98e-4779-b29b-7542a7e0f3c5", "daa7137c-884b-48fe-afc2-e42f48af2020", "7f64a786-6d4d-46bd-98b9-ef0c68500a27", "23af4c22-8112-42c6-8bb2-1fb919864cc1", "95e849fb-b940-4819-a546-0792221bf735", "858226d1-c9fc-4a2f-944c-c9b22642ccd0"]}, :allocations=>42, :cached=>nil}
2024-05-31 14:52:13.360331 D [16188:9800] [my-test] (1.264ms) ActiveRecord -- Project Load -- {:sql=>"SELECT \"projects\".* FROM \"projects\" WHERE \"projects\".\"id\" = $1", :binds=>{:id=>"cb48541e-a269-4d69-97f5-82ca695f3fbf"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.364025 D [16188:9800] [my-test] (2.030ms) ActiveRecord -- User Load -- {:sql=>"SELECT \"users\".* FROM \"users\" WHERE \"users\".\"id\" IN ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45, $46, $47, $48, $49, $50, $51)", :binds=>{:id=>["e20cfd06-a53b-4bec-870d-0fac9477973f", "32196a21-7379-4e25-8954-2b65f04618bd", "429967e0-5ede-489d-aa4c-9ccf3c523690", "73e49a4a-f01d-4cfc-9922-9dc6d1302bf9", "5427af3f-fa08-4273-8e51-759cafddbb66", "5ee4a322-2cd4-4a66-bffb-1d72cc6cfcaa", "837102eb-1057-4beb-a742-14d37f9716d3", "1f2b5859-4994-46a8-9b3a-cfcfd9735060", "35cfaabe-2485-4458-ba13-643fe2340d36", "97f002f1-c708-4bce-8a8a-4eab132bfbe2", "25f7f533-c7ed-4e30-8f38-01222d71bec7", "86ef9153-6d23-4380-ac42-3e52890be8e9", "18404919-b610-4953-b8cf-bbd4921cc528", "d73d21b2-f262-4b11-8f2f-f5e84683bfab", "7e6c251c-e7cd-4b31-ae60-94317ec4bb19", "caf79454-f34a-435d-84bf-322784d9eede", "decbad2b-703d-4729-8bb5-17292745e516", "76032b14-850b-4d20-bdd5-6fc262767593", "dbfc0251-5fc9-4b5e-8702-df441d732f4e", "173381fd-3168-4a1f-abb5-0c6f796ce4d7", "7105f1df-0c13-4dd0-a2b8-ccc8f7c1dd22", "c885b004-f041-4556-8992-d25c251e0490", "c3b81d22-621d-41c9-8755-566228d5193b", "1138510d-3ed4-4a35-8e4b-83e7b3b1d748", "66baf0aa-a14c-4215-b771-d76a93c4c1b7", "fce99d70-9d3e-4507-8129-8d035435ad3c", "623e78af-1a77-4fb7-aabc-1aec60779d71", "4e595dd1-2187-4cbc-a615-719d1efa85df", "602cc330-95dc-410a-891e-df906a3cbdbe", "848f9513-7af5-42c4-9698-3418591656aa", "de52bd6f-fa16-4881-9605-4dea3e3ba80b", "8abb65c7-3cfc-4435-b827-c27e51476ea3", "62374edb-f36f-47ea-8085-6ce1809b8337", "80df1ef5-bd0d-40b4-8f8d-dd4b8f5c85e1", "41267569-b053-4038-8f44-9922371cad98", "46daf077-f1cd-4ec8-9658-685a5b7de960", "cb1dc1ae-8c46-48a6-a622-904aa19172a9", "6333348b-b8d6-4e51-b90f-4bd25a4d0fc9", "9a3036fc-c0fd-4473-8410-58ae07449d3a", "861e7877-5399-407c-a16e-da59650d92df", "2e5c21bf-1554-4b0d-9fa6-5d722fa026d5", "0c8093e0-d174-4e96-a3b2-58460f9e7db3", "719a64e6-bddb-4350-b1ea-2c97a94568cc", "65146ef1-977c-4aa8-b9d3-38c13321a3f6", "2a75ed43-d2ab-4e33-bb42-656880f8a658", "36ec24d0-238b-4974-8408-30d01b2bba6f", "8d428d6b-59ef-4c0f-a4ff-394de9b8ac9a", "a78dc160-011a-4071-9de1-b7aa26bc8d7c", "e9090dbd-3973-4f5c-8749-e75a05d0ae04", "256f68f1-18c0-45f9-8445-f84c1aae0237", "82cac1d3-8790-4019-bd4b-0758d6506281"]}, :allocations=>46, :cached=>nil}
2024-05-31 14:52:13.371175 D [16188:9800] [my-test] (1.841ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>96, :cached=>nil}
2024-05-31 14:52:13.373350 D [16188:9800] [my-test] (1.664ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/64a89a00-5b36-4898-83a4-c03325d7503e", :enabled=>true, :created_at=>"2024-05-31 12:52:13.369031", :updated_at=>"2024-05-31 12:52:13.369031", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.377978 D [16188:9800] [my-test] (1.503ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.379804 D [16188:9800] [my-test] (1.405ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.381568 D [16188:9800] [my-test] (1.366ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.383465 D [16188:9800] [my-test] (1.518ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.385226 D [16188:9800] [my-test] (1.237ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.401157 D [16188:9800] [my-test] (2.440ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"6aabdbbf-75b4-4cf7-95d3-dd557c8c1ca1.png", :code=>"a70f9a5b-5be7-4fe5-b978-ef38b34312ed", :created_at=>"2024-05-31 12:52:13.398309", :updated_at=>"2024-05-31 12:52:13.398309"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.403423 D [16188:9800] [my-test] (1.853ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.409923 D [16188:9800] [my-test] (1.671ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.407643", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"vmdIcpJ8JT\",\"H8sqMcyZzI\",\"bfinUnGso0\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"vmdIcpJ8JT\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"a70f9a5b-5be7-4fe5-b978-ef38b34312ed\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"H8sqMcyZzI\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"bfinUnGso0\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"64a89a00-5b36-4898-83a4-c03325d7503e"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.413371 D [16188:9800] [my-test] (1.505ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.417458 D [16188:9800] [my-test] (1.462ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.420104 D [16188:9800] [my-test] (2.146ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/ed9e487a-a9ac-46b5-99f5-cea10cc2694f", :enabled=>true, :created_at=>"2024-05-31 12:52:13.415684", :updated_at=>"2024-05-31 12:52:13.415684", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.423039 D [16188:9800] [my-test] (1.948ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.425024 D [16188:9800] [my-test] (1.548ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.426870 D [16188:9800] [my-test] (1.424ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.428474 D [16188:9800] [my-test] (1.245ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.430042 D [16188:9800] [my-test] (1.220ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.437208 D [16188:9800] [my-test] (1.602ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"52a325f1-da68-4d20-9f71-c7eff9ad0f81.png", :code=>"5a706bf9-8a8d-4533-9a8f-0b99bd7b5506", :created_at=>"2024-05-31 12:52:13.435238", :updated_at=>"2024-05-31 12:52:13.435238"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.438957 D [16188:9800] [my-test] (1.388ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.445369 D [16188:9800] [my-test] (1.815ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.442914", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"brdupyP72g\",\"8uMAGLSIlW\",\"c-EY31xOgr\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"brdupyP72g\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"5a706bf9-8a8d-4533-9a8f-0b99bd7b5506\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"8uMAGLSIlW\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"c-EY31xOgr\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"ed9e487a-a9ac-46b5-99f5-cea10cc2694f"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.447239 D [16188:9800] [my-test] (1.358ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.450828 D [16188:9800] [my-test] (1.280ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.452771 D [16188:9800] [my-test] (1.479ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/78db8603-4f02-4df6-a8f1-8a8acac0a21f", :enabled=>true, :created_at=>"2024-05-31 12:52:13.449251", :updated_at=>"2024-05-31 12:52:13.449251", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.455188 D [16188:9800] [my-test] (1.698ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.456914 D [16188:9800] [my-test] (1.321ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.458681 D [16188:9800] [my-test] (1.383ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.460319 D [16188:9800] [my-test] (1.271ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.461963 D [16188:9800] [my-test] (1.299ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.467745 D [16188:9800] [my-test] (1.648ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"cd8d9933-db24-49f1-b98a-1004855799a7.png", :code=>"1a94903b-d5ac-4f94-8ea5-9768a453148c", :created_at=>"2024-05-31 12:52:13.465741", :updated_at=>"2024-05-31 12:52:13.465741"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.469346 D [16188:9800] [my-test] (1.253ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.475351 D [16188:9800] [my-test] (1.783ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.472914", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"a7mzUo2qLk\",\"CJs2_lgwI3\",\"UngFtcVWXu\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"a7mzUo2qLk\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"1a94903b-d5ac-4f94-8ea5-9768a453148c\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"CJs2_lgwI3\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"UngFtcVWXu\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"78db8603-4f02-4df6-a8f1-8a8acac0a21f"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.477170 D [16188:9800] [my-test] (1.306ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.480838 D [16188:9800] [my-test] (1.313ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.482919 D [16188:9800] [my-test] (1.609ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/683a17cf-a149-4059-850c-4dd9d4c7ae91", :enabled=>true, :created_at=>"2024-05-31 12:52:13.479215", :updated_at=>"2024-05-31 12:52:13.479215", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.485203 D [16188:9800] [my-test] (1.543ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.486993 D [16188:9800] [my-test] (1.389ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.488782 D [16188:9800] [my-test] (1.250ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.490475 D [16188:9800] [my-test] (1.350ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.492128 D [16188:9800] [my-test] (1.296ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.497804 D [16188:9800] [my-test] (1.613ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"2f32a4d6-6037-4e63-b050-b9779672017a.png", :code=>"880bb748-3a28-41d1-8e7f-6856bc069816", :created_at=>"2024-05-31 12:52:13.495838", :updated_at=>"2024-05-31 12:52:13.495838"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.499552 D [16188:9800] [my-test] (1.389ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.509235 D [16188:9800] [my-test] (1.588ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.507032", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3MJZ-oTuO0\",\"SwQlA-YtXT\",\"Xo8EDCJwxH\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3MJZ-oTuO0\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"880bb748-3a28-41d1-8e7f-6856bc069816\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"SwQlA-YtXT\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"Xo8EDCJwxH\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"683a17cf-a149-4059-850c-4dd9d4c7ae91"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.511045 D [16188:9800] [my-test] (1.306ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.514680 D [16188:9800] [my-test] (1.288ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.516674 D [16188:9800] [my-test] (1.523ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/ad1c3367-3a7c-4b4c-9434-c60a34b6a182", :enabled=>true, :created_at=>"2024-05-31 12:52:13.513079", :updated_at=>"2024-05-31 12:52:13.513079", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.519032 D [16188:9800] [my-test] (1.623ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.520672 D [16188:9800] [my-test] (1.218ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.522234 D [16188:9800] [my-test] (1.199ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.523877 D [16188:9800] [my-test] (1.293ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.525581 D [16188:9800] [my-test] (1.349ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.530927 D [16188:9800] [my-test] (1.639ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"a47f9253-ee84-41f4-aee9-8e542c3d7250.png", :code=>"2ede2482-8216-4d17-a6c8-201b657d5267", :created_at=>"2024-05-31 12:52:13.528938", :updated_at=>"2024-05-31 12:52:13.528938"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.532603 D [16188:9800] [my-test] (1.319ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.538350 D [16188:9800] [my-test] (1.911ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.535816", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"TqAi2D2D-k\",\"heScaIN1c_\",\"UgIdIOCD5m\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"TqAi2D2D-k\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"2ede2482-8216-4d17-a6c8-201b657d5267\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"heScaIN1c_\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"UgIdIOCD5m\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"ad1c3367-3a7c-4b4c-9434-c60a34b6a182"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.539927 D [16188:9800] [my-test] (1.058ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.543529 D [16188:9800] [my-test] (1.275ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.545462 D [16188:9800] [my-test] (1.442ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/a66e08c7-3df6-40a6-a977-3b0b6923e656", :enabled=>true, :created_at=>"2024-05-31 12:52:13.541943", :updated_at=>"2024-05-31 12:52:13.541943", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.547658 D [16188:9800] [my-test] (1.456ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.549242 D [16188:9800] [my-test] (1.197ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.550861 D [16188:9800] [my-test] (1.271ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.552650 D [16188:9800] [my-test] (1.433ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.554683 D [16188:9800] [my-test] (1.595ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.560224 D [16188:9800] [my-test] (1.438ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"a9f4be37-bb9d-4be5-a9fe-3297341bfdd9.png", :code=>"6dadb23d-d967-4452-8376-75fca4dc40c5", :created_at=>"2024-05-31 12:52:13.558436", :updated_at=>"2024-05-31 12:52:13.558436"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.561848 D [16188:9800] [my-test] (1.267ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.567349 D [16188:9800] [my-test] (1.838ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.564907", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"TUGn5R0nB1\",\"DRMtYuDi9g\",\"W1XNxgvvrL\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"TUGn5R0nB1\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"6dadb23d-d967-4452-8376-75fca4dc40c5\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"DRMtYuDi9g\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"W1XNxgvvrL\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"a66e08c7-3df6-40a6-a977-3b0b6923e656"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.569607 D [16188:9800] [my-test] (1.608ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.573451 D [16188:9800] [my-test] (1.403ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.575468 D [16188:9800] [my-test] (1.556ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/bd5bf52f-d76f-49f9-b1f7-543264b97918", :enabled=>true, :created_at=>"2024-05-31 12:52:13.571715", :updated_at=>"2024-05-31 12:52:13.571715", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.577598 D [16188:9800] [my-test] (1.423ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.579244 D [16188:9800] [my-test] (1.254ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.580839 D [16188:9800] [my-test] (1.239ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.582381 D [16188:9800] [my-test] (1.204ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.583942 D [16188:9800] [my-test] (1.238ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.589503 D [16188:9800] [my-test] (1.481ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"77effba2-3699-4d69-ac55-28bebb4f25df.png", :code=>"f07b5f74-2146-4f67-98f2-9a342f96bc26", :created_at=>"2024-05-31 12:52:13.587671", :updated_at=>"2024-05-31 12:52:13.587671"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.591109 D [16188:9800] [my-test] (1.254ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.596788 D [16188:9800] [my-test] (1.810ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.594341", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"p3DqVdHptD\",\"Ec0zdkIYdN\",\"SBQAgUU4uH\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"p3DqVdHptD\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"f07b5f74-2146-4f67-98f2-9a342f96bc26\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"Ec0zdkIYdN\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"SBQAgUU4uH\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"bd5bf52f-d76f-49f9-b1f7-543264b97918"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.598554 D [16188:9800] [my-test] (1.253ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.601990 D [16188:9800] [my-test] (1.179ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.603975 D [16188:9800] [my-test] (1.517ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/4ef9bb4f-d693-4918-a4c8-37a90eb6183b", :enabled=>true, :created_at=>"2024-05-31 12:52:13.600517", :updated_at=>"2024-05-31 12:52:13.600517", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.606217 D [16188:9800] [my-test] (1.526ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.607930 D [16188:9800] [my-test] (1.302ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.609520 D [16188:9800] [my-test] (1.208ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.611077 D [16188:9800] [my-test] (1.191ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.612610 D [16188:9800] [my-test] (1.191ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.617966 D [16188:9800] [my-test] (1.519ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"32e9652a-b5ba-4bde-b0b8-474c1736f8e6.png", :code=>"103c2c23-5bc0-4a3f-bbff-b477cf02e0d8", :created_at=>"2024-05-31 12:52:13.616106", :updated_at=>"2024-05-31 12:52:13.616106"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.619529 D [16188:9800] [my-test] (1.206ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.625234 D [16188:9800] [my-test] (1.775ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.622844", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"8932_BDPZl\",\"7c7HxQxq4h\",\"tL7cvnY4TU\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"8932_BDPZl\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"103c2c23-5bc0-4a3f-bbff-b477cf02e0d8\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"7c7HxQxq4h\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"tL7cvnY4TU\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"4ef9bb4f-d693-4918-a4c8-37a90eb6183b"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.627078 D [16188:9800] [my-test] (1.328ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.630790 D [16188:9800] [my-test] (1.378ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.632742 D [16188:9800] [my-test] (1.496ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/ffddf964-4d11-459b-aee7-9cb0f33023dd", :enabled=>true, :created_at=>"2024-05-31 12:52:13.629110", :updated_at=>"2024-05-31 12:52:13.629110", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.634990 D [16188:9800] [my-test] (1.547ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.636684 D [16188:9800] [my-test] (1.295ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.638451 D [16188:9800] [my-test] (1.401ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.640251 D [16188:9800] [my-test] (1.391ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.641815 D [16188:9800] [my-test] (1.200ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.647574 D [16188:9800] [my-test] (1.652ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"ea9c5337-2ccd-4d6e-aa8d-ee08834adfe7.png", :code=>"23f77cb4-c744-4f0c-9343-c4b500551112", :created_at=>"2024-05-31 12:52:13.645574", :updated_at=>"2024-05-31 12:52:13.645574"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.649187 D [16188:9800] [my-test] (1.257ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.654745 D [16188:9800] [my-test] (1.748ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.652369", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"7vuEewOh05\",\"_bf6qE2SWp\",\"dLw0_x1JlP\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"7vuEewOh05\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"23f77cb4-c744-4f0c-9343-c4b500551112\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"_bf6qE2SWp\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"dLw0_x1JlP\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"ffddf964-4d11-459b-aee7-9cb0f33023dd"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.656520 D [16188:9800] [my-test] (1.210ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.660109 D [16188:9800] [my-test] (1.268ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.662162 D [16188:9800] [my-test] (1.576ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/712d4800-e2e3-43cf-9e86-0c82e8a06f15", :enabled=>true, :created_at=>"2024-05-31 12:52:13.658541", :updated_at=>"2024-05-31 12:52:13.658541", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.664245 D [16188:9800] [my-test] (1.381ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.665921 D [16188:9800] [my-test] (1.281ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.667441 D [16188:9800] [my-test] (1.154ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.669101 D [16188:9800] [my-test] (1.270ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.670776 D [16188:9800] [my-test] (1.279ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.675985 D [16188:9800] [my-test] (1.476ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"57bd3abc-3c1c-40e9-93d0-ee81cad04e00.png", :code=>"dc11abf9-6a71-4eac-b2de-f0a308248650", :created_at=>"2024-05-31 12:52:13.674144", :updated_at=>"2024-05-31 12:52:13.674144"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.677646 D [16188:9800] [my-test] (1.293ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.683519 D [16188:9800] [my-test] (2.048ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.680863", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"n2dspIPBSc\",\"yk7pbQQOu2\",\"0NLJ6lLq4v\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"n2dspIPBSc\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"dc11abf9-6a71-4eac-b2de-f0a308248650\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"yk7pbQQOu2\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"0NLJ6lLq4v\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"712d4800-e2e3-43cf-9e86-0c82e8a06f15"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.685587 D [16188:9800] [my-test] (1.471ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.689294 D [16188:9800] [my-test] (1.336ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.691660 D [16188:9800] [my-test] (1.882ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/6516a66d-175a-4c7b-b5b2-e302b1957d6c", :enabled=>true, :created_at=>"2024-05-31 12:52:13.687655", :updated_at=>"2024-05-31 12:52:13.687655", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.694177 D [16188:9800] [my-test] (1.773ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.695952 D [16188:9800] [my-test] (1.373ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.697841 D [16188:9800] [my-test] (1.501ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.699547 D [16188:9800] [my-test] (1.308ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.701200 D [16188:9800] [my-test] (1.310ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.718849 D [16188:9800] [my-test] (2.372ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"541dd523-8f16-4568-b478-fc5c2bc5b436.png", :code=>"d93a36af-4c5d-4332-b78d-ee2115d468aa", :created_at=>"2024-05-31 12:52:13.716047", :updated_at=>"2024-05-31 12:52:13.716047"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.721075 D [16188:9800] [my-test] (1.787ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.728163 D [16188:9800] [my-test] (1.817ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.725656", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"dAuukh5wSX\",\"Ox8xuX63Y-\",\"9yshqJv4FN\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"dAuukh5wSX\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"d93a36af-4c5d-4332-b78d-ee2115d468aa\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"Ox8xuX63Y-\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"9yshqJv4FN\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"6516a66d-175a-4c7b-b5b2-e302b1957d6c"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.730184 D [16188:9800] [my-test] (1.465ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.734633 D [16188:9800] [my-test] (1.528ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.736753 D [16188:9800] [my-test] (1.599ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/83faaec0-5140-4b81-b66d-2fa4b528daf1", :enabled=>true, :created_at=>"2024-05-31 12:52:13.732763", :updated_at=>"2024-05-31 12:52:13.732763", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.739168 D [16188:9800] [my-test] (1.636ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.740739 D [16188:9800] [my-test] (1.150ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.743002 D [16188:9800] [my-test] (1.468ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.744556 D [16188:9800] [my-test] (1.162ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.746064 D [16188:9800] [my-test] (1.188ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.751449 D [16188:9800] [my-test] (1.482ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"ad25af0f-1788-4149-ae30-0b867c245ada.png", :code=>"6d2ebb7d-f1ca-40a3-9acd-d450fca98189", :created_at=>"2024-05-31 12:52:13.749619", :updated_at=>"2024-05-31 12:52:13.749619"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.753035 D [16188:9800] [my-test] (1.234ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.758981 D [16188:9800] [my-test] (1.849ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.756505", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"6MBDujep9U\",\"6hoJjCP8AS\",\"InVLKA4eY-\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"6MBDujep9U\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"6d2ebb7d-f1ca-40a3-9acd-d450fca98189\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"6hoJjCP8AS\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"InVLKA4eY-\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"83faaec0-5140-4b81-b66d-2fa4b528daf1"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.760670 D [16188:9800] [my-test] (1.162ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.764690 D [16188:9800] [my-test] (1.186ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.766788 D [16188:9800] [my-test] (1.597ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/0f591205-0dd8-4b47-a412-50a4122e5b7f", :enabled=>true, :created_at=>"2024-05-31 12:52:13.763171", :updated_at=>"2024-05-31 12:52:13.763171", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.769527 D [16188:9800] [my-test] (1.620ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.771371 D [16188:9800] [my-test] (1.416ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.773141 D [16188:9800] [my-test] (1.338ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.774802 D [16188:9800] [my-test] (1.267ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.776324 D [16188:9800] [my-test] (1.201ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.781422 D [16188:9800] [my-test] (1.331ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"1ac26a5b-d7b5-4c2f-bc34-bd0583d0b725.png", :code=>"6c4a3884-7298-4518-8d36-b468e5ab2662", :created_at=>"2024-05-31 12:52:13.779726", :updated_at=>"2024-05-31 12:52:13.779726"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.783044 D [16188:9800] [my-test] (1.252ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.789074 D [16188:9800] [my-test] (1.704ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.786727", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"FX4uJMHy17\",\"gWM7FjLui4\",\"RjheiXgbOq\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"FX4uJMHy17\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"6c4a3884-7298-4518-8d36-b468e5ab2662\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"gWM7FjLui4\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"RjheiXgbOq\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"0f591205-0dd8-4b47-a412-50a4122e5b7f"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.790955 D [16188:9800] [my-test] (1.338ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.795198 D [16188:9800] [my-test] (1.302ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.797398 D [16188:9800] [my-test] (1.737ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/a5eadf86-8741-4851-ae0e-f479c24a113d", :enabled=>true, :created_at=>"2024-05-31 12:52:13.793572", :updated_at=>"2024-05-31 12:52:13.793572", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.800059 D [16188:9800] [my-test] (1.421ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.801788 D [16188:9800] [my-test] (1.322ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.803616 D [16188:9800] [my-test] (1.414ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.805336 D [16188:9800] [my-test] (1.334ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.807103 D [16188:9800] [my-test] (1.395ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.813464 D [16188:9800] [my-test] (1.561ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"42904580-a535-4ee1-9103-8446ca4bdc6c.png", :code=>"d4eca8a6-61ca-4c45-bf01-d7bd19a1be9e", :created_at=>"2024-05-31 12:52:13.811550", :updated_at=>"2024-05-31 12:52:13.811550"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.815172 D [16188:9800] [my-test] (1.357ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.821454 D [16188:9800] [my-test] (1.758ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.818852", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"SY0XH2yvko\",\"-37c0riRAf\",\"oFFAVTMuEp\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"SY0XH2yvko\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"d4eca8a6-61ca-4c45-bf01-d7bd19a1be9e\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"-37c0riRAf\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"oFFAVTMuEp\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"a5eadf86-8741-4851-ae0e-f479c24a113d"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.823389 D [16188:9800] [my-test] (1.408ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.827674 D [16188:9800] [my-test] (1.378ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.829695 D [16188:9800] [my-test] (1.555ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/7f9257b3-a867-4a91-9532-cf1e157404c4", :enabled=>true, :created_at=>"2024-05-31 12:52:13.825980", :updated_at=>"2024-05-31 12:52:13.825980", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.832453 D [16188:9800] [my-test] (1.597ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.834137 D [16188:9800] [my-test] (1.299ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.835927 D [16188:9800] [my-test] (1.397ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.837658 D [16188:9800] [my-test] (1.327ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.839380 D [16188:9800] [my-test] (1.386ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.844626 D [16188:9800] [my-test] (1.502ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"31d4cf39-1721-4dfa-97e5-e903f9466462.png", :code=>"d2318bc1-1aec-4bc4-8aac-5b7e3709670c", :created_at=>"2024-05-31 12:52:13.842770", :updated_at=>"2024-05-31 12:52:13.842770"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.846491 D [16188:9800] [my-test] (1.504ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.852464 D [16188:9800] [my-test] (1.663ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.850137", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"80Y03VbNz_\",\"kJofnf1LtR\",\"Oxy2u6U5iP\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"80Y03VbNz_\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"d2318bc1-1aec-4bc4-8aac-5b7e3709670c\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"kJofnf1LtR\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"Oxy2u6U5iP\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"7f9257b3-a867-4a91-9532-cf1e157404c4"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.854285 D [16188:9800] [my-test] (1.267ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.858420 D [16188:9800] [my-test] (1.376ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.860751 D [16188:9800] [my-test] (1.404ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/18dd1618-80a5-49e0-ae05-5b9c792ed27d", :enabled=>true, :created_at=>"2024-05-31 12:52:13.856742", :updated_at=>"2024-05-31 12:52:13.856742", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.863096 D [16188:9800] [my-test] (1.588ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.864966 D [16188:9800] [my-test] (1.462ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.866593 D [16188:9800] [my-test] (1.256ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.868152 D [16188:9800] [my-test] (1.237ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.869756 D [16188:9800] [my-test] (1.253ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.875742 D [16188:9800] [my-test] (1.786ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"11fa1aed-4a48-4d6c-b45e-20a321ddfeff.png", :code=>"5b746863-0cd3-40e1-bcea-844b07267a2e", :created_at=>"2024-05-31 12:52:13.873225", :updated_at=>"2024-05-31 12:52:13.873225"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.877557 D [16188:9800] [my-test] (1.456ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.883686 D [16188:9800] [my-test] (1.871ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.880760", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"8BTfK8YvdI\",\"VgX4L2F1Pe\",\"gDOfZCLdWy\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"8BTfK8YvdI\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"5b746863-0cd3-40e1-bcea-844b07267a2e\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"VgX4L2F1Pe\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"gDOfZCLdWy\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"18dd1618-80a5-49e0-ae05-5b9c792ed27d"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.885622 D [16188:9800] [my-test] (1.414ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.889744 D [16188:9800] [my-test] (1.441ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.891909 D [16188:9800] [my-test] (1.690ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/82008787-2404-4fac-a792-cb826882b380", :enabled=>true, :created_at=>"2024-05-31 12:52:13.887739", :updated_at=>"2024-05-31 12:52:13.887739", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.893971 D [16188:9800] [my-test] (1.355ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.895711 D [16188:9800] [my-test] (1.345ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.897459 D [16188:9800] [my-test] (1.391ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.899108 D [16188:9800] [my-test] (1.289ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.900780 D [16188:9800] [my-test] (1.296ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.906357 D [16188:9800] [my-test] (1.587ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"62afb516-15a6-402c-ae17-d596f7e77079.png", :code=>"f0fc0997-0ba2-450f-b200-8ab9cb72abe1", :created_at=>"2024-05-31 12:52:13.904416", :updated_at=>"2024-05-31 12:52:13.904416"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.908117 D [16188:9800] [my-test] (1.374ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.914789 D [16188:9800] [my-test] (1.884ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.912156", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"ItEA1rzS02\",\"bM5hCzMkZH\",\"_xYbJfnWGt\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"ItEA1rzS02\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"f0fc0997-0ba2-450f-b200-8ab9cb72abe1\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"bM5hCzMkZH\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"_xYbJfnWGt\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"82008787-2404-4fac-a792-cb826882b380"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.916643 D [16188:9800] [my-test] (1.346ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.920803 D [16188:9800] [my-test] (1.778ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.923097 D [16188:9800] [my-test] (1.684ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/6e8e24e0-3193-4475-adc1-64f12fa5979f", :enabled=>true, :created_at=>"2024-05-31 12:52:13.918683", :updated_at=>"2024-05-31 12:52:13.918683", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.926181 D [16188:9800] [my-test] (2.358ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.927981 D [16188:9800] [my-test] (1.395ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.929501 D [16188:9800] [my-test] (1.157ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.931153 D [16188:9800] [my-test] (1.308ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.932784 D [16188:9800] [my-test] (1.245ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.938221 D [16188:9800] [my-test] (1.729ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"e621d9a5-e7ca-4600-aae6-284ba72aa953.png", :code=>"35fb2dc8-2209-44cb-9a7c-7c1ef01fc234", :created_at=>"2024-05-31 12:52:13.936147", :updated_at=>"2024-05-31 12:52:13.936147"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.939994 D [16188:9800] [my-test] (1.424ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.945537 D [16188:9800] [my-test] (1.616ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.943303", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"zcp6JyfDdF\",\"UNezFd_wGR\",\"OvPvPM-fZK\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"zcp6JyfDdF\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"35fb2dc8-2209-44cb-9a7c-7c1ef01fc234\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"UNezFd_wGR\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"OvPvPM-fZK\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"6e8e24e0-3193-4475-adc1-64f12fa5979f"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.947253 D [16188:9800] [my-test] (1.201ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.951174 D [16188:9800] [my-test] (1.342ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.953082 D [16188:9800] [my-test] (1.449ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/592b61a7-543d-42d8-b480-42c5ba923603", :enabled=>true, :created_at=>"2024-05-31 12:52:13.949515", :updated_at=>"2024-05-31 12:52:13.949515", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.955456 D [16188:9800] [my-test] (1.676ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.957269 D [16188:9800] [my-test] (1.435ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.959157 D [16188:9800] [my-test] (1.487ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.960830 D [16188:9800] [my-test] (1.289ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.962434 D [16188:9800] [my-test] (1.248ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.967578 D [16188:9800] [my-test] (1.454ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"d677331e-a908-405a-a9cd-c0b869910a16.png", :code=>"d2f2f02a-360b-4498-8e36-1b29c01ea9bc", :created_at=>"2024-05-31 12:52:13.965779", :updated_at=>"2024-05-31 12:52:13.965779"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.969385 D [16188:9800] [my-test] (1.445ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:13.975354 D [16188:9800] [my-test] (2.000ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:13.972715", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"HVYmIHOXPl\",\"cH5NblP7bQ\",\"R1ZelfSgZy\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"HVYmIHOXPl\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"d2f2f02a-360b-4498-8e36-1b29c01ea9bc\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"cH5NblP7bQ\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"R1ZelfSgZy\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"592b61a7-543d-42d8-b480-42c5ba923603"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:13.977118 D [16188:9800] [my-test] (1.243ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:13.980917 D [16188:9800] [my-test] (1.284ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.982985 D [16188:9800] [my-test] (1.590ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/5b477217-3b72-4fd6-a6ed-3ad479c3916b", :enabled=>true, :created_at=>"2024-05-31 12:52:13.979323", :updated_at=>"2024-05-31 12:52:13.979323", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:13.985150 D [16188:9800] [my-test] (1.422ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:13.986888 D [16188:9800] [my-test] (1.324ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:13.988505 D [16188:9800] [my-test] (1.227ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.990100 D [16188:9800] [my-test] (1.263ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.991602 D [16188:9800] [my-test] (1.187ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:13.997145 D [16188:9800] [my-test] (1.648ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"c81c4123-6f66-4150-b84d-af9b36ed67b1.png", :code=>"59bbbb2a-eaf3-4a65-a07a-e3a6d67e6b0f", :created_at=>"2024-05-31 12:52:13.995139", :updated_at=>"2024-05-31 12:52:13.995139"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:13.999095 D [16188:9800] [my-test] (1.574ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.004902 D [16188:9800] [my-test] (1.705ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.002500", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"nfylD9DbhI\",\"ZMe7j-x94F\",\"itgKzhjvWO\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"nfylD9DbhI\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"59bbbb2a-eaf3-4a65-a07a-e3a6d67e6b0f\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"ZMe7j-x94F\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"itgKzhjvWO\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"5b477217-3b72-4fd6-a6ed-3ad479c3916b"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.006667 D [16188:9800] [my-test] (1.260ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.010879 D [16188:9800] [my-test] (1.521ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.013064 D [16188:9800] [my-test] (1.720ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/54b2e284-f4a1-48c1-9de6-27b31adf1be6", :enabled=>true, :created_at=>"2024-05-31 12:52:14.009018", :updated_at=>"2024-05-31 12:52:14.009018", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.015344 D [16188:9800] [my-test] (1.527ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.017038 D [16188:9800] [my-test] (1.311ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.018736 D [16188:9800] [my-test] (1.312ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.020313 D [16188:9800] [my-test] (1.245ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.022008 D [16188:9800] [my-test] (1.371ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.027546 D [16188:9800] [my-test] (1.679ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"30f95ef1-7987-4519-a75b-0ff7eed1400d.png", :code=>"1069b651-1592-4c86-b9d8-cd97257b5793", :created_at=>"2024-05-31 12:52:14.025504", :updated_at=>"2024-05-31 12:52:14.025504"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.029292 D [16188:9800] [my-test] (1.365ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.034969 D [16188:9800] [my-test] (1.671ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.032462", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"0mBUvzNFr0\",\"Ob2VMbG54s\",\"d9vYfTYYZy\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"0mBUvzNFr0\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"1069b651-1592-4c86-b9d8-cd97257b5793\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"Ob2VMbG54s\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"d9vYfTYYZy\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"54b2e284-f4a1-48c1-9de6-27b31adf1be6"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.036507 D [16188:9800] [my-test] (1.029ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.040058 D [16188:9800] [my-test] (1.235ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.042171 D [16188:9800] [my-test] (1.502ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/351cd6a6-e573-4701-80ec-e11195792802", :enabled=>true, :created_at=>"2024-05-31 12:52:14.038531", :updated_at=>"2024-05-31 12:52:14.038531", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.044487 D [16188:9800] [my-test] (1.556ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.046287 D [16188:9800] [my-test] (1.400ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.047939 D [16188:9800] [my-test] (1.252ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.050440 D [16188:9800] [my-test] (2.165ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.052130 D [16188:9800] [my-test] (1.321ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.057990 D [16188:9800] [my-test] (1.438ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"75f5ede0-cebb-432d-b88a-90e1b27f4072.png", :code=>"9b2ac19e-7d5f-44e9-85fc-9f456ed4881c", :created_at=>"2024-05-31 12:52:14.056205", :updated_at=>"2024-05-31 12:52:14.056205"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.059601 D [16188:9800] [my-test] (1.264ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.068145 D [16188:9800] [my-test] (1.890ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.065656", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"wWNG28VUWu\",\"SmmS4lmdUT\",\"H9dn95fAXZ\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"wWNG28VUWu\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"9b2ac19e-7d5f-44e9-85fc-9f456ed4881c\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"SmmS4lmdUT\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"H9dn95fAXZ\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"351cd6a6-e573-4701-80ec-e11195792802"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.070039 D [16188:9800] [my-test] (1.381ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.074087 D [16188:9800] [my-test] (1.718ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.076485 D [16188:9800] [my-test] (1.829ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/a94aebba-49c9-45a2-a78b-9c93ec367029", :enabled=>true, :created_at=>"2024-05-31 12:52:14.072027", :updated_at=>"2024-05-31 12:52:14.072027", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.081208 D [16188:9800] [my-test] (3.296ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.083102 D [16188:9800] [my-test] (1.478ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.084775 D [16188:9800] [my-test] (1.281ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.086323 D [16188:9800] [my-test] (1.189ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.087927 D [16188:9800] [my-test] (1.280ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.093309 D [16188:9800] [my-test] (1.662ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"8a8d1165-204d-4e58-a309-8d25b14a6fd8.png", :code=>"b5e0877f-4c6f-4707-bb05-d1336f044fa9", :created_at=>"2024-05-31 12:52:14.091291", :updated_at=>"2024-05-31 12:52:14.091291"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.098595 D [16188:9800] [my-test] (4.901ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.104409 D [16188:9800] [my-test] (1.734ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.102045", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"nSSo_L8RL5\",\"ujGFz40Rrc\",\"oU5PNJEwm5\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"nSSo_L8RL5\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"b5e0877f-4c6f-4707-bb05-d1336f044fa9\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"ujGFz40Rrc\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"oU5PNJEwm5\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"a94aebba-49c9-45a2-a78b-9c93ec367029"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.106398 D [16188:9800] [my-test] (1.466ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.109990 D [16188:9800] [my-test] (1.286ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.112062 D [16188:9800] [my-test] (1.587ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/ab922b28-ccf2-45f0-95e5-8c49ed865037", :enabled=>true, :created_at=>"2024-05-31 12:52:14.108401", :updated_at=>"2024-05-31 12:52:14.108401", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.114374 D [16188:9800] [my-test] (1.589ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.116171 D [16188:9800] [my-test] (1.393ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.117812 D [16188:9800] [my-test] (1.295ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.119657 D [16188:9800] [my-test] (1.484ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.121613 D [16188:9800] [my-test] (1.589ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.126978 D [16188:9800] [my-test] (1.658ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"8835029d-e16d-4e4c-9a7b-ed6020a04670.png", :code=>"8ceba1c1-487f-4304-a4a4-5fe16aa782ae", :created_at=>"2024-05-31 12:52:14.124977", :updated_at=>"2024-05-31 12:52:14.124977"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.128677 D [16188:9800] [my-test] (1.334ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.134225 D [16188:9800] [my-test] (1.861ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.131740", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"0iIivLEtRZ\",\"CxBFEFabkL\",\"IWdm2XNHrz\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"0iIivLEtRZ\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"8ceba1c1-487f-4304-a4a4-5fe16aa782ae\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"CxBFEFabkL\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"IWdm2XNHrz\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"ab922b28-ccf2-45f0-95e5-8c49ed865037"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.136042 D [16188:9800] [my-test] (1.307ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.139615 D [16188:9800] [my-test] (1.289ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.141649 D [16188:9800] [my-test] (1.577ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/c08e094a-ff85-44fd-9180-7d4696ac2068", :enabled=>true, :created_at=>"2024-05-31 12:52:14.138028", :updated_at=>"2024-05-31 12:52:14.138028", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.143667 D [16188:9800] [my-test] (1.329ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.145296 D [16188:9800] [my-test] (1.231ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.147049 D [16188:9800] [my-test] (1.398ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.148716 D [16188:9800] [my-test] (1.327ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.150410 D [16188:9800] [my-test] (1.368ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.155713 D [16188:9800] [my-test] (1.585ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"028faa6b-46f9-4959-8ace-b8c89358195e.png", :code=>"0295bb37-0179-4324-ae05-2a5f71ec4c7e", :created_at=>"2024-05-31 12:52:14.153770", :updated_at=>"2024-05-31 12:52:14.153770"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.157313 D [16188:9800] [my-test] (1.237ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.162911 D [16188:9800] [my-test] (1.760ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.160542", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"kb_21Q4wkB\",\"YGukBw2740\",\"nOS4iMYhYJ\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"kb_21Q4wkB\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"0295bb37-0179-4324-ae05-2a5f71ec4c7e\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"YGukBw2740\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"nOS4iMYhYJ\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"c08e094a-ff85-44fd-9180-7d4696ac2068"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.164654 D [16188:9800] [my-test] (1.224ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.168397 D [16188:9800] [my-test] (1.417ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.170414 D [16188:9800] [my-test] (1.543ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/e3b14dfa-ff5b-4428-a217-ede078d89190", :enabled=>true, :created_at=>"2024-05-31 12:52:14.166684", :updated_at=>"2024-05-31 12:52:14.166684", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.173087 D [16188:9800] [my-test] (1.945ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.175126 D [16188:9800] [my-test] (1.613ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.176817 D [16188:9800] [my-test] (1.266ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.178428 D [16188:9800] [my-test] (1.250ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.179921 D [16188:9800] [my-test] (1.174ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.185225 D [16188:9800] [my-test] (1.627ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"d381b7c6-7379-4f27-b95d-6bdf840eb45a.png", :code=>"d235100d-4d78-439d-95f2-2a70724063e4", :created_at=>"2024-05-31 12:52:14.183243", :updated_at=>"2024-05-31 12:52:14.183243"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.187059 D [16188:9800] [my-test] (1.482ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.192723 D [16188:9800] [my-test] (1.755ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.190334", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"aZtz2N1FcA\",\"WdC_Ak0YoZ\",\"JuI3KPFyOo\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"aZtz2N1FcA\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"d235100d-4d78-439d-95f2-2a70724063e4\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"WdC_Ak0YoZ\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"JuI3KPFyOo\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"e3b14dfa-ff5b-4428-a217-ede078d89190"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.194420 D [16188:9800] [my-test] (1.184ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.197935 D [16188:9800] [my-test] (1.167ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.199891 D [16188:9800] [my-test] (1.502ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/990a90e9-3548-4852-a90e-b2acbd9f1da7", :enabled=>true, :created_at=>"2024-05-31 12:52:14.196465", :updated_at=>"2024-05-31 12:52:14.196465", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.202009 D [16188:9800] [my-test] (1.396ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.203582 D [16188:9800] [my-test] (1.173ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.205112 D [16188:9800] [my-test] (1.182ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.206712 D [16188:9800] [my-test] (1.275ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.208352 D [16188:9800] [my-test] (1.297ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.213632 D [16188:9800] [my-test] (1.489ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"51af3765-010e-47aa-a4da-6697a8710760.png", :code=>"5c26f7f9-e6d2-4898-b8de-c0d2e6e4f98b", :created_at=>"2024-05-31 12:52:14.211783", :updated_at=>"2024-05-31 12:52:14.211783"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.215289 D [16188:9800] [my-test] (1.308ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.220794 D [16188:9800] [my-test] (1.764ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.218428", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"N2sKpe3D7f\",\"lzNZSe4lDO\",\"QuzmjD6lgc\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"N2sKpe3D7f\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"5c26f7f9-e6d2-4898-b8de-c0d2e6e4f98b\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"lzNZSe4lDO\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"QuzmjD6lgc\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"990a90e9-3548-4852-a90e-b2acbd9f1da7"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.222955 D [16188:9800] [my-test] (1.621ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.226717 D [16188:9800] [my-test] (1.348ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.228751 D [16188:9800] [my-test] (1.562ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/372837ec-1833-4af0-992c-662d82c267ba", :enabled=>true, :created_at=>"2024-05-31 12:52:14.225041", :updated_at=>"2024-05-31 12:52:14.225041", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.230960 D [16188:9800] [my-test] (1.481ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.232548 D [16188:9800] [my-test] (1.197ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.234102 D [16188:9800] [my-test] (1.188ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.235691 D [16188:9800] [my-test] (1.227ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.237156 D [16188:9800] [my-test] (1.109ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.242943 D [16188:9800] [my-test] (1.661ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"388d202c-9f5c-4364-bd3c-9b1d8a789e8c.png", :code=>"cae57464-0fcc-4517-b816-fdc471ac15cf", :created_at=>"2024-05-31 12:52:14.240920", :updated_at=>"2024-05-31 12:52:14.240920"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.244517 D [16188:9800] [my-test] (1.184ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.250376 D [16188:9800] [my-test] (1.773ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.247870", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"cqQ_7q-j-s\",\"R7iz--1uFP\",\"loCuotjBqp\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"cqQ_7q-j-s\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"cae57464-0fcc-4517-b816-fdc471ac15cf\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"R7iz--1uFP\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"loCuotjBqp\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"372837ec-1833-4af0-992c-662d82c267ba"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.252187 D [16188:9800] [my-test] (1.294ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.256001 D [16188:9800] [my-test] (1.397ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.257971 D [16188:9800] [my-test] (1.476ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/2c97f715-e2dd-4c7e-9334-3d26d01081a9", :enabled=>true, :created_at=>"2024-05-31 12:52:14.254291", :updated_at=>"2024-05-31 12:52:14.254291", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.260143 D [16188:9800] [my-test] (1.426ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.261779 D [16188:9800] [my-test] (1.233ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.263376 D [16188:9800] [my-test] (1.196ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.264962 D [16188:9800] [my-test] (1.194ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.266456 D [16188:9800] [my-test] (1.144ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.279606 D [16188:9800] [my-test] (1.744ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"c9181c5b-8e72-4bb1-a5c9-3760aa44cdee.png", :code=>"cdfd678c-e0f3-4e67-b89f-279adb7f56f3", :created_at=>"2024-05-31 12:52:14.277466", :updated_at=>"2024-05-31 12:52:14.277466"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.281362 D [16188:9800] [my-test] (1.383ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.288364 D [16188:9800] [my-test] (2.068ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.285550", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"IXA52lY-aG\",\"vKOe24GO83\",\"m_up--ieWk\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"IXA52lY-aG\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"cdfd678c-e0f3-4e67-b89f-279adb7f56f3\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"vKOe24GO83\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"m_up--ieWk\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"2c97f715-e2dd-4c7e-9334-3d26d01081a9"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.290396 D [16188:9800] [my-test] (1.485ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.294715 D [16188:9800] [my-test] (1.536ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.296794 D [16188:9800] [my-test] (1.573ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/2c7bc21f-24c9-4989-bc60-39704b9b53db", :enabled=>true, :created_at=>"2024-05-31 12:52:14.292880", :updated_at=>"2024-05-31 12:52:14.292880", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.299271 D [16188:9800] [my-test] (1.729ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.301690 D [16188:9800] [my-test] (1.405ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.303354 D [16188:9800] [my-test] (1.267ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.305027 D [16188:9800] [my-test] (1.336ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.306702 D [16188:9800] [my-test] (1.307ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.311901 D [16188:9800] [my-test] (1.448ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"aee461fd-b6c7-4058-82d4-b61825f69c92.png", :code=>"f639d8ce-0ae0-4772-9748-9f40f21e63fe", :created_at=>"2024-05-31 12:52:14.310113", :updated_at=>"2024-05-31 12:52:14.310113"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.313549 D [16188:9800] [my-test] (1.276ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.319697 D [16188:9800] [my-test] (1.657ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.317382", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"2Z2teckLxb\",\"d4r0MDQRjD\",\"wii012HLd8\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"2Z2teckLxb\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"f639d8ce-0ae0-4772-9748-9f40f21e63fe\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"d4r0MDQRjD\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"wii012HLd8\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"2c7bc21f-24c9-4989-bc60-39704b9b53db"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.321497 D [16188:9800] [my-test] (1.284ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.325896 D [16188:9800] [my-test] (1.313ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.328006 D [16188:9800] [my-test] (1.602ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/8693df51-aa33-4af8-bab3-2b84b99dae0c", :enabled=>true, :created_at=>"2024-05-31 12:52:14.324243", :updated_at=>"2024-05-31 12:52:14.324243", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.330922 D [16188:9800] [my-test] (1.592ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.332538 D [16188:9800] [my-test] (1.212ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.334148 D [16188:9800] [my-test] (1.235ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.335672 D [16188:9800] [my-test] (1.199ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.337158 D [16188:9800] [my-test] (1.174ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.342327 D [16188:9800] [my-test] (1.411ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"8d8877b4-ec32-44b5-93b3-6cc4b4ea34f2.png", :code=>"11108bb0-2cb6-4f93-b59f-d46ce90b4c44", :created_at=>"2024-05-31 12:52:14.340566", :updated_at=>"2024-05-31 12:52:14.340566"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.343903 D [16188:9800] [my-test] (1.227ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.350007 D [16188:9800] [my-test] (1.712ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.347671", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"4YmVtC6eu-\",\"sy-ZXbB_N5\",\"oAly8DQCum\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"4YmVtC6eu-\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"11108bb0-2cb6-4f93-b59f-d46ce90b4c44\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"sy-ZXbB_N5\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"oAly8DQCum\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"8693df51-aa33-4af8-bab3-2b84b99dae0c"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.351788 D [16188:9800] [my-test] (1.263ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.356015 D [16188:9800] [my-test] (1.371ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.357974 D [16188:9800] [my-test] (1.480ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/a1b0db56-d70b-406c-b8d4-71a143d3ab16", :enabled=>true, :created_at=>"2024-05-31 12:52:14.354345", :updated_at=>"2024-05-31 12:52:14.354345", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.360749 D [16188:9800] [my-test] (1.433ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.362470 D [16188:9800] [my-test] (1.321ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.364202 D [16188:9800] [my-test] (1.351ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.365829 D [16188:9800] [my-test] (1.251ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.367405 D [16188:9800] [my-test] (1.236ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.372628 D [16188:9800] [my-test] (1.491ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"900caa2b-fd4a-4e9f-b3f7-7d8f06976375.png", :code=>"ccc9a904-c398-4f19-b6a4-1bbb4caa7d4e", :created_at=>"2024-05-31 12:52:14.370784", :updated_at=>"2024-05-31 12:52:14.370784"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.374253 D [16188:9800] [my-test] (1.265ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.380355 D [16188:9800] [my-test] (1.680ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.378031", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"4S3EWA3WIP\",\"nfhEgtTjrn\",\"fcZLQOMmev\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"4S3EWA3WIP\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"ccc9a904-c398-4f19-b6a4-1bbb4caa7d4e\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"nfhEgtTjrn\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"fcZLQOMmev\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"a1b0db56-d70b-406c-b8d4-71a143d3ab16"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.382103 D [16188:9800] [my-test] (1.211ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.386260 D [16188:9800] [my-test] (1.277ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.388922 D [16188:9800] [my-test] (1.601ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/c64f6618-68fd-4ffb-a2d4-cd27b7e58dcc", :enabled=>true, :created_at=>"2024-05-31 12:52:14.384678", :updated_at=>"2024-05-31 12:52:14.384678", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.391087 D [16188:9800] [my-test] (1.443ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.392740 D [16188:9800] [my-test] (1.257ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.394326 D [16188:9800] [my-test] (1.218ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.395873 D [16188:9800] [my-test] (1.220ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.397458 D [16188:9800] [my-test] (1.258ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.402976 D [16188:9800] [my-test] (1.726ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"9bb460c0-21f2-44b5-89e6-b1d5143265a7.png", :code=>"f37e5b89-7fe9-40a7-816a-7778a2760b0b", :created_at=>"2024-05-31 12:52:14.400903", :updated_at=>"2024-05-31 12:52:14.400903"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.404634 D [16188:9800] [my-test] (1.299ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.410988 D [16188:9800] [my-test] (1.649ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.408293", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"XYebMtR_g7\",\"boUukqf9wl\",\"U4bsnFwWu3\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"XYebMtR_g7\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"f37e5b89-7fe9-40a7-816a-7778a2760b0b\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"boUukqf9wl\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"U4bsnFwWu3\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"c64f6618-68fd-4ffb-a2d4-cd27b7e58dcc"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.412886 D [16188:9800] [my-test] (1.380ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.417028 D [16188:9800] [my-test] (1.297ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.418971 D [16188:9800] [my-test] (1.444ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/26bebaef-1f16-4f2c-b275-8fcbfc40a7c7", :enabled=>true, :created_at=>"2024-05-31 12:52:14.415413", :updated_at=>"2024-05-31 12:52:14.415413", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.421261 D [16188:9800] [my-test] (1.538ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.423292 D [16188:9800] [my-test] (1.611ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.425250 D [16188:9800] [my-test] (1.389ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.426868 D [16188:9800] [my-test] (1.214ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.428369 D [16188:9800] [my-test] (1.171ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.434340 D [16188:9800] [my-test] (1.745ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"a2f8a09a-471a-40ca-b809-030c6cb994e3.png", :code=>"3dad5cf7-6efb-4b38-badb-1225012ce617", :created_at=>"2024-05-31 12:52:14.432255", :updated_at=>"2024-05-31 12:52:14.432255"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.435831 D [16188:9800] [my-test] (1.140ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.441766 D [16188:9800] [my-test] (1.566ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.439546", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"Rr0Gyzf_M1\",\"WH8bi3wYGQ\",\"9Kxz4BHVus\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"Rr0Gyzf_M1\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"3dad5cf7-6efb-4b38-badb-1225012ce617\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"WH8bi3wYGQ\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"9Kxz4BHVus\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"26bebaef-1f16-4f2c-b275-8fcbfc40a7c7"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.443498 D [16188:9800] [my-test] (1.209ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.447374 D [16188:9800] [my-test] (1.133ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.449101 D [16188:9800] [my-test] (1.287ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/314dc01f-48f2-40b8-a973-1716a2afa7f9", :enabled=>true, :created_at=>"2024-05-31 12:52:14.445939", :updated_at=>"2024-05-31 12:52:14.445939", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.451324 D [16188:9800] [my-test] (1.532ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.453023 D [16188:9800] [my-test] (1.301ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.454824 D [16188:9800] [my-test] (1.421ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.456386 D [16188:9800] [my-test] (1.188ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.457867 D [16188:9800] [my-test] (1.148ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.463576 D [16188:9800] [my-test] (1.539ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"bb7f6e8d-51e1-48f4-a01c-3f47bb602990.png", :code=>"eb72cdb4-c714-448c-9531-e4f8c0c8d9b1", :created_at=>"2024-05-31 12:52:14.461693", :updated_at=>"2024-05-31 12:52:14.461693"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.465225 D [16188:9800] [my-test] (1.284ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.470992 D [16188:9800] [my-test] (1.752ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.468623", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"kpeqHba3E_\",\"nH1aBru2tk\",\"DgtFnMh2zd\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"kpeqHba3E_\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"eb72cdb4-c714-448c-9531-e4f8c0c8d9b1\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"nH1aBru2tk\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"DgtFnMh2zd\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"314dc01f-48f2-40b8-a973-1716a2afa7f9"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.472792 D [16188:9800] [my-test] (1.271ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.477132 D [16188:9800] [my-test] (1.800ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.479379 D [16188:9800] [my-test] (1.760ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/f8f156d0-5e8e-45da-b836-7ffadcff7a1b", :enabled=>true, :created_at=>"2024-05-31 12:52:14.474991", :updated_at=>"2024-05-31 12:52:14.474991", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.481737 D [16188:9800] [my-test] (1.629ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.483584 D [16188:9800] [my-test] (1.432ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.485317 D [16188:9800] [my-test] (1.317ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.486985 D [16188:9800] [my-test] (1.314ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.488568 D [16188:9800] [my-test] (1.244ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.493976 D [16188:9800] [my-test] (1.720ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"8ff5e614-f660-47bf-b702-6a7a665057e5.png", :code=>"ea4451b6-17a0-4c92-b51f-58e330309960", :created_at=>"2024-05-31 12:52:14.491914", :updated_at=>"2024-05-31 12:52:14.491914"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.495924 D [16188:9800] [my-test] (1.355ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.501487 D [16188:9800] [my-test] (1.634ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.499214", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"7rIRBDd2_n\",\"0OK0ZHRXqy\",\"jc-GsR_j-3\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"7rIRBDd2_n\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"ea4451b6-17a0-4c92-b51f-58e330309960\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"0OK0ZHRXqy\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"jc-GsR_j-3\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"f8f156d0-5e8e-45da-b836-7ffadcff7a1b"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.503228 D [16188:9800] [my-test] (1.216ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.506974 D [16188:9800] [my-test] (1.217ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.508882 D [16188:9800] [my-test] (1.438ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/57802259-da8e-463e-a0ba-f0795da82cab", :enabled=>true, :created_at=>"2024-05-31 12:52:14.505453", :updated_at=>"2024-05-31 12:52:14.505453", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.511147 D [16188:9800] [my-test] (1.541ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.513028 D [16188:9800] [my-test] (1.413ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.514771 D [16188:9800] [my-test] (1.314ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.516333 D [16188:9800] [my-test] (1.225ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.517905 D [16188:9800] [my-test] (1.201ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.523376 D [16188:9800] [my-test] (1.554ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"d79d86a8-ce25-4a88-a5c3-1750da35c150.png", :code=>"5f60b8c7-8697-4522-b306-e36bc318524a", :created_at=>"2024-05-31 12:52:14.521452", :updated_at=>"2024-05-31 12:52:14.521452"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.525009 D [16188:9800] [my-test] (1.239ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.530848 D [16188:9800] [my-test] (1.760ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.528299", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"ny7UcZTJnQ\",\"k7SAZiSxWW\",\"t0eCooiNeN\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"ny7UcZTJnQ\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"5f60b8c7-8697-4522-b306-e36bc318524a\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"k7SAZiSxWW\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"t0eCooiNeN\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"57802259-da8e-463e-a0ba-f0795da82cab"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.532656 D [16188:9800] [my-test] (1.294ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.536188 D [16188:9800] [my-test] (1.169ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.538481 D [16188:9800] [my-test] (1.602ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/2eb2a684-4614-4291-8cd6-346402899611", :enabled=>true, :created_at=>"2024-05-31 12:52:14.534715", :updated_at=>"2024-05-31 12:52:14.534715", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.540566 D [16188:9800] [my-test] (1.363ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.542235 D [16188:9800] [my-test] (1.260ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.543758 D [16188:9800] [my-test] (1.128ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.545325 D [16188:9800] [my-test] (1.225ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.546887 D [16188:9800] [my-test] (1.216ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.552210 D [16188:9800] [my-test] (1.521ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"7a2cf739-878b-4785-b2e9-284676aa6a4a.png", :code=>"0e6f6a1d-46d8-4ff4-b66a-1fa131c8d0dc", :created_at=>"2024-05-31 12:52:14.550345", :updated_at=>"2024-05-31 12:52:14.550345"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.553930 D [16188:9800] [my-test] (1.346ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.559448 D [16188:9800] [my-test] (1.617ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.557225", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"kL3BQ_rMVm\",\"ot2cjKB06J\",\"qZ5AP0KSDR\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"kL3BQ_rMVm\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"0e6f6a1d-46d8-4ff4-b66a-1fa131c8d0dc\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"ot2cjKB06J\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"qZ5AP0KSDR\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"2eb2a684-4614-4291-8cd6-346402899611"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.561228 D [16188:9800] [my-test] (1.270ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.564992 D [16188:9800] [my-test] (1.238ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.567071 D [16188:9800] [my-test] (1.619ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/538bd013-1c35-4cda-baa7-ac777f2ba649", :enabled=>true, :created_at=>"2024-05-31 12:52:14.563437", :updated_at=>"2024-05-31 12:52:14.563437", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.569148 D [16188:9800] [my-test] (1.374ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.570964 D [16188:9800] [my-test] (1.425ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.573097 D [16188:9800] [my-test] (1.699ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.575330 D [16188:9800] [my-test] (1.661ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.577229 D [16188:9800] [my-test] (1.496ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.582748 D [16188:9800] [my-test] (1.745ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"19b0430b-8d9d-4407-91e6-d7a49cd379e5.png", :code=>"db391376-60e8-4fbe-a9a2-f6095e94de67", :created_at=>"2024-05-31 12:52:14.580647", :updated_at=>"2024-05-31 12:52:14.580647"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.584503 D [16188:9800] [my-test] (1.388ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.590468 D [16188:9800] [my-test] (1.997ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.587832", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"fQeHyv4eta\",\"ya6m3SUVIv\",\"ksM9dcZ8gp\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"fQeHyv4eta\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"db391376-60e8-4fbe-a9a2-f6095e94de67\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"ya6m3SUVIv\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"ksM9dcZ8gp\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"538bd013-1c35-4cda-baa7-ac777f2ba649"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.592465 D [16188:9800] [my-test] (1.482ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.596338 D [16188:9800] [my-test] (1.403ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.598666 D [16188:9800] [my-test] (1.853ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/cfb3f39e-5580-400d-866f-7198b5c77d59", :enabled=>true, :created_at=>"2024-05-31 12:52:14.594634", :updated_at=>"2024-05-31 12:52:14.594634", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.600780 D [16188:9800] [my-test] (1.381ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.602475 D [16188:9800] [my-test] (1.305ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.604014 D [16188:9800] [my-test] (1.176ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.605630 D [16188:9800] [my-test] (1.255ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.607233 D [16188:9800] [my-test] (1.246ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.612514 D [16188:9800] [my-test] (1.571ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"421ec5bf-7770-49b0-930c-710e4454a7b1.png", :code=>"347a591a-45a0-4534-82a0-9984c277e931", :created_at=>"2024-05-31 12:52:14.610602", :updated_at=>"2024-05-31 12:52:14.610602"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.614157 D [16188:9800] [my-test] (1.275ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.622348 D [16188:9800] [my-test] (1.873ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.619869", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"85T8KkNyQJ\",\"dIuInguuEN\",\"nTAgb_UM5f\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"85T8KkNyQJ\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"347a591a-45a0-4534-82a0-9984c277e931\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"dIuInguuEN\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"nTAgb_UM5f\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"cfb3f39e-5580-400d-866f-7198b5c77d59"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.624513 D [16188:9800] [my-test] (1.631ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.628616 D [16188:9800] [my-test] (1.732ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.631398 D [16188:9800] [my-test] (2.272ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/9429e2c0-5edf-4eb3-bf16-dbbb19c98abf", :enabled=>true, :created_at=>"2024-05-31 12:52:14.626589", :updated_at=>"2024-05-31 12:52:14.626589", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.633818 D [16188:9800] [my-test] (1.693ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.635718 D [16188:9800] [my-test] (1.503ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.637455 D [16188:9800] [my-test] (1.372ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.639104 D [16188:9800] [my-test] (1.311ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.640684 D [16188:9800] [my-test] (1.213ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.645914 D [16188:9800] [my-test] (1.512ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"5334da7e-664b-4ed5-8aa8-4b5f7ff99b33.png", :code=>"b2402f30-1e04-4669-86e8-a7121b09f56c", :created_at=>"2024-05-31 12:52:14.644054", :updated_at=>"2024-05-31 12:52:14.644054"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.647763 D [16188:9800] [my-test] (1.477ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.653433 D [16188:9800] [my-test] (1.827ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.650982", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"yNilnIUQVR\",\"QxWh1xz6FF\",\"-uf8f09wX8\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"yNilnIUQVR\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"b2402f30-1e04-4669-86e8-a7121b09f56c\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"QxWh1xz6FF\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"-uf8f09wX8\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"9429e2c0-5edf-4eb3-bf16-dbbb19c98abf"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.655272 D [16188:9800] [my-test] (1.320ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.658941 D [16188:9800] [my-test] (1.339ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.660798 D [16188:9800] [my-test] (1.400ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/485e514a-69e3-441b-917c-8f322526e771", :enabled=>true, :created_at=>"2024-05-31 12:52:14.657300", :updated_at=>"2024-05-31 12:52:14.657300", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.663005 D [16188:9800] [my-test] (1.522ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.664913 D [16188:9800] [my-test] (1.493ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.666645 D [16188:9800] [my-test] (1.310ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>48, :cached=>nil}
2024-05-31 14:52:14.668233 D [16188:9800] [my-test] (1.242ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.669738 D [16188:9800] [my-test] (1.174ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.675119 D [16188:9800] [my-test] (1.663ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"6383022e-bc64-47c0-81d1-3bd365226bbe.png", :code=>"5bffff23-abbd-4b96-a968-512c79cf69f8", :created_at=>"2024-05-31 12:52:14.673099", :updated_at=>"2024-05-31 12:52:14.673099"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.676827 D [16188:9800] [my-test] (1.353ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.682668 D [16188:9800] [my-test] (1.922ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.680083", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"6rzk7-UZ6W\",\"YFSNDS0lca\",\"wMRVrTn8iz\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"6rzk7-UZ6W\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"5bffff23-abbd-4b96-a968-512c79cf69f8\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"YFSNDS0lca\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"wMRVrTn8iz\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"485e514a-69e3-441b-917c-8f322526e771"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.684393 D [16188:9800] [my-test] (1.211ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.688002 D [16188:9800] [my-test] (1.227ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.690087 D [16188:9800] [my-test] (1.614ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/156e9e24-533d-4fac-b83d-22e9287a43c5", :enabled=>true, :created_at=>"2024-05-31 12:52:14.686482", :updated_at=>"2024-05-31 12:52:14.686482", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.692311 D [16188:9800] [my-test] (1.456ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.694002 D [16188:9800] [my-test] (1.303ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.695616 D [16188:9800] [my-test] (1.246ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.697271 D [16188:9800] [my-test] (1.320ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.698849 D [16188:9800] [my-test] (1.228ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.704236 D [16188:9800] [my-test] (1.739ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"d5def80e-5f00-487f-96e8-671b788ff72a.png", :code=>"b38e9fdc-d0e2-4850-a201-f10e6f311b6d", :created_at=>"2024-05-31 12:52:14.702156", :updated_at=>"2024-05-31 12:52:14.702156"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.706025 D [16188:9800] [my-test] (1.416ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.711478 D [16188:9800] [my-test] (1.838ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.709053", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"nYjTBgVE9W\",\"X5ZHjA9gTg\",\"TYqvwVmour\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"nYjTBgVE9W\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"b38e9fdc-d0e2-4850-a201-f10e6f311b6d\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"X5ZHjA9gTg\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"TYqvwVmour\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"156e9e24-533d-4fac-b83d-22e9287a43c5"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.713551 D [16188:9800] [my-test] (1.581ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.717283 D [16188:9800] [my-test] (1.371ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.719356 D [16188:9800] [my-test] (1.618ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/b0d674f0-d8aa-4410-b7bf-5153da88a331", :enabled=>true, :created_at=>"2024-05-31 12:52:14.715581", :updated_at=>"2024-05-31 12:52:14.715581", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.721789 D [16188:9800] [my-test] (1.736ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.723701 D [16188:9800] [my-test] (1.494ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.725548 D [16188:9800] [my-test] (1.430ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.727367 D [16188:9800] [my-test] (1.421ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.729105 D [16188:9800] [my-test] (1.342ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.734688 D [16188:9800] [my-test] (1.670ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"ff194e7b-f5a0-45d8-bbc1-374ec93e4115.png", :code=>"fceb418d-ad95-4a93-853c-01fdd63754e4", :created_at=>"2024-05-31 12:52:14.732661", :updated_at=>"2024-05-31 12:52:14.732661"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.736351 D [16188:9800] [my-test] (1.316ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.742647 D [16188:9800] [my-test] (2.202ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.739813", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"7nnuw6j5VV\",\"-IyGM5sJpt\",\"L6jodAORFU\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"7nnuw6j5VV\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"fceb418d-ad95-4a93-853c-01fdd63754e4\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"-IyGM5sJpt\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"L6jodAORFU\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"b0d674f0-d8aa-4410-b7bf-5153da88a331"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.744312 D [16188:9800] [my-test] (1.152ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.747781 D [16188:9800] [my-test] (1.137ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.749889 D [16188:9800] [my-test] (1.630ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/b53dd6ce-cdb9-4329-9042-fca390721080", :enabled=>true, :created_at=>"2024-05-31 12:52:14.746338", :updated_at=>"2024-05-31 12:52:14.746338", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.752132 D [16188:9800] [my-test] (1.504ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.753803 D [16188:9800] [my-test] (1.277ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.755403 D [16188:9800] [my-test] (1.218ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.756952 D [16188:9800] [my-test] (1.181ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.758435 D [16188:9800] [my-test] (1.142ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.763643 D [16188:9800] [my-test] (1.511ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"9ed9d765-cc76-4e7c-bec2-a902dd776c91.png", :code=>"aaf08e0d-98b3-4c9f-b7b3-a0805faa96d6", :created_at=>"2024-05-31 12:52:14.761792", :updated_at=>"2024-05-31 12:52:14.761792"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.765379 D [16188:9800] [my-test] (1.314ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.771139 D [16188:9800] [my-test] (1.762ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.768750", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"11jxtKKkTh\",\"VjVZS83Y8p\",\"kpukUJXKKP\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"11jxtKKkTh\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"aaf08e0d-98b3-4c9f-b7b3-a0805faa96d6\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"VjVZS83Y8p\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"kpukUJXKKP\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"b53dd6ce-cdb9-4329-9042-fca390721080"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.773225 D [16188:9800] [my-test] (1.505ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.776904 D [16188:9800] [my-test] (1.306ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.778843 D [16188:9800] [my-test] (1.471ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/b6cd9c35-1588-42df-9af9-dca4e83161f0", :enabled=>true, :created_at=>"2024-05-31 12:52:14.775272", :updated_at=>"2024-05-31 12:52:14.775272", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.780904 D [16188:9800] [my-test] (1.329ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.782568 D [16188:9800] [my-test] (1.276ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.784143 D [16188:9800] [my-test] (1.174ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.785695 D [16188:9800] [my-test] (1.190ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.787228 D [16188:9800] [my-test] (1.179ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.792636 D [16188:9800] [my-test] (1.570ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"055b5aa0-6d88-4d2b-8fc7-5ea69b1d2a3f.png", :code=>"4aa42bf3-ddc3-44fc-863b-552805aa5963", :created_at=>"2024-05-31 12:52:14.790701", :updated_at=>"2024-05-31 12:52:14.790701"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.794292 D [16188:9800] [my-test] (1.280ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.799783 D [16188:9800] [my-test] (1.685ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.797468", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"xDGnBBT-zM\",\"T_hbgIoFL6\",\"5p1Q73K5hz\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"xDGnBBT-zM\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"4aa42bf3-ddc3-44fc-863b-552805aa5963\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"T_hbgIoFL6\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"5p1Q73K5hz\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"b6cd9c35-1588-42df-9af9-dca4e83161f0"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.801422 D [16188:9800] [my-test] (1.113ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.804925 D [16188:9800] [my-test] (1.157ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.807076 D [16188:9800] [my-test] (1.686ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/35fbb16c-0250-4f13-9d8a-47fe77541548", :enabled=>true, :created_at=>"2024-05-31 12:52:14.803460", :updated_at=>"2024-05-31 12:52:14.803460", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.809480 D [16188:9800] [my-test] (1.680ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.811359 D [16188:9800] [my-test] (1.492ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.813084 D [16188:9800] [my-test] (1.347ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.814775 D [16188:9800] [my-test] (1.355ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.816443 D [16188:9800] [my-test] (1.322ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.828755 D [16188:9800] [my-test] (1.800ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"e823d990-e871-436b-a7d5-83548f5d8999.png", :code=>"b9dfcae3-7399-47af-b413-4a6e53e33e71", :created_at=>"2024-05-31 12:52:14.826583", :updated_at=>"2024-05-31 12:52:14.826583"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.830800 D [16188:9800] [my-test] (1.679ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.837603 D [16188:9800] [my-test] (1.744ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.835212", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"fzTK2xTxI_\",\"8CdxIaOk4F\",\"8MvIi1xABL\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"fzTK2xTxI_\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"b9dfcae3-7399-47af-b413-4a6e53e33e71\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"8CdxIaOk4F\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"8MvIi1xABL\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"35fbb16c-0250-4f13-9d8a-47fe77541548"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.839341 D [16188:9800] [my-test] (1.180ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.843706 D [16188:9800] [my-test] (1.353ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.845916 D [16188:9800] [my-test] (1.723ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/0a1ed3d2-cbed-4b95-9889-bf400fec10f6", :enabled=>true, :created_at=>"2024-05-31 12:52:14.841961", :updated_at=>"2024-05-31 12:52:14.841961", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.849009 D [16188:9800] [my-test] (1.658ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.850762 D [16188:9800] [my-test] (1.348ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.852499 D [16188:9800] [my-test] (1.363ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.854185 D [16188:9800] [my-test] (1.361ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.855787 D [16188:9800] [my-test] (1.253ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.861203 D [16188:9800] [my-test] (1.644ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"b4ab6c3d-cdd1-47a4-9bde-b883838628a5.png", :code=>"f01e96bb-84f8-466e-8498-576031f73d5d", :created_at=>"2024-05-31 12:52:14.859207", :updated_at=>"2024-05-31 12:52:14.859207"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.863000 D [16188:9800] [my-test] (1.441ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.869192 D [16188:9800] [my-test] (1.803ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.866766", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"GVvj18JJ6Z\",\"j2gezekYUE\",\"INGmoIpxHu\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"GVvj18JJ6Z\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"f01e96bb-84f8-466e-8498-576031f73d5d\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"j2gezekYUE\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"INGmoIpxHu\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"0a1ed3d2-cbed-4b95-9889-bf400fec10f6"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.871038 D [16188:9800] [my-test] (1.332ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.875584 D [16188:9800] [my-test] (1.372ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.878524 D [16188:9800] [my-test] (1.858ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/104255a8-e9e4-478b-8948-8c6e67d0efd5", :enabled=>true, :created_at=>"2024-05-31 12:52:14.873866", :updated_at=>"2024-05-31 12:52:14.873866", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.880713 D [16188:9800] [my-test] (1.452ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.882441 D [16188:9800] [my-test] (1.344ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.884420 D [16188:9800] [my-test] (1.594ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.886248 D [16188:9800] [my-test] (1.437ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.888082 D [16188:9800] [my-test] (1.436ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.893235 D [16188:9800] [my-test] (1.370ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"84b9d5dc-8b06-47f6-af11-72c3ea4c1ac8.png", :code=>"aa770210-13bc-45fa-bad6-14c6c6725b7c", :created_at=>"2024-05-31 12:52:14.891520", :updated_at=>"2024-05-31 12:52:14.891520"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.894912 D [16188:9800] [my-test] (1.323ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.901050 D [16188:9800] [my-test] (1.825ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.898586", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"QB_n2-2BOE\",\"1o1gngdT4C\",\"qq4_y0JSeU\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"QB_n2-2BOE\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"aa770210-13bc-45fa-bad6-14c6c6725b7c\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"1o1gngdT4C\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"qq4_y0JSeU\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"104255a8-e9e4-478b-8948-8c6e67d0efd5"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.903354 D [16188:9800] [my-test] (1.212ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.906977 D [16188:9800] [my-test] (1.176ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.908988 D [16188:9800] [my-test] (1.527ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/2095a13a-4299-4eef-ae1f-fae04ae27d03", :enabled=>true, :created_at=>"2024-05-31 12:52:14.905485", :updated_at=>"2024-05-31 12:52:14.905485", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.911932 D [16188:9800] [my-test] (1.660ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.913708 D [16188:9800] [my-test] (1.377ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.915470 D [16188:9800] [my-test] (1.349ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.917069 D [16188:9800] [my-test] (1.235ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.918721 D [16188:9800] [my-test] (1.311ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.924037 D [16188:9800] [my-test] (1.605ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"42a9c7c3-9f40-434d-9baf-09e7055e4f8f.png", :code=>"c5db6adc-8db2-4fa5-9edc-6d28fd923e24", :created_at=>"2024-05-31 12:52:14.922087", :updated_at=>"2024-05-31 12:52:14.922087"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.925679 D [16188:9800] [my-test] (1.286ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.932839 D [16188:9800] [my-test] (1.601ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.930608", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"Ax-SZZHqrx\",\"OjPFmoauuu\",\"fFQ5J0IKtG\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"Ax-SZZHqrx\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"c5db6adc-8db2-4fa5-9edc-6d28fd923e24\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"OjPFmoauuu\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"fFQ5J0IKtG\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"2095a13a-4299-4eef-ae1f-fae04ae27d03"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.935396 D [16188:9800] [my-test] (1.342ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.939024 D [16188:9800] [my-test] (1.274ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.941391 D [16188:9800] [my-test] (1.416ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/report/727e7b7b-0abb-4387-a25c-f9cb66547409", :enabled=>true, :created_at=>"2024-05-31 12:52:14.937445", :updated_at=>"2024-05-31 12:52:14.937445", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.943526 D [16188:9800] [my-test] (1.431ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.945208 D [16188:9800] [my-test] (1.291ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.947143 D [16188:9800] [my-test] (1.578ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.949124 D [16188:9800] [my-test] (1.573ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.950997 D [16188:9800] [my-test] (1.478ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.957734 D [16188:9800] [my-test] (2.361ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"3100a531-6ed1-470d-8211-a40bfab51e58.png", :code=>"1b6ac0c5-b9f1-4410-af9b-1087269d7d01", :created_at=>"2024-05-31 12:52:14.955018", :updated_at=>"2024-05-31 12:52:14.955018"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.959796 D [16188:9800] [my-test] (1.647ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.966531 D [16188:9800] [my-test] (2.165ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.963124", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"mv5nUZh5SH\",\"QtyCCJSvlY\",\"UvHBp4grxL\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"mv5nUZh5SH\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"1b6ac0c5-b9f1-4410-af9b-1087269d7d01\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"QtyCCJSvlY\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"UvHBp4grxL\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"727e7b7b-0abb-4387-a25c-f9cb66547409"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:14.968582 D [16188:9800] [my-test] (1.516ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}
2024-05-31 14:52:14.973354 D [16188:9800] [my-test] (1.849ms) ActiveRecord -- TRANSACTION -- {:sql=>"SAVEPOINT active_record_1", :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.975710 D [16188:9800] [my-test] (1.795ms) ActiveRecord -- ContentBuilder::Layout Create -- {:sql=>"INSERT INTO \"content_builder_layouts\" (\"code\", \"enabled\", \"created_at\", \"updated_at\", \"craftjs_json\") VALUES ($1, $2, $3, $4, $5) RETURNING \"id\"", :binds=>{:code=>"backup/layout-1/5e224d51-9b11-41c2-bc47-b49630cfd756", :enabled=>true, :created_at=>"2024-05-31 12:52:14.971157", :updated_at=>"2024-05-31 12:52:14.971157", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"3h-Eizw2cD\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"3h-Eizw2cD\":{\"type\":{\"resolvedName\":\"AboutReportWidget\"},\"nodes\":[],\"props\":{\"reportId\":\"68a4aa72-8538-47ea-bbb3-04ab1cd1e286\"},\"custom\":{},\"hidden\":false,\"parent\":\"ROOT\",\"isCanvas\":false,\"displayName\":\"AboutReportWidget\",\"linkedNodes\":{\"about-text\":\"U7ecmS1hsI\",\"about-title\":\"PoSwoa3Fx5\"}},\"PoSwoa3Fx5\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"x8jJUeZx0K\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"U7ecmS1hsI\":{\"type\":{\"resolvedName\":\"Container\"},\"nodes\":[\"dpDtvSimWx\"],\"props\":{},\"custom\":{},\"hidden\":false,\"parent\":\"3h-Eizw2cD\",\"isCanvas\":true,\"displayName\":\"Container\",\"linkedNodes\":{}},\"dpDtvSimWx\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"U7ecmS1hsI\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}},\"x8jJUeZx0K\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"nodes\":[],\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"parent\":\"PoSwoa3Fx5\",\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"linkedNodes\":{}}}"}, :allocations=>41, :cached=>nil}
2024-05-31 14:52:14.978016 D [16188:9800] [my-test] (1.544ms) ActiveRecord -- AppConfiguration Load -- {:sql=>"SELECT \"app_configurations\".* FROM \"app_configurations\" ORDER BY \"app_configurations\".\"id\" ASC LIMIT $1", :binds=>{:limit=>1}, :allocations=>52, :cached=>nil}
2024-05-31 14:52:14.979622 D [16188:9800] [my-test] (1.228ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>45, :cached=>nil}
2024-05-31 14:52:14.981191 D [16188:9800] [my-test] (1.188ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.982954 D [16188:9800] [my-test] (1.402ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.984812 D [16188:9800] [my-test] (1.474ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"id\" = $1 LIMIT $2", :binds=>{:id=>"ab3bfb55-b503-4711-9efb-674a68469978", :limit=>1}, :allocations=>47, :cached=>nil}
2024-05-31 14:52:14.990853 D [16188:9800] [my-test] (1.725ms) ActiveRecord -- ContentBuilder::LayoutImage Create -- {:sql=>"INSERT INTO \"content_builder_layout_images\" (\"image\", \"code\", \"created_at\", \"updated_at\") VALUES ($1, $2, $3, $4) RETURNING \"id\"", :binds=>{:image=>"e45c7b40-ba75-458f-ad3c-be70bcdaf8a6.png", :code=>"a50306b8-e833-4639-b130-546ae5c24700", :created_at=>"2024-05-31 12:52:14.988780", :updated_at=>"2024-05-31 12:52:14.988780"}, :allocations=>7, :cached=>nil}
2024-05-31 14:52:14.992491 D [16188:9800] [my-test] (1.270ms) ActiveRecord -- Tenant Load -- {:sql=>"SELECT \"public\".\"tenants\".* FROM \"public\".\"tenants\" WHERE \"public\".\"tenants\".\"host\" = $1 LIMIT $2", :binds=>{:host=>"example.org", :limit=>1}, :allocations=>51, :cached=>nil}
2024-05-31 14:52:14.998675 D [16188:9800] [my-test] (1.783ms) ActiveRecord -- ContentBuilder::Layout Update -- {:sql=>"UPDATE \"content_builder_layouts\" SET \"updated_at\" = $1, \"craftjs_json\" = $2 WHERE \"content_builder_layouts\".\"id\" = $3", :binds=>{:updated_at=>"2024-05-31 12:52:14.996249", :craftjs_json=>"{\"ROOT\":{\"type\":\"div\",\"nodes\":[\"5jVRCbDyOi\",\"bJeByM1Q_T\",\"tbtvrgSRPP\"],\"props\":{\"id\":\"e2e-content-builder-frame\"},\"custom\":{},\"hidden\":false,\"isCanvas\":true,\"displayName\":\"div\",\"linkedNodes\":{}},\"5jVRCbDyOi\":{\"type\":{\"resolvedName\":\"ImageMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"image\":{\"dataCode\":\"a50306b8-e833-4639-b130-546ae5c24700\"},\"stretch\":false},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"Image\",\"nodes\":[],\"linkedNodes\":{}},\"bJeByM1Q_T\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003ch2\\u003emarker-about-title\\u003c/h2\\u003e\",\"fr-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\",\"nl-BE\":\"\\u003ch2\\u003eabout-this-report\\u003c/h2\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}},\"tbtvrgSRPP\":{\"type\":{\"resolvedName\":\"TextMultiloc\"},\"parent\":\"ROOT\",\"props\":{\"text\":{\"en\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"fr-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\",\"nl-BE\":\"\\u003cp\\u003emarker-about-text\\u003c/p\\u003e\"}},\"custom\":{},\"hidden\":false,\"isCanvas\":false,\"displayName\":\"TextMultiloc\",\"nodes\":[],\"linkedNodes\":{}}}", :id=>"5e224d51-9b11-41c2-bc47-b49630cfd756"}, :allocations=>5, :cached=>nil}
2024-05-31 14:52:15.000450 D [16188:9800] [my-test] (1.223ms) ActiveRecord -- TRANSACTION -- {:sql=>"RELEASE SAVEPOINT active_record_1", :allocations=>49, :cached=>nil}