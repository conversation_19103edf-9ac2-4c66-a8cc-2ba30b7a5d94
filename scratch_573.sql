SELECT "ideas".*
FROM "ideas"
         INNER JOIN (
    SELECT
        "ideas"."id"                                                           AS pg_search_id,
        (ts_rank((to_tsvector('simple', coalesce("ideas"."title_multiloc"::text, '')) ||
                  to_tsvector('simple', coalesce("ideas"."body_multiloc"::text, '')) ||
                  to_tsvector('simple', coalesce("ideas"."custom_field_values"::text, '')) ||
                  to_tsvector('simple', coalesce("ideas"."slug"::text, ''))),
                 (to_tsquery('simple', ''' ' || 'test' || ' ''' || ':*')), 0)) AS rank
    FROM "ideas"
    WHERE ((to_tsvector('simple', coalesce("ideas"."title_multiloc"::text, '')) ||
            to_tsvector('simple', coalesce("ideas"."body_multiloc"::text, '')) ||
            to_tsvector('simple', coalesce("ideas"."custom_field_values"::text, '')) ||
            to_tsvector('simple', coalesce("ideas"."slug"::text, ''))) @@
           (to_tsquery('simple', ''' ' || 'test' || ' ''' || ':*')))) AS pg_search_53d8ba9090b329f45d3650
                    ON "ideas"."id" = pg_search_53d8ba9090b329f45d3650.pg_search_id
ORDER BY pg_search_53d8ba9090b329f45d3650.rank DESC, "ideas"."id" ASC


-- test: to_tsvector on literal jsonb
select to_tsvector('simple', '{"en": "schools", "fr": "écoles", "nl": "scholen"}'::jsonb::text);

select '{ "en": "schools", "fr": "écoles", "nl": "scholen"}'::jsonb::text;

-- take only values (to text):
with sample_description as (
    select *
    from (values
        ('{"en": "schools", "fr": "écoles", "nl": "scholen"}'::jsonb),
        ('{"en": "parks", "fr": "parcs", "nl": "parken"}'::jsonb),
        ('{"en": "streets", "fr": "rues", "nl": "straatjes"}'::jsonb)
    ) as sample_description (json_data)
)
-- description, array of values (not keys)
select json_data,  to_tsvector('simple', (select string_agg(value, ' ' order by value) from jsonb_each_text(json_data))
from sample_description;


WITH sample_description AS (
    SELECT *
    FROM (VALUES
              ('{"en": "schools", "fr": "écoles", "nl": "scholen"}'::jsonb),
              ('{"en": "parks", "fr": "parcs", "nl": "parken"}'::jsonb),
              ('{"en": "streets", "fr": "rues", "nl": "straatjes"}'::jsonb)
         ) AS sample_description (json_data)
)
-- description, array of values (not keys)
SELECT
    json_data,
    to_tsvector(
            'simple',
            (SELECT string_agg(value, ' ' ORDER BY value) FROM jsonb_each_text(json_data))
    ) AS tsv
FROM sample_description;