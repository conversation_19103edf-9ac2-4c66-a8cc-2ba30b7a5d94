Add a `semantic_type` attribute to the `Files::File` model with the following requirements:

1. Add a string column named `semantic_type` to the `files` table
2. Update the model to include validation for this new attribute
3. Add the attribute to the serializer so it's included in API responses
4. Update any relevant tests to account for this new attribute
5. The `semantic_type` should represent the semantic purpose of the file (e.g., "document", "image", "spreadsheet", etc.) rather than just the MIME type
6. Ensure the attribute is properly documented in the model's schema information