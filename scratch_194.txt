Yep, just removing what's not needed. Also, `reorder` is not very restful (more of an RPC) for no good reason. I had `TopicsController#reorder` in the sights, but I wanted to keep this PR to the point. What about `Topic#ordering`? Isn't it necessary anymore?

--------

I didn’t find any reset-code emails in the Mailgun logs, but their retention is only 1 month. More strangely, I could not find any `requested_confirmation_code` activity in the database for this user. There is such an activity for an unknown user that was deleted on 2023-12-19, though (only 1 activity, so one reset attempt).

Furthermore, the reset job is executed synchronously. Therefore, congestion in the job queue cannot be the cause.

Finally, I could not reproduce it myself with different accounts, emails, and email providers.

However, there is one thing in the code I very much don't like: `return if !user.valid?` (at https://github.com/CitizenLabDotCo/citizenlab/blob/1690fd7bef9b2b4b38e4624436cffeb105b19b14/back/app/jobs/request_confirmation_code_job.rb#L18-L17), which could silence some errors. Nevertheless, I don't think it plays a role here since we should be able to find some activities in that case.


---------


The platform probably got deleted after the trial expired, but the deletion failed halfway through. Closing this ticket as I'm currently working on another ticket that will fix some of those issues. Gonna close this ticket in favor of that TAN-783 that will centralize all the issues related to tenant deletion.

# Make tenant deletion more robust



# Migrations are not running on soft-deleted tenants but tenants are still online

When the asynchroneous deletion of tenants was implemented, we removed the soft-deleted tenants from the list of tenants in Apartment, expecting that this would make the platform inaccessible. However, the only effects it seems to have is to prevent migrations from running on the deleted tenants. But the tenants are still online and accessible.

When the asynchronous deletion of tenants was introduced, we removed the soft-deleted tenants from the Apartment's tenant list, anticipating that this would make the platform inaccessible. However, the only effect this seems to have is to prevent migrations from running on the deleted tenants. But the tenants are still online and accessible.

Let's bring them back to the list of tenants in Apartment to fix migrations and find another way to make the tenants inaccessible.


----

:alphabet-white-question: I created two new dev tasks, but they don’t show up in the dispatch board. Do I need to do something else?


----

I manually scaled up the asg and it’s now resorbing the backlog. The autoscaling didn't kick in because the CPU was still below 50%.



A template platform: an actual live platform that ends on .template.citizenlab.co. Changing these platforms also change the tenant template that is based on this template platform. Tenant templates are updated every 24 hours, during the night (European time). You can easily create templates by just using the word x.template.citizenlab.co in the URL. You can easily create new templates just by using a domain name that ends on `.template.citizenlab.co` (e.g. `mycity.template.citizenlab.co`). However, please refrain from creating too many templates, as this will slow down the template update and testing process.

base: "blank" template – creates only the minimum resources required to have an operational platform
e2etests_template: technical template used for end-to-end testing (used by the dev team)

