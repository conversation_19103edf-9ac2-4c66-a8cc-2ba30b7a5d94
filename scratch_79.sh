# list files in a git repository with the date of the creation (first commit with this file)
# each line should look like this:
# filepath date
git ls-tree -r --name-only HEAD | while read filename; do echo "$(git log --pretty=format:%ad --date=short $(git log --pretty=format:%H -- $filename | tail -1)) $filename"; done

for file in db/migrate/*; do echo -n "$file: "; git log --reverse --format=%aI -- "$file" | head -n 1; done

# Adapt the command above to show the timestamp first, then the filename, followed by a carriage return
for file in engines/free/email_campaigns/app/mailers/email_campaigns/*; do echo -n "$(git log --reverse --format=%aI -- "$file" | head -n 1) $file\n"; done | sort -r
