campaign_id = "b04ffe74-f2f2-41e3-86b1-0fb8506f6de0"
campaign = EmailCampaigns::Campaign.find(campaign_id)

token = "9hkWpjWt2JjrtwuzdkEw3XQQB84jACr2MyWWKdisK7zeohsyiEq0bMvCJcCQkRl4"
unsubscription_token = EmailCampaigns::UnsubscriptionToken.find_by(token: token)
user = unsubscription_token&.user

consent = EmailCampaigns::Consent.find_by!(campaign_type: campaign.type, user: user)

defined_types = EmailCampaigns::Consent.where(user_id: user.id).pluck(:campaign_type)&.uniq
available_types = EmailCampaigns::DeliveryService.new.consentable_campaign_types_for(user)
(available_types - defined_types).each do |campaign_type|
  create!(
    user_id: user.id,
    campaign_type: campaign_type,
    consented: true
  )
end




