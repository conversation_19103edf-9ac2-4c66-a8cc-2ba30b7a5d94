SELECT "areas"."id"                            AS t0_r0,
       "areas"."title_multiloc"                AS t0_r1,
       "areas"."description_multiloc"          AS t0_r2,
       "areas"."created_at"                    AS t0_r3,
       "areas"."updated_at"                    AS t0_r4,
       "areas"."ordering"                      AS t0_r5,
       "areas"."custom_field_option_id"        AS t0_r6,
       "custom_field_option"."id"              AS t1_r0,
       "custom_field_option"."custom_field_id" AS t1_r1,
       "custom_field_option"."key"             AS t1_r2,
       "custom_field_option"."title_multiloc"  AS t1_r3,
       "custom_field_option"."ordering"        AS t1_r4,
       "custom_field_option"."created_at"      AS t1_r5,
       "custom_field_option"."updated_at"      AS t1_r6
FROM "areas"
         LEFT OUTER JOIN "custom_field_options" "custom_field_option"
                         ON "custom_field_option"."id" = "areas"."custom_field_option_id"
WHERE "custom_field_option"."ordering" = 1
ORDER BY "areas"."ordering" ASC