I don’t see any anomaly in the number of votes in the production DB.

```
submitted_baskets = phase.baskets.filter { |b| b.submitted? }
submitted_baskets.count
# => 1673 (nb of persons who voted)

basket_sizes = submitted_baskets.map {|b| b.ideas.count }
basket_sizes.uniq
# => [5] (all baskets have 5 ideas/votes)

basket_sizes.sum
# => 8365 (total nb of votes = 1673 * 5)
```

Were the erroneous votes removed/rectified manually?


There is this thing about the bad votes disappearing, but the Metabase request returns only 7.940. Assuming each user had 5 votes, this would mean that 1.588 users voted.


We need a front-end dev for that. Will try to find one. Any argument to boost the priority of this problem over the other ones?

```ruby
phase = Phase.find(1)
bi = BasketsIdea.includes(:basket, :idea, basket: [:user])
bi.where(basket: {phase: phase}).where.not(basket: {}
