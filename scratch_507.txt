**Question:** Do we have an invariant that says the form returned by `/phases/:id/custom_fields/json_forms_schema` should start and end with a page custom field?

**Context:** We're adding a new participation method, *CommonGround*, which uses very minimalistic inputs, basically just statements users can agree or disagree with. So we barely need more than a single multiloc title field, but I was wondering if there's an invariant I might have missed.
