diff --git a/cluster.rb b/cluster.rb
index 9bdf441..44b4fb1 100644
--- a/cluster.rb
+++ b/cluster.rb
@@ -5,7 +5,7 @@ class Cluster

   def initialize(
     name:,
-    region:,
+    region: nil,
     upload_bucket: nil,
     backend_db_url: nil,
     workshops_db_url: nil,
@@ -17,6 +17,8 @@ class Cluster
     @backend_db_url = backend_db_url
     @workshops_db_url = workshops_db_url
     @cloudfront_distribution_id = cloudfront_distribution_id
+
+    validate!
   end

   def local?
@@ -41,4 +43,17 @@ class Cluster
       cloudfront_distribution_id: json["cloudfront_distribution_id"]
     )
   end
+
+  private
+
+  def validate!
+    raise ArgumentError, "backend_db_url is required" if backend_db_url.nil?
+
+    unless local?
+      raise ArgumentError, "workshops_db_url is required" if workshops_db_url.nil?
+      raise ArgumentError, "region is required" if region.nil?
+      raise ArgumentError, "upload_bucket is required" if upload_bucket.nil?
+      raise ArgumentError, "cloudfront_distribution_id is required" if cloudfront_distribution_id.nil?
+    end
+  end
 end
diff --git a/helpers.rb b/helpers.rb
index 11a89d8..870ae5c 100644
--- a/helpers.rb
+++ b/helpers.rb
@@ -37,13 +37,13 @@ module Helpers

     begin
       copy_schema_data(cluster_in.db, cluster_out.db, tenant_schema)
-      copy_schema_data(cluster_in.workshops_db, cluster_out.workshops_db, tenant_schema)
+      copy_schema_data(cluster_in.workshops_db, cluster_out.workshops_db, tenant_schema) unless cluster_out.workshops_db.nil?
       copy_uploads(cluster_in, cluster_out, tenant_id)

       migrate_dns_record(cluster_in, cluster_out, tenant_host) if mode == :move

       cluster_out.db[Sequel[:public][:tenants]].insert(tenant_record)
-      cluster_out.workshops_db[Sequel[:public][:tenants]].insert(ws_tenant_record )
+      cluster_out.workshops_db[Sequel[:public][:tenants]].insert(ws_tenant_record)
       return unless mode == :move

       delete_db_schema(cluster_in.db, tenant_schema)
@@ -154,25 +154,25 @@ module Helpers
     client = Aws::Route53::Client.new(region: 'us-east-1')

     client.change_resource_record_sets({
-      change_batch: {
-        changes: [
-          {
-            action: "UPSERT",
-            resource_record_set: {
-              alias_target: {
-                dns_name: cf_distribution_domain,
-                evaluate_target_health: false,
-                # CloudFront distribution zone ID is a constant.
-                hosted_zone_id: "Z2FDTNDATAQYW2",
-              },
-              name: host,
-              type: "A",
-            },
-          },
-        ],
-      },
-      hosted_zone_id: ROUTE53_ZONE_ID,
-    })
+                                         change_batch: {
+                                           changes: [
+                                             {
+                                               action: "UPSERT",
+                                               resource_record_set: {
+                                                 alias_target: {
+                                                   dns_name: cf_distribution_domain,
+                                                   evaluate_target_health: false,
+                                                   # CloudFront distribution zone ID is a constant.
+                                                   hosted_zone_id: "Z2FDTNDATAQYW2",
+                                                 },
+                                                 name: host,
+                                                 type: "A",
+                                               },
+                                             },
+                                           ],
+                                         },
+                                         hosted_zone_id: ROUTE53_ZONE_ID,
+                                       })
   end

   def remove_cf_alt_domains(region, cf_distribution_id, alt_domains)
@@ -187,10 +187,10 @@ module Helpers
     config.aliases.quantity -= alt_domains.size

     client.update_distribution({
-      id: cf_distribution_id,
-      distribution_config: config,
-      if_match: resp.etag,
-    })
+                                 id: cf_distribution_id,
+                                 distribution_config: config,
+                                 if_match: resp.etag,
+                               })
   end

   def add_cf_alt_domains(region, cf_distribution_id, alt_domains)
@@ -202,10 +202,10 @@ module Helpers
     config.aliases.quantity = config.aliases.items.size

     client.update_distribution({
-      id: cf_distribution_id,
-      distribution_config: config,
-      if_match: resp.etag,
-    })
+                                 id: cf_distribution_id,
+                                 distribution_config: config,
+                                 if_match: resp.etag,
+                               })
   end

   # @return [Process::Status]


Commit message: Allow skipping workshops data when copying to local cluster