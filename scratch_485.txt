@Anonymous @Anonymous Do we have concrete example where it does not work as expected?

Note that those notifications and the corresponding emails are not sent to admins that have a GoVocal or Citizenlab email address. This is to avoid spamming GS managers & co with emails. This could probably be improved to send the notifications but not the emails to those users.

Just a heads-up: admins with GoVocal or CitizenLab email addresses don't receive those notifications or emails. This is to avoid overwhelming GS managers & co with too many emails. (We could probably rework this to skip only the emails but still the notifications to those users, but it does not have the same urgency.)

Note that admins with GoVocal or CitizenLab email addresses don’t receive these notifications or emails. This is to avoid overwhelming (mostly) GS managers with too many emails. (We could potentially rework this behavior to send the notifications but not the emails, but it would be much less urgent.

