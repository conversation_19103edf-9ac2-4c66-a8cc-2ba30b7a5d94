service = MultiTenancy::ChurnedTenantService.new
expired_tenants = service.send(:expired_tenants)

# sort by churn date
expired_tenant = expired_tenants.sort_by do |tenant|
  service.send(:churn_datetime, tenant)
end

# print (tenant_id, tenant_host, tenant_name, churn_date, user_count) as csv (escaped)
expired_tenant.each do |tenant|
  churn_date = service.send(:churn_datetime, tenant).to_date
  user_count = tenant.switch { User.count }
  puts "#{tenant.id},#{tenant.host},#{tenant.name},#{churn_date},#{user_count},can"
end; nil


# print (tenant_id, email of owner) as csv
TenantsOwner.includes(:user).each do |tenant_owner|
  puts "#{tenant_owner.tenant_id},#{tenant_owner.user.email}"
end; nil