# grep lines that contain any of those IPs
# ip-172-31-17-210
# ip-172-31-26-211
# ip-172-31-27-40
# ip-172-31-31-203

k8ajx8o7898b2n0fno95vcfq7     ip-172-31-17-103   Ready     Active                          27.1.1
2yrnwv6dvdndm3bvfuktzinzn     ip-172-31-17-210   Ready     Active         Reachable        24.0.2
mvfpsvbtde9ga1kgfo61gw3pm     ip-172-31-26-211   Ready     Active         Reachable        24.0.2
uu5db5ppj486grp45gjdn7h4b *   ip-172-31-31-203   Ready     Active         Leader           24.0.2

cat ... | grep -E 'ip-172-31-17-210|ip-172-31-26-211|ip-172-31-31-203|ip-172-31-17-103' | grep Running


# get docker logs using journalctl
# only the tail
journalctl -u docker.service -n 100

cd cl2-deployment && docker stack deploy --compose-file docker-compose-production.yml cl2-prd-bnlx-stack --with-registry-auth --prune