SELECT "files".*
FROM "files"
         INNER JOIN (
    SELECT
        "files"."id"                                                                                                                                                        AS pg_search_id,
        CAST((ts_rank(("files"."tsvector"), (to_tsquery('simple', ''' ' || 'dofu' || ' ''')), 0)) > 0 AS INTEGER) *
        (0.9 * (ts_rank(("files"."tsvector"), (to_tsquery('simple', ''' ' || 'dofu' || ' ''')), 0)) + 0.1) + 0.1 *
                                                                                                             (similarity(
                                                                                                                     'dofu',
                                                                                                                     (coalesce("files"."name"::text, '') ||
                                                                                                                      ' ' ||
                                                                                                                      coalesce("files"."description_multiloc"::text, '')))) AS rank
    FROM "files"
    WHERE (("files"."tsvector") @@ (to_tsquery('simple', ''' ' || 'dofu' || ' ''')))
       OR ('dofu' % (coalesce("files"."name"::text, '') || ' ' ||
                     coalesce("files"."description_multiloc"::text, '')))) AS pg_search_3d7db37d08f9140fd09f12
                    ON "files"."id" = pg_search_3d7db37d08f9140fd09f12.pg_search_id
ORDER BY pg_search_3d7db37d08f9140fd09f12.rank DESC, "files"."id" ASC
