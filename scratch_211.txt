Is there any chance we can call the Report Builder "Constructeurs de Rapport" as opposed to "Editeur de Rapports"? I'm seeing in support articles that we always refer to it as Constructeur. cc

To be honest, I'd rather adapt the support articles. I think the literal translation does work well in this case. IMO, "Constructeur de Rapports" sounds too fancy and gives the impression we are trying too hard to make it look cool. Also, in French, "constructeur" typically refers to a person or a company, not a tool.