# que worker/ version 1
[TAN-966] Add a temporary service to run version 1 of the Que worker

This is needed to process the jobs with `job_schema_version = 1` who won't be picked up by
 version 2 of the worker. This service will be removed once all the jobs with version 1
are processed.

# Changelog
## Technical
- [TAN-966] Upgrade Que to version 2. This also adds a new temporary service to run version 1 of the Que worker in order to continue processing the jobs with job_schema_version = 1 that won't be picked up by version 2 of the worker. This service will be removed once all the jobs with version 1 are processed.