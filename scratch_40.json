{"ROOT": {"type": "div", "nodes": ["JePNcDvuLi", "VmC-ljSV4b", "pv10FCDcoz", "WNt471htje", "yQpxb3psFh", "kuHUq46MKv", "6jxf1PnC3H", "lNcXh1AyLt"], "props": {"id": "e2e-content-builder-frame"}, "custom": {}, "hidden": false, "isCanvas": true, "displayName": "div", "linkedNodes": {}}, "6jxf1PnC3H": {"type": {"resolvedName": "Events"}, "nodes": [], "props": {}, "custom": {"title": {"id": "app.containers.admin.ContentBuilder.homepage.eventsTitle", "defaultMessage": "Events"}, "noPointerEvents": true}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "Events", "linkedNodes": {}}, "JePNcDvuLi": {"type": {"resolvedName": "HomepageBanner"}, "nodes": [], "props": {"image": {"dataCode": "94cc697a-c246-4367-8c89-f9fd55df009f"}, "errors": [], "hasError": false, "homepageSettings": {"banner_layout": "fixed_ratio_layout", "banner_avatars_enabled": true, "banner_cta_signed_in_url": "https://www.youtube.com/watch?v=jRHQPG1xd9o", "banner_cta_signed_in_type": "no_button", "banner_cta_signed_out_url": "https://github.com/stas00/ml-engineering", "banner_cta_signed_out_type": "customized_button", "banner_signed_in_header_multiloc": {"en": " ", "de-DE": "", "es-CL": "", "fr-BE": "", "nl-BE": "", "sr-Latn": ""}, "banner_signed_out_header_multiloc": {"en": "Banner text NEW swag", "de-DE": "Header text DE DE", "es-CL": "Header text ES CL", "fr-BE": "Header text FR BE", "nl-BE": "Header text NEW NL BE", "sr-Latn": "Header text SR LATN"}, "banner_cta_signed_in_text_multiloc": {"en": "The code is sixteen twelve (hello)", "de-DE": "4", "es-CL": "5", "fr-BE": "3", "nl-BE": "2", "sr-Latn": "6"}, "banner_cta_signed_out_text_multiloc": {"en": "custom button ", "de-DE": "test", "es-CL": "test", "fr-BE": "test", "nl-BE": "123", "sr-Latn": "test"}, "banner_signed_out_subheader_multiloc": {"en": "Subheader text  NEW", "de-DE": "subhead text DE DE ", "es-CL": "subhead text ES CL", "fr-BE": "subhead text FR BE", "nl-BE": "subhead text NL BE", "sr-Latn": "subhead text SR LATN"}, "banner_signed_in_header_overlay_color": "#32db4a", "banner_signed_out_header_overlay_color": null, "banner_signed_in_header_overlay_opacity": 100, "banner_signed_out_header_overlay_opacity": null}}, "custom": {"title": {"id": "app.containers.admin.ContentBuilder.homepage.homepageBanner", "defaultMessage": "Homepage banner"}, "noDelete": true, "noPointerEvents": true}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "HomepageBanner", "linkedNodes": {}}, "VmC-ljSV4b": {"type": {"resolvedName": "ButtonMultiloc"}, "nodes": [], "props": {"url": "https://example.org", "text": {"en": "tmp", "de-DE": "tmp", "es-CL": "tmp", "fr-BE": "tmp", "nl-BE": "tmp", "sr-Latn": "tmp"}, "type": "secondary", "alignment": "center"}, "custom": {"title": {"id": "app.containers.admin.ContentBuilder.buttonMultiloc", "defaultMessage": "<PERSON><PERSON>"}, "noPointerEvents": true}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "a", "linkedNodes": {}}, "WNt471htje": {"type": {"resolvedName": "TextMultiloc"}, "nodes": [], "props": {"text": {"en": "<p>City of Nanaimo greets you</p><p><img class=\"ql-alt-text-input-container keepHTML\" alt=\"\" data-cl2-text-image-text-reference=\"a0df8906-9cd2-416f-9d00-4626b68afaf0\"></p>", "de-DE": "<p>1111</p>", "es-CL": "<p>1111</p>", "fr-BE": "<p>1111</p>", "nl-BE": "<p>1111</p>", "sr-Latn": "<p>1111</p>"}}, "custom": {"title": {"id": "app.containers.admin.ContentBuilder.textMultiloc", "defaultMessage": "Text"}}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "TextMultiloc", "linkedNodes": {}}, "kuHUq46MKv": {"type": {"resolvedName": "Projects"}, "nodes": [], "props": {"currentlyWorkingOnText": {"en": "22", "nl-BE": "123"}}, "custom": {"title": {"id": "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle", "defaultMessage": "Projects"}, "noDelete": true, "noPointerEvents": true}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "Projects", "linkedNodes": {}}, "lNcXh1AyLt": {"type": {"resolvedName": "Proposals"}, "nodes": [], "props": {}, "custom": {"title": {"id": "app.containers.admin.ContentBuilder.homepage.proposalsTitle", "defaultMessage": "Proposals"}, "noPointerEvents": true}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "Proposals", "linkedNodes": {}}, "pv10FCDcoz": {"type": {"resolvedName": "WhiteSpace"}, "nodes": [], "props": {"size": "medium"}, "custom": {"title": {"id": "app.containers.AdminPage.ProjectDescription.whiteSpace", "defaultMessage": "White space"}}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "WhiteSpace", "linkedNodes": {}}, "yQpxb3psFh": {"type": {"resolvedName": "WhiteSpace"}, "nodes": [], "props": {"size": "medium"}, "custom": {"title": {"id": "app.containers.AdminPage.ProjectDescription.whiteSpace", "defaultMessage": "White space"}}, "hidden": false, "parent": "ROOT", "isCanvas": false, "displayName": "WhiteSpace", "linkedNodes": {}}}