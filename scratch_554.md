# Leveraging Unlabeled Data to Predict Out-of-Distribution Performance

<PERSON><PERSON><PERSON><PERSON>g*<br>Carnegie Mellon University<br><EMAIL> <PERSON><PERSON><PERSON><br>Carnegie Mellon University<br>s<PERSON><PERSON><PERSON>@andrew.cmu.edu Zachary <PERSON><br>Carnegie Mellon University<br>z<PERSON><PERSON>@andrew.cmu.edu

## <PERSON><PERSON><PERSON>

Google Research, <NAME_EMAIL>

## <PERSON><PERSON>i

Google Research, <NAME_EMAIL>

## ABSTRACT

Real-world machine learning deployments are characterized by mismatches between the source (training) and target (test) distributions that may cause performance drops. In this work, we investigate methods for predicting the target domain accuracy using only labeled source data and unlabeled target data. We propose Average Thresholded Confidence (ATC), a practical method that learns a threshold on the model's confidence, predicting accuracy as the fraction of unlabeled examples for which model confidence exceeds that threshold. ATC outperforms previous methods across several model architectures, types of distribution shifts (e.g., due to synthetic corruptions, dataset reproduction, or novel subpopulations), and datasets (WILDS, ImageNet, BREEDS, CIFAR, and MNIST). In our experiments, ATC estimates target performance $2-4 \times$ more accurately than prior methods. We also explore the theoretical foundations of the problem, proving that, in general, identifying the accuracy is just as hard as identifying the optimal predictor and thus, the efficacy of any method rests upon (perhaps unstated) assumptions on the nature of the shift. Finally, analyzing our method on some toy distributions, we provide insights concerning when it works ${ }^{1}$.

## 1 INTRODUCTION

Machine learning models deployed in the real world typically encounter examples from previously unseen distributions. While the IID assumption enables us to evaluate models using held-out data from the source distribution (from which training data is sampled), this estimate is no longer valid in presence of a distribution shift. Moreover, under such shifts, model accuracy tends to degrade (Szegedy et al., 2014; Recht et al., 2019; Koh et al., 2021). Commonly, the only data available to the practitioner are a labeled training set (source) and unlabeled deployment-time data which makes the problem more difficult. In this setting, detecting shifts in the distribution of covariates is known to be possible (but difficult) in theory (Ramdas et al., 2015), and in practice (Rabanser et al., 2018). However, producing an optimal predictor using only labeled source and unlabeled target data is well-known to be impossible absent further assumptions (Ben-David et al., 2010; Lipton et al., 2018).

Two vital questions that remain are: (i) the precise conditions under which we can estimate a classifier's target-domain accuracy; and (ii) which methods are most practically useful. To begin, the straightforward way to assess the performance of a model under distribution shift would be to collect labeled (target domain) examples and then to evaluate the model on that data. However, collecting fresh labeled data from the target distribution is prohibitively expensive and time-consuming, especially if the target distribution is non-stationary. Hence, instead of using labeled data, we aim to use unlabeled data from the target distribution, that is comparatively abundant, to predict model performance. Note that in this work, our focus is not to improve performance on the target but, rather, to estimate the accuracy on the target for a given classifier.

[^0]
[^0]:    * Work done in part while Saurabh Garg was interning at Google
${ }^{1}$ Code is available at https://github.com/saurabhgarg1996/ATC_code.

Гостиница „МЕТРОПОЛЬ“

г. Москва

I, too, Harry Crouch, do hereby request that my present citizenship in the United States of America be revoked.

I have entered the Soviet Union for the express purpose of applying for citizenship in the Soviet Union, through the means of naturalization.

My request for citizenship is now pending before the Supreme Soviet of the U.S.R.

I take these steps for political reasons. My request for the revoking of my American citizenship is made only after the long and short term. Credit: National Archives - JFK

I affirm that my allegation to the Union of Soviet Socialist Republics.

[Signature]

Commission Exhibit No. 257

Business Law Textbook Notes Chapter 5: Constitutional Law

The U.S. Constitution: "Federalism—the authority to govern is divided between Federal & State gov't." Judicial review—allows courts to review legislative & executive actions to determine if they are Constitutional (Marbury, Madison).

The Supreme Court, Clause and Federal Preemption: "Supremecy Clause—located in Article VI of the Constitutional Law & Treaties of the U.S. Constitute the Supreme Law of the Land—any state or local law that directly conflicts w/ Constitution, for all laws, or treaties is void." Federal laws include rules passed by Federal preemption—a principle asserting the supreme of federal legislation over state legislation when both pertain to the same matter.

The Commerce

Business Law Textbook Notes
Chapter 5: Constitutional Law

The U.S. Constitution:

Federalism: the authority to govern is divided between federal and state governments.
Judicial review: allows courts to review legislative and executive actions to determine if they are constitutional (Marbury v. Madison).
The Supremacy Clause and Federal Preemption:

Supremacy Clause: located in Article VI of the Constitution. Laws and treaties of the U.S. constitute the supreme law of the land.
Any state or local law that directly conflicts with the Constitution, federal laws, or treaties is void.
Federal laws include rules passed by federal agencies.
Federal preemption: a principle asserting the supremacy of federal legislation over state legislation when both pertain to the same matter.
The Commerce