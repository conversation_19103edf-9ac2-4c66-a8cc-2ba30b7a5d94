containers=$(docker ps -q)

# Loop through each container
for container in $containers; do
    # Get the path of the container's logs
    log_path=$(docker inspect --format='{{.LogPath}}' $container)

    # Check the disk usage of the logs
    echo "Container $container:"
    sudo du -sh $log_path
done

# get the name of container 4248e58eb131
docker inspect --format='{{.Name}}' 4248e58eb131


# list the size and name of each volume
docker volume ls -q | while read -r volume; do
    echo -n "$volume: "
    sudo du -sh $(docker volume inspect --format='{{.Mountpoint}}' $volume)
done


# list all containers (even stopped ones)
docker ps -a

# Connect to the PG database in the right container
docker exec -it 8d9db6a89707 psql -U postgres

# Remove part of the data from the peak in January
DELETE FROM public.nodestore_node WHERE "timestamp" BETWEEN '2024-01-08 12:00:00' AND '2024-01-08 16:00:00';
# Reclaim the space with a full vacuum.
# Watch-out: this will lock the table and will prevent the ingestion of events during the vacuum.
VACUUM FULL public.nodestore_node;

# Volume sizes after the vacuum


# list tables
\dt

# \d public.nodestore_node
#          Table "public.nodestore_node"
#  Column   |           Type           | Modifiers
#-----------+--------------------------+-----------
# id        | character varying(40)    | not null
# data      | text                     | not null
# timestamp | timestamp with time zone | not null



# get disk usage percentage
df -h


difference between VACUUM table; and VACUUM FULL table;
