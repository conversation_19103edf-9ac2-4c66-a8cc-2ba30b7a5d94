Tenant.deleted.count
tenants = Tenant.deleted

Tenant.deleted.each do |tenant|
  tenant.switch { User.destroy_all_async }
end

# deleted tenants with 0 users
Tenant.deleted.select do |tenant|
  tenant.switch { User.count.zero? }
end.each do |tenant|
  MultiTenancy::Tenants::DeleteJob.perform_now(tenant)
end

tenant = Tenant.deleted.order(deleted_at: :asc).first

tenant.switch!
User.count

Tenant.deleted.to_h do |tenant|
  tenant.switch { [tenant.id, User.pluck(:email)] }
end

Apartment::Tenant.reset

Tenant.deleted.each do |tenant|
  tenant.destroy!
  MultiTenancy::SideFxTenantService.new.after_destroy(tenant)
end

Tenant.deleted.each do |tenant|
  tenant.switch do
    ReportBuilder::Report.destroy_all
    User.each { |user| DeleteUserJob.perform_now(user) }
  end
end
