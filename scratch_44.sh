# run rubocop on the files modified in the last 3 commits
bundle exec rubocop -a $(git diff --name-only HEAD~2 HEAD | grep .rb)

# same but drop the "back/" prefix from the file names
bundle exec rubocop -a $(git diff --name-only HEAD~2 HEAD | grep .rb | sed 's/^back\///')

# dig command to get the NS records for a domain (idee.haaltert.be)
dig NS idee.haaltert.be

# aws cli - list certificates in region us-east-1 (admin profile)
aws acm list-certificates --profile admin --region us-east-1


# get aws certificate details using aws cli in region us-east-1
aws acm describe-certificate --region us-east-1 --certificate-arn arn:aws:acm:us-east-1:389841910643:certificate/9efcaf5e-4239-4298-bed5-d84cd001d719

# same using the "admin" profile
aws acm describe-certificate --profile admin --region us-east-1 --certificate-arn arn:aws:acm:us-east-1:389841910643:certificate/9efcaf5e-4239-4298-bed5-d84cd001d719

# find the hosted zone for a domain (idee.haaltert.be)
aws --profile admin route53 list-hosted-zones-by-name --dns-name idee.haaltert.be