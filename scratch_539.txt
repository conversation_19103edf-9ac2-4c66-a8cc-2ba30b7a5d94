[TAN-4478] Allow total to be 0 in `Jobs::Tracker`

It's ok to queue a job for which the work estimate is 0 and tracking should not interfere. For instance, a job to copy ideas from one phase to another, where the source phase do not have any ideas. It's not very useful, but this should not fail. Also, for some jobs, we expect total to be used more as an estimate than an exact value (it really depends on the type of job and its implementation and the guarantees it provides regarding its progress and total estimates).