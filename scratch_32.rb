res = Tenant.all.filter_map do |tenant|
  tenant.switch do
    moderator = User.find_by(email: '<EMAIL>')
    next unless moderator

    [tenant.host, moderator] if moderator.confirmation_required?
  end
end.to_a

res = Tenant.all.filter_map do |tenant|
  tenant.switch do
    moderator = User.find_by(email: '<EMAIL>')
    next unless moderator

    if moderator.send(:confirmation_required)
      moderator.confirm!
      tenant.host
    end
  end
end
