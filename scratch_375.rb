# https://stadtgestalten.duesseldorf-marketing.de/de-DE/admin/projects/2250428c-ff13-4a1c-a813-8b24e406430f/phases/b662eb02-56df-4546-b1a3-c16a28834b46/native-survey
tenant = Tenant.find_by(host: 'stadtgestalten.duesseldorf-marketing.de')
tenant.switch!

phase = Phase.find('b662eb02-56df-4546-b1a3-c16a28834b46')
project = phase.project

activities = Activity.where(item: phase).order(created_at: :asc)
activities.pluck(:action)

activities.where(action: 'changed_participation_method')

# The participation method of the phase was changed from information to native_survey on June 28, 2024.

# https://denkmee.beverwijk.nl/nl-NL/admin/dashboard/visitors
tenant = Tenant.find_by(host: 'denkmee.beverwijk.nl')
tenant.switch!

session = ImpactTracking::Session.minimum(:created_at)


# https://hello.saanich.ca/en/admin/projects/folders/bcd40769-7fea-4c2a-9842-beeaa1a6b995/projects
tenant = Tenant.find_by(host: 'hello.saanich.ca')
tenant.switch!

folder = ProjectFolders::Folder.find('bcd40769-7fea-4c2a-9842-beeaa1a6b995')
project = Project.find("6fc1d4c6-20a8-4039-8016-901ea8c26800")
Project.all.any? { |project| !project.valid? }

# https://denkmee.hollandskroon.nl/web_api/v1/projects/4f607230-355e-4cb1-a8e2-a099f1a101f5/phases
tenant = Tenant.find_by(host: 'denkmee.hollandskroon.nl')
tenant.switch!

project = Project.find("4f607230-355e-4cb1-a8e2-a099f1a101f5")
phases = project.phases

