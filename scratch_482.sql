SELECT "users".*
FROM "users"
WHERE "users"."id" IN (
    SELECT "users"."id"
    FROM "users"
             INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
    WHERE "users"."id" IN (
        SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $1))
  AND "users"."id" NOT IN (
    SELECT "users"."id"
    FROM "users"
    WHERE "users"."id" IN (
        SELECT "users"."id"
        FROM "users"
                 INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
        WHERE "users"."id" IN (
            SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $2))
      AND "users"."id" IN (
        SELECT "users"."id"
        FROM "users"
                 INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
        WHERE "users"."id" IN (
            SELECT "users"."id"
            FROM "users"
                     INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
            WHERE "users"."id" IN (
                SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $3))
          AND "users"."id" IN (
            SELECT "followers"."user_id"
            FROM "followers"
            WHERE "followers"."followable_id" = $4
              AND "followers"."followable_type" = $5)))
  AND "users"."id" NOT IN (
    SELECT "users"."id"
    FROM "users"
    WHERE "users"."id" IN (
        SELECT "users"."id"
        FROM "users"
                 INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
        WHERE "users"."id" IN (
            SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $6))
      AND "users"."id" NOT IN (
        SELECT "users"."id"
        FROM "users"
        WHERE "users"."id" IN (
            SELECT "users"."id"
            FROM "users"
                     INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
            WHERE "users"."id" IN (
                SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $7))
          AND "users"."id" IN (
            SELECT "users"."id"
            FROM "users"
                     INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
            WHERE "users"."id" IN (
                SELECT "users"."id"
                FROM "users"
                         INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
                WHERE "users"."id" IN (
                    SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $8))
              AND "users"."id" IN (
                SELECT "followers"."user_id"
                FROM "followers"
                WHERE "followers"."followable_id" = $9
                  AND "followers"."followable_type" = $10)))
      AND "users"."id" IN (
        SELECT "users"."id"
        FROM "users"
                 INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
        WHERE "users"."id" IN (
            SELECT "users"."id"
            FROM "users"
                     INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
            WHERE "users"."id" IN (
                SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $11))
          AND "users"."id" NOT IN (
            SELECT "users"."id"
            FROM "users"
            WHERE "users"."id" IN (
                SELECT "users"."id"
                FROM "users"
                         INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
                WHERE "users"."id" IN (
                    SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $12))
              AND "users"."id" IN (
                SELECT "users"."id"
                FROM "users"
                         INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
                WHERE "users"."id" IN (
                    SELECT "users"."id"
                    FROM "users"
                             INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
                    WHERE "users"."id" IN (
                        SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $13))
                  AND "users"."id" IN (
                    SELECT "followers"."user_id"
                    FROM "followers"
                    WHERE "followers"."followable_id" = $14
                      AND "followers"."followable_type" = $15)))
          AND "users"."id" IN (
            SELECT "followers"."user_id"
            FROM "followers"
            WHERE "followers"."followable_id" = $16
              AND "followers"."followable_type" = $17)))
  AND "users"."id" NOT IN (
    SELECT "users"."id"
    FROM "users"
    WHERE "users"."id" IN (
        SELECT "users"."id"
        FROM "users"
                 INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
        WHERE "users"."id" IN (
            SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $18))
      AND "users"."id" NOT IN (
        SELECT "users"."id"
        FROM "users"
        WHERE "users"."id" IN (
            SELECT "users"."id"
            FROM "users"
                     INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
            WHERE "users"."id" IN (
                SELECT "followers"."user_id" FROM "followers" WHERE "followers"."followable_id" = $19))
          AND "users"."id" IN (
            SELECT "users"."id"
            FROM "users"
                     INNER JOIN "followers" ON "followers"."user_id" = "users"."id"
            WHERE "user...