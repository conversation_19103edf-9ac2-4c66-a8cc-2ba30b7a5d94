docker pull cimg/ruby:3.3-node

# mount front directory
docker run --rm -it -v ./front:/front -w /front cimg/ruby:3.3-node /bin/bash

# docker stats: cpu
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}\t{{.PIDs}}"

# v7.1.0
gem install license_finder -v 7.1.0

# show the cache path  of node
npm config get cache

# check memory usage
free -m

# speed up npm ci
npm ci --prefer-offline --no-audit --progress=false
