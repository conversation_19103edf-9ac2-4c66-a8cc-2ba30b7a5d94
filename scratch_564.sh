@Amanda A, I just updated TAN-4829/files-endpoints. It adds:
- the uploader and project filter parameters on the index, despite their names they actually support both single value and arrays.
- the project  parameter when creating a new file (POST) + the projects relationship in the serialized files.
- the `mime_type` in the serialized jobs (maybe not that useful)
- the `sort` parmeter on the index. It currently supports sorting by `created_at`, `name` and `size`. As I mentioned in the showcase, it can also be used for multisort (e.g. `sort: 'name,-size'`)