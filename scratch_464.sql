SELECT "phases".*
FROM "phases"
         INNER JOIN "custom_forms" ON "custom_forms"."participation_context_type" = 'Phase' AND
                                      "custom_forms"."participation_context_id" = "phases"."id"
         INNER JOIN "custom_fields" ON "custom_fields"."resource_type" = 'CustomForm' AND
                                       "custom_fields"."resource_id" = "custom_forms"."id"
WHERE "phases"."participation_method" != 'native_survey'
  AND "custom_fields"."input_type" = 'page'
GROUP BY "phases"."id"
HAVING (COUNT(*) = 1)


SELECT "custom_fields".*
FROM "custom_fields"
         INNER JOIN "custom_forms" ON "custom_forms"."id" = "custom_fields"."resource_id"
WHERE "custom_fields"."input_type" = 'page'
  AND "custom_fields"."key" = 'survey_end'
  AND "custom_forms"."participation_context_id" IN (
    SELECT "phases"."id"
    FROM "phases"
             INNER JOIN "custom_forms" ON "custom_forms"."participation_context_type" = 'Phase' AND
                                          "custom_forms"."participation_context_id" = "phases"."id"
             INNER JOIN "custom_fields" ON "custom_fields"."resource_type" = 'CustomForm' AND
                                           "custom_fields"."resource_id" = "custom_forms"."id"
    WHERE "phases"."participation_method" != 'native_survey'
      AND "custom_fields"."input_type" = 'page'
    GROUP BY "phases"."id"
    HAVING (COUNT(*) = 1))
