SELECT "files".*
FROM (
         SELECT files.*, 1.0 AS pg_search_rank
         FROM "files"
         WHERE (name ILIKE '%import meet source allocat%')
         UNION
         SELECT files.*, pg_search_3d7db37d08f9140fd09f12.rank AS pg_search_rank
         FROM "files"
                  INNER JOIN (
             SELECT
                 "files"."id"                                                      AS pg_search_id,
                 (ts_rank((to_tsvector('simple', coalesce("files"."name"::text, '')) ||
                           to_tsvector('simple', coalesce("files"."description_multiloc"::text, ''))),
                          (to_tsquery('simple', ''' ' || 'import' || ' ''') &&
                           to_tsquery('simple', ''' ' || 'meet' || ' ''') &&
                           to_tsquery('simple', ''' ' || 'source' || ' ''') &&
                           to_tsquery('simple', ''' ' || 'allocat' || ' ''')), 0)) AS rank
             FROM "files"
             WHERE ('import meet source allocat' % (coalesce("files"."name"::text, '') || ' ' ||
                                                    coalesce("files"."description_multiloc"::text, '')))) AS pg_search_3d7db37d08f9140fd09f12
                             ON "files"."id" = pg_search_3d7db37d08f9140fd09f12.pg_search_id
         WHERE "files"."id" NOT IN (
             SELECT "files"."id" FROM "files" WHERE (name ILIKE '%import meet source allocat%'))) AS files
ORDER BY pg_search_rank DESC

select 'mportan eetin bou udge lannin esourc llocatio lanificati'% (coalesce('meeting_notes.pdf'::text, '') || ' ' || coalesce('{"en"=>"Important meeting about budget planning and resource allocation", "fr-FR"=>"Réunion importante sur la planification budgétaire"}'::text, ''));