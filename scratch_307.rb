let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:end_at) { timezone.at(now - 1.month).end_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
let(:start_at) { timezone.at(now - 1.month).beginning_of_month }
travel_to(timezone.at(now - 1.month).beginning_of_month - 1.day) do
travel_to(timezone.at(now - 1.month).beginning_of_month - 1.day) do
travel_to(timezone.at(now - 5.months).beginning_of_month + 1.day) do
