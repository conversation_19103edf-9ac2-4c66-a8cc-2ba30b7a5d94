  - run:
      name: <PERSON><PERSON> Docker image and run pre-deployment tasks
      command: |
        ssh << parameters.ssh_user >>@<< parameters.ssh_host >> -o StrictHostKeyChecking=no <<EOF
        docker pull citizenlabdotco/back-ee:$CIRCLE_BRANCH && \
        docker run \
          --env-file cl2-deployment/<< parameters.env_file >> \
          citizenlabdotco/back-ee:$CIRCLE_BRANCH \
          bin/rake \
          db:migrate_if_pending \
          cl2back:clean_tenant_settings \
          email_campaigns:assure_campaign_records \
          fix_existing_tenants:update_permissions \
          cl2back:clear_cache_store \
          email_campaigns:remove_deprecated
        EOF