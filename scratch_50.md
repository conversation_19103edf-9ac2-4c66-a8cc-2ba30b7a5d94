# Hosting: improve reliability of docker swarms

This page is a working document that compiles a list of observations, issues, and avenues for improvement regarding the reliability of our docker swarms.

## task-history-limit is not respected

The `task-history-limit` should be set to 5 by default, but `docker service cl2_web ps` currently lists 8144 tasks on the eu cluster.

I don't have an explanation yet, but I have a strong suspicion that this is causing a lot of the instability issues we're experiencing, including errors like this:
```
Error response from daemon: rpc error: code = DeadlineExceeded desc = context deadline exceeded
```

## Remove `cl2-prd-bnlx-stack_integration_cdenv`?

## Nodes from the auto-scaling group do not leave the swarm

When the auto-scaling group scales down and terminates instances, the corresponding nodes do not leave the swarm explicitly. They are listed as down, but are still considered part of the swarm. While this probably doesn't cause issues, it's worth mentioning just in case.

According to the documentation for `docker swarm leave`:
> The node will still appear in the node list, and marked as down. It no longer affects swarm operation, but a long list of down nodes can clutter the node list. To remove an inactive node from the list, use the node rm command.

However, there's still some uncertainty about whether termination has the same effect as leaving.

## Error: `rpc error: code = DeadlineExceeded desc = context deadline exceeded`

Error response from daemon: rpc error: code = DeadlineExceeded desc = context deadline exceeded

## Align docker versions

Ensure that Docker versions are aligned among manager and worker nodes, as well as across clusters.

## Align the OS versions

Make sure that the OS versions are aligned and locked in the Terraform configuration.

## Strategy for updating the dependencies

## Adapt placement constraints for `nginx-prerender-proxy_prerender-proxy`

Adapt the placement constraints for `nginx-prerender-proxy_prerender-proxy` to ensure that several tasks are not scheduled on the same node. We could also require that those tasks are only scheduled on manager nodes. This would prevent the prerender proxy to be scheduled on the instances of the auto-scaling group.

## Disk space issues

Make sure it's solved once and for all. However, in order to fully address this type of issue, we should also improve the observability of the system.
