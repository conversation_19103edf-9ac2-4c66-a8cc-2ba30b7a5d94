# Bad users
# ["mailhanne<PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>"]

# Remove avatars
users = User.where.not(avatar: nil)
users.each do |user|
  user.remove_avatar!
  user.save!
end

# Bad user:
# uploads/b5c8ca87-529e-424e-b04a-1d39d75e50d3/user/avatar/0bf3 4e90-d31f-4f83-8595-757c4dfaf49f/2e775ae6-1c1e-4cb1-a67d-fcfb6c651a29.png
# + see screenshot on Desktop

# Attributes:
# {"id"=>"0bf34e90-d31f-4f83-8595-757c4dfaf49f",
#  "email"=>"<EMAIL>",
#  "password_digest"=>"$2a$12$t7APo2b46eb0CKuK0H42leXOJtJS2wMdF91qAKk8pgF70L2.TgAN.",
#  "slug"=>"stefan-schoenmaker",
#  "roles"=>[],
#  "reset_password_token"=>nil,
#  "created_at"=>Tue, 19 Mar 2024 15:47:31.147146000 UTC +00:00,
#  "updated_at"=>Tue, 19 Mar 2024 15:47:53.810243000 UTC +00:00,
#  "avatar"=>"2e775ae6-1c1e-4cb1-a67d-fcfb6c651a29.png",
#  "first_name"=>"Stefan",
#  "last_name"=>"Schoenmaker",
#  "locale"=>"nl-NL",
#  "bio_multiloc"=>{},
#  "cl1_migrated"=>false,
#  "invite_status"=>nil,
#  "custom_field_values"=>{"domicile"=>"269adce3-5b15-4a4d-9c58-48ee2cfcf8ca"},
#  "registration_completed_at"=>Tue, 19 Mar 2024 15:47:47.663341000 UTC +00:00,
#  "verified"=>false,
#  "email_confirmed_at"=>Tue, 19 Mar 2024 15:47:47.662287000 UTC +00:00,
#  "email_confirmation_code"=>"0880",
#  "email_confirmation_retry_count"=>0,
#  "email_confirmation_code_reset_count"=>0,
#  "email_confirmation_code_sent_at"=>Tue, 19 Mar 2024 15:47:32.440654000 UTC +00:00,
#  "confirmation_required"=>false,
#  "block_start_at"=>nil,
#  "block_reason"=>nil,
#  "new_email"=>nil,
#  "block_end_at"=>nil,
#  "followings_count"=>1,
#  "onboarding"=>{},
#  "unique_code"=>nil}
hash = Digest::MD5.hexdigest("<EMAIL>")