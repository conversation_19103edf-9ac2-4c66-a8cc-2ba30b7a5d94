Not without checking the numbers myself. We could enable some additional logging, but that comes with a cost, so we should do it sparingly. I have a personal VPN subscription—I'll check if it has servers in Peru.

Aside from that, I’d just echo what <PERSON> mentioned: the sooner they report it, the better. Most of our logs and metrics have limited retention policies. The best is of course to report it while the incident is still happening, but if that's not possible, getting it during the next week is already much better.

------------------------------------------------------------

What changes do you suggest for the next cycle?

If we’re going to continue handling bug duty and platform work together (and right now, it’s clear we don’t have enough capacity to do things differently — or maybe... see below), we need to find a way to create a bit of focused time for platform tasks when needed. It’s especially difficult, I think, when you’re the first point of contact for bugs since you’re constantly getting pinged on Slack.

Another thought: we could introduce more continuity in the tandem by rotating just one person at a time and shortening the bug duty tandem to half the length of a regular one. There could even be two rotations per cycle, depending on whether we want to maintain the bug duty tandem during cooldown and platform weeks. The idea is that the reboot at the beginning of each cycle is costing us some time and energy, and makes it more difficult to continously improve the process.

But the question is: how do we implement this rotation? Where do we assign the person rotating out, and from which tandem do we bring someone in? Here, we are hitting one limitation of our tandem workflow: we don't have many of them, so they all need to be in sync to allow for good permutations. I don't think this would as much of an issue if we had, say, 8 or 10 tandems (maybe something to keep in mind for the future).

That said, all hope is not lost, and maybe we could play with bite-sized tandems or even introduce (bite-sized) solo platform work. The latter could be a good way to get focused time on platform tasks, and the quality quest would be replaced by a full-blown bug duty tandem. Also, in my experience, platform work is often more focused on one part of the stack, and platform tandems in the past had very minimal collaboration between back and front end. That's a bit of pity, but we could use it to our advantage here.

There were too many status updates. We had the weekly prep and planning sessions, time tracking, daily stand-ups, managing the board, and posting individual updates on each ticket (which we did — I think — very thoroughly) + we had another mid-week ping in Slack for another update, which I never responded to (sorry) because I felt it was going too far.

Other attention point: Keep overhead under control.
- Is time tracking really worth it?
- How can we reduce time spent on status updates while still keeping support in the loop? On the latter, I think during the week, most of that communication should take place in the ticket itself.
- Do we need a daily standup every day? In our last tandem, for instance, we had the planning session on Monday afternoon and a daily standup on Tuesday morning.

