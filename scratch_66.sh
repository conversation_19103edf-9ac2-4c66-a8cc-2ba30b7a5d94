# run rubocop on all files that were modified compared to master
git diff --name-only master | grep .rb | xargs bundle exec rubocop -a

# same as above, but with full paths
git diff --name-only master | grep .rb | xargs -I{} bundle exec rubocop -a $(git rev-parse --show-toplevel)/{}


bundle exec rubocop -a Gemfile Gemfile.lock app/controllers/web_api/v1/events_controller.rb app/mailers/application_mailer.rb app/models/event.rb app/models/initiative.rb app/services/events/ics_generator.rb app/services/multiloc_service.rb engines/free/email_campaigns/app/mailers/email_campaigns/manual_campaign_mailer.rb lib/participation_method/ideation.rb spec/acceptance/events_spec.rb spec/factories/events.rb spec/services/events/ics_generator_spec.rb spec/services/multiloc_service_spec.rb
