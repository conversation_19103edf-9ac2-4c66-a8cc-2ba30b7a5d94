[TAN-1939] Use CurrentAttributes to cache app_configuration

This is our second attempt to cache app_configuration to improve application performance. The first attempt was buggy because it used a class instance variable, which is not thread-safe. This new implementation uses CurrentAttributes instead.

The tenant and app_configuration are now lazily loaded when needed, i.e., when accessing Current.tenant or Current.app_configuration. As a result, we no longer need to explicitly set the tenant or app_configuration (allowing us to remove the `set_current_tenant` callback from ApplicationController). This "lazy-caching" approach has the advantage to also work outside of the request-response cycle, e.g., in background jobs or rake tasks, without the need to set the tenant or app_configuration explicitly.


