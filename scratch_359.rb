# project: aadba4cf-176b-4ce8-874b-0e6a79ef571a
# phase: c59cc6f3-5a82-4ad2-b1bb-838b33343a3a

project = Project.find("aadba4cf-176b-4ce8-874b-0e6a79ef571a")
phase = Phase.find("c59cc6f3-5a82-4ad2-b1bb-838b33343a3a")

User.find_by(first_name: "<PERSON> <PERSON>", last_name: "<PERSON>")

# user_requirements_service = params[:user_requirements_service] || Permissions::UserRequirementsService.new(check_groups: false)
user_requirements_service = Permissions::UserRequirementsService.new(check_groups: false)
service = Permissions::ProjectPermissionsService.new(project, user, user_requirements_service: user_requirements_service)
service.denied_reason_for_action('posting_idea')


# You have already completed this survey. Thanks for your response!

# Is it considered as a survey?
# It also has native_survey_title_multiloc and native_survey_button_multiloc set.

phase
# #<Phase:0x000077020df6a000
#  id: "c59cc6f3-5a82-4ad2-b1bb-838b33343a3a",
#  project_id: "aadba4cf-176b-4ce8-874b-0e6a79ef571a",
#  title_multiloc: {"en-GB"=>"Tanner Street to Willow Walk Cycle Route"},
#  description_multiloc:
#   {"en-GB"=>
#     "<p>We would like to hear from directly affected residents and businesses, to feed in to the design for both cycling and walking improvements.</p><ul>\n<li>What cycling or walking improvements can we make along this proposed route?</li>\n<li>Are there any existing issues with the roads at the moment?</li>\n<li>Things we can fix when setting up the cycle route?</li>\n<li>Are there any places that are particularly difficult for cyclists or pedestrians that we could improve?</li>\n<li>What are your thoughts on the current ideas for development?</li>\n</ul><p>Currently we are in early stages of designing the route and so are engaging early in the process with local residents to hear their thoughts. There will be a full consultation later in the year when we will invite comments on a proposed outline design.</p>"},
#  start_at: Mon, 08 Jul 2024,
#  end_at: Fri, 02 Aug 2024,
#  created_at: Tue, 07 May 2024 10:29:25.291110000 UTC +00:00,
#  updated_at: Thu, 25 Jul 2024 08:28:29.089780000 UTC +00:00,
#  participation_method: "ideation",
#  posting_enabled: true,
#  commenting_enabled: false,
#  reacting_enabled: false,
#  reacting_like_method: "unlimited",
#  reacting_like_limited_max: 10,
#  survey_embed_url: nil,
#  survey_service: nil,
#  presentation_mode: "map",
#  voting_max_total: nil,
#  poll_anonymous: false,
#  reacting_dislike_enabled: true,
#  ideas_count: 11,
#  ideas_order: "random",
#  input_term: "issue",
#  voting_min_total: 0,
#  reacting_dislike_method: "unlimited",
#  reacting_dislike_limited_max: 10,
#  posting_method: "limited",
#  posting_limited_max: 1,
#  allow_anonymous_participation: true,
#  document_annotation_embed_url: nil,
#  campaigns_settings: {"project_phase_started"=>true},
#  voting_method: nil,
#  voting_max_votes_per_idea: nil,
#  voting_term_singular_multiloc: {},
#  voting_term_plural_multiloc: {},
#  baskets_count: 0,
#  votes_count: 0,
#  native_survey_title_multiloc: {"en-GB"=>"Your thoughts on the proposed cycle route"},
#  native_survey_button_multiloc: {"en-GB"=>"Share your thoughts"}>