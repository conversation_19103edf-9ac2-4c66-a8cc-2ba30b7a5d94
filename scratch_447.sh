git show b193d05f97f25b8f6112ac195787fcda78139163 | grep "module \"tenant_domain"
#+module "tenant_domain_ae7c915a-c43e-40da-840e-31577a111f73" {
#+module "tenant_domain_729c1c4f-f09b-4942-b513-9158d91005c3" {
#+module "tenant_domain_789f9e9c-e6ad-41fc-bba8-a8fde4b45439" {
#+module "tenant_domain_14a7c37c-c925-45b7-9f42-29b97330dbe4" {
#+module "tenant_domain_5410e656-c434-434a-89e5-73e1fe7966c1" {

# keep the middle column and strip the quotes
git show b193d05f97f25b8f6112ac195787fcda78139163 | grep "module \"tenant_domain" | awk '{print $2}' | sed 's/"//g'
#"tenant_domain_ae7c915a-c43e-40da-840e-31577a111f73
#"tenant_domain_729c1c4f-f09b-4942-b513-9158d91005c3"
#"tenant_domain_789f9e9c-e6ad-41fc-bba8-a8fde4b45439"
#"tenant_domain_14a7c37c-c925-45b7-9f42-29b97330dbe4"

# prefix the output with -target= and add "module." to the beginning and add the closing quote
git show b193d05f97f25b8f6112ac195787fcda78139163 | grep "module \"tenant_domain" | awk '{print $2}' | sed 's/"//g' | sed 's/^/-target="module./g' | sed 's/$/"/g'
#-target="module.tenant_domain_ae7c915a-c43e-40da-840e-31577a111f73"
#-target="module.tenant_domain_729c1c4f-f09b-4942-b513-9158d91005c3"
#-target="module.tenant_domain_789f9e9c-e6ad-41fc-bba8-a8fde4b45439"
#-target="module.tenant_domain_14a7c37c-c925-45b7-9f42-29b97330dbe4"

# join the lines with a space
git show b193d05f97f25b8f6112ac195787fcda78139163 | grep "module \"tenant_domain" | awk '{print $2}' | sed 's/"//g' | sed 's/^/-target="module./g' | sed 's/$/"/g' | tr '\n' ' '


