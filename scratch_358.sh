# run the following command inside of a running container but do no wait for it to finish:
#   cd /cl2_script_application_to_analytics && ./crontask.sh >> /var/log/cron.log 2>&1

docker exec -d my_container_name /bin/bash -c "cd /cl2_script_application_to_analytics && ./crontask.sh >> /var/log/cron.log 2>&1"
docker exec -d cl2-global_script-cl2back-to-analytics-prd.1.y5x861md5jonrr1dtp5ezq8gb /bin/bash -c "cd /cl2_script_application_to_analytics && ./crontask.sh >> /var/log/cron.log 2>&1"