def spherical_factory(opts = {})
  coord_sys = opts[:coord_sys]
  srid = opts[:srid]
  srid ||= coord_sys.authority_code if coord_sys
  Geographic::Factory.new(
    "Spherical",
    has_z_coordinate: opts[:has_z_coordinate],
    has_m_coordinate: opts[:has_m_coordinate],
    coord_sys: coord_sys || coord_sys4055,
    buffer_resolution: opts[:buffer_resolution],
    wkt_parser: opts[:wkt_parser],
    wkb_parser: opts[:wkb_parser],
    wkt_generator: opts[:wkt_generator],
    wkb_generator: opts[:wkb_generator],
    srid: (srid || 4055).to_i
  )
end


RGeo::Geographic.spherical_factory({
  has_z_coordinate: true,
  has_m_coordinate: true,
}).point(...)