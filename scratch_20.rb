class H
  extend CitizenLab::Mixins::FeatureSpecification

  def self.feature_name
    'feature_name'
  end

  def self.feature_title
    'Feature title'
  end

  add_setting 'new_setting', required: true, schema: {
    'title': 'Enabled Fragments',
    'type': 'array',
    'default': [],
    'description': "List of fragment names that should be enabled (e.g., 'signed-out-header').",
    'items': {
      'type': 'string'
    }
  }
end

module MyEngine
  module Feature1
    module Specification
      extend CitizenLab::Mixins::FeatureSpecification

      def self.feature_name
        'feature_1'
      end

      def self.feature_title
        'Feature title'
      end

      def self.feature_description # optional
        'Feature description'
      end

      add_setting 'setting_1', required: true, schema: { 'type' => 'boolean' }
      add_setting 'setting_2', schema: { 'type' => 'boolean' } # required is false by default
    end
  end
end

AppConfiguration::Settings.add_feature(MyEngine::Feature1::Specification)
