# run terraform plan locally using state on Terraform Cloud
aws-vault exec dev-admin -- terraform plan -var-file="aws-development.tfvars" -out="planfile"
aws-vault exec dev-admin -- terraform apply planfile

# change workspace to "iam-project-aws-public"
terraform workspace select iam-project-aws-public
aws-vault exec dev-admin -- terraform plan -var-file="aws-public.tfvars" -out="planfile"



# workspace is "iam-project-aws-development"
terraform init -backend-config="workspace=iam-project-aws-development"