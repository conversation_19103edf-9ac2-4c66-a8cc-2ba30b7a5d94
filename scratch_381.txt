diff --git a/dangerfile.ts b/dangerfile.ts
index 943cc80ab03..207a79d6ad2 100644
--- a/dangerfile.ts
+++ b/dangerfile.ts
@@ -28,9 +28,9 @@ if (branchKeys.length === 0) {
   warn("The branch name contains no Jira issue key (case-sensitive)");
 }

-new Set([...branchKeys, ...prKeys]).forEach((jiraKey) => {
+new Set([...branchKeys, ...prKeys]).forEach((key) => {
   message(
-    `Jira issue: <a href="https://citizenlab.atlassian.net/browse/${jiraKey}">${jiraKey}</a>`
+    `Notion issue: <a href="https://notion.so/${key}">${key}</a>`
   );
 });


Commit message:
Fix links to tickets in Dangerfile

The links produced by Danger were pointing to Jira tickets, but we are using Notion for tracking now.

