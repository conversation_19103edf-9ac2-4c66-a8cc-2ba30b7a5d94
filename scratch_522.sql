SELECT "ideas".* FROM "ideas" WHERE "ideas"."publication_status" IN ('submitted', 'published') AND ("ideas"."creation_phase_id" IN (SELECT "phases"."id" FROM "phases" WHERE "phases"."participation_method" IN ('common_ground', 'ideation', 'proposals', 'voting')) OR "ideas"."creation_phase_id" IS NULL)

SELECT "ideas".* FROM "ideas" WHERE "ideas"."publication_status" IN ('submitted', 'published') AND ("ideas"."creation_phase_id" IN (SELECT "phases"."id" FROM "phases" WHERE "phases"."participation_method" IN ('common_ground', 'ideation', 'proposals', 'voting')) OR "ideas"."creation_phase_id" IS NULL)
