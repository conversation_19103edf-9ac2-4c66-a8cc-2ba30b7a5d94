FUNCTION que_determine_job_state(job public.que_jobs) RETURNS text AS $$
FUNCTION que_job_notify() R<PERSON><PERSON><PERSON> trigger AS $$
FUNCTION que_state_notify() R<PERSON>URNS trigger AS $$
FUNCTION que_validate_tags(tags_array jsonb) RETURNS boolean AS $$
INDEX que_jobs_args_gin_idx ON que_jobs USING gin (args jsonb_path_ops);
INDEX que_jobs_data_gin_idx ON que_jobs USING gin (data jsonb_path_ops);
INDEX que_poll_idx ON que_jobs (queue, priority, run_at, id) WHERE (finished_at IS NULL AND expired_at IS NULL);
INDEX que_poll_idx_with_job_schema_version
TABLE que_jobs
TABLE que_lockers (
TABLE que_values (
TRIGGER que_job_notify
TRIGGER que_state_notify
