@Anonymous How many users are impacted by this? Is it only about a single user? If so, I would tend to think that the problem is on the user's side and that something is wrong with their browser or network configuration. In any case, it's going to be hard to help without a more explicit error message.

Nevertheless, here are a few things to try out:
- Open the website in a private/incognito window. If it works, they should probably clear their browser cache.
- Restart the computer and try again.
- Click the `Trust` toggle in your first screenshot and see if they can find a more precise error message.
- Check their browser here: https://clienttest.ssllabs.com:8443/ssltest/viewMyClient.html

> It seems that the domain forwarding is causing a red flag with this individual’s browser security. When we share the (custom domain) link to the platform, perhaps we should also share the citizenlab domain as an alternative?

- What domain forwarding are they referring to?
- What do they mean by the "custom" domain and the "citizenlab" domain? The platforms can only have a single domain.