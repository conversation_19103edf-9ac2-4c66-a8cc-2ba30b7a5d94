ActivitiesService

before-all hook in spec_helper.rb: Switching from public to example_org
before-context hook in spec_helper.rb: Switching from example_org to example_org

  #create_periodic_activities
    #create_phase_started_activities

before-hook in create_phase_started_activities: Setting timezone from Europe/Brussels to Asia/Kamchatka
before hook in spec_helper.rb: Switching from example_org to example_org

      logs phase started activity when a new phase starts (in the application timezone)


    #create_phase_upcoming_activities
before-hook in spec_helper.rb
Setting timezone from Asia/Kamchatka to Asia/Kamchatka
before hook in spec_helper.rb
Switching from example_org to example_org
      logs phase upcoming activity when a new phase starts in a week (in the application timezone)

