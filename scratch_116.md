```
ubuntu@ip-172-31-24-234:~$ docker node ls | grep Ready
yy2dbeijoa40tbexz3np2bgcd     ip-172-31-17-86    Ready     Active         Reachable        20.10.16
c5su9zhxgxtsrgkxgrf4z8g59     ip-172-31-21-138   Ready     Active                          20.10.8
43ohv3sokg3ycfs121nwkxr23 *   ip-172-31-24-234   Ready     Active         Leader           20.10.16
jgpnjr2xvxddkzxpm821s89xi     ip-172-31-25-181   Ready     Active         Reachable        20.10.16
```

```
ubuntu@ip-172-31-21-138:~$ docker node ls | grep Ready
Error response from daemon: This node is not a swarm manager. Worker nodes can't be used to view or modify cluster state. Please run this command on a manager node or promote the current node to a manager.
```

Command to promote a node to manager:
```
docker node promote ip-172-31-21-138
```

```bash
node_ids=$(docker node ls | grep -v Leader | grep -v Ready | grep -v Status | grep Down | awk '{print $1}')
  
for node_id in $node_ids; do
  docker node rm $node_id
done
```

```
# time the execution of: docker service ps ejx3xqsc4aub
time docker service ps ejx3xqsc4aub
```


# only workers that are down 
docker node ls --filter "role=worker" | grep Down | awk '{print $1}' | xargs -t -n25 docker node rm

# cronjob to remove down nodes, once every day at 12:00
0 12 * * * docker node ls --filter "role=worker" | grep Down | awk '{print $1}' | xargs -t -n25 docker node rm