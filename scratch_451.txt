Tip to speed up the Terraform runs

The `tenants` workspace manages many resources (almost 4k resources at the time of writing). As a result, runs take a long time. One way to speed up the runs is to use "resource targeting". When targeting is in effect, Terraform will essentially focus on the resources that are being targeted, and ignore the rest.

To enable targeting in a workspace on Terraform cloud, you have to set the `TF_CLI_ARGS` environment variable to `-target=<resource_type.resource_name>`. You can specify the `-target` option multiple times to target multiple resources. This needs to be set up before the run is triggered to take effect (e.g., before the pull request is created or merged). If you forgot and don’t want to wait, you can cancel the run and restart it with `TF_CLI_ARGS` set.

Another thing to note is that this will affect all the runs in the workspace, not just "yours". So, try to think to remove the `TF_CLI_ARGS` after you are done.

For example, you want to target the following resources :
```terraform
module "tenant_domain_ba6e236f-aac4-43c0-bd54-ef05ce9c3c25" {
  source = "./modules/cl_tenant_domain"
  # ...
}

module "redirect_ba6e236f-aac4-43c0-bd54-ef05ce9c3c25" {
  source      = "./modules/cl_redirect"
  # ...
}

resource "aws_route53_record" "tenant_ba6e236f-aac4-43c0-bd54-ef05ce9c3c25_extra_mx" {
  # ...
}

resource "foo" "bar" {
  # ...
}
```
the value of `TF_CLI_ARGS` should be:
```
-target="module.tenant_domain_ba6e236f-aac4-43c0-bd54-ef05ce9c3c25" -target="module.redirect_ba6e236f-aac4-43c0-bd54-ef05ce9c3c25" -target="aws_route53_record.tenant_ba6e236f-aac4-43c0-bd54-ef05ce9c3c25_extra_mx" -target="foo.bar"
```
(as a single line).




Side remark
Terraform (HashiCorp) discourages the use of the targeting feature. There are other potential solutions to this problem, such as splitting resources into different workspaces or exploring how we could leverage the new Terraform Stack feature.

However, targeting doesn’t require any changes to the current setup—it’s available immediately and for free. In contrast, using Stacks would require us to switch to a new plan and give up the benefits of our legacy plan (which is probably one of the reasons why they discourage the use of targeting in the first place).