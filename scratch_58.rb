# frozen_string_literal: true

# == Schema Information
#
# Table name: app_configurations
#
#  id         :uuid             not null, primary key
#  name       :string
#  host       :string
#  logo       :string
#  favicon    :string
#  settings   :jsonb
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  style      :jsonb
#
class AppConfiguration < ApplicationRecord
  # ...

  class << self
    private :new # We need a singleton

    def instance
      first!
    end

    def timezone
      timezone_str = instance.settings.dig('core', 'timezone')
      ActiveSupport::TimeZone[timezone_str] || (raise KeyError, timezone_str)
    end
  end

  # ...
end


# Assuming AppConfiguration timezeon offset is +01:00 (e.g. Europe/Brussels)
AppConfiguration.timezone.parse('2019-01-01T12:00:00')  # => 2019-01-01 12:00:00 +0100
AppConfiguration.timezone.parse('2019-01-01T12:00:00Z') # => 2019-01-01 13:00:00 +0100