class WebApi::V1::TenantsController < WebApi::V1::AppConfigurationsController

  def current
    render json: ActiveSupport::JSON.encode(merge(tenant, app_configuration(:show?)))
    response.set_header('Warning', '299 - "Deprecated API"')
  end

  def update
    # TODO OS what should we do tenant side fx
    # SideFxTenantService.new.before_update tenant, current_user
    if update_configuration(app_configuration, config_params)
      # SideFxTenantService.new.after_update tenant, current_user
      render json: ActiveSupport::JSON.encode(merge(tenant, app_configuration))
    else
      render json: {errors: app_configuration.errors.details}, status: :unprocessable_entity
    end
    response.set_header('Warning', '299 - "Deprecated API"')
  end

  private

  def secure_controller?
    false
  end

  def tenant
    @tenant ||= authorize Tenant.current
  end

  # Produces a "legacy tenant" by merging it with a configuration object.
  #
  # @param [Tenant] tenant
  # @param [AppConfiguration] configuration
  # @return [Hash] serializable hast
  def merge(tenant, configuration)
    tenant_hash = WebApi::V1::TenantSerializer.new(tenant).serializable_hash
    configuration_hash = WebApi::V1::AppConfigurationSerializer.new(configuration).serializable_hash
    tenant_hash[:data][:attributes].deep_merge!(configuration_hash[:data][:attributes])
    tenant_hash
  end

  def config_params
    return @config_params if @config_params
    @config_params = params.require(:tenant).permit(:logo, :header_bg, :favicon, settings:{}, style: {})
  end

  def app_configuration(action=nil)
    return @app_configuration if @app_configuration
    @app_configuration = authorize AppConfiguration.instance, action
  end

end
