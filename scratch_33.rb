Tenant.find_by(host: 'participa.muniarica.cl').switch!

project_id = "345b6540-114e-40b1-b2ce-9991d45a60f0"
xlsx = XlsxExport::GeneratorService.new.generate_inputs_for_project(project_id, true)
File.open("participa_muniarica_cl.inputs.xlsx", "wb") do |f|
  f.write(xlsx.read)
end

# copy file from docker container to host
# docker cp 6f0b86abd22b:/cl2_back/participa_muniarica_cl.inputs.xlsx participa_muniarica_cl.inputs.xlsx

# copy from server to local with scp
# scp aws-sam-1:/home/<USER>/participa_muniarica_cl.inputs.xlsx participa_muniarica_cl.inputs.xlsx


####

string_io = StringIO.new
# write StringIO to file
File.open("participa_muniarica_cl.inputs.xlsx", "wb") do |f|

end

ldsnxigassk