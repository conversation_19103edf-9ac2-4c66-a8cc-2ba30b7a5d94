locales = AppConfiguration.instance.settings('core', 'locales') << 'en' << 'es-ES'
chat = RubyLLM.chat(model: 'gpt-4.1')

summary_prompt = <<~PROMPT
Analyze the provided document and generate a concise description or abstract (2-3 sentences) that accurately summarizes its nature, main purpose, key content, and relevant context. 
The description should be informative and capture the essential meaning of the document.

Output the results as a properly formatted JSON object with the following requirements:
- Avoid starting the summary with "The document is..." or similar generic introductions.
- Keys must be the following locale codes: #{locales.join(', ')}
- Values must be the description text accurately translated for each respective locale
- All translations should maintain the same meaning and level of detail
- Use proper JSON formatting with double quotes around all keys and string values
- Ensure the JSON is valid and parseable

The response must contain ONLY the JSON object - no additional text, explanations, code blocks, or formatting outside the JSON structure.

Example format:
```json
#{JSON.pretty_generate(locales.map { |locale| [locale, "... (summary in #{locale}) ..."] }.to_h)}
```
PROMPT

file_path = Rails.root.join('spec/fixtures/roth2006.pdf')
response = chat.ask(summary_prompt, with: file_path.to_s)
puts JSON.pretty_generate(JSON.parse(response.content))

