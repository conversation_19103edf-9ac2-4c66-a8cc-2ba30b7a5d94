What development goals will I focus on in the next 6 months?

Goal 1: Improve my working knowledge of Kubernetes
Goal 2: Take more initiatives in the context of the BE chapter



How can you improve this skill?

What does success looks like?

Goal 1:
- Finish reading Production Kubernetes, <PERSON> et al.
- Run the citizenlab stack on a playground k8s

Goal 2:
- Being more vocal about issues or potential improvements in the BE




