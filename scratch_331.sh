# get the ssl certificate from aws arn: arn:aws:acm:us-east-1:389841910643:certificate/d4862b5c-c860-4ac0-9298-42bf3dbaf380
# regions is us-east-1

aws-vault exec prd-admin -- aws acm describe-certificate --certificate-arn arn:aws:acm:us-east-1:389841910643:certificate/d4862b5c-c860-4ac0-9298-42bf3dbaf380 --region us-east-1

# get the list of SubjectAlternativeNames (SANs) for the specified certificate
aws-vault exec prd-admin -- aws acm describe-certificate --certificate-arn arn:aws:acm:us-east-1:389841910643:certificate/d4862b5c-c860-4ac0-9298-42bf3dbaf380 --region us-east-1 | jq -r '.Certificate.SubjectAlternativeNames'

# do the same for a list of certificates:
#arn:aws:acm:us-east-1:389841910643:certificate/0b4d2c7c-9375-44c5-a003-a3b0d302fee6 sam
#arn:aws:acm:us-east-1:389841910643:certificate/148c91cf-bef3-4ec7-afd2-8d38cb834b0d europe
#arn:aws:acm:us-east-1:389841910643:certificate/7250229e-1727-4e59-a484-78c7ed0cbd68 can
#arn:aws:acm:us-east-1:389841910643:certificate/7250229e-1727-4e59-a484-78c7ed0cbd68 staging
#arn:aws:acm:us-east-1:389841910643:certificate/7250229e-1727-4e59-a484-78c7ed0cbd68 usw
#arn:aws:acm:us-east-1:389841910643:certificate/d4862b5c-c860-4ac0-9298-42bf3dbaf380 uk

certs=(
  "arn:aws:acm:us-east-1:389841910643:certificate/0b4d2c7c-9375-44c5-a003-a3b0d302fee6"
  "arn:aws:acm:us-east-1:389841910643:certificate/148c91cf-bef3-4ec7-afd2-8d38cb834b0d"
  "arn:aws:acm:us-east-1:389841910643:certificate/7250229e-1727-4e59-a484-78c7ed0cbd68"
  "arn:aws:acm:us-east-1:389841910643:certificate/d4862b5c-c860-4ac0-9298-42bf3dbaf380"
)

for cert in "${certs[@]}"; do
  echo "Certificate: $cert"
  aws-vault exec prd-admin -- aws acm describe-certificate --certificate-arn $cert --region us-east-1 | jq -r '.Certificate.SubjectAlternativeNames'
done