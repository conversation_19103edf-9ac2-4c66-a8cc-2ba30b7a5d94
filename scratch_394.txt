0 * * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec rake email_campaigns:schedule_email_campaigns
5 * * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec rake cl2back:hourly
10 4 * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec rake cl2back:shift_tenant_timestamps['1']
15 5 * * * /usr/bin/docker system prune -f -a
20 */12 * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec rake churned_tenants:remove_expired_pii

# Import Matomo visit data
0 0,3,6,9,12,15,18,21 * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec bin/rails runner -e production 'Analytics::ImportLatestMatomoDataJob.perform_for_all_tenants'




# Cron tasks execute at slightly offset minutes to prevent overlaps
# Launching multiple rake tasks at the same time led to out of memory errors in the past
0 * * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec rake email_campaigns:schedule_email_campaigns
5 * * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec rake cl2back:hourly
10 4 * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec rake cl2back:shift_tenant_timestamps['1']
15 5 * * * /usr/bin/docker system prune -f -a
20 */12 * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec rake churned_tenants:remove_expired_pii
# 20 2 * * 0 WH_AUTH_PART1="T0945ADL2" WH_AUTH_PART2="B02RG1QB8Q6" WH_AUTH_PART3="TrN7yFxdb8rZ4jgVv5das790" /home/<USER>/cl2-deployment/scripts/check-inconsistent-data.sh Benelux

# Import Matomo visit data
0 0,3,6,9,12,15,18,21 * * * docker run --rm --env-file /home/<USER>/cl2-deployment/.env-web citizenlabdotco/back-ee:production bundle exec bin/rails runner -e production 'Analytics::ImportLatestMatomoDataJob.perform_for_all_tenants'