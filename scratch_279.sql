SELECT "content_builder_layouts".*
FROM "content_builder_layouts"
WHERE ("content_builder_layouts"."id" IN (SELECT "content_builder_layouts"."id"
                                          FROM "content_builder_layouts"
                                                   CROSS JOIN jsonb_each(content_builder_layouts.craftjs_json) AS jsonb_each
                                          WHERE (jsonb_each.value -> 'type' ->> 'resolvedName' = 'Widget1')) OR
       "content_builder_layouts"."id" IN (SELECT "content_builder_layouts"."id"
                                          FROM "content_builder_layouts"
                                                   CROSS JOIN jsonb_each(content_builder_layouts.craftjs_json) AS jsonb_each
                                          WHERE (jsonb_each.value -> 'type' ->> 'resolvedName' = 'Widget2')))

SELECT "content_builder_layouts"."id"
FROM "content_builder_layouts"
         CROSS JOIN jsonb_each(content_builder_layouts.craftjs_json) AS jsonb_each
WHERE (jsonb_each.value -> 'type' ->> 'resolvedName' in 'Widget1')
