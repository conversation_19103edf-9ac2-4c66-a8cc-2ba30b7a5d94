
# TODO
- [ ] dont skip final snapshots for db (set db_skip_final_snapshot to false)
- [ ] review variables (e.g. cf distribution price class)
- [ ] Add protection against deletion
- [ ] Review and delete the security groups that are no longer in use
- [ ] add `delete_on_termination` on the root_block_device of the leader?

(en) Oh, nice. That's my boy!

(en) I know only two words.
(it) Io conosco solo due parole.

(en) I know only one word.
(it) Io conosco solo una parola.

(en) I don't believe it.
(it) Non ci credo.

(en) What?
(it) Cosa?

(fr) Je ne comprends pas.
(it) Non capisco.

# AWS
In the context of AWS, a delegation set is a group of four authoritative name servers assigned to a hosted zone. If you want to use the same name servers for multiple hosted zones, you create a reusable delegation set. Then, when you create a hosted zone, you can assign the delegation set to the new hosted zone. For more information, see Working with reusable delegation sets.

This is a way to tell which name servers are authoritative for a domain name. For example, if you register the domain name example.com, the four name servers in the delegation set are the ones that you give to the registrar to be listed in the parent DNS. The registrar then sends the name servers to the registry, which adds them to the parent DNS. When a DNS resolver queries the parent DNS for the name servers for example.com, the parent DNS returns the four name servers in the delegation set.


Get the NS servers for mitgestalten.wien.gv.at:
```
dig +short mitgestalten.wien.gv.at NS
```

Show all the steps of the DNS resolution:
```
dig +trace mitgestalten.wien.gv.at
```

Name servers
ns-2005.awsdns-58.co.uk
ns-837.awsdns-40.net
ns-280.awsdns-35.com
ns-1473.awsdns-56.org

where can i see all the delegation sets in the aws console?
= https://console.aws.amazon.com/route53/v2/delegationsets