I don’t see a direct link yet, but 

There was no similar peak the previous weeks. 


# AWS CloudWatch Insights

fields @message 
| sort @timestamp desc
| filter payload.status != 200
| filter message not like /sentry/

Same but count the total number rows

fields @message
| sort @timestamp desc
| filter payload.status != 200
| filter message not like /sentry/
| stats count()

Count by message value

fields @message
| sort @timestamp desc
| filter payload.status != 200
| filter message not like /sentry/
| stats count() by message

name the count column as "c"

fields @message
| sort @timestamp desc
| filter payload.status != 200
| filter message not like /sentry/
| stats count() as c by message
| sort c desc

filter record where payload.tenant_host is null/not present

fields @message
| sort @timestamp desc
| filter payload.status != 200
| filter message not like /sentry/
| filter payload.tenant_host is null

> The sudden shutdown of all containers could be caused by out of memory where docker will restart the containers.

Not all containers were restarted, only containers of `web` and `queue` services.