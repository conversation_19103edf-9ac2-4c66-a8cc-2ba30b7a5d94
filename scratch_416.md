# Notes (2024-10-02)

Lorsqu'un project est publié, un utilisateur non conecté peut:
- voir la description des phases d'information
- par contre, les rapports (publiés) ne sont visibles qu'une fois que la phase est active.

- What happens if a signed-in user uses the preview link?
- How are participation requirements applied when previewing the project?
- How not to pollute our activity table with action that are taken in preview mode?

- Action taken in preview mode should not affect participation statistics and should not be recorded in the activity table.
- The same routes would be used for getting resources in preview mode, but they should behave differently in terms of permissions.
- If we use temporary user accounts for previewing they must not be taken into accound in user statistics.

- Create a "Danger zone" section in the project settings page with:
  - Delete project button (should be removed from the dot menu in the project list)
  - Clear all participation data button 
    (main risk is that we forget to update this when there is a new type of participation)

- Review request:
  - You can pick a list of users to review the project before it is published.
  - Any admin can always publish the project
  - Moderator can publish the project only if all reviewers have approved it. 