We must check the default assignee when moving project to another folder
branch: TAN-2434/check-default-assignee-when-moving-project-to-different-folder

diff --git a/back/engines/commercial/idea_assignment/spec/acceptance/moderators_spec.rb b/back/engines/commercial/idea_assignment/spec/acceptance/moderators_spec.rb
index 69397c2b5e9..8f72835eede 100644
--- a/back/engines/commercial/idea_assignment/spec/acceptance/moderators_spec.rb
+++ b/back/engines/commercial/idea_assignment/spec/acceptance/moderators_spec.rb
@@ -48,7 +48,7 @@ resource 'Moderators' do
       end
     end

-    delete 'web_api/v1/project_folders/:project_folder_id/moderators/:user_id', if: defined?(ProjectFolders::Engine) do
+    delete 'web_api/v1/project_folders/:project_folder_id/moderators/:user_id' do
       ValidationErrorHelper.new.error_fields self, User

       let(:project1) { create(:project) }
diff --git a/back/engines/commercial/idea_assignment/spec/acceptance/projects_spec.rb b/back/engines/commercial/idea_assignment/spec/acceptance/projects_spec.rb
index 4b242e01f4f..9bcb0c3b3d5 100644
--- a/back/engines/commercial/idea_assignment/spec/acceptance/projects_spec.rb
+++ b/back/engines/commercial/idea_assignment/spec/acceptance/projects_spec.rb
@@ -26,7 +26,7 @@ resource 'Projects' do
       let(:project2) { create(:project, folder: folder, default_assignee: assignee) }
       let(:id) { project1.id }

-      example 'Assignees of moved project remain valid', document: false, if: defined?(ProjectFolders::Engine) do
+      example 'Assignees of moved project remain valid', document: false do
         idea1 = create(:idea, project: project1, assignee: assignee)
         idea2 = create(:idea, project: project2, assignee: assignee)

diff --git a/back/engines/commercial/idea_assignment/spec/acceptance/users_spec.rb b/back/engines/commercial/idea_assignment/spec/acceptance/users_spec.rb
index 7d038c1f285..88a5b4288ed 100644
--- a/back/engines/commercial/idea_assignment/spec/acceptance/users_spec.rb
+++ b/back/engines/commercial/idea_assignment/spec/acceptance/users_spec.rb
@@ -69,7 +69,7 @@ resource 'Users' do
         end
       end

-      describe 'when removing folder moderator rights', if: defined?(ProjectFolders::Engine) do
+      describe 'when removing folder moderator rights' do
         let(:folder) { create(:project_folder) }
         let(:assignee) { create(:project_folder_moderator, project_folders: [folder]) }

@@ -124,7 +124,7 @@ resource 'Users' do
         end
       end

-      describe 'when admin becomes folder moderator', if: defined?(ProjectFolders::Engine) do
+      describe 'when admin becomes folder moderator' do
         let(:folder1) { create(:project_folder) }
         let(:folder2) { create(:project_folder) }
         let(:assignee) { create(:admin) }
@@ -156,7 +156,7 @@ resource 'Users' do
         end
       end

-      describe 'when folder moderator becomes project moderator', if: defined?(ProjectManagement::Engine) && defined?(ProjectFolders::Engine) do
+      describe 'when folder moderator becomes project moderator', if: defined?(ProjectManagement::Engine) do
         let(:folder) { create(:project_folder) }
         let(:project1) { create(:project, folder: folder) }
         let(:project2) { create(:project, folder: folder) }
diff --git a/back/engines/commercial/idea_assignment/spec/services/idea_assignment/idea_assignment_service_spec.rb b/back/engines/commercial/idea_assignment/spec/services/idea_assignment/idea_assignment_service_spec.rb
index 4a7019db545..1848802e666 100644
--- a/back/engines/commercial/idea_assignment/spec/services/idea_assignment/idea_assignment_service_spec.rb
+++ b/back/engines/commercial/idea_assignment/spec/services/idea_assignment/idea_assignment_service_spec.rb
@@ -5,7 +5,7 @@ require 'rails_helper'
 describe IdeaAssignment::IdeaAssignmentService do
   let(:service) { described_class.new }

-  describe 'clean_idea_assignees_for_user!', if: defined?(ProjectManagement::Engine) && defined?(ProjectFolders::Engine) do
+  describe 'clean_idea_assignees_for_user!', if: defined?(ProjectManagement::Engine) do
     it 'clears the assignee where they no longer moderate the ideas' do
       assignee = create(:admin)
       folder1 = create(:project_folder)
@@ -36,7 +36,7 @@ describe IdeaAssignment::IdeaAssignmentService do
     end
   end

-  describe 'clean_assignees_for_project!', if: defined?(ProjectManagement::Engine) && defined?(ProjectFolders::Engine) do
+  describe 'clean_assignees_for_project!', if: defined?(ProjectManagement::Engine) do
     it 'clears the assignee where they no longer moderate the ideas' do
       folder = create(:project_folder)
       project = create(:project, folder: folder)


Commit message:
[TAN-2434] Fix specs for project folders

The `project_folders` engine has been merged into the core app, so the specs that were conditionally checking for the existence of `ProjectFolders::Engine` weren’t running as expected.