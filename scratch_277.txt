[TAN-1930] Remove `projectId` from props of VisitorsWidget nodes

We now use other data sources to compute the visitor data in order to improve their accuracy.
In order to improve the accuracy of the visitor data, we had to drop support for filtering by `projectId` in the `VisitorsWidget`. Hence, this rake will remove the `projectId` prop from the `VisitorsWidget` nodes in the content builder layouts.

[TAN-1930] Remove `projectId` from VisitorsWidget props

We now use other data sources to compute the visitor data in order to improve their accuracy.


To improve the accuracy of visitor data, we decided to stop supporting filtering by `projectId` in the `VisitorsWidget`. This rake task will remove the `projectId` prop from the `VisitorsWidget` nodes in the content builder layouts.
