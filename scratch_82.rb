Tenant.all.each do |tenant|
  tenant.switch do
    attributes = Area.includes(:custom_field_option).pluck(:'custom_field_options.ordering', :'custom_field_options.title_multiloc', :ordering, :title_multiloc)
    if attributes.map(&:uniq).map(&:size).any? { |size| size != 2 }
      pp(tenant_id: tenant.id, attributes: attributes.map(&:uniq))
      puts
    end
  end
end; nil

Tenant.find('233f92a7-b719-466e-9111-57e7b22c0722').switch { Area.recreate_custom_field_options }