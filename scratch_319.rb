lifecycle_stages = %w[demo not_applicable]
tenants = lifecycle_stages.reduce(Tenant.none) do |acc, stage|
  acc.or(Tenant.with_lifecycle(stage))
end.where("host LIKE '%citizenlab.co'")
tenant_hosts = tenants.map(&:host)
pp tenant_hosts


tenant_hosts = [
  "https://adrien-test-project-copy.citizenlab.co",


  "https://participationcopy.citizenlab.co",
  "https://sunderlandcitytrial.demo.citizenlab.co",
  "https://kklegeplads.citizenlab.co",
  "https://20262031gemeentemonitor.citizenlab.co",
  "https://thomas-demo.citizenlab.co",
  "https://dialoogplatform.citizenlab.co",
  "https://anoukdemo.citizenlab.co",
  "https://clara-demo.citizenlab.co",
  "https://us-template.demo.citizenlab.co",
  "https://hannah.demo.citizenlab.co",
  "https://adminhqtest5.citizenlab.co",
  "https://luebeck-demo.citizenlab.co",
  "https://ajsdfjiepafasdkle.citizenlab.co",
  "https://cristele-demo.citizenlab.co",
  "https://demo-willebroek.citizenlab.co",
  "https://neustadt-demo.citizenlab.co",
  "https://deutschland-demo.citizenlab.co",
  "https://jaow485uq90w4utaoisfjg.citizenlab.co",
  "https://fr-be-template-copy-080922.citizenlab.co",
  "https://nyhamnen.demo.citizenlab.co",
  "https://internal-demo.citizenlab.co",
  "https://privwroclaw-pl.demo.citizenlab.co",
  "https://trainingvideos.citizenlab.co",
  "https://privstalowa-wola.demo.citizenlab.co",
  "https://mechelen.demo.citizenlab.co",
  "https://jelena.demo.citizenlab.co",
  "https://befr.citizenlab.co",
  "https://marine-demo.citizenlab.co",
  "https://hodb-demo.citizenlab.co",
  "https://global-partners.demo.citizenlab.co",
  "https://wiesbaden-demo.citizenlab.co",
  "https://demopauline.citizenlab.co",
  "https://privpila.demo.citizenlab.co",
  "https://yourcommunity-demonstration.citizenlab.co",
  "https://emileeng.demo.citizenlab.co",
  "https://wahlheymat-demo.citizenlab.co",
  "https://cl-en3.demo.citizenlab.co",
  "https://cl-nl1.demo.citizenlab.co",
  "https://ourcommunity-demo.citizenlab.co",
  "https://belgie-template.demo.citizenlab.co",
  "https://simonssandbox.citizenlab.co",
  "https://projecttemplategallery.citizenlab.co",
  "https://emile.demo.citizenlab.co",
  "https://ovre-eiker.citizenlab.co",
  "https://opensource.citizenlab.co",
  "https://cl-nl5.demo.citizenlab.co",
  "https://cl-nl3.demo.citizenlab.co",
  "https://es-was-template.citizenlab.co",
  "https://usertesting.demo.citizenlab.co"
]

tenant_hosts = [
  "https://a11y.stg.govocal.com",
]

tenant_service = MultiTenancy::TenantService.new
tenant_hosts.each do |tenant_host|
  attrs = { host: tenant_host.gsub(/citizenlab\.co/, 'govocal.com') }
  puts "Updating host for #{tenant_host} to #{attrs[:host]}"
  tenant = Tenant.find_by(host: tenant_host)
  tenant_service.update_tenant(tenant, attrs)
end



