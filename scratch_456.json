{"data": {"type": "json_forms_schema", "attributes": {"ui_schema_multiloc": {"fr-FR": {"type": "Categorization", "options": {"formId": "idea-form", "inputTerm": "idea"}, "elements": [{"type": "Page", "options": {"input_type": "page", "id": "c1dffc47-f729-424a-817e-f6e36000a0e0", "title": "", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/titre_zt0", "label": "Titre", "options": {"description": "<p>Indiquer le titre de votre projet</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/description_b6s", "label": "Description", "options": {"description": "<p><PERSON><PERSON><PERSON> votre projet au maximum</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/adresse_vno", "label": "<PERSON><PERSON><PERSON>", "options": {"description": "<p>Indiquer ici la localisation de votre projet</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/selon_vous_a_combien_de_personnes_ce_projet_va_t_il_beneficier_whd", "label": "<PERSON><PERSON><PERSON>vous, à combien de personnes ce projet va-t-il bénéficier ?", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/selon_vous_a_combien_s_eleve_le_montant_de_votre_projet_lkd", "label": "<PERSON><PERSON><PERSON>vous, à combien s'élève le montant de votre projet ?", "options": {"description": "", "input_type": "number", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/deposez_vous_ce_projet_au_nom_d_un_collectif_d_habitant_hng", "label": "<PERSON><PERSON><PERSON><PERSON>-vous ce projet au nom d'un collectif d'habitant ?", "options": {"description": "", "input_type": "select", "isAdminField": false, "hasRule": false, "dropdown_layout": false, "enumNames": ["O<PERSON>", "Non"]}}, {"type": "Control", "scope": "#/properties/si_oui_merci_de_preciser_qoh", "label": "Si oui, merci de préciser :", "options": {"description": "", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/images_vb2", "label": "Images", "options": {"description": "<p>Nous vous invitons à illustrer votre projet en nous communiquant une image.</p>", "input_type": "file_upload", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/pieces_jointes_dmg", "label": "Pièces jointes", "options": {"description": "<p>Si vous avez des documents complémentaires à nous fournir, merci de les télécharger ici</p>", "input_type": "file_upload", "isAdminField": false, "hasRule": false}}]}, {"type": "Page", "options": {"input_type": "page", "id": "01b4a9db-4038-45c7-96b8-ae355f2816e3", "title": "Coordonnées", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/prenom_s7n", "label": "Prénom", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/nom_42i", "label": "Nom", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/mail_eiw", "label": "Mail", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/telephone_3fs", "label": "Téléphone", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/reglement_interieur_05h", "label": "Règlement intérieur", "options": {"description": "", "input_type": "select", "isAdminField": false, "hasRule": false, "dropdown_layout": false, "enumNames": ["Je reconnais avoir pris connaissance du règlement intérieur du budget participatif que je m'engage à respecter."]}}]}, {"type": "Page", "options": {"input_type": "page", "id": "d51b356a-b2e0-4199-ae99-03ce01942cdb", "title": "Merci pour votre participation !", "description": "Votre contribution a été soumise avec succès.", "page_layout": "default", "map_config_id": null}, "elements": []}]}}}}}