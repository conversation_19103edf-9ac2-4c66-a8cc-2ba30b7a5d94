Processing projectpubliek.bilzen.be...
Processing habay.citizenlab.co...
Processing borsele.citizenlab.co...
Processing mitgestalten.wetzlar.de...
Processing denkmee.sint-truiden.be...
Processing hulst.citizenlab.co...
Processing kraainem.citizenlab.co...
Processing umk.citizenlab.co...
Processing leeuwdenktmee.be...
Processing wolfurt.citizenlab.co...
Processing samen.stedebroec.nl...
Processing emmaus.citizenlab.co...
Processing waalwijk.citizenlab.co...
Processing jeparticipe.soumagne.be...
Processing denkmee.noord-beveland.nl...
Processing zottegem.citizenlab.co...
Processing gamleosloinvolverer.no...
rake aborted!
NameError: uninitialized constant HomePage
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/inflector/methods.rb:278:in `const_get'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/inflector/methods.rb:278:in `constantize'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/core_ext/string/inflections.rb:74:in `constantize'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activerecord-*******/lib/active_record/inheritance.rb:207:in `polymorphic_class_for'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activerecord-*******/lib/active_record/associations/belongs_to_polymorphic_association.rb:9:in `klass'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activerecord-*******/lib/active_record/associations/association.rb:215:in `ensure_klass_exists!'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activerecord-*******/lib/active_record/associations/singular_association.rb:8:in `reader'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activerecord-*******/lib/active_record/associations/builder/association.rb:104:in `imageable'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activemodel-*******/lib/active_model/validator.rb:150:in `block in validate'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activemodel-*******/lib/active_model/validator.rb:149:in `each'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activemodel-*******/lib/active_model/validator.rb:149:in `validate'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:423:in `block in make_lambda'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:199:in `block (2 levels) in halting'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:687:in `block (2 levels) in default_terminator'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:686:in `catch'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:686:in `block in default_terminator'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:200:in `block in halting'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:595:in `block in invoke_before'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:595:in `each'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:595:in `invoke_before'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:106:in `run_callbacks'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:929:in `_run_validate_callbacks'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activemodel-*******/lib/active_model/validations.rb:406:in `run_validations!'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activemodel-*******/lib/active_model/validations/callbacks.rb:115:in `block in run_validations!'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:107:in `run_callbacks'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activesupport-*******/lib/active_support/callbacks.rb:929:in `_run_validation_callbacks'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activemodel-*******/lib/active_model/validations/callbacks.rb:115:in `run_validations!'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activemodel-*******/lib/active_model/validations.rb:337:in `valid?'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activerecord-*******/lib/active_record/validations.rb:68:in `valid?'
/cl2_back/app/services/invalid_data_checker.rb:47:in `validation_errors'
/cl2_back/app/services/invalid_data_checker.rb:32:in `block (3 levels) in check_tenant'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activerecord-*******/lib/active_record/relation/delegation.rb:88:in `each'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/activerecord-*******/lib/active_record/relation/delegation.rb:88:in `each'
/cl2_back/app/services/invalid_data_checker.rb:31:in `block (2 levels) in check_tenant'
/cl2_back/app/services/invalid_data_checker.rb:29:in `each'
/cl2_back/app/services/invalid_data_checker.rb:29:in `block in check_tenant'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/ros-apartment-2.11.0/lib/apartment/adapters/abstract_adapter.rb:89:in `switch'
/cl2_back/app/services/invalid_data_checker.rb:25:in `check_tenant'
/cl2_back/engines/commercial/multi_tenancy/lib/tasks/core/invalid_data_checker.rake:13:in `block (3 levels) in <main>'
/cl2_back/engines/commercial/multi_tenancy/lib/tasks/core/invalid_data_checker.rake:11:in `each'
/cl2_back/engines/commercial/multi_tenancy/lib/tasks/core/invalid_data_checker.rake:11:in `block (2 levels) in <main>'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/sentry-ruby-5.8.0/lib/sentry/rake.rb:26:in `execute'
/cl2_back/vendor/bundle/ruby/2.7.0/gems/rake-13.0.6/exe/rake:27:in `<top (required)>'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/cli/exec.rb:58:in `load'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/cli/exec.rb:58:in `kernel_load'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/cli/exec.rb:23:in `run'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/cli.rb:492:in `exec'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/vendor/thor/lib/thor/command.rb:27:in `run'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/vendor/thor/lib/thor/invocation.rb:127:in `invoke_command'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/vendor/thor/lib/thor.rb:392:in `dispatch'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/cli.rb:34:in `dispatch'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/vendor/thor/lib/thor/base.rb:485:in `start'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/cli.rb:28:in `start'
/usr/local/bundle/gems/bundler-2.4.20/exe/bundle:37:in `block in <top (required)>'
/usr/local/bundle/gems/bundler-2.4.20/lib/bundler/friendly_errors.rb:117:in `with_friendly_errors'
/usr/local/bundle/gems/bundler-2.4.20/exe/bundle:29:in `<top (required)>'
/usr/local/bundle/bin/bundle:23:in `load'
/usr/local/bundle/bin/bundle:23:in `<main>'
Tasks: TOP => checks:invalid_data
(See full trace by running task with --trace)
{"host":"40c5ab7f6bfc","application":"Semantic Logger","environment":"production","timestamp":"2024-01-11T09:29:58.292556Z","level":"info","level_index":2,"pid":1,"thread":"worker-1","name":"Rails","message":"sentry -- [Transport] Sending envelope with items [event] 313bcf10c4b5439fab3626a1100fa0b9 to Sentry"}
Reporting Error: :boom: FAILURE: Some data is likely invalid.


TextImage b0680fe4-663b-4807-9f87-5ea08fc5a6cf

["800-jaar-sint-niklaas",
 "Gemeente Lelystad",
 "Ketenpartners",
 "Uitleg",
 "a-new-page",
 "a-propos",
 "a-propos-concertation",
 "a-quoi-sert-votre-avis",
 "about",
 "about-demokrati-garage",
 "aboutspeakup",
 "aboutus",
 "accessibility-statement",
 "acessibilidade-das-eleicoes-para-pessoas-com-deficiencia",
 "acikveri",
 "activiteiten",
 "actualites",
 "adviesfora",
 "alex-test",
 "alex-test-1",
 "alex-test-2",
 "alle-duurzame-projecten",
 "allegebieden",
 "amay",
 "anbefalinger",
 "appels-a-volontaires",
 "apropos",
 "ateliers-creative-valley",
 "baggrund",
 "beheerplanwageningenhoog",
 "beheerplanwageningenhoogdefinitief",
 "beispiel",
 "beleidsplan-ruimte",
 "benevole",
 "beteiligen",
 "bijlagenomgevingsvisie",
 "biodiversitet",
 "boa",
 "borgerforslag",
 "borgersamling-om-lynetteholm",
 "brent-what-do-you-think",
 "brent-what-do-you-think-1",
 "budget-participatif",
 "budgets-participatifs",
 "budzet-obywatelski",
 "burgerbudget",
 "burgerinitiatieven",
 "buurtuitvoeringsplannen-amstelveen",
 "by-havns-opslagsvaerk-til-borgersamlingen-om-lynetteholm",
 "cdq",
 "centrada",
 "chapel-hill",
 "charte-d-utilisation",
 "charte-de-l-utilisateur",
 "charte-de-participation",
 "citizenlab-s-community-of-practice",
 "cmj",
 "comment-ca-marche",
 "communes",
 "concertation",
 "condities-van-het-wedstrijd",
 "constat",
 "consultations",
 "consultations-de-l-agglo",
 "contact",
 "contact-us",
 "contactez-nous",
 "copil",
 "customa",
 "declaration-sur-l-accessibilite",
 "deelgebied-a-noordelijke-rand",
 "deelgebied-c-veenweide-bij-ter-aar",
 "deelgebied-d-ten-noorden-van-zuideinde",
 "deelgebied-e-bij-groene-jonker-en-kromme-mijdrecht",
 "deelgebied-f-middengebied",
 "deelgebied-g-westkant-ter-aar",
 "deelgebied-h-natura-2000-nnn",
 "deelgebied-i-woerdense-verlaat-zuid",
 "deelgebied-j-woerdense-verlaat-oost",
 "deelgebied-k-dorpskernen",
 "deelgebieden-gemeente-heerde",
 "deelgemeenten",
 "deltag-i-borgersamlingen",
 "department-sustainability",
 "designprincipper",
 "dialoguez-avec-vos-elus",
 "dialoogkamer",
 "documentation",
 "draaiboeken",
 "droit-a-l-image-simplify-coffee",
 "duurzaamheid",
 "educationmaroc",
 "energy-mobility",
 "everybody",
 "extra-info-over-de-stemming",
 "faq",
 "feuille-de-route",
 "flevoland",
 "fonde",
 "food",
 "forums",
 "gelote-wijkpanels",
 "gemeinde-x",
 "greenville",
 "grez-doiceau",
 "groups",
 "hakkimizda",
 "handleiding",
 "hello",
 "hiergroeiteenplan",
 "hoe-stemmen",
 "horinger",
 "horizon-2026",
 "howconcert",
 "howto",
 "howtoconcert",
 "howtoparticipate",
 "humane-byer-byrum-og-bebyggelse",
 "i-letisim",
 "ideeen-inwoners",
 "iletisim",
 "impressum",
 "indbrudsforebyggelse",
 "info",
 "info-verificatie",
 "infopagine-texel",
 "informacije",
 "informacion-acciona-energia",
 "informatie",
 "information",
 "information-plateforme-democratie-participative",
 "informations",
 "infos",
 "infos-pratiques",
 "initiatives",
 "initiatives-1",
 "inspiratie",
 "inspiratievoorbeelden",
 "instructions",
 "interview-met-erik-jan-bijleveld-over-het-wagenings-kompas",
 "jeres-side-3",
 "jeres-side-4",
 "jeres-side-5",
 "jeugd",
 "jongeren",
 "kapitalen-kobenhavn-under-forandring-ifversen",
 "keydates",
 "klankbordgroep-buurtbewoners",
 "klimaafdelingen",
 "klimadagsordenen",
 "klimaplan",
 "klimaplanen",
 "kraftwerksumgestaltung",
 "kranenburg",
 "kriterier",
 "kunstwerken",
 "l-agenda-des-rencontres",
 "la-charte",
 "la-transformation-de-la-place-aux-foires",
 "laga",
 "le-kit-de-communication",
 "les-grandes-etapes",
 "lintbebouwing-en-kernen",
 "liste-des-projets",
 "logos",
 "lot",
 "mandat",
 "maritim-areal-planlaegning-i-oresund",
 "materialesamling",
 "meerjaren-mijn-gemeente",
 "merci",
 "mitmachen",
 "mobilitaet",
 "mobilitat-stationen-auswertung-abfrage",
 "mobiliteit-steenokkerzeel-bepaal-mee-de-proefopstelling",
 "mobility",
 "mobility-2023",
 "moderatieregels",
 "moja",
 "municipality-1",
 "my-test",
 "nachhaltigkeit",
 "nachhaltigkeit-1",
 "nachhaltigkeit-2",
 "nationprojet",
 "netiquette",
 "neue-seite",
 "neuigkeiten",
 "news",
 "nieuws",
 "nous-contacter",
 "objectifs",
 "observatorpolitik",
 "omgevingsanalyse2631",
 "onboardingvideos-communication",
 "onboardingvideos-generalsettings",
 "onboardingvideos-managingprojects",
 "onboardingvideos-processing",
 "openbare-werken",
 "opgaven-gemeente-heerde",
 "oproep-van-het-stadsbestuur",
 "organisatie-projecten",
 "our-team",
 "over",
 "over-de-vraag-van-2019",
 "over-dit-platform",
 "over-haspershoven",
 "over-het-platform",
 "own-page",
 "page-test",
 "page-test-2",
 "palmares",
 "palmares-2022",
 "participatie",
 "participatietraject",
 "partnership",
 "peace",
 "people",
 "pictures-from-annual-conference-2023",
 "plan-numerique",
 "planet",
 "planstrategi",
 "podcasts",
 "politique-de-confidentialite",
 "principes-gemeente-heerde",
 "privacy-policy",
 "program-for-1-samling",
 "programma",
 "programme",
 "projects",
 "projet-de-ville-reinventons-liege",
 "projetrans",
 "prosperity",
 "public-spaces-goings-on",
 "referater-m-m",
 "reglement",
 "rencontres-citoyennes-du-college-communal",
 "rest",
 "retningslinjer",
 "roadmap",
 "rup",
 "samenwerkings-projecten",
 "scenarios-wageningen-over-20-jaar",
 "secret-page",
 "sie-sind-am-ende-des-fragebogens-angelangt",
 "sint-niklaas",
 "soumettre-un-formulaire-papier",
 "spain",
 "spelregels",
 "stappen",
 "stationsgebied",
 "stekene",
 "sterke-kanten-gemeente-heerde",
 "strategy-budgeting-1",
 "stuurgroep",
 "success-story-2-back-up",
 "success-story-3-back-up",
 "success-story-back-up",
 "sustainability",
 "tekst-video",
 "telechargement",
 "terms-and-conditions",
 "terugblik-bijeenkomst-30-november",
 "test",
 "test-1",
 "test-for-gs",
 "test-page",
 "test-page-4",
 "test-survey-in-page",
 "test-til-admins",
 "tilskudogpuljer",
 "toegankelijkheid",
 "townhall-meetings",
 "transport-plan",
 "tredjepartsaftale",
 "trees",
 "tutorials",
 "udsendelse-om-transport",
 "ueberuns",
 "uitleg-deelname-webinar-via-ms-teams",
 "umfragen",
 "ungeforslag",
 "van-6-naar-2-scenario-s",
 "veelgestelde-vragen",
 "visieschets-kapermolen",
 "vlaamse-mobiliteitsvisie-2040",
 "voorstellen",
 "vorhabenliste2023",
 "vraagstellen",
 "vrijwilligers-gezocht",
 "vrijwilligers-gezocht-1",
 "waar-vind-je-jouw-identiteitskaartnummer",
 "waarom-eenrichtingsverkeer-richting-de-haachtsesteenweg",
 "warmeinfo",
 "wat-is-citizenlab",
 "wat-is-salt",
 "water",
 "we-asked-you-said-we-did",
 "weitere-beteiligungsmoeglichkeiten",
 "weitere-moglichkeiten",
 "whoparticipate",
 "wijkbudget",
 "wil-je-op-de-hoogte-blijven-van-het-reilen-en-zeilen-in-t-dorp",
 "wolfurt",
 "workshop-overview",
 "x",
 "x-1",
 "yayinlarimiz",
 "yeni-sayfa",
 "youth"]

 {"ROOT"=>
   {"type"=>"div",
    "nodes"=>
     ["M5HXG4JSBP", "D7WbZdtNqg", "IBLmxPDKm5", "4cJRvY6gIN", "U2Ep61PpyE"],
    "props"=>{"id"=>"e2e-content-builder-frame"},
    "custom"=>{},
    "hidden"=>false,
    "isCanvas"=>true,
    "displayName"=>"div",
    "linkedNodes"=>{}},
  "4cJRvY6gIN"=>
   {"type"=>{"resolvedName"=>"TextMultiloc"},
    "nodes"=>[],
    "props"=>
     {"text"=>
       {"en"=>"",
        "da-DK"=>
         "<p class=\"ql-align-center\"></p><h3 class=\"ql-align-center\">We are all ears</h3><p class=\"ql-align-center\">In our municipality, we want to involve our citizens in as many ways as possible, in policy or in specific projects. This platform helps us to do this. As a citizen, you can leave an idea or ask a question at any time to start a process on this platform. My municipality staff can also use this platform for internal consultation processes or to involve people in a plan or project.</p><p class=\"ql-align-center\">Depending on current events, we use this platform to collect votes, to discuss, to launch surveys, to give your opinion on a point of view, the citizen's budget, public studies, online mapping, mobility plans, scenario planning, support for advisory councils and committees, etc.</p><p class=\"ql-align-center\">If you have an idea of how we can use this platform to better involve you or certain target groups, please let us know via this platform. Who knows, maybe your proposal will be placed on the platform soon.</p>",
        "de-DE"=>"",
        "en-CA"=>"",
        "en-GB"=>"",
        "es-CL"=>"",
        "es-ES"=>"",
        "fr-BE"=>"",
        "fr-FR"=>"",
        "nb-NO"=>
         "<p class=\"ql-align-center\">Bydel Gamle Oslo tester ut digital innbyggerinvolvering på plattformen frem til 31. august 2024.</p><p class=\"ql-align-center\">Bli med å gi innspill, ideer og kommentarer og delta i utviklingen av bydelen.</p><p class=\"ql-align-center\">For å være med å gi innspill på prosjektene som er aktive må du registrere en bruker øverst i høyre hjørne på forsiden til Gamleosloinvolverer.no, med epostaddresse og et passord du lager selv.</p>",
        "nl-BE"=>"",
        "nl-NL"=>""}},
    "custom"=>
     {"title"=>
       {"id"=>"app.containers.admin.ContentBuilder.textMultiloc",
        "defaultMessage"=>"Text"}},
    "hidden"=>false,
    "parent"=>"ROOT",
    "isCanvas"=>false,
    "displayName"=>"TextMultiloc",
    "linkedNodes"=>{}},
  "D7WbZdtNqg"=>
   {"type"=>{"resolvedName"=>"Projects"},
    "nodes"=>[],
    "props"=>{"currentlyWorkingOnText"=>{"nb-NO"=>""}},
    "custom"=>
     {"title"=>
       {"id"=>
         "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle",
        "defaultMessage"=>"Projects"},
      "noDelete"=>true,
      "noPointerEvents"=>true},
    "hidden"=>false,
    "parent"=>"ROOT",
    "isCanvas"=>false,
    "displayName"=>"Projects",
    "linkedNodes"=>{}},
  "IBLmxPDKm5"=>
   {"type"=>{"resolvedName"=>"WhiteSpace"},
    "nodes"=>[],
    "props"=>{"size"=>"medium"},
    "custom"=>
     {"title"=>
       {"id"=>"app.containers.AdminPage.ProjectDescription.whiteSpace",
        "defaultMessage"=>"White space"}},
    "hidden"=>false,
    "parent"=>"ROOT",
    "isCanvas"=>false,
    "displayName"=>"WhiteSpace",
    "linkedNodes"=>{}},
  "M5HXG4JSBP"=>
   {"type"=>{"resolvedName"=>"HomepageBanner"},
    "nodes"=>[],
    "props"=>
     {"image"=>{"dataCode"=>"1f2feaca-e32e-4e5e-9097-f7f5b0ce2299"},
      "errors"=>[],
      "hasError"=>false,
      "homepageSettings"=>
       {"banner_layout"=>"full_width_banner_layout",
        "banner_avatars_enabled"=>true,
        "banner_cta_signed_in_url"=>nil,
        "banner_cta_signed_in_type"=>"no_button",
        "banner_cta_signed_out_url"=>nil,
        "banner_cta_signed_out_type"=>"sign_up_button",
        "banner_signed_in_header_multiloc"=>
         {"nb-NO"=>
           "Bydel Gamle Oslo vil gjerne høre fra deg. Bidra til forandring i dag!"},
        "banner_signed_out_header_multiloc"=>{"nb-NO"=>"Gamle Oslo involverer"},
        "banner_cta_signed_in_text_multiloc"=>{},
        "banner_cta_signed_out_text_multiloc"=>{},
        "banner_signed_out_subheader_multiloc"=>
         {"nb-NO"=>"Delta i utvikling av bydelen din!"},
        "banner_signed_in_header_overlay_color"=>"#034b45",
        "banner_signed_out_header_overlay_color"=>"#034b45",
        "banner_signed_in_header_overlay_opacity"=>nil,
        "banner_signed_out_header_overlay_opacity"=>20}},
    "custom"=>
     {"title"=>
       {"id"=>"app.containers.admin.ContentBuilder.homepage.homepageBanner",
        "defaultMessage"=>"Homepage banner"},
      "noDelete"=>true,
      "noPointerEvents"=>true},
    "hidden"=>false,
    "parent"=>"ROOT",
    "isCanvas"=>false,
    "displayName"=>"HomepageBanner",
    "linkedNodes"=>{}},
  "U2Ep61PpyE"=>
   {"type"=>{"resolvedName"=>"WhiteSpace"},
    "nodes"=>[],
    "props"=>{"size"=>"medium"},
    "custom"=>
     {"title"=>
       {"id"=>"app.containers.AdminPage.ProjectDescription.whiteSpace",
        "defaultMessage"=>"White space"}},
    "hidden"=>false,
    "parent"=>"ROOT",
    "isCanvas"=>false,
    "displayName"=>"WhiteSpace",
    "linkedNodes"=>{}}}