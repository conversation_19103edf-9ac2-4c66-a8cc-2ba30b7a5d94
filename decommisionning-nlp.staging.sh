# Configure kubectl to use the dev cluster
aws-vault exec dev-admin -- aws eks --region eu-central-1 update-kubeconfig --name alan-turing
# Updated context arn:aws:eks:eu-central-1:************:cluster/alan-turing in /Users/<USER>/.kube/config

aws-vault exec dev-account -- kubectl get namespaces
#NAME              STATUS   AGE
#default           Active   697d
#kube-node-lease   Active   697d
#kube-public       Active   697d
#kube-system       Active   697d
#ml-stage          Active   693d
#steward-stage     Active   697d

aws-vault exec dev-account -- helm list
#NAME            NAMESPACE       REVISION        UPDATED                                         STATUS          CHART           APP VERSION
#graylog-stage   default         6               2022-06-16 18:54:52.********* +0100 +0100       deployed        graylog-0.0.1
#nlp-stage       default         101             2023-06-26 00:04:21.308042 +0200 CEST           deployed        nlp-0.0.1

aws-vault exec dev-account -- helm list -n ml-stage
#NAME            NAMESPACE       REVISION        UPDATED                                 STATUS          CHART           APP VERSION
#weaviate        ml-stage        1               2022-07-19 16:22:24.391225 +0200 CEST   deployed        weaviate-14.3.0 v1.13.0

# Uninstall applications
aws-vault exec dev-account -- helm uninstall graylog-stage
aws-vault exec dev-account -- helm uninstall nlp-stage
aws-vault exec dev-account -- helm uninstall weaviate -n ml-stage

cd infrastructure/applications
terraform init # + select workspace "graylog-stage" when prompted
terraform workspace list
#  graylog-prod
#* graylog-stage
#  nlp-prod
#  nlp-stage

# Replace:
#   workspace    = join("-", ["cloud", local.environment.breadcrumbs.cloud])
# with:
#   workspace    = "cloud-alan-turing"
# to solve errors about "breadcrumbs" not being defined at:
# infrastructure/applications/main.tf:26
aws-vault exec dev-account -- terraform plan -destroy -out=destroy-graylog-stage.tfplan -var-file=graylog-stage.tfvars
aws-vault exec dev-account -- terraform apply destroy-graylog-stage.tfplan

terraform workspace select nlp-stage
aws-vault exec dev-account -- terraform plan -destroy -out=destroy-nlp-stage.tfplan -var-file=nlp-stage.tfvars
aws-vault exec dev-account -- terraform apply destroy-nlp-stage.tfplan

cd ../environments
terraform init # + select workspace "steward-stage" when prompted
aws-vault exec dev-account -- terraform plan -destroy -out=destroy-steward-stage.tfplan -var-file=steward-stage.tfvars
aws-vault exec dev-account -- terraform apply destroy-steward-stage.tfplan

# Since ml-stage is the last environment, we have to operate differently as documented here:
# https://www.notion.so/citizenlab/Terraform-example-deployment-01da7b6c8a0640c09dafb2e91e694efa?pvs=4#22bfaef5e75041d187f48bb85652e1c2
# We have to remove:
#   > 1. Remove the `ingress_groups` from the environment
#   > 2. Remove the ingress controller from the cloud (set `eks_ingress_enabled = false`
#   > 3. Delete the environment
#   > 4. Delete the cloud
terraform workspace select ml-stage
aws-vault exec dev-account -- terraform apply -var-file=ml-stage.tfvars -var "ingress_groups=[]"

cd ../clouds
terraform workspace select alan-turing
aws-vault exec --no-session dev-account -- terraform plan -out=disable-eks-ingress-controller.alan-turing.tfplan -var-file=alan-turing.tfvars -var "eks_ingress_enabled=false"
aws-vault exec --no-session dev-account -- terraform apply disable-eks-ingress-controller.alan-turing.tfplan
# The plan didn't fully succeed:
# │ Error: error deleting IAM policy arn:aws:iam::************:policy/AlanTuringEKSIngressControl: DeleteConflict: Cannot delete a policy attached to entities.
# │       status code: 409, request id: 2bc61bc1-e938-4133-b150-5497c7b31978

cd ../environments
aws-vault exec dev-account -- terraform plan -destroy -out=destroy-ml-stage.tfplan -var-file=ml-stage.tfvars
aws-vault exec dev-account -- terraform apply destroy-ml-stage.tfplan

cd ../clouds
aws-vault exec --no-session dev-account -- terraform plan -destroy -out=destroy-alan-turing.tfplan -var-file=alan-turing.tfvars
aws-vault exec --no-session dev-account -- terraform apply destroy-alan-turing.tfplan
# The plan didn't fully succeed:
  #│ Error: error deleting IAM policy arn:aws:iam::************:policy/AlanTuringEKSIngressControl: DeleteConflict: Cannot delete a policy attached to entities.
  #│       status code: 409, request id: 166f681a-9d23-409a-80f5-559a0d43a702
  #│ Error: error deleting IAM policy arn:aws:iam::************:policy/AlanTuringEKSEFSControl: DeleteConflict: Cannot delete a policy attached to entities.
  #│       status code: 409, request id: f6eca72d-78ed-4da1-abdf-b2e9ba77800b
# I removed the policies manually from the kube-manager IAM user group and re-ran the apply command
aws-vault exec --no-session dev-account -- terraform destroy -var-file=alan-turing.tfvars

# TODO:
# - [ ] Check the elasticsearch is no longer used on AWS