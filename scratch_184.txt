We troubleshot it with <PERSON> this morning. Here's the result of the investigation 🧐

tl;dr
That is the same issue that https://www.notion.so/citizenlab/3rd-party-login-from-Vienna-is-not-working-for-a-user-b1dfd6c65af14244bf563b8b77847231.
The jwt token is too large for the cookie to be set because of all the roles that are associated with the user.

The problem stems from the fact that when we grant a user the project-folder moderator role, the project moderator roles for all the projects in the folder are also added to the user. So instead of:

```
user.roles
=> [{"type"=>"project_folder_moderator", "project_folder_id"=>"6a6c2707-b76d-485a-a829-e69d7f6fa435"}]
```

We end up with:
```
user.roles
=> [{"type"=>"project_moderator", "project_id"=>"38084173-31b5-4629-a407-f14e57f090bb"}, {"type"=>"project_moderator", "project_id"=>"d7504e73-317e-4ed5-acc6-cf77b605a15c"}, {"type"=>"project_moderator", "project_id"=>"8e46a865-a206-4eb1-8165-7d0499e18295"}, {"type"=>"project_moderator", "project_id"=>"c47a7909-ac60-4210-909b-4ebca0cac006"}, {"type"=>"project_moderator", "project_id"=>"72d0f23b-e29c-4a7e-aef7-7e727b9c94ff"}, {"type"=>"project_moderator", "project_id"=>"6828d94b-e584-4785-aa3d-1e97c6af728b"}, {"type"=>"project_moderator", "project_id"=>"fcdb1711-4288-4a85-836c-0af901a1965b"}, {"type"=>"project_folder_moderator", "project_folder_id"=>"6a6c2707-b76d-485a-a829-e69d7f6fa435"}]
```

That should not be necessary/the case.

A few more things to note:
- If we remove the individual project moderator rights, the front-end still behaves as expected in most places (that is, the user can still moderate the projects since it is in the moderated folder). However, there are a few places where it breaks. For instance:
    - The user is no longer allowed to see the list of other moderators in the project settings.
    - The "edit project" button is missing when viewing the project page.
- If some projects are added to the folder down the line, the individual project moderator rights are not added to the user, which is inconsistent with the behavior described above.

# Short term solutions:
- We could remove the individual project moderator rights and live with the few places where the front-end breaks. This is not ideal, but it is a quick fix.
- We could remove the individual project moderator rights and fix the front-end where it breaks. Note that we didn't do a comprehensive testing but most of the front-end seems to work as expected.
- The impacted users moderate two big folders. If we remove moderation rights from one of the folders, they should be able to login again.

On the longer term, we need to get rid of the extra project moderator roles for project-folder moderators.

@Anonymous @Anonymous @Anonymous what do you think is the best course of action here?