Trying to pull your comments together:

> The one larger item is whether we want to support the child jobs/batching mechanism or not. I'm slightly opposed, unless there's a clear case where the copy is really slow and we definitely need it.

> I wonder whether we actually need this, and if it’s worth the extra complexity. How slow is the job copy?

But if we flip the question: if the jobs aren’t slow, why bother measuring progress?

I don’t have any definitive data on job durations, but I don’t think it’s unreasonable to assume that copying ideas could take a while, especially if there are images or files attached. I also wanted to build a solution that’s generic enough to be useful in other contexts. This isn’t the first time I’ve needed progress tracking and lacked the proper abstractions.

I agree that it adds some complexity, but I think it’s well-contained and, more importantly, doesn’t surface in the interface.  I also don’t think it would make migrating to another job backend significantly more difficult.

> I trust that this works, but as mentioned before, part of me worries this mechanism might be too brittle in "unknown unknown" ways.

I can’t guarantee there won’t be any issues, but I don’t think this carries more risk than most pieces of code I write. Conceptually, it’s very simple: each `QueJob` holds a reference to the tracker that it updates. In comparison, I think this is much less intrusive than how we store the tenant ID to support multi-tenancy.

> I’d rather rely on primitives from the job library for this, but I know <PERSON> doesn’t offer anything. If you believe this is absolutely necessary for the current feature (copying ideas), then I’m okay with it. Otherwise, I’d suggest dropping it and just doing the copy in one go, and waiting until we switch job libraries.

But as you said, they don’t offer anything in that area. We can’t rely on primitives that don’t exist. And as far as I know, the more modern libraries we have in mind (like Solid Queue,  or GoodJob) don’t support progress tracking either. Also, from experience, I know that "at some point" can mean a year, two years, even longer. Even if the current solution isn’t perfect, I think it can still be useful in the meantime.
