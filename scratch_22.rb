  class Roles
  
    class Collection < Array
      include TSort
  
      alias :tsort_each_node :each
      def tsort_each_child(role, &block)
        select { |r| r.lower == role.name || r.name == role.higher }.each(&block)
      end
  
      def +(other)
        Collection.new(to_a + other.to_a)
      end
    end
    
    def self.roles
      @roles ||= Collection.new
    end
  
    attr_accessor :lower, :higher, :name
    
    def initialize(name, lower=nil, higher=nil)
      @name = name
      @lower = lower
      @higher = higher
    end
    
    def self.add_role(name, lower=nil, higher=nil)
      roles << Roles.new(name, lower, higher)
    end
  end
  
