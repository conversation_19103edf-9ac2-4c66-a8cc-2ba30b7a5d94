# Drain the current node in docker swarm
docker node ls
docker node update --availability drain stc40ylvtro7mkf109e5koh7s
docker system prune -f

# sudo apt update
# sudo apt upgrade -y
# udo reboot
sudo apt update && sudo apt upgrade -y && sudo reboot

sudo do-release-upgrade

docker node update --availability active stc40ylvtro7mkf109e5koh7s

screen -S stg2 ssh aws-stg-2
screen -ls
screen -X quit


# get current cpu usage of a container "container_name"
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}"