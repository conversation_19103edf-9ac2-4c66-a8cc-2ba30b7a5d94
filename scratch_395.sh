# search for installed homebrew packages with emacs in the name
brew list | grep emacs

# use homebrew to install emacs (with emacs-plus formula) with native compilation
# (version 29)
brew install emacs-plus@29 --with-native-comp --with-imagemagick

# list only hidden files and directories in the home directory
ls -a ~

# copy and rename directory at the same time: for example, copy the directory emacs from folder1 to folder2 and rename it to emacs2
cp -r folder1/emacs folder2/emacs2

# in emacs, list only installed packages
M-x list-packages

# search in buffer for a string
C-s string
# or using one of the following packages: swiper, helm, ivy

