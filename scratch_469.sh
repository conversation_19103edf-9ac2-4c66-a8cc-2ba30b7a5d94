ssh aws-stg-1

# run command ls over ssh on aws-stg-1
ssh aws-stg-1 ls



docker exec -it "$(docker ps | awk '/web/ {print $1}')" rails single_use:remove_erroneous_last_page_fields DRY_RUN=true


servers=(
  aws-stg-1
  aws-frankfurt-1
  aws-can-1
  aws-uk-1
  aws-sam-1
  aws-usw-1
  aws-stockholm-1
  aws-paris-1
)

for server in "${servers[@]}"; do
  echo "Server $server: "
  ssh $server "sudo sh -c 'docker node ls'"
  echo ""
done

docker compose -f ~/cl2-deployment/docker-compose-production.yml run --rm web bin/rails single_use:remove_erroneous_last_page_fields DRY_RUN=true
