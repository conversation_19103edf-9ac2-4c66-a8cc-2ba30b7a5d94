# ORIGINAL PROJECT
tenant = Tenant.find_by(host: 'heistdenktmee.be')
tenant.switch!
project1 = Project.find('b04c6aec-334d-401c-9628-59569ac1dff5')
phase = project1.phases.first
phase.ideas.native_survey.published.count

# COPY PROJECT
# https://adrien-test-project-copy-2.govocal.com/en/admin/projects/c7a992cf-7abc-44c9-9f30-044fdb2f1186
tenant = Tenant.find_by(host: 'adrien-test-project-copy-2.govocal.com')
tenant.switch!
project2 = Project.find('c7a992cf-7abc-44c9-9f30-044fdb2f1186')
ParticipantsService.new.project_participants_count_uncached(project2)
phase = project2.phases.first
phase.ideas.native_survey.published.count
