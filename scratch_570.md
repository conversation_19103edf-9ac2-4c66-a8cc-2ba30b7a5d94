# Refactoring of file models

Why? because we want to make it possible to attach files that were uploaded via the back-office to the other resources that support file attachments (projects, phases, events, ideas, static pages). A more unified file modelling and storage layer will open new possibilities in terms of reusing files in different contexts.

Other advantages:
- Make the code simpler and more maintainable (hopefuly)
- 


# Refactoring of file models

Plan: Replace the legacy files (IdeaFile, ProjectFile, EventFile, PhaseFile, etc.) with:
`Files::File` model + `Files::FileAttachment` model

```mermaid
classDiagram
  File "1" --> "*" FileAttachment: has_many
  
  
  class File {
    id: uuid
    name: string
    content: string
    size: integer
    created_at: datetime
    updated_at: datetime
    deleted_at: datetime
    uploader_id: uuid
  }

  class FileAttachment {
    id: uuid
    file_id: uuid
    attachable_id: uuid
    attachable_type: string
    position: integer
  }