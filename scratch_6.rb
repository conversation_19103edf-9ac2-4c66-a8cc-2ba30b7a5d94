HOSTS_WITH_AGE_MINIMUM_15 = %w[kobenhavntaler.kk.dk]

AGE_REQUIREMENTS = {
  'kobenhavntaler.kk.dk' => 15
}

issuer = AppConfiguration.instance.settings['verification']['verification_methods'][0]['issuer']


# raise Verification::VerificationService::NotEntitledError, 'under_15_years_of_age' if age < 15


raise Verification::VerificationService::NotEntitledError, "under_#{age_requirements}_years_of_age" if age < age_requirement

age_requirement = AGE_REQUIREMENTS[issuer]
raise Verification::VerificationService::NotEntitledError, <<~MSG if age < age_requirement
  under_#{age_requirements}_years_of_age
MSG