field = CustomField.find("d7517cfa-6897-4fa8-b677-488b6fadd87e")
phase = field.resource.participation_context
idea = phase.ideas.where("custom_field_values -> 'wonen_en_woonbehoeften_4v6' ? 'luxe_woningzoekers_4p0'").sole
values = idea.custom_field_values
values['wonen_en_woonbehoeften_4v6'] = values['wonen_en_woonbehoeften_4v6'] - ['luxe_woningzoekers_4p0', 'zzp_ers_met_werkruimte_in_woning_xqd']
idea.update!(custom_field_values: values)



option = CustomFieldOption.find_by(key: "knarrenhof_zog")

field = CustomField.find("550480b3-f919-470d-bb37-02480d917a51")
phase = field.resource.participation_context
idea = phase.ideas.where("custom_field_values -> :field_key ? :option_key", field_key: field.key, option_key: 'hofjeswoningen_9ut').sole
values = idea.custom_field_values
values['wonen_en_woonbehoeften_4v6'] = values['wonen_en_woonbehoeften_4v6'] - ['hofjeswoningen_9ut']
idea.update!(custom_field_values: values)
idea.reload



def fix(field_id, option_key)
  field = CustomField.find(field_id)
  phase = field.resource.participation_context
  ideas = phase.ideas.where("custom_field_values -> :field_key ? :option_key", field_key: field.key, option_key: option_key)

  count = ideas.count
  ideas.each do |idea|
    values = idea.custom_field_values
    values[field.key] = values[field.key] - [option_key]
    idea.update!(custom_field_values: values)
  end
  
  count
end

fix("d7517cfa-6897-4fa8-b677-488b6fadd87e", "hofjeswoningen_9ut")
fix("d7517cfa-6897-4fa8-b677-488b6fadd87e", "andere_3pj")
fix("63847a06-be1f-4049-a7c1-8622d7820ab7", "zzp_ers_met_werkruimte_in_woning_ity")
fix("63847a06-be1f-4049-a7c1-8622d7820ab7", "luxe_woningzoekers_bg4")
fix("abf8c8e6-22cd-4325-861b-1af18fb26177", "hofjeswoningen_15z")
fix("abf8c8e6-22cd-4325-861b-1af18fb26177", "andere_1o0")
fix("0873a18b-32da-4b16-a426-3050815c93bc", "zzp_ers_met_werkruimte_in_woning_4vw")
fix("0873a18b-32da-4b16-a426-3050815c93bc", "luxe_woningzoekers_cwy")
fix("47b12cd5-4f37-44fa-a2a3-4c374cb25ad0", "hofjeswoningen_nas")
fix("47b12cd5-4f37-44fa-a2a3-4c374cb25ad0","andere_1n5")
fix("f100ac37-f3e1-4cd8-9010-9b43d5ea2dcd","zzp_ers_met_werkruimte_in_woning_rmh")
fix("f100ac37-f3e1-4cd8-9010-9b43d5ea2dcd","luxe_woningzoekers_u4l")
fix("ffa7da72-4a09-423a-b421-6a9e6703c7cb","andere_rpi")
fix("ffa7da72-4a09-423a-b421-6a9e6703c7cb","hofjeswoningen_edi")

