I think DMARC is a bit more than just a way to tell the receiver what to do if SPF or DKIM checks fail. I think the main intérêt here is to enforce alignment between the domains used by the different authentication mechanisms. For instance, SPF on its own only checks the "mail from" of the SMTP envelope, which can be different from the "from" header in the email (shown in the email client).

I think DMARC is more than just a way to tell the receiver what to do if SPF or DKIM checks fail. I think there are two main reasons for us to use DMARC:
- Improving security by enforcing alignment between the domains used by different authentication mechanisms. For instance, SPF alone only checks the "mail from" in the SMTP envelope, which can be different from the "from" header shown in the email client.
- Improving deliverability by following the sender guidelines of major email providers (=Gmail).

I also think we won’t be able to regularly follow up on the reports, but they could be occasionally useful for debugging deliverability issues. But again, not super familiar with this.


On va déjà vérifier dans quelle mesure cela se résorbe.
