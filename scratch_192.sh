# count the number of volumes in frankfurt region on aws
aws-vault exec prd-admin -- aws ec2 describe-volumes --region eu-central-1 | grep VolumeId | wc -l

# number of such volumes of type gp3
aws-vault exec prd-admin -- aws ec2 describe-volumes --region eu-central-1 --filters Name=volume-type,Values=gp3 | grep VolumeId | wc -l

# number of such volumes of type gp3 and in use
aws-vault exec prd-admin -- awbundles ec2 describe-volumes --region eu-central-1 --filters Name=volume-type,Values=gp3 Name=status,Values=in-use | grep VolumeId | wc -l

# delete all volumes of type gp3 and that are not in use
aws-vault exec prd-admin -- aws ec2 describe-volumes --region eu-central-1 --filters Name=volume-type,Values=gp3 Name=status,Values=available --query 'Volumes[*].{ID:VolumeId}' --output text | head -n 1 | xargs -n1 aws-vault exec prd-admin -- aws ec2 delete-volume --region eu-central-1 --volume-id
# check if that vol-07dcdb452ed6d12a4 is not in this list
aws-vault exec prd-admin -- aws ec2 describe-volumes --region eu-central-1 --filters Name=volume-type,Values=gp3 Name=status,Values=available | grep VolumeId | grep vol-07dcdb452ed6d12a4

# get list of commits on