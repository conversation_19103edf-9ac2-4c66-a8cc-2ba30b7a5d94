class SomeClass
  def self.klass_method
    puts 'SomeClass klass_method'
  end

  def instance_method_a
    puts 'SomeClass instance_method'
  end
end

module PrependedModule
  def klass_method
    puts 'PrependedModule klass_method'
  end

  def instance_method_a
    puts 'PrependedModule instance method'
  end
end
SomeClass.singleton_class.prepend PrependedModule

SomeClass.klass_method
SomeClass.instance_method_a
SomeClass.new.instance_method_a