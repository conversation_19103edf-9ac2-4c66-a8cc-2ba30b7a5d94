
Cyber security can be seen as a group of defensive processes, technologies, and practices that are specifically designed to protect internet connected machines.

Conventional approaches to cyber defense are firewalls, authentication tools, and network software that monitors, tracks, and blocks viruses and other malicious cyber attacks.

However, threats are created by the vulnerabilities in applications.

Machine learning and data mining play significant roles in the future of cyber security.

One cannot build a perfect defense system against all threats. The question is what amount of risk is acceptable given the cost of mitigating the threats. The risk can be minimized but not removed completely!

