# AWS ACM Certificates

Pourquoi faut-il créer un certificat manuellement plutôt que de le provisionner avec Terraform ?

What is the point of having different certificates for the same domain for the different cloudfront distributions?
Answer: 

arn:aws:acm:us-east-1:389841910643:certificate/0b4d2c7c-9375-44c5-a003-a3b0d302fee6 sam (*.citizenlab.cl — still in use?)

arn:aws:acm:us-east-1:389841910643:certificate/148c91cf-bef3-4ec7-afd2-8d38cb834b0d europe (*.citizenlab.co — still in use?)

arn:aws:acm:us-east-1:389841910643:certificate/7250229e-1727-4e59-a484-78c7ed0cbd68 can (*.citizenlab.co)
arn:aws:acm:us-east-1:389841910643:certificate/7250229e-1727-4e59-a484-78c7ed0cbd68 staging
arn:aws:acm:us-east-1:389841910643:certificate/7250229e-1727-4e59-a484-78c7ed0cbd68 usw

arn:aws:acm:us-east-1:389841910643:certificate/d4862b5c-c860-4ac0-9298-42bf3dbaf380 uk (*.citizenlab.co) 

