[TAN-1062] Recognize project folder moderators as workshop admins

CitizenLab JWT tokens have been modified to remove redundant roles. This change was made to mitigate the issue of JWT tokens exceeding the maximum size of a cookie in some cases. However, as a result, the roles listed in the JWT token no longer include project-moderator roles that are redundant with folder-moderator roles.

This change makes sure we also take folder-moderator roles into account when checking if a user is a workshop admin.