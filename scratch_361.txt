Thanks! So, am I interpreting correctly that consistenly since February this year about 20% or so of summaries have no references

---- response ----

@<PERSON> yes, roughly. I didn’t push the analysis to check if there was a correlation with the number of inputs, tokens or the size of the summary.

I would also add that in some cases, no included references is the right thing to do. I've observed, for example, that summaries where generated for survey questions related to personal details such the name of the respondent, their email, or their postal code.


