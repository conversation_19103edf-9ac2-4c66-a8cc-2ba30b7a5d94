# Google sheet formulas

## Number of customers per country code
=QUERY('Data-percustomer'!D:D, "SELECT D, COUNT(D) WHERE D IS NOT NULL GROUP BY D LABEL COUNT(D) 'Count'", 1)

## (Region, country code) pairs that are not US or UK
=QUERY('Data-percustomer'!C:D, "SELECT C, D WHERE C IS NOT NULL AND NOT C MATCHES 'US|UK' LABEL C 'Region', D 'Country code'", 1)

## (Region, country code, count)
=QUERY('Data-percustomer'!C:D, "SELECT C, D, COUNT(D) WHERE C IS NOT NULL GROUP BY C, D LABEL COUNT(D) 'Count'", 1)

Same but order by C
=QUERY('Data-percustomer'!C:D, "SELECT C, D, COUNT(D) WHERE C IS NOT NULL GROUP BY C, D ORDER BY C LABEL COUNT(D) 'Count'", 1)

Same but only rows that have a tenant-id. Tenant-id is in column Q
=QUERY('Data-percustomer'!C:Q, "SELECT C, D, COUNT(D) WHERE C IS NOT NULL AND Q IS NOT NULL GROUP BY C, D ORDER BY C LABEL COUNT(D) 'Count'", 1)

And where last-end-date is after 2024-08-01. Last-end-date is in column W
=QUERY('Data-percustomer'!C:W, "SELECT C, D, COUNT(D) WHERE C IS NOT NULL AND W > date '2024-08-01' GROUP BY C, D ORDER BY C LABEL COUNT(D) 'Count'", 1)