
tenant_ids = %w[
  f077469b-0687-43d1-a874-08ef032ea668
]

tenants = Tenant.where(id: tenant_ids)

service = MultiTenancy::ChurnedTenantService.new
service.remove_expired_pii(tenants)

# puts user counts
tenants.map do |tenant|
  tenant.switch do
    [User.count, tenant.host]
  end
end.sort_by(&:first)

# sum of user counts
tenants.sum { |tenant| tenant.switch { User.count } }

service = MultiTenancy::ChurnedTenantService.new
service.expired_tenants(tenants)

