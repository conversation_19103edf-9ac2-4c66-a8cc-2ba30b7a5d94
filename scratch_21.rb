def allowed?(action, record, user)
  !why_denied?(action, record, user)
end

def why_denied?(action, record, user)
  raise NotImplementedError
end

# from the resource you can get a scope
# from a scope you can get a list of permission records
# for each permission record you need to decide if the action is allowed for the user

# Permissions::ProjectScope.
#

Permission.resolve_scope(record) -> Project
Permission.resove_permission_service(record) -> Permission::ProjectService



record -> scope -> service -> service(action, record, user)