url = "https://denkmee.amstelveen.nl/uploads/c3cfe5a3-0e16-4ef5-858e-86891cc2dc53/project/header_bg/6eca48fd-884f-42f0-a2be-f332492bfa6a/8f3301b2-de87-426f-8d53-c220f3930215.jpeg"
file = Files::File.new(name: 'test.pdf')
file.remote_content_url = url
file.save!

require 'open-uri'
open(url)

require 'net/http'
require 'uri'

uri = URI.parse(url)
http = Net::HTTP.new(uri.host, uri.port)
http.use_ssl = true
http.verify_mode = OpenSSL::SSL::VERIFY_PEER  # Important: this is what triggers cert validation

request = Net::HTTP::Get.new(uri.request_uri)