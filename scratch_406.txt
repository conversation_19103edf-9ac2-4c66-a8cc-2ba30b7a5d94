diff --git a/aws/databases.tf b/aws/databases.tf
index e894379..841547b 100644
--- a/aws/databases.tf
+++ b/aws/databases.tf
@@ -149,13 +149,6 @@ resource "aws_security_group" "postgres" {
     cidr_blocks = ["**************/32"]
     description = "<PERSON><PERSON><PERSON>"
   }
-  ingress {
-    from_port   = 5432
-    to_port     = 5432
-    protocol    = "tcp"
-    cidr_blocks = ["*************/32"]
-    description = "<PERSON>"
-  }

   egress {
     from_port   = 0
diff --git a/aws/main.tf b/aws/main.tf
index faf5d44..01ebc52 100644
--- a/aws/main.tf
+++ b/aws/main.tf
@@ -1,4 +1,11 @@
 terraform {
+  backend "remote" {
+    organization = "CitizenLab"
+    workspaces {
+      prefix = "cl2-deployment-aws-"
+    }
+  }
+
   required_providers {
     aws = {
       source  = "hashicorp/aws"
diff --git a/infrastructure/iam/projects/aws-development.tfvars b/infrastructure/iam/projects/aws-development.tfvars
index cda6a31..554b761 100644
--- a/infrastructure/iam/projects/aws-development.tfvars
+++ b/infrastructure/iam/projects/aws-development.tfvars
@@ -130,19 +130,11 @@ users = [
   {
     name   = "adrien"
     groups = ["admins", "dev-insight", "kube-manager", "dev-infra"]
-  },
-  {
-    name   = "alexander"
-    groups = ["dev-structure"]
-  },
-  {
+   },
+   {
     name   = "amanda"
     groups = ["dev-method"]
   },
-  {
-    name   = "fran"
-    groups = ["dev-insight", "support"]
-  },
   {
     name   = "james"
     groups = ["admins", "dev-lead", "dev-infra"]
@@ -163,10 +155,6 @@ users = [
     name   = "simon"
     groups = ["dev-structure", "support", "admins"]
   },
-  {
-    name   = "stijn"
-    groups = ["support"]
-  },
   {
     name   = "nola"
     groups = ["support"]



# commit message
Remove access for alumni