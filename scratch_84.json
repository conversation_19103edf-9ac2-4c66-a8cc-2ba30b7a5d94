{"version": "3.12.2", "messages": ["Run options: exclude {:template_test=>true}"], "examples": [{"id": "./spec/services/permissions_service_spec.rb[1:1:1]", "description": "returns nil when action is allowed", "full_description": "PermissionsService#denied_reason_for_resource returns nil when action is allowed", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 19, "run_time": 1e-05, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:1:2]", "description": "returns `not_signed_in` when user needs to be signed in", "full_description": "PermissionsService#denied_reason_for_resource returns `not_signed_in` when user needs to be signed in", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 26, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:1:3]", "description": "returns `not_in_group` when user is not in authorized groups", "full_description": "PermissionsService#denied_reason_for_resource returns `not_in_group` when user is not in authorized groups", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 31, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:1:1:1]", "description": "example at ./spec/services/permissions_service_spec.rb:72", "full_description": "PermissionsService#denied_reason_for_permission when permitted by everyone when not signed in ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 72, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:1:2:1]", "description": "example at ./spec/services/permissions_service_spec.rb:81", "full_description": "PermissionsService#denied_reason_for_permission when permitted by everyone when light unconfirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 81, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:1:3:1]", "description": "example at ./spec/services/permissions_service_spec.rb:90", "full_description": "PermissionsService#denied_reason_for_permission when permitted by everyone when light unconfirmed inactive resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 90, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:1:4:1]", "description": "example at ./spec/services/permissions_service_spec.rb:94", "full_description": "PermissionsService#denied_reason_for_permission when permitted by everyone when fully registered confirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 94, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:1:5:1]", "description": "example at ./spec/services/permissions_service_spec.rb:103", "full_description": "PermissionsService#denied_reason_for_permission when permitted by everyone when unconfirmed admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 103, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:1:1]", "description": "example at ./spec/services/permissions_service_spec.rb:113", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when not signed in ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 113, "run_time": 2.3e-05, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:2:1]", "description": "example at ./spec/services/permissions_service_spec.rb:122", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when light unconfirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 122, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:3:1]", "description": "example at ./spec/services/permissions_service_spec.rb:131", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when light unconfirmed inactive resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 131, "run_time": 1.8e-05, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:4:1]", "description": "example at ./spec/services/permissions_service_spec.rb:137", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when light confirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 137, "run_time": 4e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:5:1]", "description": "example at ./spec/services/permissions_service_spec.rb:143", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when fully registered unconfirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 143, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:6:1]", "description": "example at ./spec/services/permissions_service_spec.rb:147", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when fully registered confirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 147, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:7:1]", "description": "example at ./spec/services/permissions_service_spec.rb:153", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when fully registered confirmed inactive resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 153, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:8:1]", "description": "example at ./spec/services/permissions_service_spec.rb:162", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when unconfirmed admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 162, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:9:1]", "description": "example at ./spec/services/permissions_service_spec.rb:168", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when confirmed admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 168, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:2:10:1]", "description": "example at ./spec/services/permissions_service_spec.rb:174", "full_description": "PermissionsService#denied_reason_for_permission when permitted by light users when confirmed inactive admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 174, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:1:1]", "description": "example at ./spec/services/permissions_service_spec.rb:184", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when not signed in ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 184, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:2:1]", "description": "example at ./spec/services/permissions_service_spec.rb:190", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when light confirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 190, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:3:1]", "description": "example at ./spec/services/permissions_service_spec.rb:196", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when light confirmed inactive resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 196, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:4:1]", "description": "example at ./spec/services/permissions_service_spec.rb:202", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when fully registered unconfirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 202, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:5:1]", "description": "example at ./spec/services/permissions_service_spec.rb:206", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when fully registered confirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 206, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:6:1]", "description": "example at ./spec/services/permissions_service_spec.rb:217", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when fully registered sso user ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 217, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:7:1]", "description": "example at ./spec/services/permissions_service_spec.rb:223", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when fully registered confirmed inactive resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 223, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:8:1]", "description": "example at ./spec/services/permissions_service_spec.rb:232", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when unconfirmed admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 232, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:9:1]", "description": "example at ./spec/services/permissions_service_spec.rb:238", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when confirmed admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 238, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:3:10:1]", "description": "example at ./spec/services/permissions_service_spec.rb:244", "full_description": "PermissionsService#denied_reason_for_permission when permitted by full residents when confirmed inactive admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 244, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:4:1:1]", "description": "example at ./spec/services/permissions_service_spec.rb:255", "full_description": "PermissionsService#denied_reason_for_permission when permitted by groups when not signed in ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 255, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:4:2:1]", "description": "example at ./spec/services/permissions_service_spec.rb:264", "full_description": "PermissionsService#denied_reason_for_permission when permitted by groups when light unconfirmed resident who is group member ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 264, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:4:3:1]", "description": "example at ./spec/services/permissions_service_spec.rb:270", "full_description": "PermissionsService#denied_reason_for_permission when permitted by groups when light confirmed resident who is not a group member ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 270, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:4:4:1]", "description": "example at ./spec/services/permissions_service_spec.rb:274", "full_description": "PermissionsService#denied_reason_for_permission when permitted by groups when fully registered resident who is not a group member ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 274, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:4:5:1]", "description": "example at ./spec/services/permissions_service_spec.rb:280", "full_description": "PermissionsService#denied_reason_for_permission when permitted by groups when admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 280, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:4:6:1]", "description": "example at ./spec/services/permissions_service_spec.rb:286", "full_description": "PermissionsService#denied_reason_for_permission when permitted by groups when confirmed inactive admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 286, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:4:7:1]", "description": "example at ./spec/services/permissions_service_spec.rb:292", "full_description": "PermissionsService#denied_reason_for_permission when permitted by groups when permitted by is changed from groups to users ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 292, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:5:1:1]", "description": "example at ./spec/services/permissions_service_spec.rb:302", "full_description": "PermissionsService#denied_reason_for_permission when permitted by moderators when not signed in ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 302, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:5:2:1]", "description": "example at ./spec/services/permissions_service_spec.rb:308", "full_description": "PermissionsService#denied_reason_for_permission when permitted by moderators when light confirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 308, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:5:3:1]", "description": "example at ./spec/services/permissions_service_spec.rb:314", "full_description": "PermissionsService#denied_reason_for_permission when permitted by moderators when fully registered unconfirmed resident ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 314, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:5:4:1]", "description": "example at ./spec/services/permissions_service_spec.rb:323", "full_description": "PermissionsService#denied_reason_for_permission when permitted by moderators when unconfirmed admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 323, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:5:5:1]", "description": "example at ./spec/services/permissions_service_spec.rb:329", "full_description": "PermissionsService#denied_reason_for_permission when permitted by moderators when confirmed admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 329, "run_time": 4e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:2:5:6:1]", "description": "example at ./spec/services/permissions_service_spec.rb:335", "full_description": "PermissionsService#denied_reason_for_permission when permitted by moderators when confirmed inactive admin ", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 335, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:1:1]", "description": "permits a visitor", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone permits a visitor", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 371, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:1:2]", "description": "permits a light unconfirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone permits a light unconfirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 392, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:1:3]", "description": "permits a fully registered confirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone permits a fully registered confirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 415, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:1:4]", "description": "permits a confirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone permits a confirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 436, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:2:1]", "description": "does not permit a visitor", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone_confirmed_email does not permit a visitor", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 467, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:2:2]", "description": "does not permit a light unconfirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone_confirmed_email does not permit a light unconfirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 490, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:2:3]", "description": "permits a light confirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone_confirmed_email permits a light confirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 515, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:2:4]", "description": "does not permit a fully registered unconfirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone_confirmed_email does not permit a fully registered unconfirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 539, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:2:5]", "description": "permits a fully registered confirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone_confirmed_email permits a fully registered confirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 563, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:2:6]", "description": "does not permit an unconfirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone_confirmed_email does not permit an unconfirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 586, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:2:7]", "description": "permits a confirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to everyone_confirmed_email permits a confirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 611, "run_time": 1.9e-05, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:3:1]", "description": "does not permit a visitor", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to users does not permit a visitor", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 644, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:3:2]", "description": "does not permit a light confirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to users does not permit a light confirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 670, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:3:3]", "description": "does not permit a fully registered unconfirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to users does not permit a fully registered unconfirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 697, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:3:4]", "description": "permits a fully registered confirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to users permits a fully registered confirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 724, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:3:5]", "description": "does not ask onboarding for a fully registered confirmed resident when onboarding is not possible", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to users does not ask onboarding for a fully registered confirmed resident when onboarding is not possible", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 750, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:3:6]", "description": "does not permit an unconfirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to users does not permit an unconfirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 780, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:3:7]", "description": "permits a confirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to users permits a confirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 808, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:4:1:1]", "description": "does not permit a visitor", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to groups user is not in the group does not permit a visitor", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 846, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:4:1:2]", "description": "does not permit a light unconfirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to groups user is not in the group does not permit a light unconfirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 869, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:4:1:3]", "description": "does not permit a fully registered confirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to groups user is not in the group does not permit a fully registered confirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 894, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:4:1:4]", "description": "does not permit an unconfirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to groups user is not in the group does not permit an unconfirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 917, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:4:1:5]", "description": "does not permit a confirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to groups user is not in the group does not permit a confirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 942, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:4:2:1]", "description": "permits a fully registered confirmed resident who is in the group", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to groups user is in the group permits a fully registered confirmed resident who is in the group", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 970, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:5:1]", "description": "does not permit a visitor", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to admins_moderators does not permit a visitor", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 998, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:5:2]", "description": "does not permit a light unconfirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to admins_moderators does not permit a light unconfirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 1019, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:5:3]", "description": "permits a fully registered unconfirmed resident", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to admins_moderators permits a fully registered unconfirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 1042, "run_time": 6e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:5:4]", "description": "permits an unconfirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to admins_moderators permits an unconfirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 1064, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:1:5:5]", "description": "permits a confirmed admin", "full_description": "PermissionsService#requirements when onboarding is possible (there are topics and areas assigned to projects) when permitted_by is set to admins_moderators permits a confirmed admin", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 1087, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:3:2:1:1]", "description": "permits a fully registered confirmed resident", "full_description": "PermissionsService#requirements when onboarding is not possible (there are no topics or areas assigned to projects) when permitted_by is set to users permits a fully registered confirmed resident", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 1120, "run_time": 3e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:4:1:1]", "description": "returns the global fields", "full_description": "PermissionsService#requirements_fields when global_custom_fields is true returns the global fields", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 1164, "run_time": 2e-06, "pending_message": null}, {"id": "./spec/services/permissions_service_spec.rb[1:4:2:1]", "description": "returns the global fields", "full_description": "PermissionsService#requirements_fields when global_custom_fields is false returns the global fields", "status": "passed", "file_path": "./spec/services/permissions_service_spec.rb", "line_number": 1173, "run_time": 2e-06, "pending_message": null}], "summary": {"duration": 0.006007, "example_count": 73, "failure_count": 0, "pending_count": 0, "errors_outside_of_examples_count": 0}, "summary_line": "73 examples, 0 failures"}