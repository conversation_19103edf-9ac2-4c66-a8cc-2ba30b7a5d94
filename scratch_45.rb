TextImage.where(imageable_type: 'HomePage').count
ContentBuilder::Layout

craftjs_json = ContentBuilder::Layout.first.craftjs_json

def extract_image_codes(json)
  nodes = json.except('ROOT').values

  nodes.filter_map do |node|
    node.dig('props', 'image', 'dataCode')
  end
end

Tenant.all.map do |tenant|
  tenant.switch do
    layout = ContentBuilder::Layout.find_by(code: 'homepage')
    image_codes = extract_image_codes(layout.craftjs_json)
    layout_images = ContentBuilder::LayoutImage.where(code: image_codes)

    if layout_images.count < image_codes.count
      puts "Missing images for #{tenant.name}"
      break
    end

    text_images = TextImage.where(imageable_type: 'HomePage')
    if text_images.count != image_codes.count
      puts "==================================================="
      puts "Layout images urls:"
      layout_images.map(&:image_url).each do |url|
        puts "*  #{url}"
      end
      puts "Text images urls:"
      text_images.map(&:image_url).each do |url|
        puts "*  #{url}"
      end
    end

    [image_codes.count, text_images.count]
  end
end

total = Tenant.count
last_message = ''
Tenant.all.map.with_index do |tenant, index|
  print "\e[#{last_message.size}D\e[K"
  last_message = "Processing #{index + 1}/#{total} - #{tenant.name}"
  print last_message
  tenant.switch { TextImage.where(imageable_type: 'HomePage').destroy_all }
end

  Tenant.all.map.with_index do |tenant, index|
    tenant.switch { TextImage.where(imageable_type: 'HomePage').count }
  end.sum


TextImage.where(imageable_type: 'HomePage').destroy_all
