pg_dump --schema-only --no-privileges --no-owner --file /cl2_back/db/structure.sql --schema=public --schema=shared_extensions -T geography_columns -T geometry_columns -T layer -T raster_columns -T raster_overviews -T spatial_ref_sys -T topology -T shared_extensions.geography_columns -T shared_extensions.geometry_columns -T shared_extensions.raster_columns -T shared_extensions.raster_overviews cl2_back_staging

# Adapt the command to run it against the remote database
# db: cl2_back_staging
# username: postgres
# host: cl2-back-staging.cekjw1iai1hl.eu-central-1.rds.amazonaws.com
# port: 5432
# password: 5d4f5d4f5d4f
pg_dump --schema-only --no-privileges --no-owner --file ./back/db/structure.sql --schema=public --schema=shared_extensions -T geography_columns -T geometry_columns -T layer -T raster_columns -T raster_overviews -T spatial_ref_sys -T topology -T shared_extensions.geography_columns -T shared_extensions.geometry_columns -T shared_extensions.raster_columns -T shared_extensions.raster_overviews -h cl2-back-staging.cekjw1iai1hl.eu-central-1.rds.amazonaws.com -p 5432 -U postgres cl2_back_staging