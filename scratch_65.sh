bin/rspec --bisect engines/commercial/multi_tenancy/spec/services/multi_tenancy/templates/tenant_serializer_spec.rb spec/services/permissions_service_spec.rb engines/commercial/user_custom_fields/spec/acceptance/stats_users_spec.rb spec/policies/follower_policy_spec.rb engines/commercial/granular_permissions/spec/acceptance/permissions_spec.rb spec/policies/comment_reaction_policy_spec.rb spec/acceptance/stats_reactions_spec.rb engines/free/email_campaigns/spec/mailers/status_change_on_initiative_you_follow_mailer_spec.rb engines/free/email_campaigns/spec/mailers/project_published_mailer_spec.rb engines/commercial/analysis/spec/services/inputs_finder_spec.rb engines/commercial/public_api/spec/acceptance/v2/initiatives_spec.rb spec/services/user_role_service_spec.rb spec/services/*****_project_copy_service_spec.rb spec/models/project_spec.rb engines/commercial/insights/spec/services/insights/category_assignments_service_spec.rb spec/services/trending_idea_service_spec.rb engines/commercial/idea_custom_fields/spec/services/xlsx_service_spec.rb spec/acceptance/events_spec.rb spec/acceptance/idea_official_feedbacks_spec.rb engines/commercial/public_api/spec/acceptance/v2/phases_spec.rb engines/commercial/idea_custom_fields/spec/acceptance/idea_custom_fields/project_context/index_spec.rb spec/serializers/web_api/v1/idea_serializer_spec.rb spec/acceptance/event_files_spec.rb engines/commercial/custom_maps/spec/acceptance/map_configs_spec.rb engines/commercial/smart_groups/spec/services/rules_service_spec.rb engines/commercial/public_api/spec/acceptance/v2/reactions_spec.rb spec/services/side_fx_reaction_service_spec.rb engines/commercial/analysis/spec/acceptance/taggings_spec.rb spec/acceptance/confirmations_spec.rb spec/policies/permissions_custom_field_policy_spec.rb spec/models/notifications/internal_comments/internal_comment_on_unassigned_initiative_spec.rb spec/models/notifications/internal_comments/internal_comment_on_idea_you_moderate_spec.rb spec/acceptance/projects_allowed_input_topics_spec.rb engines/commercial/user_custom_fields/spec/acceptance/user_custom_field_options_spec.rb spec/acceptance/events/attendances_spec.rb spec/acceptance/idea_images_spec.rb engines/free/polls/spec/policies/response_policy_spec.rb engines/commercial/bulk_import_ideas/spec/services/import_ideas_service_spec.rb spec/acceptance/event_images_spec.rb engines/commercial/analytics/spec/acceptance/analytics_participations_spec.rb spec/acceptance/phase_custom_fields_spec.rb spec/models/notifications/internal_comments/mention_in_internal_comment_spec.rb engines/commercial/analysis/spec/lib/q_and_a_method_spec.rb engines/commercial/content_builder/spec/services/content_builder/side_fx_layout_service_spec.rb spec/models/area_spec.rb engines/free/polls/spec/acceptance/options_spec.rb engines/commercial/analytics/spec/acceptance/analytics_fact_schema_spec.rb spec/models/custom_field_spec.rb