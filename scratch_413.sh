# eval expression with terraform cli
# get aws_cloudfront_distribution.cluster_cf
terraform console
aws_cloudfront_distribution.cluster_cf


ssh -L 5434:cl2-back-production-benelux.cekjw1iai1hl.eu-central-1.rds.amazonaws.com:5432 -L 5435:cl2-workshops-production-benelux.cekjw1iai1hl.eu-central-1.rds.amazonaws.com:5432 aws-prd-1
ssh -L 5436:cl2-back-prd-paris.c9cia58cd5l0.eu-west-3.rds.amazonaws.com:5432 -L 5437:cl2-workshops-prd-paris.c9cia58cd5l0.eu-west-3.rds.amazonaws.com:5432 aws-paris-1
aws-vault exec prd-admin -- docker compose run --rm --build script -m move -f frankfurt -t paris