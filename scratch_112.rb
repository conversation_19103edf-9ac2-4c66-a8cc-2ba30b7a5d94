require 'que/active_record/model'

class QueJob < Que::ActiveRecord::Model
end

# Query by the first element of the args (jsonb column)
QueJob.scheduled.select("args->0->>'job_class' AS job_class").limit(10).pluck(:job_class).tally


# SELECT COUNT(*)
# FROM que_jobs
# WHERE expired_at IS NULL
#   AND finished_at IS NULL
#   AND job_schema_version = 1;

QueJob.not_expired.not_finished.where(job_schema_version: 1).count
QueJob.not_expired.not_finished.where(job_schema_version: 1).delete_all