# build and run the docker image in the current directory
docker build . && docker run -it <image_id> /bin/bash

# build until the 3rd layer and start a bash shell in the container
docker build --target=3 <image_id>

# update prerender dependency (yarn) from 5.6.0 to 5.21.1
yarn add prerender@5.21.1

# update pm2 dependency (yarn) from 5.3.1
yarn add pm2@5.3.1

# pull node:12.13 image from docker hub
docker pull node:12.13
# get meta info about node:12.13 image from docker hub
docker inspect node:12.13

# run container and sync the current directory with /app in the container
docker run -v $(pwd):/usr/src/app -it --rm b37ea8a17672 /bin/bash


# start a container with image 'prerender'
# with its default command
# port mapping 3300:3000
docker run -p 3300:3000 prerender