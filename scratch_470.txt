{
    "AWSTemplateFormatVersion": "2010-09-09",
    "Parameters": {
        "wildCardDomain": {
            "Type": "String"
        },
        "LoadBalancerDomain": {
            "Type": "String",
            "Default": "cl2-prd-benelux-load-balancer-47306379.eu-central-1.elb.amazonaws.com"
        },
        "s3Domain": {
            "Type": "String",
            "Default": "cl2-front-production-benelux.s3.amazonaws.com"
        },
        "sslCertificate": {
            "Type": "String"
        }
    },
    "Resources": {
        "cloudfrontDistribution": {
            "Type": "AWS::CloudFront::Distribution",
            "Properties": {
                "DistributionConfig": {
                    "Origins": [
                        {
                            "CustomOriginConfig": {
                                "OriginProtocolPolicy": "http-only"
                            },
                            "DomainName": {
                                "Ref": "LoadBalancerDomain"
                            },
                            "Id": "LoadBalancer"
                        },
                        {
                            "S3OriginConfig": {},
                            "DomainName": {
                                "Ref": "s3Domain"
                            },
                            "Id": "S3-cl2-front"
                        }
                    ],
                    "Aliases": [
                        {
                            "Ref": "wildCardDomain"
                        }
                    ],
                    "CacheBehaviors": [
                        {
                            "Compress": true,
                            "ForwardedValues": {
                                "QueryString": false
                            },
                            "PathPattern": "*.js",
                            "TargetOriginId": "S3-cl2-front",
                            "ViewerProtocolPolicy": "redirect-to-https"
                        },
                        {
                            "Compress": true,
                            "ForwardedValues": {
                                "QueryString": false
                            },
                            "PathPattern": "*.svg",
                            "TargetOriginId": "S3-cl2-front",
                            "ViewerProtocolPolicy": "redirect-to-https"
                        },
                        {
                            "Compress": true,
                            "ForwardedValues": {
                                "QueryString": false
                            },
                            "PathPattern": "*.woff2",
                            "TargetOriginId": "S3-cl2-front",
                            "ViewerProtocolPolicy": "redirect-to-https"
                        },
                        {
                            "Compress": true,
                            "ForwardedValues": {
                                "QueryString": false
                            },
                            "PathPattern": "*.woff",
                            "TargetOriginId": "S3-cl2-front",
                            "ViewerProtocolPolicy": "redirect-to-https"
                        },
                        {
                            "Compress": true,
                            "ForwardedValues": {
                                "QueryString": false
                            },
                            "PathPattern": "*.ttf",
                            "TargetOriginId": "S3-cl2-front",
                            "ViewerProtocolPolicy": "redirect-to-https"
                        },
                        {
                            "Compress": true,
                            "ForwardedValues": {
                                "QueryString": false
                            },
                            "PathPattern": "*.jpg",
                            "TargetOriginId": "S3-cl2-front",
                            "ViewerProtocolPolicy": "redirect-to-https"
                        },
                        {
                            "Compress": true,
                            "ForwardedValues": {
                                "QueryString": false
                            },
                            "PathPattern": "*.png",
                            "TargetOriginId": "S3-cl2-front",
                            "ViewerProtocolPolicy": "redirect-to-https"
                        },
                        {
                            "Compress": true,
                            "ForwardedValues": {
                                "QueryString": false
                            },
                            "PathPattern": "*.css",
                            "TargetOriginId": "S3-cl2-front",
                            "ViewerProtocolPolicy": "redirect-to-https"
                        },
                    ],
                    "DefaultRootObject": "index.html",
                    "Enabled": true,
                    "DefaultCacheBehavior": {
                        "AllowedMethods": [
                            "DELETE",
                            "GET",
                            "HEAD",
                            "OPTIONS",
                            "PATCH",
                            "POST",
                            "PUT"
                        ],
                        "Compress": false,
                        "DefaultTTL": 0,
                        "MinTTL": 0,
                        "MaxTTL": 0,
                        "ForwardedValues": {
                            "Cookies": {
                                "Forward": "all"
                            },
                            "Headers": [
                                "*"
                            ],
                            "QueryString": true
                        },
                        "ViewerProtocolPolicy": "redirect-to-https",
                        "TargetOriginId": "LoadBalancer"
                    },
                    "ViewerCertificate": {
                        "AcmCertificateArn": {
                            "Ref": "sslCertificate"
                        },
                        "SslSupportMethod": "sni-only"
                    }
                },
                "Tags" : [
                    {"Key": "platform", "Value": "cl2"},
                    {"Key": "auto-provisioned", "Value": "true"}
                ]
            }
        }
    }
}


live:.cid.75fbcb8b26fa06c6
damon__uk

live:.cid.305d5d37c621690f