# List all classes (not modules) in SmartGroups::Rules
rule_classes = SmartGroups::Rules.constants.filter_map do |const|
  mod = SmartGroups::Rules.const_get(const)
  mod if mod.is_a?(Class)
end

# Get all the public methods of instances of rule_classes
# without listing the methods from the included modules: ActiveModel::Validations, DescribableRule
rule_methods = rule_classes.to_h do |klass|
  methods = klass.public_instance_methods(false)

  [klass, methods]
end



# Compute the intersection of the methods
intersection = rule_methods.values.reduce(:&)


# List methods defined in a module
SmartGroups::Rules::DescribableRule.public_methods(false)
