I actually find it quite readable that way, certainly more so than something like this:
```
job_trackers = paginate(
  policy_scope(
    Jobs::Tracker
      .where(index_params)
      .order(created_at: :desc)
  )
)
```

I’d say it’s on par with:
```
job_trackers = Jobs::Tracker
  .where(index_params)
  .order(created_at: :desc)

job_trackers = policy_scope(job_trackers)
job_trackers = paginate(job_trackers)
```

It’s just a bit more minimalistic, with a more functional feel. Also looking forward to upgrading to the next version of Ruby, where this can be written as:
```
job_trackers = Jobs::Tracker
  .where(index_params)
  .order(created_at: :desc)
  .then { policy_scope(it) }
  .then { paginate(it) }
```

That said, I get that it ultimately comes down to personal preference (kind of like the `unless`/`if` debate, which I’ve never fully understood). Still, I find it pretty frustrating that something like this gets flagged when IMO the complexity of your codebase isn’t coming from things like this.
