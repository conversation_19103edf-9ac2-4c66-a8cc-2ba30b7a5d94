{"Resources": {"cloudfrontDistribution": {"Type": "AWS::CloudFront::Distribution", "Properties": {"Tags": [{"Key": "platform", "Value": "cl2"}, {"Key": "auto-provisioned", "Value": "true"}], "DistributionConfig": {"Aliases": [{"Ref": "wildCardDomain"}], "Enabled": true, "Origins": [{"Id": "LoadBalancer", "DomainName": {"Ref": "LoadBalancerDomain"}, "CustomOriginConfig": {"OriginReadTimeout": 60, "OriginProtocolPolicy": "http-only"}}, {"Id": "GlobalLoadBalancer", "DomainName": {"Ref": "GlobalLoadBalancerDomain"}, "CustomOriginConfig": {"OriginReadTimeout": 60, "OriginProtocolPolicy": "https-only"}}, {"Id": "S3-cl2-front", "DomainName": {"Ref": "s3Domain"}, "S3OriginConfig": {}}, {"Id": "S3-cl2-cms", "DomainName": {"Ref": "s3Cms"}, "S3OriginConfig": {}}, {"Id": "S3-cl2-tenants", "DomainName": {"Ref": "s3Assets"}, "S3OriginConfig": {}}], "HttpVersion": "http2", "CacheBehaviors": [{"MaxTTL": 0, "MinTTL": 0, "DefaultTTL": 0, "PathPattern": "/workshops*", "AllowedMethods": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"], "TargetOriginId": "LoadBalancer", "ForwardedValues": {"Cookies": {"Forward": "all"}, "Headers": ["*"], "QueryString": true}, "ViewerProtocolPolicy": "redirect-to-https"}, {"MaxTTL": 86400, "MinTTL": 0, "Compress": true, "DefaultTTL": 86400, "PathPattern": "/fonts/*", "TargetOriginId": "S3-cl2-cms", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "*.js", "TargetOriginId": "S3-cl2-front", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "/uploads/*", "TargetOriginId": "S3-cl2-tenants", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "*.svg", "TargetOriginId": "S3-cl2-front", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "*.woff2", "TargetOriginId": "S3-cl2-front", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "*.woff", "TargetOriginId": "S3-cl2-front", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "*.ttf", "TargetOriginId": "S3-cl2-front", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "*.jpg", "TargetOriginId": "S3-cl2-front", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "*.png", "TargetOriginId": "S3-cl2-front", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"MaxTTL": 86400, "MinTTL": 0, "Compress": true, "DefaultTTL": 86400, "PathPattern": "/fragments/*", "TargetOriginId": "S3-cl2-cms", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "*.css", "TargetOriginId": "S3-cl2-front", "ForwardedValues": {"QueryString": false}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "/sitemap.xml", "TargetOriginId": "LoadBalancer", "ForwardedValues": {"Cookies": {"Forward": "all"}, "Headers": ["*"], "QueryString": true}, "ViewerProtocolPolicy": "redirect-to-https"}, {"Compress": true, "PathPattern": "/robots.txt", "TargetOriginId": "LoadBalancer", "ForwardedValues": {"Cookies": {"Forward": "all"}, "Headers": ["*"], "QueryString": true}, "ViewerProtocolPolicy": "redirect-to-https"}, {"MaxTTL": 120, "MinTTL": 120, "Compress": true, "DefaultTTL": 120, "PathPattern": "/widgets/*", "TargetOriginId": "LoadBalancer", "ForwardedValues": {"Cookies": {"Forward": "none"}, "Headers": ["Accept-Language", "Host"], "QueryString": true}, "ViewerProtocolPolicy": "redirect-to-https"}, {"PathPattern": "/admin_templates_api/*", "AllowedMethods": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"], "TargetOriginId": "GlobalLoadBalancer", "ForwardedValues": {"Cookies": {"Forward": "all"}, "Headers": ["*"], "QueryString": true}, "ViewerProtocolPolicy": "redirect-to-https"}], "ViewerCertificate": {"SslSupportMethod": "sni-only", "AcmCertificateArn": {"Ref": "sslCertificate"}, "MinimumProtocolVersion": "TLSv1.2_2021"}, "DefaultCacheBehavior": {"MaxTTL": 0, "MinTTL": 0, "Compress": false, "DefaultTTL": 0, "AllowedMethods": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"], "TargetOriginId": "LoadBalancer", "ForwardedValues": {"Cookies": {"Forward": "all"}, "Headers": ["*"], "QueryString": true}, "ViewerProtocolPolicy": "redirect-to-https", "LambdaFunctionAssociations": [{"Fn::If": ["setHSTSLambda", {"EventType": "origin-response", "LambdaFunctionARN": "arn:aws:lambda:us-east-1:389841910643:function:setHSTSResponseHeader:2"}, {"Ref": "AWS::NoValue"}]}]}}}}}, "Conditions": {"setHSTSLambda": {"Fn::Equals": [{"Ref": "setHSTSLambda"}, "true"]}}, "Parameters": {"s3Cms": {"Type": "String", "Default": "cl2-cms.s3.amazonaws.com"}, "s3Assets": {"Type": "String", "Default": "cl2-tenants-production-benelux.s3.amazonaws.com"}, "s3Domain": {"Type": "String", "Default": "cl2-front-production-benelux.s3.amazonaws.com"}, "setHSTSLambda": {"Type": "String", "Default": "false"}, "sslCertificate": {"Type": "String"}, "wildCardDomain": {"Type": "String"}, "LoadBalancerDomain": {"Type": "String", "Default": "cl2-prd-benelux-load-balancer-47306379.eu-central-1.elb.amazonaws.com"}, "GlobalLoadBalancerDomain": {"Type": "String", "Default": "templates.hq.citizenlab.co"}}, "AWSTemplateFormatVersion": "2010-09-09"}