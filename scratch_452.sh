#!/bin/bash

# Store the nameserver we're looking for
SEARCH_NS="ns1.citizenlab.eu"

# Get all hosted zones and process them
aws-vault exec prd-admin -- aws route53 list-hosted-zones --query 'HostedZones[*].Id' --output text | while read -r zone_id; do
    # Remove the leading /hostedzone/ from the ID
    clean_zone_id="${zone_id#/hostedzone/}"

    # Get the nameservers for this zone
    aws-vault exec prd-admin -- aws route53 get-hosted-zone --id "$clean_zone_id" --query 'DelegationSet.NameServers' --output text | grep -q "$SEARCH_NS"

    if [ $? -eq 0 ]; then
        # If we found the nameserver, get and display the zone name
        zone_name=$aws-vault exec prd-admin -- (aws route53 get-hosted-zone --id "$clean_zone_id" --query 'HostedZone.Name' --output text)
        echo "Found matching zone: $zone_name (ID: $clean_zone_id)"
    fi
done

aws route53 create-hosted-zone --name voresviden.slagelse.dk --caller-reference cl-slagelse --delegation-set-id "/delegationset/N2Y2089R06R2NY" --profile support

# find the delegation set
aws-vault exec prd-admin -- aws route53 list-reusable-delegation-sets