[
  "multi_tenancy",

  "admin_api",
  "analytics",
  "bulk_import_ideas",
  "content_builder",
  "custom_idea_statuses",
  "custom_maps",
  "custom_topics",
  "flag_inappropriate_content",
  "geographic_dashboard",
  "google_tag_manager",
  "granular_permissions",
  "idea_assignment",
  "idea_custom_fields",
  "impact_tracking",
  "insights",
  "machine_translations",
  "matomo",
  "moderation",
  "nlp",
  "public_api",
  "remove_vendor_branding",
  "report_builder",
  "smart_groups",
  "texting",
  "user_custom_fields",

  # ID verification engines
  # ID verification methods must be installed before the verification engine.
  "id_auth0",
  "id_bogus",
  "id_bosa_fas",
  "id_clave_unica",
  "id_cow",
  "id_franceconnect",
  "id_gent_rrn",
  "id_id_card_lookup",
  "id_oostende_rrn",
  "id_vienna_saml",
  "verification"
]