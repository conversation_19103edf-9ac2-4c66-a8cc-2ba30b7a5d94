processed_tenants = ["prc-demo_citizenlab_co",
"urbanparticipationdemo_citizenlab_co",
"codelcoandina_citizenlab_co",
"consultaunicanacional_citizenlab_co",
"sanisidroparticipa_citizenlab_co",
"demosanpedroatacama_citizenlab_co",
"participaconenergia_minenergia_cl",
"santaanatzacuala-hidalgo_citizenlab_co",
"penalolenmascerca_cl",
"derechoshumanoslocales_cl",
"constitucion_citizenlab_co",
"vinadecide_cl",
"participa_muniarica_cl",
"demoaquachile_citizenlab_co",
"bucaramangaparticipa_citizenlab_co",
"temuco2024_citizenlab_co",
"participacionuruguay_citizenlab_co",
"muni-sanborja-demo_citizenlab_co",
"paraguayacclab-demo_citizenlab_co",
"sectrademo_citizenlab_co",
"cocacolademo_citizenlab_co",
"turismochile_citizenlab_co",
"demoestacioncentral_citizenlab_co",
"participapuchuncavi_citizenlab_co",
"participacionminticcolombia_citizenlab_co",
"prolima-demo_citizenlab_co",
"algarroboparticipa_citizenlab_co",
"participa_minmineria_gob_cl",
"vitacuraparticipa_citizenlab_co",
"agendadigitalrd_citizenlab_co",
"democolbunmulchen_citizenlab_co",
"nuestravozimporta_cl",
"consultadeley_citizenlab_co",
"participa_mpudahuel_cl",
"sernacdemo_citizenlab_co",
"antucoyaparticipa_cl",
"mopchile_citizenlab_co",
"dialogoreformas3_citizenlab_co",
"participaquinta-normal_citizenlab_co",
"san-esteban_citizenlab_co",
"mirafloresparticipa_citizenlab_co",
"municipiosenpandemia_citizenlab_co",
"sanmiguelparticipa_citizenlab_co",
"banco-santander-demo_citizenlab_co",
"conectajusticia_minjusticia_gob_cl",
"redmercosurfromargentina_citizenlab_co",
"acerca-redes_citizenlab_co",
"talca_citizenlab_co",
"conectados_citizenlab_co",
"participa_lareina_cl",
"mimunicipioemergencia_citizenlab_co",
"sanpedrodecide-demo_citizenlab_co",
"jerico_amigo_community",
"saneamentodebasenatural_citizenlab_co",
"dguminvuparticipa_citizenlab_co",
"participacion_digital_gob_cl",
"san-vicente_citizenlab_co",
"participacionambiental_citizenlab_co",
"digitalrd_citizenlab_co",
"caimanespelambres_citizenlab_co",
"san-bernardo_citizenlab_co",
"ejealamedaprovidencia_citizenlab_co",
"vinadelmarparticipa_citizenlab_co",
"jaiba_citizenlab_co",
"girlsrights_citizenlab_co",
"participacion_cecrea_cl",
"minvu_citizenlab_co",
"participanoneutrales_cl"]

# Partially processed
"centinelaparticipa_cl"


