# File Attachment Shared Examples

This document explains how to use the reusable shared examples for testing file attachment functionality across different container types.

## Overview

The `file_attachment_examples.rb` shared examples provide comprehensive test coverage for file attachment functionality that can be reused across different container types (projects, ideas, events, phases, static pages, etc.).

## Features Tested

The shared examples test the following functionality:

1. **File Listing (GET)**: List files for a container, handling both legacy files and new file attachments
2. **File Retrieval (GET)**: Get individual files by ID, supporting both legacy and new file systems
3. **File Ordering (PATCH)**: Update file ordering/positioning
4. **File Deletion (DELETE)**: Delete files, with proper cleanup of orphaned files
5. **File Creation (POST)**: Create new files, with automatic detection of legacy vs new file system
6. **Permission Checks**: Proper authorization based on container type
7. **JSON Response Structure**: Validation of API response format
8. **Error Handling**: Proper error responses for invalid operations

## Usage

### Basic Usage

```ruby
require_relative '../shared/file_attachment_examples'

resource 'YourContainerFileAttachments' do
  header 'Content-Type', 'application/json'
  before { admin_header_token } # or appropriate authentication

  let_it_be(:your_container) { create(:your_container_factory) }
  let(:your_container_id) { your_container.id }

  include_examples 'file attachment functionality',
    container_factory: :your_container_factory,
    container_id_param: :your_container_id,
    api_path_prefix: 'your_containers'
end
```

### Parameters

- `container_factory`: The factory name for creating the container (e.g., `:project`, `:idea`, `:event`)
- `container_id_param`: The parameter name for the container ID in the URL (e.g., `:project_id`, `:idea_id`)
- `api_path_prefix`: The API path prefix for the container (e.g., `'projects'`, `'ideas'`, `'events'`)

### Supported Container Types

The shared examples currently support:

- **Projects** (`container_factory: :project`)
- **Ideas** (`container_factory: :idea`)
- **Events** (`container_factory: :event`)
- **Phases** (`container_factory: :phase`)
- **Static Pages** (`container_factory: :static_page`)

### Examples

#### Project File Attachments
```ruby
include_examples 'file attachment functionality',
  container_factory: :project,
  container_id_param: :project_id,
  api_path_prefix: 'projects'
```

#### Idea File Attachments
```ruby
include_examples 'file attachment functionality',
  container_factory: :idea,
  container_id_param: :idea_id,
  api_path_prefix: 'ideas'
```

#### Event File Attachments
```ruby
include_examples 'file attachment functionality',
  container_factory: :event,
  container_id_param: :event_id,
  api_path_prefix: 'events'
```

## Authentication Requirements

Different container types may have different authentication requirements:

- **Projects/Events/Phases**: Usually require admin authentication (`admin_header_token`)
- **Ideas**: May require the user to be the author of the idea (`header_token_for(user)`)
- **Static Pages**: Usually require admin authentication

Make sure to set up appropriate authentication in your test file's `before` block.

## Legacy File System Support

The shared examples automatically handle both legacy file systems (e.g., `ProjectFile`, `IdeaFile`) and the new unified file attachment system (`Files::FileAttachment`). The tests verify:

- When legacy files exist, they are returned instead of file attachments
- When no legacy files exist, file attachments are used
- File creation uses the appropriate system based on existing files
- Both systems maintain proper ordering and metadata

## Adding Support for New Container Types

To add support for a new container type:

1. Ensure your container model includes `Files::FileAttachable`
2. Create the appropriate legacy file model and factory if needed
3. Add the container type to the case statements in the shared examples
4. Create a test file using the shared examples

## File Structure

- `back/spec/acceptance/shared/file_attachment_examples.rb` - The shared examples
- `back/spec/acceptance/files/file_attachments_spec.rb` - Project file attachment tests
- `back/spec/acceptance/files/idea_file_attachments_spec.rb` - Idea file attachment tests
- `back/spec/acceptance/files/event_file_attachments_spec.rb` - Event file attachment tests

## Benefits

Using these shared examples provides:

1. **Consistency**: All container types have the same test coverage
2. **Maintainability**: Changes to file attachment logic only need to be updated in one place
3. **Comprehensive Coverage**: All edge cases and scenarios are tested
4. **Rails Best Practices**: Tests follow established patterns with proper let blocks and clear descriptions
5. **Extensibility**: Easy to add new container types or test scenarios
