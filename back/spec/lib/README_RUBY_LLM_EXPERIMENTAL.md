# RubyLLM Experimental Testing

This directory contains experimental tests for the RubyLLM gem integration in the CitizenLab application.

## Overview

The `ruby_llm_experimental_spec.rb` file serves as a playground for testing RubyLLM functionality, including:

- Basic text generation and chat functionality
- Streaming responses
- File analysis capabilities
- Custom tool creation and usage
- Image generation (when configured)
- Text embeddings (when configured)
- Error handling
- Performance testing

## Setup

### 1. Install Dependencies

The RubyLLM gem has already been added to the Gemfile. If you need to reinstall:

```bash
docker compose exec web bundle install
```

### 2. Configure API Keys

Set up environment variables for the AI providers you want to test:

```bash
# OpenAI (recommended for initial testing)
export OPENAI_API_KEY="your-openai-api-key"

# Anthropic Claude
export ANTHROPIC_API_KEY="your-anthropic-api-key"

# Google Gemini
export GEMINI_API_KEY="your-gemini-api-key"

# AWS Bedrock (uses existing AWS credentials)
export AWS_ACCESS_KEY_ID="your-aws-access-key"
export AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
export AWS_REGION="us-east-1"

# OpenRouter
export OPENROUTER_API_KEY="your-openrouter-api-key"

# DeepSeek
export DEEPSEEK_API_KEY="your-deepseek-api-key"

# Ollama (for local models)
export OLLAMA_API_BASE="http://localhost:11434/v1"
```

### 3. Optional Configuration

You can customize default settings:

```bash
export RUBY_LLM_DEFAULT_MODEL="gpt-4o-mini"
export RUBY_LLM_DEFAULT_EMBEDDING_MODEL="text-embedding-3-small"
export RUBY_LLM_DEFAULT_IMAGE_MODEL="dall-e-3"
export RUBY_LLM_REQUEST_TIMEOUT="120"
export RUBY_LLM_MAX_RETRIES="3"
```

## Running Tests

### Run All Experimental Tests

```bash
docker compose exec web bash -c "cd /cl2_back && RUBY_LLM_EXPERIMENTAL=true bundle exec rspec spec/lib/ruby_llm_experimental_spec.rb"
```

### Run Specific Test Groups

```bash
# Test basic configuration only
docker compose exec web bash -c "cd /cl2_back && RUBY_LLM_EXPERIMENTAL=true bundle exec rspec spec/lib/ruby_llm_experimental_spec.rb -e 'Basic Configuration'"

# Test text generation
docker compose exec web bash -c "cd /cl2_back && RUBY_LLM_EXPERIMENTAL=true bundle exec rspec spec/lib/ruby_llm_experimental_spec.rb -e 'Text Generation'"

# Test streaming responses
docker compose exec web bash -c "cd /cl2_back && RUBY_LLM_EXPERIMENTAL=true bundle exec rspec spec/lib/ruby_llm_experimental_spec.rb -e 'Streaming Responses'"

# Test custom tools
docker compose exec web bash -c "cd /cl2_back && RUBY_LLM_EXPERIMENTAL=true bundle exec rspec spec/lib/ruby_llm_experimental_spec.rb -e 'Custom Tools'"
```

### Run with VCR for Recording API Calls

The tests use VCR to record API interactions. To record new cassettes:

```bash
docker compose exec web bash -c "cd /cl2_back && RUBY_LLM_EXPERIMENTAL=true VCR_RECORD_MODE=new_episodes bundle exec rspec spec/lib/ruby_llm_experimental_spec.rb"
```

## Test Structure

### Basic Configuration Tests
- Verifies RubyLLM module is available
- Tests chat instance creation
- Validates basic setup

### Text Generation Tests
- Simple question/answer interactions
- Conversation memory testing
- Model-specific configurations

### Streaming Tests
- Real-time response streaming
- Chunk processing validation

### File Analysis Tests
- CSV file analysis
- Document processing capabilities

### Custom Tools Tests
- Function calling with custom Ruby classes
- Tool parameter validation
- Error handling in tools

### Performance Tests
- Response time measurements
- Token limit testing
- Temperature setting validation

## Customizing Tests

### Adding New Test Cases

1. Add new test cases to the appropriate `describe` block
2. Use `let` blocks for setup following Rails testing best practices
3. Include `:vcr` tag for tests that make API calls
4. Add `skip` conditions for tests requiring specific configurations

### Creating Custom Tools

```ruby
class YourCustomTool < RubyLLM::Tool
  description 'Description of what your tool does'
  param :param_name, desc: 'Parameter description'
  
  def execute(param_name:)
    # Your tool logic here
    { result: 'your result' }
  end
end
```

### Testing Different Models

```ruby
let(:specific_model_chat) { RubyLLM.chat(model_id: 'gpt-4o') }
```

## Safety Notes

- Tests are skipped by default to prevent accidental API calls
- Always set `RUBY_LLM_EXPERIMENTAL=true` to run tests
- API calls will consume credits/tokens from your accounts
- Use VCR cassettes to avoid repeated API calls during development
- Be mindful of rate limits when running tests frequently

## Troubleshooting

### Common Issues

1. **Tests are skipped**: Ensure `RUBY_LLM_EXPERIMENTAL=true` is set
2. **API key errors**: Verify environment variables are set correctly
3. **Network timeouts**: Check internet connection and API service status
4. **Model not found**: Verify the model ID is correct for your provider
5. **Rate limiting**: Wait between test runs or use VCR cassettes

### Getting Help

- Check the [RubyLLM documentation](https://rubyllm.com/)
- Review the existing Analysis engine in `back/engines/commercial/analysis/` for patterns
- Look at existing service specs in `back/spec/services/` for testing patterns

## Integration with Existing Code

This experimental setup can be used to prototype features that might later be integrated with:

- The existing Analysis engine (`back/engines/commercial/analysis/`)
- Content generation services
- User assistance features
- Document processing workflows

### Comparison with Existing Analysis Engine

The CitizenLab application already has an Analysis engine that uses OpenAI directly via the `ruby-openai` gem. Here's how RubyLLM compares:

**Existing Analysis Engine:**
- Located in `back/engines/commercial/analysis/`
- Uses `ruby-openai` gem directly
- Supports Azure OpenAI via custom configuration
- Has specialized classes like `Analysis::LLM::AzureOpenAI` and `Analysis::LLM::GPT41`
- Integrated with the application's analysis workflows

**RubyLLM Advantages:**
- Unified interface for multiple providers (OpenAI, Anthropic, Gemini, Bedrock, etc.)
- Simpler API with consistent response formats
- Built-in streaming support
- Tool/function calling capabilities
- File analysis (images, PDFs, audio)
- Rails integration helpers
- Better error handling and retry logic

**When to Use Each:**
- **Use existing Analysis engine** for production analysis features that are already working
- **Use RubyLLM** for new features, prototyping, or when you need multi-provider support
- **Consider migrating** existing Analysis features to RubyLLM for consistency and additional capabilities

Remember to follow the project's existing patterns and conventions when integrating RubyLLM functionality into the main application.
