# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'RubyLLM Experimental Playground' do
  let(:chat) { RubyLLM.chat }
  let(:test_prompt) { 'Hello! Please respond with exactly: "Ruby<PERSON><PERSON> is working correctly"' }
  let(:simple_question) { 'What is 2 + 2? Please respond with just the number.' }

  describe 'Text Generation' do
    context 'with simple prompts' do
      it 'responds to basic questions' do
        response = chat.ask(simple_question)
        expect(response).to be_present
        expect(response.content).to eq('4')
      end

      it 'handles longer conversations' do
        # First message
        response1 = chat.ask('My name is <PERSON>. Please remember this.')
        expect(response1).to be_present

        # Follow-up message testing memory
        response2 = chat.ask('What is my name?')
        expect(response2).to be_present
        expect(response2.downcase).to include('alice')
      end
    end

    context 'with different model configurations' do
      let(:gpt_chat) { RubyLLM.chat(model_id: 'gpt-4o-mini') }

      it 'can use specific models' do
        skip 'OpenAI API key not configured' unless ENV['OPENAI_API_KEY'].present?

        response = gpt_chat.ask(test_prompt)
        expect(response).to be_present
        expect(response).to include('RubyLLM is working correctly')
      end
    end
  end

  describe 'Streaming Responses' do
    context 'when streaming is enabled' do
      it 'can stream responses in chunks' do
        chunks = []

        chat.ask('Count from 1 to 5, each number on a new line') do |chunk|
          chunks << chunk.content if chunk.content.present?
        end

        expect(chunks).not_to be_empty
        full_response = chunks.join
        expect(full_response).to be_present
      end
    end
  end

  describe 'File Analysis' do
    let(:test_file_path) { Rails.root.join('spec/fixtures/david.csv') }

    context 'when analyzing files' do
      it 'can analyze CSV files' do
        require 'pry'
        binding.pry
        chat = RubyLLM.chat(model_id: 'gemini-1.5-pro-latest')
        response = chat.ask('What type of data is in this file? Give a brief summary.', with: test_file_path.to_s)
        expect(response).to be_present
        expect(response.downcase).to include('csv').or include('data')
      end
    end
  end

  describe 'Custom Tools' do
    # Example tool for testing function calling
    class TestCalculator < RubyLLM::Tool
      description 'Performs basic arithmetic calculations'
      param :operation, desc: 'The operation to perform (add, subtract, multiply, divide)'
      param :a, desc: 'First number'
      param :b, desc: 'Second number'

      def execute(operation:, a:, b:)
        a = a.to_f
        b = b.to_f

        case operation.downcase
        when 'add'
          { result: a + b }
        when 'subtract'
          { result: a - b }
        when 'multiply'
          { result: a * b }
        when 'divide'
          return { error: 'Cannot divide by zero' } if b.zero?

          { result: a / b }
        else
          { error: 'Unknown operation' }
        end
      end
    end

    context 'when using custom tools' do
      let(:tool_chat) { chat.with_tool(TestCalculator) }

      it 'can use tools for calculations' do
        response = tool_chat.ask('Please calculate 15 + 27 using the calculator tool')
        expect(response).to be_present
        # The response should mention the result or show that the tool was used
        expect(response).to include('42').or include('calculation')
      end

      it 'handles tool errors gracefully' do
        response = tool_chat.ask('Please divide 10 by 0 using the calculator tool')
        expect(response).to be_present
        # Should handle the division by zero error
        expect(response.downcase).to include('zero').or include('error')
      end
    end
  end

  describe 'Image Generation' do
    context 'when generating images' do
      it 'can generate images with paint method' do
        skip 'Image generation requires specific API configuration'

        # This is a placeholder for image generation testing
        # Uncomment and modify based on your API setup
        # image_result = RubyLLM.paint('a simple red circle on white background')
        # expect(image_result).to be_present
      end
    end
  end

  describe 'Embeddings' do
    context 'when creating embeddings' do
      it 'can create text embeddings' do
        skip 'Embeddings require specific API configuration'

        # This is a placeholder for embeddings testing
        # Uncomment and modify based on your API setup
        # embedding = RubyLLM.embed('Ruby is a beautiful programming language')
        # expect(embedding).to be_present
        # expect(embedding).to be_an(Array)
      end
    end
  end

  describe 'Error Handling' do
    context 'when handling errors' do
      it 'gracefully handles invalid prompts' do
        expect do
          chat.ask('')
        end.not_to raise_error
      end

      it 'handles network timeouts gracefully' do
        # This would require mocking network calls
        # expect { ... }.not_to raise_error
        pending 'Network timeout testing requires additional setup'
      end
    end
  end

  describe 'Configuration Testing' do
    context 'when testing different configurations' do
      it 'can test with different temperature settings' do
        # Test with low temperature (more deterministic)
        low_temp_chat = RubyLLM.chat(temperature: 0.1)
        response1 = low_temp_chat.ask('Say exactly: "Low temperature test"')

        # Test with high temperature (more creative)
        high_temp_chat = RubyLLM.chat(temperature: 0.9)
        response2 = high_temp_chat.ask('Say exactly: "High temperature test"')

        expect(response1).to be_present
        expect(response2).to be_present
      end

      it 'can test with different max_tokens settings' do
        short_chat = RubyLLM.chat(max_tokens: 10)
        response = short_chat.ask('Tell me about Ruby programming language')

        expect(response).to be_present
        # Response should be shorter due to token limit
        expect(response.length).to be < 100
      end
    end
  end

  describe 'Performance Testing' do
    context 'when measuring performance' do
      it 'measures response time for simple queries' do
        start_time = Time.current
        response = chat.ask('Hello')
        end_time = Time.current

        expect(response).to be_present
        response_time = end_time - start_time

        # Log the response time for analysis
        puts "Response time: #{response_time} seconds"
        expect(response_time).to be < 30 # Should respond within 30 seconds
      end
    end
  end
end
