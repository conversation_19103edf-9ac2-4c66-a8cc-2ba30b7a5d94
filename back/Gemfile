# frozen_string_literal: true

source 'https://rubygems.org'
ruby file: '.ruby-version'

git_source(:github) do |repo_name|
  repo_name = "#{repo_name}/#{repo_name}" unless repo_name.include?('/')
  "https://github.com/#{repo_name}.git"
end

gem 'rails', '~> 7.1.5.1'
gem 'pg', '~> 1.5.6'
gem 'puma', '~> 6.4.3'
gem 'bcrypt', '~> 3.1.20' # Use ActiveModel has_secure_password
gem 'csv', '~> 3.3.0'

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
gem 'rack-cors', '2.0.0'

group :development, :test do
  gem 'ricecream', '~> 0.2.1'
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platforms: %i[mri mingw x64_mingw]
  gem 'pry'
  gem 'pry-byebug'
  gem 'pry-rails'
  gem 'pry-rescue'
  gem 'database_cleaner', '~> 2.0.2'
  gem 'factory_bot_rails'
  gem 'license_finder'
  # Use module prepend to patch NET::HTTP to avoid stack level too deep errors.
  # The errors are due to a conflict between patches that are using two
  # different patching methods (method aliasing vs module prepend).
  # Here, rack-mini-profiler conflicts with sentry-ruby which patches NET::HTTP
  # with prepend.
  # See https://github.com/MiniProfiler/rack-mini-profiler#nethttp-stack-level-too-deep-errors
  gem 'rack-mini-profiler', require: %w[prepend_net_http_patch rack-mini-profiler]
  gem 'rspec_api_documentation'
  gem 'rspec_junit_formatter'
  gem 'rspec-rails', '~> 6.1.2'
  gem 'rspec-parameterized'
  gem 'rspec-sqlimit'
  gem 'rspec-its'
  gem 'rubocop', require: false
  gem 'rubocop-ast', require: false
  gem 'rubocop-performance', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-rspec', require: false
  gem 'bundler-audit', require: false
  gem 'saharspec'
  gem 'simplecov'
  gem 'simplecov-rcov'
  gem 'test-prof', '~> 1.3'
  gem 'vcr', '~> 6.2'
end

group :development do
  gem 'bullet'
  gem 'listen', '>= 3.0.5', '< 4.0'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'redcarpet'
  gem 'spring'
  gem 'spring-commands-rspec'
  gem 'spring-watcher-listen', '~> 2.1'
  gem 'annotate'
end

group :test do
  gem 'shoulda-matchers', '~> 6.2.0'
  gem 'webmock', '~> 3.23'

  # Adds rspec matchers and helpers for testing html structure and styling.
  gem 'capybara', '~> 3.40'
  gem 'rspec-html-matchers', '~> 0.10'
  # gem 'bullet'
end

gem 'pundit', '~> 2.5.0'
gem 'active_model_serializers', '~> 0.10.15'

gem 'jwt', '~> 2.8.1'
gem 'que', '~> 2.3.0'

gem 'activerecord-import', '~> 1.7'
gem 'json-schema', '~> 4.3'
gem 'activerecord-postgis-adapter', '~> 9.0.2'

gem 'carrierwave', '~> 3.0.7'
gem 'carrierwave-base64', '~> 2.11'
gem 'fog-aws', '~> 3.24'

gem 'api-pagination', '~> 6.0.0'
gem 'kaminari', '~> 1.2'
gem 'rails-i18n', '~> 7.0.9'

gem 'awesome_nested_set', '~> 3.6.0'
gem 'axlsx', '3.0.0.pre' # write xlsx files
gem 'rubyXL', '~> 3.4.27' # read xlsx files. Has issues with writing files https://github.com/CitizenLabDotCo/citizenlab/pull/7834
gem 'counter_culture', '~> 3.5'
gem 'groupdate', '~> 6.5'
gem 'icalendar', '~> 2.10'
gem 'interactor'
gem 'interactor-rails'
gem 'liquid', '~> 5.5'
gem 'mini_magick', '~> 4.13'
gem 'nokogiri', '~> 1.18.9'
gem 'pg_search', '~> 2.3.5'
gem 'premailer-rails', '~> 1.12.0'
gem 'rest-client'
gem 'rgeo-geojson'
gem 'rubyzip', '~> 1.3.0'
gem 'stackprof', '~> 0.2.26'
gem 'scientist', '~> 1.6.4'

gem 'okcomputer'
gem 'omniauth', '~> 2.1'
gem 'omniauth-saml', '~> 2.2.3'
gem 'omniauth-rails_csrf_protection'
gem 'omniauth-facebook'
gem 'omniauth-google-oauth2'
gem 'sentry-ruby'
gem 'sentry-rails'
gem 'simple_segment', '~>1.5'
gem 'omniauth_openid_connect', '~> 0.7.1'
gem 'faraday-jwt', '~> 0.1.0'
gem 'acts_as_list', '~> 1.2'
gem 'positioning', '~> 0.4.6'
gem 'bunny', '>= 2.7.2'
gem 'faker'
gem 'order_as_specified'
gem 'scenic'
gem 'ice_cube', '~> 0.17'

# Libraries for PDF generation /reading
gem 'prawn', '~> 2.5'
gem 'prawn-grouping', '~> 0.2.0'
gem 'pdf-reader'
gem 'faraday'
gem 'faraday-multipart'

# Also required here to be able to initialize Mailgun in
# e.g. production.rb, which would otherwise result in an
# "undefined method 'mailgun_settings=' for ActionMailer::Base:Class"
# exception.
gem 'aws-sdk-s3', '~> 1'
gem 'aws-sdk-bedrockruntime', '~> 1'
gem 'bootsnap', '~> 1', require: false
gem 'dalli', '~> 3.2.8'
gem 'mailgun-ruby', '~>1.2.16'
gem 'rails_semantic_logger'
gem 'rinku', '~> 2'
gem 'jsonapi-serializer'
gem 'rack-attack', '~> 6'

gem 'intercom', '~> 4.2'
# mjml-rails cannot find the MJML parser when installed
# through the emails engine and is therefore specified
# in the main app.
gem 'mjml-rails', '~> 4.11'
gem 'mrml'
gem 'cloudfront-rails', '~> 0.4'
gem 'google-cloud-document_ai', '~> 1.4'
gem 'combine_pdf', '~> 1.0.26'
gem 'neighbor', '~> 0.5.0'
gem 'browser', '~> 6.1'

# Temporarily add nkf and mutex_m to the Gemfile to clear the following warnings:
#   .../.rbenv/versions/3.3.1/lib/ruby/gems/3.3.0/gems/bootsnap-1.18.3/lib/bootsnap/load_path_cache/core_ext/kernel_require.rb:30:
#   warning: .../.rbenv/versions/3.3.1/lib/ruby/3.3.0/drb.rb was loaded from the standard
#   library, but will no longer be part of the default gems since Ruby 3.4.0. Add drb to
#   your Gemfile or gemspec. Also contact author of activesupport-7.0.8.4 to add drb into
#   its gemspec.
#   .../.rbenv/versions/3.3.1/lib/ruby/gems/3.3.0/gems/bootsnap-1.18.3/lib/bootsnap/load_path_cache/core_ext/kernel_require.rb:30:
#   warning: .../.rbenv/versions/3.3.1/lib/ruby/3.3.0/mutex_m.rb was loaded from
#   the standard library, but will no longer be part of the default gems since Ruby 3.4.0.
#   Add mutex_m to your Gemfile or gemspec. Also contact author of activesupport-7.0.8.4
#   to add mutex_m into its gemspec.
#   .../.rbenv/versions/3.3.1/lib/ruby/gems/3.3.0/gems/bootsnap-1.18.3/lib/bootsnap/load_path_cache/core_ext/kernel_require.rb:30:
#   warning: .../.rbenv/versions/3.3.1/lib/ruby/3.3.0/kconv.rb is found in nkf,
#   which will no longer be part of the default gems since Ruby 3.4.0. Add nkf to your
#   Gemfile or gemspec. Also contact author of httpi-3.0.1 to add nkf into its gemspec.
#
# They are not direct dependencies, hence we can remove them once the dependent gems are
# updated.
gem 'drb'
gem 'nkf'
gem 'mutex_m'

free_engines = %w[
  document_annotation
  email_campaigns
  frontend
  onboarding
  polls
  seo
  surveys
  volunteering
]

free_engines.each do |engine_name|
  path = "engines/free/#{engine_name}"
  gem engine_name, path: path if File.directory?(path)
end

gem 'ros-apartment', '~> 3.1.0', require: 'apartment'

commercial_engines = [
  'multi_tenancy',

  'admin_api',
  'aggressive_caching',
  'analysis',
  'analytics',
  'bulk_import_ideas',
  'content_builder',
  'custom_idea_statuses',
  'custom_maps',
  'flag_inappropriate_content',
  'google_tag_manager',
  'idea_assignment',
  'idea_custom_fields',
  'impact_tracking',
  'machine_translations',
  'matomo',
  'moderation',
  'posthog_integration',
  'public_api',
  'remove_vendor_branding',
  'report_builder',
  'smart_groups',
  'user_custom_fields',

  # ID verification & SSO engines.
  'id_auth0',
  'id_bogus',
  'id_bosa_fas',
  'id_clave_unica',
  'id_cow',
  'id_criipto',
  'id_fake_sso',
  'id_franceconnect',
  'id_gent_rrn',
  'id_id_card_lookup',
  'id_keycloak',
  'id_id_austria',
  'id_nemlog_in',
  'id_oostende_rrn',
  'id_hoplr',
  'id_vienna_saml',
  'id_twoday'
]

commercial_engines.each do |engine_name|
  path = "engines/commercial/#{engine_name}"
  gem engine_name, path: path if File.directory?(path)
end

gem "ruby_llm", "~> 1.3"
