# frozen_string_literal: true

# RubyLLM Configuration
# This initializer sets up <PERSON><PERSON><PERSON> for the CitizenLab application
#
# To use RubyLL<PERSON> in development/testing:
# 1. Set the appropriate API keys in your environment variables
# 2. Run experimental tests with: RUBY_LLM_EXPERIMENTAL=true bundle exec rspec spec/lib/ruby_llm_experimental_spec.rb
#
# Supported providers and their environment variables:
# - OpenAI: OPENAI_API_KEY
# - Anthropic: ANTHROPIC_API_KEY
# - Google Gemini: GEMINI_API_KEY
# - AWS Bedrock: AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION
# - OpenRouter: OPENROUTER_API_KEY
# - DeepSeek: DEEPSEEK_API_KEY
# - Ollama: OLLAMA_API_BASE (defaults to http://localhost:11434/v1)

if defined?(RubyLLM)
  RubyLLM.configure do |config|
    # OpenAI Configuration
    config.openai_api_key = ENV.fetch('OPENAI_API_KEY', nil)
    config.openai_organization_id = ENV.fetch('OPENAI_ORGANIZATION_ID', nil)
    config.openai_project_id = ENV.fetch('OPENAI_PROJECT_ID', nil)

    # Anthropic Configuration
    config.anthropic_api_key = ENV.fetch('ANTHROPIC_API_KEY', nil)

    # Google Gemini Configuration
    config.gemini_api_key = ENV.fetch('GEMINI_API_KEY', nil)

    # AWS Bedrock Configuration (uses existing AWS credentials)
    if ENV['AWS_ACCESS_KEY_ID'].present? && ENV['AWS_SECRET_ACCESS_KEY'].present?
      config.bedrock_api_key = ENV.fetch('AWS_ACCESS_KEY_ID')
      config.bedrock_secret_key = ENV.fetch('AWS_SECRET_ACCESS_KEY')
      config.bedrock_region = ENV.fetch('AWS_REGION', 'us-east-1')
      config.bedrock_session_token = ENV.fetch('AWS_SESSION_TOKEN', nil)
    end

    # OpenRouter Configuration
    config.openrouter_api_key = ENV.fetch('OPENROUTER_API_KEY', nil)

    # DeepSeek Configuration
    config.deepseek_api_key = ENV.fetch('DEEPSEEK_API_KEY', nil)

    # Ollama Configuration (for local models)
    config.ollama_api_base = ENV.fetch('OLLAMA_API_BASE', 'http://localhost:11434/v1')

    # Custom OpenAI endpoint (for Azure OpenAI, proxies, etc.)
    config.openai_api_base = ENV.fetch('OPENAI_API_BASE', nil)

    # Default settings for the application
    config.default_model = ENV.fetch('RUBY_LLM_DEFAULT_MODEL', 'gpt-4o-mini')
    config.default_embedding_model = ENV.fetch('RUBY_LLM_DEFAULT_EMBEDDING_MODEL', 'text-embedding-3-small')
    config.default_image_model = ENV.fetch('RUBY_LLM_DEFAULT_IMAGE_MODEL', 'dall-e-3')

    # Connection settings
    config.request_timeout = ENV.fetch('RUBY_LLM_REQUEST_TIMEOUT', '120').to_i
    config.max_retries = ENV.fetch('RUBY_LLM_MAX_RETRIES', '3').to_i

    # Logging configuration
    config.log_level = Rails.env.production? ? :warn : :info
    config.log_assume_model_exists = false
  end

  # Log configuration status
  Rails.logger.info '[RubyLLM] Initialized with the following providers:' if Rails.logger

  providers = []
  providers << 'OpenAI' if ENV['OPENAI_API_KEY'].present?
  providers << 'Anthropic' if ENV['ANTHROPIC_API_KEY'].present?
  providers << 'Google Gemini' if ENV['GEMINI_API_KEY'].present?
  providers << 'AWS Bedrock' if ENV['AWS_ACCESS_KEY_ID'].present? && ENV['AWS_SECRET_ACCESS_KEY'].present?
  providers << 'OpenRouter' if ENV['OPENROUTER_API_KEY'].present?
  providers << 'DeepSeek' if ENV['DEEPSEEK_API_KEY'].present?
  providers << 'Ollama' if ENV['OLLAMA_API_BASE'].present?

  if providers.any?
    Rails.logger.info "[RubyLLM] Available providers: #{providers.join(', ')}" if Rails.logger
  else
    Rails.logger.warn '[RubyLLM] No API keys configured. Set environment variables to enable providers.' if Rails.logger
  end
end
