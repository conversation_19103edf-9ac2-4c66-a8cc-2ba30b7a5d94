#!/usr/bin/env ruby
# frozen_string_literal: true

# RubyLLM Example Script
# 
# This script demonstrates basic RubyLLM functionality for the CitizenLab application.
# It can be run from the Rails console or as a standalone script.
#
# Usage:
#   # From Rails console:
#   load 'lib/ruby_llm_example.rb'
#   RubyLLMExample.run_basic_test
#
#   # From command line (in Docker container):
#   docker compose exec web bash -c "cd /cl2_back && OPENAI_API_KEY=your_key bundle exec ruby lib/ruby_llm_example.rb"

class RubyLLMExample
  def self.run_basic_test
    puts "🤖 RubyLLM Basic Test"
    puts "=" * 50
    
    # Check if RubyLLM is available
    unless defined?(RubyLLM)
      puts "❌ RubyLLM not available. Make sure the gem is installed."
      return false
    end
    
    puts "✅ RubyLLM gem is available"
    
    # Check if any API keys are configured
    providers = check_available_providers
    if providers.empty?
      puts "⚠️  No API keys configured. Set environment variables to test with real providers."
      puts "   Example: export OPENAI_API_KEY='your-key-here'"
      return false
    end
    
    puts "✅ Available providers: #{providers.join(', ')}"
    
    # Test basic chat functionality
    test_basic_chat
    
    # Test streaming
    test_streaming
    
    # Test custom tools
    test_custom_tools
    
    puts "\n🎉 All tests completed successfully!"
    true
  rescue => e
    puts "❌ Error during testing: #{e.message}"
    puts e.backtrace.first(5).join("\n") if ENV['DEBUG']
    false
  end
  
  def self.check_available_providers
    providers = []
    providers << 'OpenAI' if ENV['OPENAI_API_KEY'].present?
    providers << 'Anthropic' if ENV['ANTHROPIC_API_KEY'].present?
    providers << 'Gemini' if ENV['GEMINI_API_KEY'].present?
    providers << 'Bedrock' if ENV['AWS_ACCESS_KEY_ID'].present? && ENV['AWS_SECRET_ACCESS_KEY'].present?
    providers << 'OpenRouter' if ENV['OPENROUTER_API_KEY'].present?
    providers << 'DeepSeek' if ENV['DEEPSEEK_API_KEY'].present?
    providers << 'Ollama' if ENV['OLLAMA_API_BASE'].present?
    providers
  end
  
  def self.test_basic_chat
    puts "\n📝 Testing basic chat functionality..."
    
    chat = RubyLLM.chat
    response = chat.ask("Say exactly: 'Hello from RubyLLM!'")
    
    puts "   Question: Say exactly: 'Hello from RubyLLM!'"
    puts "   Response: #{response}"
    
    if response.include?('Hello from RubyLLM')
      puts "✅ Basic chat test passed"
    else
      puts "⚠️  Basic chat test: response doesn't match expected format"
    end
  rescue => e
    puts "❌ Basic chat test failed: #{e.message}"
  end
  
  def self.test_streaming
    puts "\n🌊 Testing streaming functionality..."
    
    chunks = []
    chat = RubyLLM.chat
    
    print "   Streaming response: "
    chat.ask("Count from 1 to 3, each number on a new line") do |chunk|
      if chunk.content.present?
        print chunk.content
        chunks << chunk.content
      end
    end
    
    puts "\n✅ Streaming test completed (#{chunks.size} chunks received)"
  rescue => e
    puts "\n❌ Streaming test failed: #{e.message}"
  end
  
  def self.test_custom_tools
    puts "\n🔧 Testing custom tools..."
    
    # Define a simple calculator tool
    calculator = Class.new(RubyLLM::Tool) do
      description 'Performs basic arithmetic'
      param :operation, desc: 'add, subtract, multiply, or divide'
      param :a, desc: 'First number'
      param :b, desc: 'Second number'
      
      def execute(operation:, a:, b:)
        a, b = a.to_f, b.to_f
        case operation.downcase
        when 'add' then { result: a + b }
        when 'subtract' then { result: a - b }
        when 'multiply' then { result: a * b }
        when 'divide'
          return { error: 'Cannot divide by zero' } if b.zero?
          { result: a / b }
        else
          { error: 'Unknown operation' }
        end
      end
    end
    
    chat = RubyLLM.chat.with_tool(calculator)
    response = chat.ask("Please calculate 15 + 27 using the calculator tool")
    
    puts "   Question: Please calculate 15 + 27 using the calculator tool"
    puts "   Response: #{response}"
    
    if response.include?('42') || response.downcase.include?('calculation')
      puts "✅ Custom tools test passed"
    else
      puts "⚠️  Custom tools test: response doesn't show tool usage"
    end
  rescue => e
    puts "❌ Custom tools test failed: #{e.message}"
  end
  
  def self.run_configuration_test
    puts "⚙️  RubyLLM Configuration Test"
    puts "=" * 50
    
    # Test different model configurations
    models_to_test = [
      'gpt-4o-mini',
      'gpt-4o',
      'claude-3-haiku-20240307',
      'gemini-1.5-flash'
    ]
    
    models_to_test.each do |model|
      test_model_configuration(model)
    end
  end
  
  def self.test_model_configuration(model_id)
    puts "\n🧪 Testing model: #{model_id}"
    
    begin
      chat = RubyLLM.chat(model: model_id)
      response = chat.ask("Respond with just the word 'SUCCESS'")
      
      if response.upcase.include?('SUCCESS')
        puts "✅ #{model_id} - Working"
      else
        puts "⚠️  #{model_id} - Unexpected response: #{response[0..50]}..."
      end
    rescue => e
      puts "❌ #{model_id} - Error: #{e.message}"
    end
  end
  
  def self.interactive_mode
    puts "🎮 RubyLLM Interactive Mode"
    puts "=" * 50
    puts "Type 'exit' to quit, 'help' for commands"
    
    chat = RubyLLM.chat
    
    loop do
      print "\n> "
      input = gets.chomp
      
      case input.downcase
      when 'exit', 'quit'
        puts "👋 Goodbye!"
        break
      when 'help'
        show_help
      when 'stream'
        puts "Enter your message for streaming:"
        print "> "
        message = gets.chomp
        print "Response: "
        chat.ask(message) { |chunk| print chunk.content if chunk.content.present? }
        puts
      when 'clear'
        chat = RubyLLM.chat # Create new chat to clear history
        puts "🧹 Chat history cleared"
      else
        if input.strip.empty?
          puts "Please enter a message or type 'help' for commands"
          next
        end
        
        puts "Response: #{chat.ask(input)}"
      end
    end
  end
  
  def self.show_help
    puts <<~HELP
      
      Available commands:
      - Type any message to chat with the AI
      - 'stream' - Enter streaming mode for the next message
      - 'clear' - Clear chat history
      - 'help' - Show this help message
      - 'exit' or 'quit' - Exit interactive mode
      
    HELP
  end
end

# Run the basic test if this file is executed directly
if __FILE__ == $0
  RubyLLMExample.run_basic_test
end
