docker-compose run --name master_db_create back_master bundle exec rake db:create
docker-compose run --name master_db_schema_load back_#{epic_id} bundle exec rake db:schema:load

# nvm : set default node version
nvm alias default 10.15.3

# nvm : list all node versions
nvm ls

# ubuntu: reboot system from cli
sudo reboot


mkdir -p deployments/progressreports && cd deployments/progressreports && <NAME_EMAIL>:CitizenLabDotCo/citizenlab --depth 5 --branch TAN-1902-random-fixes
cp templates/secrets/front-secret.env deployments/progressreports/citizenlab/env_files/front-secret.env
cd deployments/progressreports/citizenlab/front && npm install &&


# max verbosity
TEST_BUILD=true NODE_ENV=staging NODE_OPTIONS='--max-old-space-size=8192' npm exec -- webpack --config internals/webpack/webpack.config.js --color --stats verbose