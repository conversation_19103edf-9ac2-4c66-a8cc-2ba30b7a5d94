#   cypress:
  #    profiles: [cypress]
  #    image: citizenlabdotco/front-e2e:${CIRCLE_SHA1}
  #    environment:
  #      API_HOST: e2e.front
  #    env_file:
  #      - "../env_files/front-safe.env"
  #      - "../env_files/front-secret.env"

docker run --env API_HOST=e2e-paris.govocal.com citizenlabdotco/front-e2e:latest

cd ../e2e
docker compose -f docker-compose.yml -f docker-compose.test.yml --profile cypress run  \
  cypress npm run cypress:run -- \
  --reporter cypress-circleci-reporter \
  --config baseUrl=http://e2e.front:3000 \
  --spec ${COMMA_SEPARATED_TESTFILES}


# synchronize cypress:/front/cypress/screenshots with ./cypress/screenshots on the host
docker compose -f docker-compose.yml -f docker-compose.test.yml --profile cypress run --volume ${PWD}/cypress/screenshots:/front/cypress/screenshots cypress npm run cypress:run -- --reporter cypress-circleci-reporter --config baseUrl=https://e2e-paris.govocal.com --spec ../front/cypress/e2e/about_page.cy.ts

# same but override env vars with
# API is available at https://e2e-paris.govocal.com/web_api/v1
docker compose -f docker-compose.yml -f docker-compose.test.yml --profile cypress run --env API_HOST=e2e-paris.govocal.com --env API_SCHEME=https --env API_PORT=443 --env API_PATH=/web_api/v1 --volume ${PWD}/cypress/screenshots:/front/cypress/screenshots cypress npm run cypress:run -- --reporter cypress-circleci-reporter --config baseUrl=https://e2e-paris.govocal.com --spec ../front/cypress/e2e/about_page.cy.ts,../front/cypress/e2e/blocked-users.cy.ts,../front/cypress/e2e/budgeting_permissions.cy.ts,../front/cypress/e2e/comment_voting_permissions.cy.ts,../front/cypress/e2e/commenting_anonymous.cy.ts,../front/cypress/e2e/create_idea_in_any_phase.cy.ts

# run locally instead
npm run cypress:run -- --reporter cypress-circleci-reporter --config baseUrl=https://e2e-paris.govocal.com --spec