require 'statsample'

male_count = 106
female_count = 121
total_count = female_count + male_count

male_ratio = 14_955.to_f / (14_955 + 15_234)
female_ratio = 15_234.to_f / (14_955 + 15_234)

m = Matrix[[male_count, female_count], [total_count * male_ratio, total_count * female_ratio]]
Statsample::Test.chi_square(m).probability


r_male = male_count.to_f / 821_498
r_female = female_count.to_f / 892_729





(1200..total_count-1200).step(50).map do |i|
  male_count = i
  female_count = total_count - i

  m = Matrix[[male_count, female_count], [total_count * male_ratio, total_count * female_ratio]]
  [Statsample::Test.chi_square(m).probability, m]
end


scores = 10000.times.map do
  s = population.sample(250)
  r_female = s.count('f') / female_count.to_f
  r_male = s.count('m')/ male_count.to_f
  [r_male, r_female].min / [r_male, r_female].max
end
