Round 1: bisecting over non-failing examples 1-742 .. multiple culprits detected - splitting candidates (3 minutes 10.4 seconds)
Round 2: bisecting over non-failing examples 1-371 .. multiple culprits detected - splitting candidates (4 minutes 13.5 seconds)
Round 3: bisecting over non-failing examples 1-186 . ignoring examples 1-93 (1 minute 54.69 seconds)
Round 4: bisecting over non-failing examples 94-186 .. ignoring examples 141-186 (3 minutes 38.2 seconds)
Round 5: bisecting over non-failing examples 94-140 .. ignoring examples 118-140 (3 minutes 40.5 seconds)
Round 6: bisecting over non-failing examples 94-117 .. ignoring examples 106-117 (3 minutes 13.5 seconds)
Round 7: bisecting over non-failing examples 94-105 .. ignoring examples 100-105 (2 minutes 54.4 seconds)
Round 8: bisecting over non-failing examples 94-99 .. ignoring examples 97-99 (3 minutes 45.6 seconds)
Round 9: bisecting over non-failing examples 94-96 .. ignoring example 96 (3 minutes 25 seconds)
Round 10: bisecting over non-failing examples 94-95 .. multiple culprits detected - splitting candidates (3 minutes 7.8 seconds)
Round 11: bisecting over non-failing examples 187-371 .. multiple culprits detected - splitting candidates (2 minutes 45.4 seconds)
Round 12: bisecting over non-failing examples 187-279 .. ignoring examples 234-279 (2 minutes 41.9 seconds)
Round 13: bisecting over non-failing examples 187-233 .. ignoring examples 211-233 (2 minutes 6.2 seconds)
Round 14: bisecting over non-failing examples 187-210 .. multiple culprits detected - splitting candidates (2 minutes 1.4 seconds)
Round 15: bisecting over non-failing examples 187-198 .. multiple culprits detected - splitting candidates (2 minutes 31.6 seconds)
Round 16: bisecting over non-failing examples 187-192 . ignoring examples 187-189 (1 minute 10.15 seconds)
Round 17: bisecting over non-failing examples 190-192 . ignoring examples 190-191 (1 minute 15.68 seconds)
Round 18: bisecting over non-failing examples 193-198 .. multiple culprits detected - splitting candidates (3 minutes 37.4 seconds)
Round 19: bisecting over non-failing examples 193-195 .. multiple culprits detected - splitting candidates (3 minutes 6.6 seconds)
Round 20: bisecting over non-failing examples 193-194 .. multiple culprits detected - splitting candidates (2 minutes 32.6 seconds)
Round 21: bisecting over non-failing examples 196-198 .. multiple culprits detected - splitting candidates (2 minutes 22 seconds)
Round 22: bisecting over non-failing examples 196-197 .. multiple culprits detected - splitting candidates (2 minutes 35.3 seconds)
Round 23: bisecting over non-failing examples 199-210 .. ignoring examples 205-210 (2 minutes 28.4 seconds)
Round 24: bisecting over non-failing examples 199-204 .. ignoring examples 202-204 (2 minutes 30.8 seconds)
Round 25: bisecting over non-failing examples 199-201 .. multiple culprits detected - splitting candidates (2 minutes 40.2 seconds)
Round 26: bisecting over non-failing examples 199-200 .. multiple culprits detected - splitting candidates (3 minutes 6 seconds)
Round 27: bisecting over non-failing examples 280-371 .. multiple culprits detected - splitting candidates (2 minutes 13.9 seconds)
Round 28: bisecting over non-failing examples 280-325 . ignoring examples 280-302 (1 minute 16.59 seconds)
Round 29: bisecting over non-failing examples 303-325 .. ignoring examples 315-325 (1 minute 59.49 seconds)
Round 30: bisecting over non-failing examples 303-314 .. ignoring examples 309-314 (2 minutes 3.6 seconds)
Round 31: bisecting over non-failing examples 303-308 . ignoring examples 303-305 (1 minute 4.72 seconds)
Round 32: bisecting over non-failing examples 306-308 .. ignoring example 308 (2 minutes 16.8 seconds)
Round 33: bisecting over non-failing examples 306-307 .. ignoring example 307 (2 minutes 1.5 seconds)
Round 34: bisecting over non-failing examples 326-371 . ignoring examples 326-348 (58.97 seconds)
Round 35: bisecting over non-failing examples 349-371 . ignoring examples 349-360 (1 minute 3.54 seconds)
Round 36: bisecting over non-failing examples 361-371 .. ignoring examples 367-371 (2 minutes 7.3 seconds)
Round 37: bisecting over non-failing examples 361-366 .. ignoring examples 364-366 (1 minute 56.34 seconds)
Round 38: bisecting over non-failing examples 361-363 .. ignoring example 363 (1 minute 57.79 seconds)
Round 39: bisecting over non-failing examples 361-362 .. ignoring example 362 (1 minute 45.6 seconds)
Round 40: bisecting over non-failing examples 372-742 .. multiple culprits detected - splitting candidates (1 minute 2.22 seconds)
Round 41: bisecting over non-failing examples 372-557 .. ignoring examples 465-557 (1 minute 11.33 seconds)
Round 42: bisecting over non-failing examples 372-464 .. ignoring examples 419-464 (1 minute 9.56 seconds)
Round 43: bisecting over non-failing examples 372-418 .. ignoring examples 396-418 (48.16 seconds)
Round 44: bisecting over non-failing examples 372-395 .. multiple culprits detected - splitting candidates (51.3 seconds)
Round 45: bisecting over non-failing examples 372-383 . ignoring examples 372-377 (27.69 seconds)
Round 46: bisecting over non-failing examples 378-383 .. multiple culprits detected - splitting candidates (43.55 seconds)
Round 47: bisecting over non-failing examples 378-380 . ignoring examples 378-379 (21.11 seconds)
Round 48: bisecting over non-failing examples 381-383 .. multiple culprits detected - splitting candidates (41.97 seconds)
Round 49: bisecting over non-failing examples 381-382 .. multiple culprits detected - splitting candidates (47 seconds)
Round 50: bisecting over non-failing examples 384-395 .. ignoring examples 390-395 (54.29 seconds)
Round 51: bisecting over non-failing examples 384-389 . ignoring examples 384-386 (26.35 seconds)
Round 52: bisecting over non-failing examples 387-389 .. ignoring example 389 (1 minute 4.09 seconds)
Round 53: bisecting over non-failing examples 387-388 .. ignoring example 388 (56.55 seconds)
Round 54: bisecting over non-failing examples 558-742 . ignoring examples 558-650 (22.37 seconds)
Round 55: bisecting over non-failing examples 651-742 .. ignoring examples 697-742 (34.56 seconds)
Round 56: bisecting over non-failing examples 651-696 .. multiple culprits detected - splitting candidates (29.88 seconds)
Round 57: bisecting over non-failing examples 651-673 . ignoring examples 651-662 (13.03 seconds)
Round 58: bisecting over non-failing examples 663-673 .. multiple culprits detected - splitting candidates (25.26 seconds)
Round 59: bisecting over non-failing examples 663-668 . ignoring examples 663-665 (14.13 seconds)
Round 60: bisecting over non-failing examples 666-668 . ignoring examples 666-667 (14.98 seconds)