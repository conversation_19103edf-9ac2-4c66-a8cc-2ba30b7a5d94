
-- Rewrite this query in such a way that end_at is NULL if any phase is not finished (end_at is NULL)
SELECT project_id,
       min(start_at)                             as start_at,
       max(start_at)                             as last_phase_start_at,
       max(end_at) as end_at
FROM "phases"
GROUP BY "phases"."project_id";

-- same but end_at should be NULL if any phase is not finished (end_at is NULL)
SELECT project_id,
       min(start_at)                             as start_at,
       max(start_at)                             as last_phase_start_at,
       CASE WHEN count(end_at) = count(*) THEN max(end_at) ELSE NULL END as end_at

-- as active record query:
-- Phase.select("project_id, min(start_at) as start_at, max(start_at) as last_phase_start_at, CASE WHEN count(end_at) = count(*) THEN max(end_at) ELSE NULL END as end_at").group(:project_id)


