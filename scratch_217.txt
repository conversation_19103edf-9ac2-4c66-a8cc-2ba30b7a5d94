AWS Logs Insights
=================

fields @timestamp, @message, @logStream, @log
| filter payload.tenant_host = "doemee.veere.nl"
| sort @timestamp desc
| limit 1000

with method = PATCH or POST:

fields @timestamp, @message, @logStream, @log
| filter payload.tenant_host = "doemee.veere.nl"
| filter @message like /f2eef048-4428-4db6-988d-1ef93ef381fd/
| filter payload.params.user.avatar not null
| filter payload.method = "PATCH" or payload.method = "POST"
| sort @timestamp desc
| limit 1000
