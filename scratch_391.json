{"Version": "2012-10-17", "Statement": [{"Sid": "LB service account store log", "Effect": "Allow", "Principal": {"AWS": "arn:aws:iam::************:root"}, "Action": "s3:PutObject", "Resource": ["arn:aws:s3:::cl2-logs-prod-frankfurt/AWSLogs/************/*", "arn:aws:s3:::cl2-logs-prod-frankfurt/AWSLogs/************/"]}, {"Sid": "LB service store log", "Effect": "Allow", "Principal": {"Service": "delivery.logs.amazonaws.com"}, "Action": "s3:PutObject", "Resource": "arn:aws:s3:::cl2-logs-prod-frankfurt/AWSLogs/************/*", "Condition": {"StringEquals": {"s3:x-amz-acl": "bucket-owner-full-control"}}}, {"Sid": "LB service account get ACL", "Effect": "Allow", "Principal": {"Service": "delivery.logs.amazonaws.com"}, "Action": "s3:GetBucketAcl", "Resource": "arn:aws:s3:::cl2-logs-prod-frankfurt"}, {"Sid": "AWSLogDeliveryWrite", "Effect": "Allow", "Principal": {"Service": "delivery.logs.amazonaws.com"}, "Action": "s3:PutObject", "Resource": "arn:aws:s3:::cl2-logs-prod-frankfurt/tmp/default-vpc-flow-logs/AWSLogs/************/*", "Condition": {"StringEquals": {"s3:x-amz-acl": "bucket-owner-full-control", "aws:SourceAccount": "************"}, "ArnLike": {"aws:SourceArn": "arn:aws:logs:eu-central-1:************:*"}}}, {"Sid": "AWSLogDeliveryAclCheck", "Effect": "Allow", "Principal": {"Service": "delivery.logs.amazonaws.com"}, "Action": "s3:GetBucketAcl", "Resource": "arn:aws:s3:::cl2-logs-prod-frankfurt", "Condition": {"StringEquals": {"aws:SourceAccount": "************"}, "ArnLike": {"aws:SourceArn": "arn:aws:logs:eu-central-1:************:*"}}}]}