
It seems the AI has been generating summaries without references from the start, and the proportion hasn't increased over time. As of today, the AI has produced 2,803 summaries in total, out of which 559 lack references. This means about 20% of the summaries do not include references. That's about 20% of the summaries.

This makes sense, given that the prompt is formulated as:
> You can refer to individual responses within the summary where relevant as example, by adding their ID between square brackets.

See details herebelow.


@Anonymous @Anonymous I've added a small data analysis in the ticket. What do you think the next steps should be?

Reworking the prompt is an option, but it's always a bit risky and tricky to test and get right. It might me more appropriate to include this in the scope of a future iteration of the sense-making tandem. In the meantime, we could still add a message to summaries without references to manage expectations.
