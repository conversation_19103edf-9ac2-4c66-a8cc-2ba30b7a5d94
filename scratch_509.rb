# when copying an idea


# Not sure about these:
#  publication_status              :string
#  published_at                    :datetime
#  idea_status_id                  :uuid
#  creation_phase_id               :uuid

# These counts should be set to 0
#  likes_count                     :integer          default(0), not null
#  dislikes_count                  :integer          default(0), not null
#  comments_count                  :integer          default(0), not null # should we copy comments over?
#  baskets_count                   :integer          default(0), not null
#  official_feedbacks_count        :integer          default(0), not null
#  internal_comments_count         :integer          default(0), not null
#  followers_count                 :integer          default(0), not null
#  votes_count                     :integer          default(0), not null
#  manual_votes_amount             :integer
#  manual_votes_last_updated_by_id :uuid
#  manual_votes_last_updated_at    :datetime

# Not copied:
#  slug                            :string # a new slug should be created
#  assignee_id                     :uuid
#  assigned_at                     :datetime
#  updated_at                      :datetime         not null # updated at should be computed automatically


# Associations:
has_many :ideas_topics, dependent: :destroy
has_many :embeddings_similarities, as: :embeddable, dependent: :destroy
has_many :text_images, as: :imageable, dependent: :destroy
has_many :idea_images, -> { order(:ordering) }, dependent: :destroy, inverse_of: :idea
has_many :idea_files, -> { order(:ordering) }, dependent: :destroy, inverse_of: :idea
has_one :idea_trending_info
