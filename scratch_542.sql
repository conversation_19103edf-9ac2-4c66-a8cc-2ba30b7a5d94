SELECT "ideas".*
FROM "ideas"
         INNER JOIN "ideas_phases" ON "ideas"."id" = "ideas_phases"."idea_id"
WHERE "ideas_phases"."phase_id" = '0cada0f1-a388-489f-a530-218a15ef8593'
  AND "ideas"."publication_status" IN ('submitted', 'published')
  AND NOT ("ideas"."title_multiloc" = '{}'::jsonb AND "ideas"."body_multiloc" = '{}'::jsonb);

SELECT "ideas".* FROM "ideas" WHERE "ideas"."publication_status" IN ('submitted', 'published') AND



--   AND (ideas.title_multiloc != '{}' AND ideas.body_multiloc != '{}')
