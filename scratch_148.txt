require 'base64'
#!/bin/bash.decode64(base64data)

sudo /bin/dd if=/dev/zero of=/var/swap.1 bs=1M count=4096
sudo /sbin/mkswap /var/swap.1
sudo chmod 600 /var/swap.1
sudo /sbin/swapon /var/swap.1
sudo echo "/var/swap.1   swap    swap    defaults        0   0" >> /etc/fstab

sudo apt-get --yes install \
    apt-transport-https \
    ca-certificates \
    curl \
    software-properties-common

curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

sudo add-apt-repository \
   "deb [arch=amd64] https://download.docker.com/linux/ubuntu \
   $(lsb_release -cs) \
   stable"

sudo apt-get -y update

sudo apt-get -y install docker-ce docker-ce-cli containerd.io

sudo groupadd docker
sudo usermod -aG docker ubuntu

echo -e '{\n  "log-driver": "json-file",\n  "log-opts": {\n    "max-size": "1g",\n    "max-file": "5"\n  }\n}\n' | sudo tee /etc/docker/daemon.json > /dev/null

sudo sh -c 'echo "0 5 * * 1 docker system prune -f" > /etc/cron.d/prune-docker'
sudo echo "" >> /home/<USER>/.ssh/authorized_keys

docker swarm join --token SWMTKN-1-4qu27aqf4sm2fth7fy0bu6f0eubyj8s2p184eu668ibc3si6v7-dvjxcb2h5fc6ukjt4e2c7fy20 172.31.24.234:2377
