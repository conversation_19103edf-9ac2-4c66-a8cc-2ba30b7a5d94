Tenant.find_by(host: 'stadtwien-presentation.citizenlab.co').switch!

User.order(Arel.sql('RANDOM()')).take(80).each do |user|
  area = Area.where('ordering < 12').reorder('').order(Arel.sql('RANDOM()')).first
  user.domicile = area.id
  user.save!
end

# User.order(Arel.sql('RANDOM()')).take(25).each do |user|
#   user.registration_completed_at = rand(10.days).seconds.ago
#   user.save!
# end



# t = Tenant.find_by(host: 'stadtwien-presentation.citizenlab.co').switch!
# t.created_at = '2021-08-15'
# t.save!




# project = Project.find('6456c048-37ec-4be1-9ea1-99135b1ae6e7')
#
# project.ideas.each do |i|
#   i.body_multiloc.slice!('de-DE')
#   i.title_multiloc.slice!('de-DE')
#   i.save!
# end