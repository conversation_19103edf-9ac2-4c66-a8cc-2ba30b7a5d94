{"json_schema_multiloc": {"nl-NL": {"type": "object", "additionalProperties": false, "properties": {"voor_welke_kern_wilt_u_participeren_ucg": {"type": "string", "enum": ["aagtekerke_t7n", "biggekerke_ryh", "domburg_68k", "<PERSON>e_oyn", "grijpskerke_l8j", "koudekerke_ygb", "meliskerke_d8o", "oostkapelle_g9z", "serooskerke_cm9", "veere_v04", "vrouwenpolder_a9x", "westkapelle_4a2", "zoutelande_dq4"]}, "vorige_vraag_overnemen_v6r": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_uwf": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_6of", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_kms", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_2k8", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_ksz", "title": "Speelplekken"}, {"const": "groen_d6t", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_uwf_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_imh": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_mqu", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_y6b", "title": "Horeca"}, {"const": "park_hqg", "title": "Park"}, {"const": "speeltuin_8kw", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_imh_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_lk7": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_nja": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_j60": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_1g9": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_gkr": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fun": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_i3k": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_bzt": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fmz": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_5hr": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_7a4": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_4v6": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_k5g", "title": "Starters"}, {"const": "gezinnen_ok7", "title": "Gezinnen"}, {"const": "senioren_sr9", "title": "<PERSON>en"}, {"const": "alleenstaanden_a8r", "title": "Alleenstaanden"}, {"const": "sociale_huurders_8hy", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_0zr": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "knarrenhof_d1c", "title": "Knarrenhof"}, {"const": "rijwoningen_f86", "title": "Rijwoningen"}, {"const": "twee_onder_een_kapwoning_whc", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "villa_8lr", "title": "Vrijstaand"}, {"const": "seniorenwoningen_fu8", "title": "Seniorenwoningen"}, {"const": "appartementen_tsi", "title": "Appartementen "}, {"const": "tiny_houses_8cs", "title": "Tiny houses "}]}}, "uitstraling_woningen_ual": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_6ze", "title": "Landelijk"}, {"const": "dorps_s6n", "title": "Dorps"}, {"const": "duurzaam_e6i", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_3er", "title": "Klassiek"}, {"const": "in_het_groen_in6", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_ual_other": {"type": "string"}, "aanvullende_opmerkingen_xwv": {"type": "string"}, "aanvullende_opmerkingen_kaart_ovc": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_6ky": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_v56": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_q39", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_l6w", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_n82", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_8xh", "title": "Speelplekken"}, {"const": "groen_vxw", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_v56_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_hw2": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_b39", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_4ui", "title": "Horeca"}, {"const": "park_s4o", "title": "Park"}, {"const": "speeltuin_nu7", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_hw2_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_zyt": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_1xe": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_4o0": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_jnz": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_znj": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_z9v": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_hg4": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_v5h": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_o8d": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_du2": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_ycq": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_dyz": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_86v", "title": "Starters"}, {"const": "gezinnen_0cm", "title": "Gezinnen"}, {"const": "senioren_b19", "title": "<PERSON>en"}, {"const": "alleenstaanden_u7i", "title": "Alleenstaanden"}, {"const": "sociale_huur<PERSON>_she", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_yk5": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "villa_pvg", "title": "Vrijstaand"}, {"const": "knarrenhof_suc", "title": "Knarrenhof"}, {"const": "appartementen_ech", "title": "Appartementen "}, {"const": "rijwoningen_fdo", "title": "Rijwoningen"}, {"const": "twee_onder_een_kapwoning_5ul", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "seniorenwoningen_i5b", "title": "Seniorenwoningen"}, {"const": "tiny_houses_iya", "title": "Tiny houses "}]}}, "uitstraling_woningen_h8s": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_86l", "title": "Landelijk"}, {"const": "dorps_9oe", "title": "Dorps"}, {"const": "duurzaam_04y", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_a0d", "title": "Klassiek"}, {"const": "in_het_groen_9ir", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_h8s_other": {"type": "string"}, "aanvullende_opmerkingen_s9z": {"type": "string"}, "aanvullende_opmerkingen_kaart_67f": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_0ft": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_ist": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_hmb", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_c6p", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_vtl", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_kv0", "title": "Speelplekken"}, {"const": "groen_xpv", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_ist_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_l5x": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_3qz", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_3mu", "title": "Horeca"}, {"const": "park_8c3", "title": "Park"}, {"const": "speeltuin_slr", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_l5x_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_ozg": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_sf6": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_9ks": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_u69": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_b7o": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_ed2": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_9b6": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_w9b": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_3b8": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_f6y": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_ds4": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_zs9": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_w29", "title": "Starters"}, {"const": "gezinnen_8br", "title": "Gezinnen"}, {"const": "senioren_9t3", "title": "<PERSON>en"}, {"const": "alleenstaanden_8ip", "title": "Alleenstaanden"}, {"const": "sociale_huur<PERSON>_840", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_qlx": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "appartementen_lgn", "title": "Appartementen "}, {"const": "twee_onder_een_kapwoning_wp9", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "seniorenwoningen_veq", "title": "Seniorenwoningen"}, {"const": "knarrenhof_3lt", "title": "Knarrenhof"}, {"const": "tiny_houses_fpr", "title": "Tiny houses "}, {"const": "rijwoningen_sd0", "title": "Rijwoningen"}, {"const": "villa_2d3", "title": "Vrijstaand"}]}}, "uitstraling_woningen_fo9": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_zvr", "title": "Landelijk"}, {"const": "dorps_wk9", "title": "Dorps"}, {"const": "duurzaam_b9c", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_bjq", "title": "Klassiek"}, {"const": "in_het_groen_ax9", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_fo9_other": {"type": "string"}, "aanvullende_opmerkingen_xcq": {"type": "string"}, "aanvullende_opmerkingen_kaart_86m": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_o7s": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_xul": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_j19", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_z4c", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_rns", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_xus", "title": "Speelplekken"}, {"const": "groen_8v0", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_xul_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_n5z": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_5n3", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_t1f", "title": "Horeca"}, {"const": "park_vc7", "title": "Park"}, {"const": "speeltuin_7z8", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_n5z_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_xrf": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_p1h": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_6f8": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_6ny": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_263": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_l9b": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_vye": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_4hs": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_icg": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_9o7": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_nmx": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_45c": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_rgk", "title": "Starters"}, {"const": "gezinnen_jb4", "title": "Gezinnen"}, {"const": "senioren_hdm", "title": "<PERSON>en"}, {"const": "alleenstaanden_i91", "title": "Alleenstaanden"}, {"const": "sociale_huurders_5pu", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_zlc": {"type": "array", "uniqueItems": true, "minItems": 6, "maxItems": 6, "items": {"type": "string", "oneOf": [{"const": "knarrenhof_9ba", "title": "Knarrenhof"}, {"const": "rijwoningen_6gx", "title": "Rijwoningen"}, {"const": "seniorenwoningen_4k3", "title": "Seniorenwoningen"}, {"const": "twee_onder_een_kapwoning_p62", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "appartementen_276", "title": "Appartementen "}, {"const": "villa_c1k", "title": "Vrijstaand"}]}}, "uitstraling_woningen_irc": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_n4m", "title": "Landelijk"}, {"const": "dorps_op9", "title": "Dorps"}, {"const": "duurzaam_sny", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_9x8", "title": "Klassiek"}, {"const": "in_het_groen_sd9", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_irc_other": {"type": "string"}, "aanvullende_opmerkingen_tb8": {"type": "string"}, "aanvullende_opmerkingen_kaart_git": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_etw": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_zkh": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_9i0", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_fec", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_6bd", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_t7z", "title": "Speelplekken"}, {"const": "groen_qyx", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_zkh_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_erm": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_02t", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_ydj", "title": "Horeca"}, {"const": "park_2bq", "title": "Park"}, {"const": "speeltuin_t1w", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_erm_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_rh2": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_sgk": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_lw9": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_9hk": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_bsw": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fsa": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_5nm": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_pd3": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fd8": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_mfw": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_kaw": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_7ax": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_ifo", "title": "Starters"}, {"const": "gezinnen_x0l", "title": "Gezinnen"}, {"const": "senioren_lbm", "title": "<PERSON>en"}, {"const": "alleenstaanden_uo5", "title": "Alleenstaanden"}, {"const": "sociale_huur<PERSON>_dob", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_c5o": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "knarrenhof_xce", "title": "Knarrenhof"}, {"const": "twee_onder_een_kapwoning_tno", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "rijwoningen_wr0", "title": "Rijwoningen"}, {"const": "villa_8le", "title": "Vrijstaand"}, {"const": "seniorenwoningen_kh6", "title": "Seniorenwoningen"}, {"const": "appartementen_ykv", "title": "Appartementen "}, {"const": "tiny_houses_mq7", "title": "Tiny houses "}]}}, "uitstraling_woningen_egy": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_kd2", "title": "Landelijk"}, {"const": "dorps_9na", "title": "Dorps"}, {"const": "duurzaam_ztb", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_vlh", "title": "Klassiek"}, {"const": "in_het_groen_1gl", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_egy_other": {"type": "string"}, "aanvullende_opmerkingen_vqc": {"type": "string"}, "aanvullende_opmerkingen_kaart_1ht": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_p93": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_epk": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_gpw", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_4gf", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_mh3", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_c82", "title": "Speelplekken"}, {"const": "groen_rpi", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_epk_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_dt4": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_67v", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_cr1", "title": "Horeca"}, {"const": "park_wky", "title": "Park"}, {"const": "speeltuin_9hu", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_dt4_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_bdu": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_65j": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_79j": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_v42": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_ptv": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7pa": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_ni1": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_oy0": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_yot": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_2xg": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_fv9": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_h3i": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_z2k", "title": "Starters"}, {"const": "gezinnen_cq8", "title": "Gezinnen"}, {"const": "senioren_dr7", "title": "<PERSON>en"}, {"const": "alleenstaanden_b0p", "title": "Alleenstaanden"}, {"const": "sociale_huurders_yql", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_qf6": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "appartementen_o7j", "title": "Appartementen "}, {"const": "seniorenwoningen_98i", "title": "Seniorenwoningen"}, {"const": "knarrenhof_u9l", "title": "Knarrenhof"}, {"const": "rijwoningen_9p3", "title": "Rijwoningen"}, {"const": "tiny_houses_s9c", "title": "Tiny houses "}, {"const": "twee_onder_een_kapwoning_nwv", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "villa_vfg", "title": "Vrijstaand"}]}}, "uitstraling_woningen_pqz": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_uyr", "title": "Landelijk"}, {"const": "dorps_tj3", "title": "Dorps"}, {"const": "duurzaam_cyj", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_q71", "title": "Klassiek"}, {"const": "in_het_groen_wfr", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_pqz_other": {"type": "string"}, "aanvullende_opmerkingen_7le": {"type": "string"}, "aanvullende_opmerkingen_kaart_67w": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_4o5": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_5eh": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_iye", "title": "Sportvoorzieningen"}, {"const": "winkel_aan<PERSON><PERSON>_sru", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_51l", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_128", "title": "Speelplekken"}, {"const": "groen_nci", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_5eh_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_1np": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_05m", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_tol", "title": "Horeca"}, {"const": "park_jxi", "title": "Park"}, {"const": "speeltuin_p6v", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_1np_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_g68": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_yje": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_ve8": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_ido": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_fta": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_3cz": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_6x7": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_xfv": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_5pu": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_x6c": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_lgf": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_kfq": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_1jg", "title": "Starters"}, {"const": "gezinnen_zhc", "title": "Gezinnen"}, {"const": "senioren_952", "title": "<PERSON>en"}, {"const": "alleenstaanden_q5r", "title": "Alleenstaanden"}, {"const": "sociale_huurders_aqf", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_15m": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "seniorenwoningen_lxj", "title": "Seniorenwoningen"}, {"const": "rijwoningen_j4q", "title": "Rijwoningen"}, {"const": "appartementen_kcg", "title": "Appartementen "}, {"const": "villa_jzk", "title": "Vrijstaand"}, {"const": "tiny_houses_iwz", "title": "Tiny houses "}, {"const": "twee_onder_een_kapwoning_2vx", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "knarrenhof_2hw", "title": "Knarrenhof"}]}}, "uitstraling_woningen_5ld": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_vfo", "title": "Landelijk"}, {"const": "dorps_nqk", "title": "Dorps"}, {"const": "duurzaam_tw8", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_7ru", "title": "Klassiek"}, {"const": "in_het_groen_fir", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_5ld_other": {"type": "string"}, "aanvullende_opmerkingen_mwe": {"type": "string"}, "aanvullende_opmerkingen_kaart_2wa": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_l8g": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_sry": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_fgj", "title": "Sportvoorzieningen"}, {"const": "winkel_aan<PERSON>d_byj", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_lt7", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_qlz", "title": "Speelplekken"}, {"const": "groen_49l", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_sry_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fig": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_nix", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_psh", "title": "Horeca"}, {"const": "park_d50", "title": "Park"}, {"const": "speeltuin_kcv", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fig_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_wc9": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_3di": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_sg7": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_ohy": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_l7e": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_xvq": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_8e2": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_hlg": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_vtm": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_qjs": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_80z": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_jsh": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_qzf", "title": "Starters"}, {"const": "gezinnen_9cm", "title": "Gezinnen"}, {"const": "senioren_5t2", "title": "<PERSON>en"}, {"const": "alleenstaanden_hlb", "title": "Alleenstaanden"}, {"const": "sociale_huurders_t7r", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_x4s": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "twee_onder_een_kapwoning_rjb", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "rijwoningen_31f", "title": "Rijwoningen"}, {"const": "appartementen_g3i", "title": "Appartementen "}, {"const": "tiny_houses_rlh", "title": "Tiny houses "}, {"const": "knarrenhof_ngv", "title": "Knarrenhof"}, {"const": "seniorenwoningen_52s", "title": "Seniorenwoningen"}, {"const": "villa_tmp", "title": "Vrijstaand"}]}}, "uitstraling_woningen_olh": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_p42", "title": "Landelijk"}, {"const": "dorps_jvn", "title": "Dorps"}, {"const": "duurzaam_4yn", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_ep4", "title": "Klassiek"}, {"const": "in_het_groen_k2b", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_olh_other": {"type": "string"}, "aanvullende_opmerkingen_ahf": {"type": "string"}, "aanvullende_opmerkingen_kaart_kmr": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_3o1": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_q81": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_xce", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_1db", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_4uc", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_k5l", "title": "Speelplekken"}, {"const": "groen_s4y", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_q81_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_e37": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_7m2", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_d21", "title": "Horeca"}, {"const": "park_cvf", "title": "Park"}, {"const": "speeltuin_ztv", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_e37_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_z6a": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_vto": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_rxl": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_sux": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_3jt": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_a1t": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_3wq": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_rzh": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_4hs": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_ois": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_n73": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_uan": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_hgd", "title": "Starters"}, {"const": "gezinnen_m01", "title": "Gezinnen"}, {"const": "senioren_9ne", "title": "<PERSON>en"}, {"const": "alleenstaanden_jgf", "title": "Alleenstaanden"}, {"const": "sociale_huurders_bhf", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_ae9": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "tiny_houses_98a", "title": "Tiny houses "}, {"const": "appartementen_2zr", "title": "Appartementen "}, {"const": "seniorenwoningen_0sd", "title": "Seniorenwoningen"}, {"const": "rijwoningen_cfs", "title": "Rijwoningen"}, {"const": "knarrenhof_smt", "title": "Knarrenhof"}, {"const": "twee_onder_een_kapwoning_ygo", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "villa_v92", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}}, "uitstraling_woningen_efs": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_c7o", "title": "Landelijk"}, {"const": "dorps_f9q", "title": "Dorps"}, {"const": "duurzaam_5k1", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_3c0", "title": "Klassiek"}, {"const": "in_het_groen_6ba", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_efs_other": {"type": "string"}, "aanvullende_opmerkingen_9ho": {"type": "string"}, "aanvullende_opmerkingen_kaart_2wi": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_ihf": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_g5d": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_ng2", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_d20", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_q2y", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_1lp", "title": "Speelplekken"}, {"const": "groen_nu7", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_g5d_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7te": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_63z", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_kyr", "title": "Horeca"}, {"const": "park_mp7", "title": "Park"}, {"const": "speeltuin_94z", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7te_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_gm6": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_toe": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_3p6": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_1yg": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_54d": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_wks": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_knq": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_pws": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_pvr": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_edl": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_wds": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_ukg": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_k31", "title": "Starters"}, {"const": "gezinnen_vd1", "title": "Gezinnen"}, {"const": "senioren_b70", "title": "<PERSON>en"}, {"const": "alleenstaanden_zy8", "title": "Alleenstaanden"}, {"const": "sociale_huurders_04h", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_ueh": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "rijwoningen_fm2", "title": "Rijwoningen"}, {"const": "knarrenhof_26a", "title": "Knarrenhof"}, {"const": "appartementen_y0b", "title": "Appartementen "}, {"const": "villa_5b2", "title": "Vrijstaand"}, {"const": "seniorenwoningen_egf", "title": "Seniorenwoningen"}, {"const": "tiny_houses_m7n", "title": "Tiny houses "}, {"const": "twee_onder_een_kapwoning_f9k", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}]}}, "uitstraling_woningen_naw": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_jbl", "title": "Landelijk"}, {"const": "dorps_cjz", "title": "Dorps"}, {"const": "duurzaam_a20", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_p4z", "title": "Klassiek"}, {"const": "in_het_groen_d3k", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_naw_other": {"type": "string"}, "aanvullende_opmerkingen_zob": {"type": "string"}, "aanvullende_opmerkingen_kaart_vox": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_0tk": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_36x": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_c72", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_oij", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_y8c", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_tn9", "title": "Speelplekken"}, {"const": "groen_dpk", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_36x_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_oil": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_6vh", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_f9y", "title": "Horeca"}, {"const": "park_j8y", "title": "Park"}, {"const": "speeltuin_6g3", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_oil_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_h2y": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_12y": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_1e8": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_zt2": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_hra": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_7mg": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_6ls": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_wik": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_xfc": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_eub": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_ufd": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_i1z": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_7k1", "title": "Starters"}, {"const": "gezinnen_c86", "title": "Gezinnen"}, {"const": "senioren_2g0", "title": "<PERSON>en"}, {"const": "alleenstaanden_42e", "title": "Alleenstaanden"}, {"const": "sociale_huurders_kcv", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_9ty": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "tiny_houses_0a6", "title": "Tiny houses "}, {"const": "twee_onder_een_kapwoning_hxr", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "appartementen_d34", "title": "Appartementen "}, {"const": "knarrenhof_ljo", "title": "Knarrenhof"}, {"const": "villa_q9s", "title": "Vrijstaand"}, {"const": "seniorenwoningen_cj3", "title": "Seniorenwoningen"}, {"const": "rijwoningen_oh9", "title": "Rijwoningen"}]}}, "uitstraling_woningen_3ub": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_0jt", "title": "Landelijk"}, {"const": "dorps_1uy", "title": "Dorps"}, {"const": "duurzaam_be7", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_pca", "title": "Klassiek"}, {"const": "in_het_groen_njl", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_3ub_other": {"type": "string"}, "aanvullende_opmerkingen_mn4": {"type": "string"}, "aanvullende_opmerkingen_kaart_w3n": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_hse": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_jtf": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_xn4", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_2yt", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_hvs", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_vkt", "title": "Speelplekken"}, {"const": "groen_94j", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_jtf_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_zey": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_u0i", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_d3k", "title": "Horeca"}, {"const": "park_eui", "title": "Park"}, {"const": "speeltuin_lk6", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_zey_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_spb": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_94o": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_d63": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_01f": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_kopie_z0v": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_kopie_w2m": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_d8j": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_evm": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fr9": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_7ew": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_ke7": {"type": "number", "minimum": 1, "maximum": 10}, "wonen_en_woonbehoeften_eov": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_h6a", "title": "Starters"}, {"const": "gezinnen_c6z", "title": "Gezinnen"}, {"const": "senioren_ic6", "title": "<PERSON>en"}, {"const": "alleenstaanden_84k", "title": "Alleenstaanden"}, {"const": "sociale_huurders_vpb", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_xy2": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "seniorenwoningen_kl6", "title": "Seniorenwoningen"}, {"const": "appartementen_g9s", "title": "Appartementen "}, {"const": "twee_onder_een_kapwoning_j7q", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "knarrenhof_d5l", "title": "Knarrenhof"}, {"const": "villa_pxj", "title": "Vrijtaand"}, {"const": "rijwoningen_54f", "title": "Rijwoningen"}, {"const": "tiny_houses_ikc", "title": "Tiny houses "}]}}, "uitstraling_woningen_jg1": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_218", "title": "Landelijk"}, {"const": "dorps_r4a", "title": "Dorps"}, {"const": "duurzaam_zop", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_f01", "title": "Klassiek"}, {"const": "in_het_groen_xe1", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_jg1_other": {"type": "string"}, "aanvullende_opmerkingen_od1": {"type": "string"}, "aanvullende_opmerkingen_kaart_3xz": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}, "algemene_vraag_over_uw_kern_n2c": {"type": "number", "minimum": 1, "maximum": 5}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_evh": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 2, "items": {"type": "string", "oneOf": [{"const": "sportvoorzieningen_eub", "title": "Sportvoorzieningen"}, {"const": "winkel_aanbod_y7a", "title": "<PERSON><PERSON>"}, {"const": "woning_aanbod_l9v", "title": "Woning a<PERSON><PERSON>d"}, {"const": "speelplekken_7ge", "title": "Speelplekken"}, {"const": "groen_g5k", "title": "<PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_evh_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_cq1": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "pleinen_qk3", "title": "<PERSON><PERSON><PERSON>"}, {"const": "horeca_d0c", "title": "Horeca"}, {"const": "park_u9q", "title": "Park"}, {"const": "speeltuin_mie", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_cq1_other": {"type": "string"}, "publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_5uq": {"type": "object", "minProperties": 3, "maxProperties": 3, "properties": {"er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_lit": {"type": "number", "minimum": 1, "maximum": 5}, "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_v1c": {"type": "number", "minimum": 1, "maximum": 5}, "er_is_in_mijn_kern_voldoende_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_6rw": {"type": "number", "minimum": 1, "maximum": 5}}}, "publieke_ruimte_en_leefomgeving_cuz": {"type": "string"}, "voorzieningen_en_dagelijkse_activiteiten_dbu": {"type": "object", "minProperties": 4, "maxProperties": 4, "properties": {"er_zijn_voldoende_voorzieningen_in_mijn_kern_kwm": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_mvh": {"type": "number", "minimum": 1, "maximum": 5}, "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_eqi": {"type": "number", "minimum": 1, "maximum": 5}, "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_uc1": {"type": "number", "minimum": 1, "maximum": 5}}}, "wonen_en_woonbehoeften_u9g": {"type": "array", "uniqueItems": true, "minItems": 5, "maxItems": 5, "items": {"type": "string", "oneOf": [{"const": "starters_6on", "title": "Starters"}, {"const": "gezinnen_uma", "title": "Gezinnen"}, {"const": "senioren_tl6", "title": "<PERSON>en"}, {"const": "alleenstaanden_02j", "title": "Alleenstaanden"}, {"const": "sociale_huur<PERSON>_yek", "title": "<PERSON><PERSON> h<PERSON>"}]}}, "wonen_en_woonbehoeften_af1": {"type": "array", "uniqueItems": true, "minItems": 7, "maxItems": 7, "items": {"type": "string", "oneOf": [{"const": "twee_onder_een_kapwoning_1tx", "title": "<PERSON>we<PERSON>-onder-een-kapwoning"}, {"const": "villa_852", "title": "Vrijstaand"}, {"const": "appartementen_f3t", "title": "Appartementen "}, {"const": "knarrenhof_zog", "title": "Knarrenhof"}, {"const": "tiny_houses_jqx", "title": "Tiny houses "}, {"const": "seniorenwoningen_lbf", "title": "Seniorenwoningen"}, {"const": "rijwoningen_80n", "title": "Rijwoningen"}]}}, "uitstraling_woningen_0me": {"type": "array", "uniqueItems": true, "minItems": 1, "maxItems": 3, "items": {"type": "string", "oneOf": [{"const": "landelijk_6k8", "title": "Landelijk"}, {"const": "dorps_vot", "title": "Dorps"}, {"const": "duurzaam_98z", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"const": "klassiek_tv6", "title": "Klassiek"}, {"const": "in_het_groen_vxn", "title": "In het groen"}, {"const": "other", "title": "<PERSON><PERSON>"}]}}, "uitstraling_woningen_0me_other": {"type": "string"}, "aanvullende_opmerkingen_49x": {"type": "string"}, "aanvullende_opmerkingen_kaart_ej4": {"required": ["type", "coordinates"], "type": "object", "properties": {"type": {"const": "Point"}, "coordinates": {"type": "array", "minItems": 2, "maxItems": 2, "items": {"type": "number"}}}}}, "required": ["voor_welke_kern_wilt_u_participeren_ucg", "vorige_vraag_overnemen_v6r", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_uwf", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_uwf_other", "publieke_ruimte_en_leefomgeving_imh", "publie<PERSON>_ruimte_en_leefomgeving_imh_other", "publieke_ruimte_en_leefomgeving_lk7", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fun", "wonen_en_woonbehoeften_7a4", "wonen_en_woonbehoeften_4v6", "wonen_en_woonbehoeften_0zr", "uitstraling_woningen_ual", "uits<PERSON>ing_woningen_ual_other", "algemene_vraag_over_uw_kern_6ky", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_v56", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_v56_other", "publieke_ruimte_en_leefomgeving_hw2", "publie<PERSON>_ruimte_en_leefomgeving_hw2_other", "publieke_ruimte_en_leefomgeving_zyt", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_z9v", "wonen_en_woonbehoeften_ycq", "wonen_en_woonbehoeften_dyz", "wonen_en_woonbehoeften_yk5", "uitstraling_woningen_h8s", "uits<PERSON>ing_woningen_h8s_other", "algemene_vraag_over_uw_kern_0ft", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_ist", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_ist_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_l5x", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_l5x_other", "publieke_ruimte_en_leefomgeving_ozg", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_ed2", "wonen_en_woonbehoeften_ds4", "wonen_en_woonbehoeften_zs9", "wonen_en_woonbehoeften_qlx", "uitstraling_woningen_fo9", "uits<PERSON>ing_woningen_fo9_other", "algemene_vraag_over_uw_kern_o7s", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_xul", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_xul_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_n5z", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_n5z_other", "publieke_ruimte_en_leefomgeving_xrf", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_l9b", "wonen_en_woonbehoeften_nmx", "wonen_en_woonbehoeften_45c", "wonen_en_woonbehoeften_zlc", "uitstraling_woningen_irc", "uitstraling_woningen_irc_other", "algemene_vraag_over_uw_kern_etw", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_zkh", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_zkh_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_erm", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_erm_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_rh2", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fsa", "wonen_en_woonbehoeften_kaw", "wonen_en_woonbehoeften_7ax", "wonen_en_woonbehoeften_c5o", "uitstraling_woningen_egy", "uits<PERSON><PERSON>_woningen_egy_other", "algemene_vraag_over_uw_kern_p93", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_epk", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_epk_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_dt4", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_dt4_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_bdu", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7pa", "wonen_en_woonbehoeften_fv9", "wonen_en_woonbehoeften_h3i", "wonen_en_woonbehoeften_qf6", "uitstraling_woningen_pqz", "uitstraling_woningen_pqz_other", "algemene_vraag_over_uw_kern_4o5", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_5eh", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_5eh_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_1np", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_1np_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_g68", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_3cz", "wonen_en_woonbehoeften_lgf", "wonen_en_woonbehoeften_kfq", "wonen_en_woonbehoeften_15m", "uitstraling_woningen_5ld", "uits<PERSON>ing_woningen_5ld_other", "algemene_vraag_over_uw_kern_l8g", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_sry", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_sry_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fig", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fig_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_wc9", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_xvq", "wonen_en_woonbehoeften_80z", "wonen_en_woonbehoeften_jsh", "wonen_en_woonbehoeften_x4s", "uitstraling_woningen_olh", "uits<PERSON>ing_woningen_olh_other", "algemene_vraag_over_uw_kern_3o1", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_q81", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_q81_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_e37", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_e37_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_z6a", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_a1t", "wonen_en_woonbehoeften_n73", "wonen_en_woonbehoeften_uan", "wonen_en_woonbehoeften_ae9", "uitstraling_woningen_efs", "uitstraling_woningen_efs_other", "algemene_vraag_over_uw_kern_ihf", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_g5d", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_g5d_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7te", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7te_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_gm6", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_wks", "wonen_en_woonbehoeften_wds", "wonen_en_woonbehoeften_ukg", "wonen_en_woonbehoeften_ueh", "uitstraling_woningen_naw", "uits<PERSON>ing_woningen_naw_other", "algemene_vraag_over_uw_kern_0tk", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_36x", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_36x_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_oil", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_oil_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_h2y", "voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_7mg", "wonen_en_woonbehoeften_ufd", "wonen_en_woonbehoeften_i1z", "wonen_en_woonbehoeften_9ty", "uitstraling_woningen_3ub", "uitstraling_woningen_3ub_other", "algemene_vraag_over_uw_kern_hse", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_jtf", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_jtf_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_zey", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_zey_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_spb", "voorzieningen_en_dagelijkse_activiteiten_kopie_w2m", "wonen_en_woonbehoeften_ke7", "wonen_en_woonbehoeften_eov", "wonen_en_woonbehoeften_xy2", "uitstraling_woningen_jg1", "uitstraling_woningen_jg1_other", "algemene_vraag_over_uw_kern_n2c", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_evh", "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_evh_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_cq1", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_cq1_other", "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_5uq", "voorzieningen_en_dagelijkse_activiteiten_dbu", "wonen_en_woonbehoeften_u9g", "wonen_en_woonbehoeften_af1", "uitstraling_woningen_0me", "uitstraling_woningen_0me_other"]}}, "ui_schema_multiloc": {"nl-NL": {"type": "Categorization", "options": {"formId": "idea-form", "inputTerm": "idea"}, "elements": [{"type": "Page", "options": {"input_type": "page", "id": "65db42de-9ab5-4b0e-9fe8-64d39bba2343", "title": "<PERSON><PERSON> kern", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "label": "Voor welke kern wilt u participeren?", "options": {"description": "", "input_type": "select", "isAdminField": false, "hasRule": true, "dropdown_layout": false, "enumNames": ["Aagtekerke", "Biggekerke", "Domburg", "Gapinge", "Grijpskerke", "Koudekerke", "Meliskerke", "Oostkapelle", "Serooskerke", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Westkapelle", "Zoutelande"]}}]}, {"type": "Page", "options": {"input_type": "page", "id": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0", "title": "Aagtekerke", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/vorige_vraag_overnemen_v6r", "label": "Algemen<PERSON> v<PERSON> over uw kern", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_uwf", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_uwf_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_uwf_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_imh", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten?  </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_imh_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_imh_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_lk7", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_nja", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_j60", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_1g9", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_gkr", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist  in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fun", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_i3k", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_bzt", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen. "}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fmz", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_5hr", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden. "}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_7a4", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_4v6", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_0zr", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_ual", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uits<PERSON>ing_woningen_ual_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_ual_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_xwv", "label": "Aanvullende opmerkingen", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_ovc", "label": "Aanvullende opmerkingen kaart ", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "796fc8ff-2c40-4826-a633-d5aefac8d48c"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["biggekerke_ryh"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["domburg_68k"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["<PERSON>e_oyn"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["grijpskerke_l8j"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["koudekerke_ygb"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["meliskerke_d8o"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["oostkapelle_g9z"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["serooskerke_cm9"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}]}, {"type": "Page", "options": {"input_type": "page", "id": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816", "title": "Biggekerke", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_6ky", "label": "Algemen<PERSON> v<PERSON> over uw kern", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_v56", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_v56_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_v56_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_hw2", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_hw2_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_hw2_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_zyt", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_1xe", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_4o0", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_jnz", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_znj", "label": "Publieke ruimte en leefomgeving  ", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_z9v", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_hg4", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_v5h", "label": "<PERSON>k kan mijn boodschappen mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_o8d", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_du2", "label": " Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_ycq", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_dyz", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_yk5", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_h8s", "label": "<PERSON>its<PERSON>ing woningen ", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uits<PERSON>ing_woningen_h8s_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_h8s_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_s9z", "label": "Aanvullende opmerkingen ", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_67f", "label": "Aanvullende opmerkingen kaart ", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "*************-414a-938e-be1a68b453c6"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["domburg_68k"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["<PERSON>e_oyn"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["grijpskerke_l8j"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["koudekerke_ygb"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["meliskerke_d8o"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["oostkapelle_g9z"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["serooskerke_cm9"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "ac015b59-accc-48ca-89f8-7b20165c8ee4", "title": "Domburg", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_0ft", "label": "Algemen<PERSON> v<PERSON> over uw kern", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_ist", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_ist_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_ist_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_l5x", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_l5x_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_l5x_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_ozg", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_sf6", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_9ks", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_u69", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_b7o", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_ed2", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_9b6", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_w9b", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_3b8", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_f6y", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_ds4", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_zs9", "label": "Wonen en woonbehoeften ", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_qlx", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_fo9", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uits<PERSON>ing_woningen_fo9_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_fo9_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_xcq", "label": "Aanvullende opmerkingen ", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_86m", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "b3f52d1a-a689-4cf9-97f7-63ab3f69c6b2"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["<PERSON>e_oyn"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["grijpskerke_l8j"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["koudekerke_ygb"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["meliskerke_d8o"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["oostkapelle_g9z"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["serooskerke_cm9"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "70c5c7a6-94a3-475b-abc0-a7381c4dac97", "title": "Gapinge", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_o7s", "label": "Algemen<PERSON> v<PERSON> over uw kern", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_xul", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_xul_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_xul_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_n5z", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_n5z_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_n5z_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_xrf", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_p1h", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldo<PERSON>e_speelvoorzieningen_voor_kinderen_6f8", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_6ny", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_263", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_l9b", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_vye", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_4hs", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_icg", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwik<PERSON>ing_zouden_ook_nieuwe_voorzieningen_moeten_landen_9o7", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_nmx", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_45c", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_zlc", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_irc", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uitstraling_woningen_irc_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_irc_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_tb8", "label": "Aanvullende opmerkingen ", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_git", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "a5534191-102f-427e-b317-2ca566dc767d"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["grijpskerke_l8j"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["koudekerke_ygb"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["meliskerke_d8o"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["oostkapelle_g9z"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["serooskerke_cm9"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "f2140909-dfd3-4308-84f1-6e54b6dd4e36", "title": "Grijpskerke", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_etw", "label": "Algemen<PERSON> v<PERSON> over uw kern", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_zkh", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_zkh_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_zkh_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_erm", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_erm_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_erm_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_rh2", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_sgk", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldo<PERSON>e_speelvoorzieningen_voor_kinderen_lw9", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_9hk", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_bsw", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fsa", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_5nm", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_pd3", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fd8", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_mfw", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_kaw", "label": "Wonen en woonbehoeften ", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_7ax", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_c5o", "label": "Wonen en woonbehoeften ", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_egy", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uits<PERSON><PERSON>_woningen_egy_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_egy_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_vqc", "label": "Aanvullende opmerkingen", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_1ht", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "6eee7963-d0ff-4bd8-ab23-1e0e6fec438f"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["koudekerke_ygb"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["meliskerke_d8o"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["oostkapelle_g9z"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["serooskerke_cm9"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "f809fbe0-dc41-4db3-8b00-fc6a5462941c", "title": "Koudekerke", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_p93", "label": "Algemen<PERSON> v<PERSON> over uw kern ", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_epk", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_epk_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_epk_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_dt4", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_dt4_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_dt4_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_bdu", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_65j", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_79j", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_v42", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_ptv", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7pa", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_ni1", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_oy0", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_yot", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwik<PERSON>ing_zouden_ook_nieuwe_voorzieningen_moeten_landen_2xg", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_fv9", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_h3i", "label": "Wonen en woonbehoeften ", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_qf6", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_pqz", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uitstraling_woningen_pqz_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_pqz_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_7le", "label": "Aanvullende opmerkingen", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_67w", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "f83c3aa4-b12e-499d-9a2a-8eeed00f095a"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["meliskerke_d8o"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["oostkapelle_g9z"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["serooskerke_cm9"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "68502174-2d4d-4565-b1b7-26450f1df28f", "title": "Meliskerke", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_4o5", "label": "Algemen<PERSON> v<PERSON> over uw kern ", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_5eh", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? ", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_5eh_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_5eh_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_1np", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_1np_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_1np_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_g68", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_yje", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_ve8", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_ido", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_fta", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_kopie_3cz", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_6x7", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_xfv", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_5pu", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwik<PERSON>ing_zouden_ook_nieuwe_voorzieningen_moeten_landen_x6c", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_lgf", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_kfq", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_15m", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_5ld", "label": "<PERSON>its<PERSON>ing woningen ", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uits<PERSON>ing_woningen_5ld_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_5ld_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_mwe", "label": "Aanvullende opmerkingen ", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_2wa", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "c45e8479-6b25-47e3-a572-a0074bfdb1e9"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["oostkapelle_g9z"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["serooskerke_cm9"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f809fbe0-dc41-4db3-8b00-fc6a5462941c"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "abc7d56a-cdfd-4932-83bb-b4005a7119a5", "title": "Oostkapelle", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_l8g", "label": "Algemen<PERSON> v<PERSON> over uw kern ", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_sry", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_sry_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_sry_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fig", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fig_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_fig_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_wc9", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_3di", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_sg7", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_ohy", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_l7e", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_kopie_xvq", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_8e2", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_hlg", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_vtm", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwik<PERSON>ing_zouden_ook_nieuwe_voorzieningen_moeten_landen_qjs", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_80z", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_jsh", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_x4s", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_olh", "label": "<PERSON>its<PERSON>ing woningen ", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uits<PERSON>ing_woningen_olh_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_olh_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_ahf", "label": "Aanvullende opmerkingen", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_kmr", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "768b38eb-790d-4a4e-995b-052609f49c81"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["serooskerke_cm9"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f809fbe0-dc41-4db3-8b00-fc6a5462941c"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "68502174-2d4d-4565-b1b7-26450f1df28f"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "6b4cf172-c925-43c0-8ad6-7c369b82fd5b", "title": "Serooskerke", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_3o1", "label": "Algemen<PERSON> v<PERSON> over uw kern", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_q81", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? ", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_q81_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_q81_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_e37", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_e37_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_e37_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_z6a", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_vto", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldo<PERSON>e_speelvoorzieningen_voor_kinderen_rxl", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_sux", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_3jt", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_kopie_a1t", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_3wq", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_rzh", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_4hs", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_ois", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_n73", "label": "Wonen en woonbehoeften ", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_uan", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_ae9", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_efs", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uitstraling_woningen_efs_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_efs_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_9ho", "label": "Aanvullende opmerkingen", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_2wi", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "10736e47-499d-4fdf-81a1-73445681843b"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["veere_v04"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f809fbe0-dc41-4db3-8b00-fc6a5462941c"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "68502174-2d4d-4565-b1b7-26450f1df28f"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "abc7d56a-cdfd-4932-83bb-b4005a7119a5"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "498a9955-48c3-48e4-8155-538f86ff7dd9", "title": "<PERSON><PERSON><PERSON>", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_ihf", "label": "Algemen<PERSON> v<PERSON> over uw kern", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_g5d", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? ", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_g5d_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_g5d_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7te", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7te_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_7te_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_gm6", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_toe", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldo<PERSON>e_speelvoorzieningen_voor_kinderen_3p6", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_voldo<PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_1yg", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_54d", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_kopie_wks", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_knq", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_pws", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_pvr", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwik<PERSON>ing_zouden_ook_nieuwe_voorzieningen_moeten_landen_edl", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_wds", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_ukg", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_ueh", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_naw", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uits<PERSON>ing_woningen_naw_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_naw_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_zob", "label": "Aanvullende opmerkingen", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_vox", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "330f7285-bb7b-4085-b82c-f0cd890b7c8a"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["vrouwenpolder_a9x"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f809fbe0-dc41-4db3-8b00-fc6a5462941c"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "68502174-2d4d-4565-b1b7-26450f1df28f"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "abc7d56a-cdfd-4932-83bb-b4005a7119a5"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "6b4cf172-c925-43c0-8ad6-7c369b82fd5b"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "267e3ffc-b559-4f43-b946-6397c477a283", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_0tk", "label": "Algemen<PERSON> v<PERSON> over uw kern ", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_36x", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? ", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_36x_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_36x_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_oil", "label": "Publieke ruimte en leefomgeving ", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_oil_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_oil_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_h2y", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_12y", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_1e8", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_zt2", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_hra", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_kopie_7mg", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_6ls", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_wik", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dageli<PERSON>se_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_xfc", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_eub", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_ufd", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_i1z", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_9ty", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_3ub", "label": "<PERSON>its<PERSON>ing woningen ", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uitstraling_woningen_3ub_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_3ub_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_mn4", "label": "Aanvullende opmerkingen ", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_w3n", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "dd989380-de97-46d5-8943-dc2ba200d2cc"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["westkapelle_4a2"]}}}, {"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f809fbe0-dc41-4db3-8b00-fc6a5462941c"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "68502174-2d4d-4565-b1b7-26450f1df28f"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "abc7d56a-cdfd-4932-83bb-b4005a7119a5"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "6b4cf172-c925-43c0-8ad6-7c369b82fd5b"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "498a9955-48c3-48e4-8155-538f86ff7dd9"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "38abe218-9edb-4bf6-a4d5-2e1f5e152349", "title": "Westkapelle", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_hse", "label": "Algemen<PERSON> v<PERSON> over uw kern ", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_jtf", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern? ", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_jtf_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_jtf_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_zey", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_zey_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_zey_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_spb", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_94o", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldo<PERSON>e_speelvoorzieningen_voor_kinderen_d63", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_01f", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_z0v", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_kopie_w2m", "label": "Voorzieningen en dagelijkse activiteiten ", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_d8j", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_evm", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_fr9", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_7ew", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_ke7", "label": "Wonen en woonbehoeften ", "options": {"description": "<p>Hoe ervaart u de woningmarkt in uw kern? Zijn er voldoende geschikte woningen voor verschillende doelgroepen? Geen een score op 10.</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_eov", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_xy2", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_jg1", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uitstraling_woningen_jg1_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_jg1_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_od1", "label": "Aanvullende opmerkingen", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_3xz", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "5080b82d-039b-4c58-aa22-e7a6e49ee108"}}], "ruleArray": [{"effect": "HIDE", "condition": {"scope": "#/properties/voor_welke_kern_wilt_u_participeren_ucg", "schema": {"enum": ["zoutelande_dq4"]}}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f809fbe0-dc41-4db3-8b00-fc6a5462941c"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "68502174-2d4d-4565-b1b7-26450f1df28f"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "abc7d56a-cdfd-4932-83bb-b4005a7119a5"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "6b4cf172-c925-43c0-8ad6-7c369b82fd5b"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "498a9955-48c3-48e4-8155-538f86ff7dd9"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "267e3ffc-b559-4f43-b946-6397c477a283"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "2af1e421-d478-4540-843a-7046fee92561", "title": "Zoutelande", "description": "", "page_layout": "default", "map_config_id": null}, "elements": [{"type": "Control", "scope": "#/properties/algemene_vraag_over_uw_kern_n2c", "label": "Algemen<PERSON> v<PERSON> over uw kern", "options": {"description": "<p><PERSON><PERSON>ft u uw kern als een aangename plek om te wonen?</p>", "input_type": "rating", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_evh", "label": "Wat kan een nieuwe ontwikkeling toevoegen of verbeteren aan uw kern?", "options": {"description": "", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_evh_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/wat_kan_een_nieuwe_ontwikkeling_toevoegen_of_verbeteren_aan_uw_kern_evh_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_cq1", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Waar in uw kern komt u graag om mensen te ontmoeten? </p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "publie<PERSON>_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_cq1_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_cq1_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_kopie_5uq", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p><PERSON><PERSON> aan of u het eens bent met de volgende stellingen?</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_in_mijn_kern_voldoende_voorzieningen_voor_hondenbezitters_zoals_uitlaatplekken_lit", "label": "<PERSON><PERSON> zijn in mijn kern voldoende voorzieningen voor hondenbezitters, zoals uitlaatplekken."}, {"key": "er_zijn_in_mijn_kern_voldoende_speelvoorzieningen_voor_kinderen_v1c", "label": "<PERSON><PERSON> zijn in mijn kern voldoende speelvoorzieningen voor kinderen. "}, {"key": "er_is_in_mijn_kern_vol<PERSON><PERSON>e_schaduw_en_verkoeling_in_de_openbare_ruimte_bijv_bomen_waterpartijen_6rw", "label": "Er is in mijn kern voldoende schaduw en verkoeling in de openbare ruimte. ( bijv. bomen, waterpartijen)"}]}}, {"type": "Control", "scope": "#/properties/publieke_ruimte_en_leefomgeving_cuz", "label": "Publieke ruimte en leefomgeving", "options": {"description": "<p>Als je één ding kan opnoemen wat u mist in de openbare ruimte wat zou dat dan zijn?</p>", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/voorzieningen_en_dagelijkse_activiteiten_dbu", "label": "Voorzieningen en dagelijkse activiteiten", "options": {"description": "<p><PERSON><PERSON> aan hoeveel je het eens met deze stelling.</p>", "input_type": "matrix_linear_scale", "isAdminField": false, "hasRule": false, "linear_scale_label1": "Zeer mee oneens", "linear_scale_label2": "<PERSON><PERSON>", "linear_scale_label3": "Neutraal", "linear_scale_label4": "<PERSON>e eens", "linear_scale_label5": "<PERSON>eer mee eens", "linear_scale_label6": "", "linear_scale_label7": "", "linear_scale_label8": "", "linear_scale_label9": "", "linear_scale_label10": "", "linear_scale_label11": "", "statements": [{"key": "er_zijn_voldoende_voorzieningen_in_mijn_kern_kwm", "label": "<PERSON>r zijn voldoende voorzieningen in mijn kern."}, {"key": "ik_kan_mijn_boodschappen_en_andere_dagelijkse_activiteiten_zoals_een_doktersbezoek_in_mijn_eigen_kern_doen_mvh", "label": "<PERSON>k kan mijn boodschappen in mijn eigen kern doen."}, {"key": "ik_kan_mijn_dagelijkse_activiteiten_zoals_naar_werk_gaan_en_boodschappen_doen_met_de_fiets_doen_eqi", "label": "<PERSON>k kan mijn dagelijkse activiteiten met de fiets doen. "}, {"key": "bij_een_nieuwe_ontwikkeling_zouden_ook_nieuwe_voorzieningen_moeten_landen_uc1", "label": "Bij een nieuwe ontwikkeling zouden ook nieuwe voorzieningen moeten gerealiseerd worden."}]}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_u9g", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Voor welke doelgroepen is het volgens u het moeilijkst om een passende woning te vinden in uw kern? Rankschik de antwoorden van het moeilijkst naar het makkelijkst.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/wonen_en_woonbehoeften_af1", "label": "Wonen en woonbehoeften", "options": {"description": "<p>Welke type woningen zijn volgens u het meest nodig in uw kern?  Rankschik de antwoorden meest nodig naar minst nodig.</p>", "input_type": "ranking", "isAdminField": false, "hasRule": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_0me", "label": "<PERSON>its<PERSON>ing woningen", "options": {"description": "<p>Welk uitstraling zou de uitbreiding volgens u moeten krijgen?</p>", "input_type": "multiselect", "isAdminField": false, "hasRule": false, "otherField": "uitstraling_woningen_0me_other", "dropdown_layout": false}}, {"type": "Control", "scope": "#/properties/uitstraling_woningen_0me_other", "label": "<PERSON><PERSON> je antwoord", "options": {"description": "", "input_type": "text", "isAdminField": false, "hasRule": false, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_49x", "label": "Aanvullende opmerkingen", "options": {"description": "<p>Als u naast de vragen nog aanvullende opmerkingen of suggesties heeft, kunt u deze hieronder toevoegen. Wij verzoeken u vriendelijk om uw inbreng te richten op het thema <strong>wonen</strong> en op de <strong>nieuwe ontwikkeling</strong>. Input die hier niet op gericht is of louter negatief van toon is, kunnen wij helaas niet verwerken vanwege de extra verwerkingstijd die dit met zich meebrengt.</p><p>Als u specifieke opmerkingen heeft over een bepaalde locatie, kunt u deze op de volgende kaart aanduiden.</p>", "input_type": "multiline_text", "isAdminField": false, "hasRule": false, "textarea": true, "transform": "trim_on_blur"}}, {"type": "Control", "scope": "#/properties/aanvullende_opmerkingen_kaart_ej4", "label": "Aanvullende opmerkingen kaart", "options": {"description": "<p>V<PERSON>g hier event<PERSON><PERSON> de locatie van uw eerdere opmerking toe.</p>", "input_type": "point", "isAdminField": false, "hasRule": false, "map_config_id": "4833754d-dcc0-4ec6-a43c-deb93ad904d2"}}], "ruleArray": [{"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "4cf8874b-9de7-434c-bd01-7bae9e1a44e0"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "e6e445f2-d527-4b8a-b1c9-ed688b5d8816"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "ac015b59-accc-48ca-89f8-7b20165c8ee4"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "70c5c7a6-94a3-475b-abc0-a7381c4dac97"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f2140909-dfd3-4308-84f1-6e54b6dd4e36"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "f809fbe0-dc41-4db3-8b00-fc6a5462941c"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "68502174-2d4d-4565-b1b7-26450f1df28f"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "abc7d56a-cdfd-4932-83bb-b4005a7119a5"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "6b4cf172-c925-43c0-8ad6-7c369b82fd5b"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "498a9955-48c3-48e4-8155-538f86ff7dd9"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "267e3ffc-b559-4f43-b946-6397c477a283"}}, {"effect": "HIDE", "condition": {"type": "HIDEPAGE", "pageId": "38abe218-9edb-4bf6-a4d5-2e1f5e152349"}}]}, {"type": "Page", "options": {"input_type": "page", "id": "ad8e2ff4-15fc-42f4-bd25-eeb87708d21a", "title": "Bedankt voor het delen van je invoer!", "description": "Je invoer is succesvol ingediend.", "page_layout": "default", "map_config_id": null}, "elements": []}]}}}