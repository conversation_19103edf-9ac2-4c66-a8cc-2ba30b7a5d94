CL_CLUSTER_NAME=can docker stack deploy --compose-file docker-compose-production.yml cl2 --with-registry-auth

# check env vars of a service run by systemd
systemctl show --property=Environment docker


LANG=C.UTF-8PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/binNOTIFY_SOCKET=/run/systemd/notifyLISTEN_PID=26219LISTEN_FDS=1LISTEN_FDNAMES=docker.socketINVOCATION_ID=412670a1115041e29018c0bb0ea8b5cfJOURNAL_STREAM=9:18958856

LANG=C.UTF-8PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/binNOTIFY_SOCKET=/run/systemd/notifyLISTEN_PID=893LISTEN_FDS=1LISTEN_FDNAMES=docker.socketINVOCATION_ID=cf5c71a3a0fc49be98cf1aaf1a589102JOURNAL_STREAM=9:20374


# get docker node id
docker node ls | grep -v "Down" | grep '*'

## name it
#id=$(docker node ls | grep -v "Down" | grep '*' | awk '{print $1}')

# drain docker node with id
docker node update --availability drain jdtpgour8ieaa5oa9c6wy9x0m

sudo apt-get update \
&& sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin \
&& docker node update --availability active jgpnjr2xvxddkzxpm821s89xi

cd cl2-deployment && CL_CLUSTER_NAME=sam docker stack deploy --compose-file docker-compose-production.yml cl2 --with-registry-auth