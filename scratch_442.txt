72507ef8-fa62-45bd-92c8-4c7d08008d04,Danish Demo Platform,cl-dk.demo.govocal.com,"{""core"": {""allowed"": true, ""enabled"": true, ""locales"": [""da-DK""], ""currency"": ""TOK"", ""timezone"": ""Copenhagen"", ""area_term"": {""da-DK"": ""område""}, ""areas_term"": {""da-DK"": ""områder""}, ""color_main"": ""#002653"", ""color_text"": ""#333333"", ""meta_title"": {}, ""topic_term"": {}, ""topics_term"": {}, ""reply_to_email"": ""<EMAIL>"", ""color_secondary"": ""#4d6687"", ""lifecycle_stage"": ""demo"", ""meta_description"": {}, ""organization_name"": {""da-DK"": ""Min Kommune""}, ""organization_site"": ""https://www.citizenlab.dk"", ""organization_type"": ""medium_city"", ""signup_helper_text"": {""da-DK"": ""Dette er Step 1 teksten""}, ""custom_onboarding_button"": {}, ""currently_working_on_text"": {""da-DK"": ""Min Kommune ønsker dine input til disse projekter""}, ""custom_onboarding_message"": {""da-DK"": ""Digital borgerinddragelse i Min Kommune""}, ""custom_fields_signup_helper_text"": {""da-DK"": ""Dette er Step 2 teksten""}, ""authentication_token_lifetime_in_days"": 30}, ""maps"": {""allowed"": true, ""enabled"": true, ""map_center"": {""lat"": ""55.6320"", ""long"": ""12.4667""}, ""zoom_level"": 12, ""tile_provider"": ""https://api.maptiler.com/maps/basic/{z}/{x}/{y}.png?key=R0U21P01bsRLx7I7ZRqp"", ""osm_relation_id"": 50046}, ""pages"": {""allowed"": true, ""enabled"": true}, ""polls"": {""allowed"": true, ""enabled"": true}, ""matomo"": {""allowed"": true, ""enabled"": true, ""tenant_site_id"": ""229"", ""product_site_id"": ""22""}, ""segment"": {""allowed"": false, ""enabled"": false}, ""surveys"": {""allowed"": true, ""enabled"": true}, ""texting"": {""allowed"": false, ""enabled"": false, ""monthly_sms_segments_limit"": 100000}, ""widgets"": {""allowed"": true, ""enabled"": true}, ""intercom"": {""allowed"": true, ""enabled"": true}, ""analytics"": {""allowed"": true, ""enabled"": true}, ""fragments"": {""allowed"": false, ""enabled"": false, ""enabled_fragments"": []}, ""redirects"": {""rules"": [], ""allowed"": false, ""enabled"": false}, ""workshops"": {""allowed"": true, ""enabled"": true}, ""clustering"": {""allowed"": true, ""enabled"": true}, ""moderation"": {""allowed"": true, ""enabled"": true}, ""satismeter"": {""allowed"": true, ""enabled"": true, ""write_key"": ""QJHRRSiX3ER3foZa""}, ""custom_maps"": {""allowed"": true, ""enabled"": true}, ""initiatives"": {""allowed"": true, ""enabled"": true, ""days_limit"": 90, ""voting_threshold"": 300, ""eligibility_criteria"": {""en"": ""<p>The initiative should</p> <p>- be within the city's competences</p>\n"", ""da-DK"": ""<ul><li>Dit projekt skal falde inden for kommunens politikområder og kompetencer</li><li>Det skal tjene den offentlige interesse snarere end din individuelle interesse</li><li>Det diskriminerer ikke andre borgere i forhold til køn, race, alder eller baggrund</li><li>Det skader ikke andre</li></ul>""}, ""threshold_reached_message"": {""en"": ""<p>The initiators are invited to present their initiative on the next council meeting. We will provide an official update.</p>\n"", ""da-DK"": ""<p>Initiativtagerne er inviteret til at præsentere deres borgerforslag på næste rådsmøde. Vi bringer en officiel opdatering.</p>""}}, ""google_login"": {""allowed"": true, ""enabled"": true, ""client_id"": ""fienfioenfefh"", ""client_secret"": ""fienfioenfefh""}, ""smart_groups"": {""allowed"": true, ""enabled"": true}, ""verification"": {""allowed"": false, ""enabled"": false, ""verification_methods"": [{""name"": ""id_card_lookup"", ""card_id_multiloc"": {""da-DK"": ""Personligt identifikationsnummer""}, ""card_id_placeholder"": ""DDMMYY-SSSS"", ""explainer_image_url"": ""https://international.kk.dk/sites/international.kk.dk/files/yellow-health-card.png"", ""method_name_multiloc"": {""da-DK"": ""Personligt identifikationsnummer""}, ""card_id_tooltip_multiloc"": {""da-DK"": ""Here""}}]}, ""volunteering"": {""allowed"": true, ""enabled"": true}, ""custom_topics"": {""allowed"": true, ""enabled"": true}, ""events_widget"": {""allowed"": true, ""enabled"": true, ""widget_title"": {}}, ""similar_ideas"": {""allowed"": false, ""enabled"": false}, ""azure_ad_login"": {""tenant"": ""fienfioenfefh"", ""allowed"": false, ""enabled"": false, ""logo_url"": ""https://www.signicat.com/hubfs/Tomorrow%20People/Website/Images/landscape-logos_nem-id-logo.jpg"", ""client_id"": ""fienfioenfefh"", ""login_mechanism_name"": ""Nets""}, ""facebook_login"": {""app_id"": ""fienfioenfefh"", ""allowed"": true, ""enabled"": true, ""app_secret"": ""fienfioenfefh""}, ""native_surveys"": {""allowed"": true, ""enabled"": true}, ""password_login"": {""phone"": false, ""allowed"": true, ""enabled"": true, ""enable_signup"": true, ""minimum_length"": 8, ""phone_email_pattern"": ""<EMAIL>""}, ""report_builder"": {""allowed"": false, ""enabled"": false}, ""content_builder"": {""allowed"": true, ""enabled"": true}, ""idea_assignment"": {""allowed"": true, ""enabled"": true}, ""manual_emailing"": {""allowed"": true, ""enabled"": true}, ""project_folders"": {""allowed"": true, ""enabled"": true}, ""project_reports"": {""allowed"": true, ""enabled"": true}, ""enalyzer_surveys"": {""allowed"": true, ""enabled"": true}, ""google_analytics"": {""allowed"": true, ""enabled"": true, ""tracking_id"": ""UA-101738826-17""}, ""idea_custom_copy"": {""allowed"": true, ""enabled"": true}, ""private_projects"": {""allowed"": true, ""enabled"": true}, ""typeform_surveys"": {""allowed"": true, ""enabled"": true, ""user_token"": ""4H2ePYrNmXA2Pem59nn5G4jhYXCttxKKrrVakLZncLyE""}, ""bulk_import_ideas"": {""allowed"": true, ""enabled"": true}, ""disable_user_bios"": {""allowed"": false, ""enabled"": false}, ""dynamic_idea_form"": {""allowed"": true, ""enabled"": true}, ""insights_nlp_flow"": {""allowed"": true, ""enabled"": true}, ""qualtrics_surveys"": {""allowed"": true, ""enabled"": true}, ""user_confirmation"": {""allowed"": true, ""enabled"": false}, ""blocking_profanity"": {""allowed"": true, ""enabled"": true}, ""disable_downvoting"": {""allowed"": true, ""enabled"": true}, ""google_tag_manager"": {""allowed"": true, ""enabled"": true, ""category"": ""analytics"", ""container_id"": ""GTM-KBM5894""}, ""idea_author_change"": {""allowed"": true, ""enabled"": true}, ""idea_custom_fields"": {""allowed"": true, ""enabled"": true}, ""project_management"": {""allowed"": true, ""enabled"": true}, ""project_visibility"": {""allowed"": true, ""enabled"": true}, ""representativeness"": {""allowed"": true, ""enabled"": true}, ""visitors_dashboard"": {""allowed"": true, ""enabled"": true}, ""franceconnect_login"": {""allowed"": false, ""enabled"": false, ""environment"": ""production""}, ""snap_survey_surveys"": {""allowed"": false, ""enabled"": false}, ""survey_xact_surveys"": {""allowed"": true, ""enabled"": true}, ""custom_idea_statuses"": {""allowed"": true, ""enabled"": true}, ""geographic_dashboard"": {""allowed"": false, ""enabled"": false}, ""google_forms_surveys"": {""allowed"": true, ""enabled"": true}, ""granular_permissions"": {""allowed"": true, ""enabled"": true}, ""insights_manual_flow"": {""allowed"": true, ""enabled"": true}, ""machine_translations"": {""allowed"": true, ""enabled"": true}, ""smart_survey_surveys"": {""allowed"": true, ""enabled"": true}, ""surveymonkey_surveys"": {""allowed"": true, ""enabled"": true}, ""vienna_citizen_login"": {""allowed"": false, ""enabled"": false}, ""advanced_custom_pages"": {""allowed"": false, ""enabled"": false}, ""vienna_employee_login"": {""allowed"": false, ""enabled"": false}, ""abbreviated_user_names"": {""allowed"": false, ""enabled"": false}, ""remove_vendor_branding"": {""allowed"": false, ""enabled"": false}, ""admin_project_templates"": {""allowed"": true, ""enabled"": true}, ""ideaflow_social_sharing"": {""allowed"": true, ""enabled"": true}, ""jsonforms_custom_fields"": {""allowed"": false, ""enabled"": false}, ""microsoft_forms_surveys"": {""allowed"": true, ""enabled"": true}, ""participatory_budgeting"": {""allowed"": true, ""enabled"": true}, ""integration_onze_stad_app"": {""allowed"": false, ""enabled"": false}, ""automated_emailing_control"": {""allowed"": true, ""enabled"": true}, ""flag_inappropriate_content"": {""allowed"": true, ""enabled"": true}, ""customizable_homepage_banner"": {""allowed"": true, ""enabled"": true}, ""initiativeflow_social_sharing"": {""allowed"": true, ""enabled"": true}, ""custom_accessibility_statement_link"": {""allowed"": false, ""enabled"": false}}",2020-05-17 08:09:54.035820,2024-10-28 11:42:39.354343,05b27bff-e78b-484e-9942-b0ddd34e5ec8.png,cc6808c3-0016-4353-9674-a5ccd3213249.png,"{""signedOutHeaderOverlayColor"": ""#233978"", ""signedInHeaderOverlayOpacity"": 20, ""signedOutHeaderOverlayOpacity"": 20}",,2020-05-17 08:09:54.035820
e730d553-4481-4a09-9f09-0371ba7b4382,HK Danmark OK26,hkok26.demo.govocal.com,{},2024-09-20 16:51:31.220412,2024-10-28 11:41:58.433230,,,{},,2024-09-20 16:51:53.351738
