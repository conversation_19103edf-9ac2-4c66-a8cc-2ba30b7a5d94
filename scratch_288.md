# How are visitors calculated?

A visit is counted whenever someone visits the platform. If they view multiple pages before leaving, it’s still counted as one visit. But if they visit the the platform and, for example, refresh the page, it’s counted as two visits.

A visitor is someone who makes one or more visits. To be able to see which visits belong to the same visitor, we need a bit of information to identify the visitor during their visit. Before, we were using cookies for this- when you accepted a cookie, you consented to having a random code stored in your browser. With this random code, our application can identify you as the same visitor without having an account.

Now, instead of a cookie, we use a browser fingerprint. Basically, we take the visitor’s IP address and some other information about the browser they use, and generate a ‘hash’ from this. A hash is also a kind of random code. It gets a bit technical here, but what’s important to understand is that if two visits have the exact same IP address and browser, they will generate the same hash, but from the hash it’s impossible to calculate back what the IP address and browser were. We only store this hash, which does not fall under Personal Identifiable Information, and next time someone visits the page we check if this hash already exists in our database- if it does, we know it’s the same visitor.

We only use the same ‘hash’ for 30 days. So if you visit the platform once and then two months later, you will still be counted as two visitors.

Some limitations of this approach are that we of course cannot distinguish between two people using the same computer- this was the case with cookies too. But now, even two people on the same network but who happen to use the exact same type of browser on different computers will be seen as the same visitor. Another gotcha: we will count the same person as two visitors if, for example, this person visits the platform twice with the same device and browser, but from different Wi-Fi networks, since these will have different IP addresses (this was not a problem with cookies).

# How do we calculate ‘participants’ in our reports? What is the definition of participants? Is it the same number across the platform?

A participant **at the platform** level is anyone who

- Posted on input
- Submitted a survey
- Posted a proposal
- Commented (on an input, proposal or other comment)
- Reacted (to an input, proposal or comment)
- Submitted a poll response
- Volunteered
- Submitted a basket
- Attended any event
- Followed anything

A participant **in a project** is anyone who

- Posted on input in that project
- Submitted a survey in that project
- Commented (on an input, proposal or other comment) in that project
- Reacted (to an input, proposal or comment) in that project
- Submitted a poll response in that project
- Volunteered in that project
- Submitted a basket in that project
- Attended any event associated with that project
- Followed the project or any input in that project

Normally, when someone takes multiple actions that would qualify then as a participant, they will still be counted as a single participant. However, for anonymous participation (e.g. surveys open to ‘anyone’ or inputs and comments that can be posted anonymously), every participation is counted as a single participant. This means that if a person posts an anonymous idea, an anonymous comment, and follows the project, they will be counted as 3 participants.

# I heard we used to use Matomo to track visitors, do we still, and, is it accurate? 

In the product don’t use Matomo anymore to count the number of visits and visitors (see [How are visitors calculated?](https://www.notion.so/How-are-visitors-calculated-1f6f1d9639b045e699179a82a8ac1bff?pvs=21) for a more detailed explanation of how we currently do this). However, we still use Matomo for other statistics, such as the traffic sources, visitor types, and registration and participation rate.

Outside of our product, in Matomo itself, even more information is available such as device type the of visitors, entry and exit pages, etc. This information still works the same as before, so we only measure it for people who accept cookies.

In the future, we might replace Matomo completely with a different solution that works without cookies.

# Who can access the reports in the back office?

## Global reports / reports on the ‘reporting’ tab (`/admin/reporting/report-builder`)

- If you are an admin or super-admin, you can see all reports
- If you are a project or folder moderator, you can see all reports that you created.
    - However, you need to have access to all data in the report. So if an admin added a chart that refers to a project that you don’t have rights to moderate, you will be able to see the report in the list, but you won’t be able to open it.

## Phase reports

- If you are an admin or super-admin, you can see all phase reports
- If you are a project or folder moderator, you can see all phase reports in projects that you can moderate.
    - However, similar to the global reports, you can only edit these as long as you have access to all data in the report. Normally, when you make a phase report, the report will be about the project that the phase is in, so this won’t be an issue. But there’s nothing stopping admins or other moderators from adding graphs that refer to other projects. When this happens, you lose access to the report.
- If you are a citizen, you can view these reports once they are public (the phase has started and the ‘visible’ toggle is on).

# Can a project manager create a platform report?

Currently, only super-admins can access the platform report template. Once the new template has been battle tested by GovSuccess and we've integrated their feedback, we will also make it available to admins. 

There's been no discussion yet about extending access to project managers. However, even if they do get access, it wouldn't change their data permissions in the report builder—they would still only be able to see data from the projects they moderate.

# I heard there’s a ‘Progress Report’ tab in the Reporting tab, but I don’t see it. Why?

The Progress Report tab only appears if you have the right permissions (i.e. you are a super-admin or admin) and there exists such a reports.

For more information on which reports are considered progress reports, see How are reports shown under the ‘Progress Report’ tab selected?.

# How are reports shown under ‘Progress Report’ tab selected?

The Progress Report tab displays all and only reports owned (= created) by a super-admin. It doesn’t matter if a template was used or which template it was. The only exception is phase reports, which are not shown in any of the tabs in the Reporting tab.

Limitations:
- GovSuccess (super-admins) cannot selectively mark reports as 'progress reports'.
- Incomplete or work-in-progress reports are still listed.
- When a super-admin account is deleted, the reports they created are no longer considered progress reports because those reports don't have an owner anymore.


# What does "stale" projects mean?

```typescript
const STALENESS_PERIOD = 30;

export const deriveProjectStatus = (period: Period, now: Moment) => {
    const startAt = moment(period.start_at);
    const lastPhaseStartAt = moment(period.last_phase_start_at);
    const endAt = period.end_at ? moment(period.end_at) : null;

    if (startAt.isAfter(now)) {
        return 'planned';
    }

    if (endAt === null) {
        const daysSinceLastPhaseStart = now.diff(lastPhaseStartAt, 'days');
        return daysSinceLastPhaseStart < STALENESS_PERIOD ? 'active' : 'stale';
    }

    return now.isAfter(endAt) ? 'finished' : 'active';
};

export type ProjectStatus = ReturnType<typeof deriveProjectStatus>;
```

Staleness only applies to projects without an end data, aka continuous projects. Such projects are considered stale if the last phase started more than 30 days ago.