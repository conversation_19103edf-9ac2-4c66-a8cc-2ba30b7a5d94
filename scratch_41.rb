s = MultiTenancy::ChurnedTenantService.new(pii_retention_period: 90)

churned = Tenant.churned.map { |t| [t, s.churn_datetime(t).to_date] }.select { |_, d| s.send(:pii_expired?, d) }

csv_string = CSV.generate do |csv|
  churned.each do |tenant, churn_date| 
    csv << [tenant.id, tenant.name, tenant.host, churn_date]
  end
end

puts csv_string


tenant_id = '875d5c95-a040-46d6-970f-47508536feee'
tenants = Tenant.where(id: tenant_id)
MultiTenancy::ChurnedTenantService.new.remove_expired_pii(tenants)

