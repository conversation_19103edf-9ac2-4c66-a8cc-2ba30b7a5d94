SELECT "ideas".*
FROM "ideas"
         INNER JOIN "ideas_phases" ON "ideas"."id" = "ideas_phases"."idea_id"
WHERE "ideas_phases"."phase_id" = '455568e6-520a-424e-869e-245cdd274596'
  AND "ideas"."publication_status" = 'published'
  AND (likes_count + dislikes_count > 0)
ORDER BY greatest(likes_count, dislikes_count) * 1.0 / (likes_count + dislikes_count) DESC,
         (likes_count + dislikes_count) desc
LIMIT 2

SELECT "ideas".*
FROM "ideas"
         INNER JOIN "ideas_phases" ON "ideas"."id" = "ideas_phases"."idea_id"
WHERE "ideas_phases"."phase_id" = '455568e6-520a-424e-869e-245cdd274596'
  AND "ideas"."publication_status" = 'published'
  AND (likes_count + dislikes_count > 0)
ORDER BY greatest(likes_count, dislikes_count) * 1.0 / (likes_count + dislikes_count) ASC,
         (likes_count + dislikes_count) desc
LIMIT 2
