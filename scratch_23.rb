user_counts = {}

(0..6).to_a.each do |day|
  user_counts[day] = 0
end

tenants = []
Tenant.find_each do |tenant|
  tenant.switch do
    next unless EmailCampaigns::Campaigns::UserDigest.first

    tenants << { tenant: tenant, user_count: User.count }
  end
end

counts = tenants.pluck(:user_count).sort.reverse
counts.each do |c|
  day = user_counts.keys.min_by {|d| user_counts[d]}
  user_counts[day] += c
end




tenants.sort_by! { |tenant| tenant[:user_count] }
tenants.each_slice(7) do |slice|
  slice.each_with_index do |tenant, index|
    tenant[:day] = index
  end
end

tenants.each do |tenant_hash|
  user_counts[tenant_hash[:day]] = User.count
end

p user_counts