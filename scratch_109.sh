# check logs of docker daemon (systemd service)
journalctl -u docker.service

# join token for manager
docker swarm join-token manager

docker pull citizenlabdotco/back-ee:production && docker run --env-file cl2-deployment/.env-web citizenlabdotco/back-ee:production bin/rake db:migrate_if_pending cl2back:clean_tenant_settings email_campaigns:assure_campaign_records fix_existing_tenants:update_permissions cl2back:clear_cache_store email_campaigns:remove_deprecated
cd cl2-deployment && docker stack deploy --compose-file docker-compose-production.yml cl2 --with-registry-auth