# frozen_string_literal: true

template_names = MultiTenancy::TenantTemplateService.new.available_templates[:external]

template_names.each do |template_name|
  host_name = "#{template_name.tr('_', '-')}.localhost"
  next if Tenant.find_by(host: host_name)

  required_locales = MultiTenancy::TenantTemplateService.new.required_locales(template_name)

  tenant = FactoryBot.create(:tenant, locales: required_locales, host: host_name)
  MultiTenancy::ApplyTenantTemplateJob.perform_now(template_name, tenant)
end

##########################################

template_utils = MultiTenancy::Templates::Utils.new(template_bucket: 'cl2-tenant-templates-dev')
available_templates = template_utils.external_template_names(prefix: 'blue')

available_templates.each do |template_name|
  host_name = "#{template_name.chomp('.localhost')}applied.localhost"

  Tenant.find_by(host: host_name)&.destroy!

  required_locales = template_utils.required_locales(template_name, external_subfolder: 'blue')
  tenant = FactoryBot.create(:tenant, locales: required_locales, host: host_name)

  side_fx_tenant = MultiTenancy::SideFxTenantService.new
  side_fx_tenant.before_apply_template(tenant, template_name)
  side_fx_tenant.around_apply_template(tenant, template_name) do
    tenant.switch { MultiTenancy::Templates::ApplyService.new.apply(template_name, external_template_group: 'blue') }
  end
  side_fx_tenant.after_apply_template(tenant, template_name)
end

