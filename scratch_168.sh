# dig short answer
dig +short -x

# network ACL stands for network access control list
# it is a firewall that controls traffic to and from your subnet
# so this is a firewall at the subnet level instead of the instance level

docker stack deploy --compose-file docker-compose-staging.yml cl2 --with-registry-auth

echo "CL_CLUSTER_NAME=\"prd-usw\"" | sudo tee -a /etc/environment
sudo cat /etc/environment

# SSH Tunneling to RDS
ssh -L 5432:cl2-back-staging.cekjw1iai1hl.eu-central-1.rds.amazonaws.com:5432 aws-stg-1

# list tables in postgres
psql -p 5432 -h localhost -U postgres -d cl2_back_staging -W -c '\dt'
psql -h localhost -p 5432 -d cl2_back_staging -U postgres -W -c '\dt'

# check CRON logs
sudo sh -c 'echo "10 12 * * * root docker node ls | grep Down | awk '\''{print $1}'\'' | xargs -n25 docker node rm" > /etc/cron.d/rm-down-docker-swarm-workers'
sudo sh -c 'echo "* * * * * root docker node ls | grep Down | awk '\''{print $1}'\'' | xargs -n25 docker node rm" > /etc/cron.d/rm-down-docker-swarm-workers'
cat /etc/cron.d/rm-down-docker-swarm-workers
sudo grep CRON /var/log/syslog

# search commit where the number of occurrences of 'CROWDIN_PLUGIN_ENABLED' changed
git log -S CROWDIN_PLUGIN_ENABLED --oneline

# search commit where the number of occurrences of 'participation_method' changed in file back/app/models/project.rb
git log -S participation_method --oneline -- back/app/models/project.rb


dea41b33b8 Delete old unused CI configs
f4b3c3215f Up
5899c39cdf Remove i18n.ts
a0d996fb91 Only load translations that are needed
e23ce41417 WIP: only import necessary translations
749674bc8b e2e tests WIP
e356b8b5c3 e2e tests WIP
57fc5dcd11 WIP - Webpack config refactoring
92413bd5f4 feat(footer): update the locale on selection
b44b8004ea Hardcode Acholi as language when crowdin plugin is enabled. CL2-664
9cd1e07128 Added circleci config. CL2-425 #done
95fc58aaf5 Added circleci config. CL2-425 #done
00f0cb073a Enabled in-context translaton activation through env variable


# list background jobs on linux
jobs -l

