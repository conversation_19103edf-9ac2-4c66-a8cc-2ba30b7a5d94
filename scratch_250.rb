def classification_fields
  fields = analysis.associated_custom_fields
  if fields.map(&:code).include?('topic_ids') || analysis.participation_method != 'ideation'
    fields
  else
    custom_form = analysis.project.custom_form || CustomForm.new(participation_context: analysis.project)
    project_fields = IdeaCustomFieldsService.new(custom_form).submittable_fields
    fields + [project_fields.find { |field| field.code == 'topic_ids' }].compact
  end
end

# suggestions
def classification_fields
  fields = analysis.associated_custom_fields
  return fields if analysis.participation_method != 'ideation' || 'topic_ids'.in?(fields.map(&:code))

  custom_form = analysis.project.custom_form || CustomForm.new(participation_context: analysis.project)
  topics_field = IdeaCustomFieldsService
    .new(custom_form)
    .submittable_fields
    .find_by(code: 'topic_ids')

  topics_field ? fields + [topics_field] : fields
end