description: >
  Install your Node packages with automated caching and best practices applied.
  Requires lock file.
parameters:
  app-dir:
    default: .
    description: >-
      Path to the directory containing your package.json file. Not needed if
      package.json lives in the root.
    type: string
  cache-only-lockfile:
    default: true
    description: >
      If true, package.json will be ignored in the cache key. Useful for
      projects where package.json changes do not always invalidate dependencies.

      Note: package.json will still be the final fallback key incase a project
      is not configured with a lock file.
    type: boolean
  cache-path:
    default: ''
    description: >
      By default, this orb will utilize 'npm ci' and cache the '~/.npm'
      directory. Override which path to cache with this parameter.
    type: string
  cache-version:
    default: v1
    description: >-
      Change the default cache version if you need to clear the cache for any
      reason.
    type: string
  check-cache:
    default: never
    description: |
      Yarn berry only for Zero install support -
      Use 'always' to always --check-cache argument to yarn install.
      Use 'detect' to enable caching of yarn.lock and to only add when required.
    enum:
      - never
      - always
      - detect
    type: enum
  include-branch-in-cache-key:
    default: true
    description: |
      If true, this cache bucket will only apply to jobs within the same branch.
    type: boolean
  override-ci-command:
    default: ''
    description: >
      By default, packages will be installed with "npm ci", "yarn install
      --frozen-lockfile" or "yarn install --immutable".

      Optionally supply a custom package installation command, with any
      additional flags needed.
    type: string
  pkg-manager:
    default: npm
    description: Select the default node package manager to use. NPM v5+ Required.
    enum:
      - npm
      - yarn
      - yarn-berry
    type: enum
  with-cache:
    default: true
    description: Cache your node packages automatically for faster install times.
    type: boolean
steps:
  - run:
      command: |-
        #!/usr/bin/env bash

        # Fail if package.json does not exist in working directory

        if [ ! -f "package.json" ]; then
            echo
            echo "---"
            echo "Unable to find your package.json file. Did you forget to set the app-dir parameter?"
            echo "---"
            echo
            echo "Current directory: $(pwd)"
            echo
            echo
            echo "List directory: "
            echo
            ls
            exit 1
        fi
      name: Checking for package.json
      working_directory: <<parameters.app-dir>>
  - when:
      condition: << parameters.with-cache >>
      steps:
        - run:
            command: >
              #!/usr/bin/env bash


              TARGET_DIR="/tmp"

              if [ -n "$HOMEDRIVE" ]; then
                  TARGET_DIR="$HOMEDRIVE\\tmp"
              fi


              # Link corresponding lock file to a temporary file used by cache
              commands

              if [ -f "package-lock.json" ]; then
                  echo "Found package-lock.json file, assuming lockfile"
                  cp package-lock.json "$TARGET_DIR"/node-project-lockfile
              elif [ -f "npm-shrinkwrap.json" ]; then
                  echo "Found npm-shrinkwrap.json file, assuming lockfile"
                  cp npm-shrinkwrap.json "$TARGET_DIR"/node-project-lockfile
              elif [ -f "yarn.lock" ]; then
                  echo "Found yarn.lock file, assuming lockfile"
                  cp yarn.lock "$TARGET_DIR"/node-project-lockfile
              else
                  echo "Found no lockfile, adding empty one"
                  touch "$TARGET_DIR"/node-project-lockfile
              fi


              cp package.json "$TARGET_DIR"/node-project-package.json
            name: Determine lockfile
            working_directory: <<parameters.app-dir>>
        - restore_cache:
            keys:
              - >-
                node-deps-{{ arch
                }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                .Branch
                }}-<</parameters.include-branch-in-cache-key>><<^parameters.cache-only-lockfile>>{{
                checksum "/tmp/node-project-package.json"
                }}-<</parameters.cache-only-lockfile>>{{ checksum
                "/tmp/node-project-lockfile" }}
              - >-
                node-deps-{{ arch
                }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                .Branch }}-<</parameters.include-branch-in-cache-key>>{{
                checksum "/tmp/node-project-package.json" }}
              - >-
                node-deps-{{ arch
                }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                .Branch }}-<</parameters.include-branch-in-cache-key>>
  - when:
      condition:
        equal:
          - npm
          - << parameters.pkg-manager >>
      steps:
        - run:
            command: >-
              #!/usr/bin/env bash


              # Configure npm cache path if provided

              if [[ -n "$PARAM_CACHE_PATH" ]]; then
                  npm config set cache "$PARAM_CACHE_PATH"
              fi


              # Run override ci command if provided, otherwise run default npm
              install

              if [[ -n "$PARAM_OVERRIDE_COMMAND" ]]; then
                  echo "Running override package installation command:"
                  eval "$PARAM_OVERRIDE_COMMAND"
              else
                  npm ci
              fi
            environment:
              PARAM_CACHE_PATH: << parameters.cache-path >>
              PARAM_OVERRIDE_COMMAND: << parameters.override-ci-command >>
            name: Installing NPM packages
            working_directory: <<parameters.app-dir>>
        - when:
            condition: << parameters.with-cache >>
            steps:
              - when:
                  condition: << parameters.cache-path >>
                  steps:
                    - save_cache:
                        key: >-
                          node-deps-{{ arch
                          }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                          .Branch
                          }}-<</parameters.include-branch-in-cache-key>><<^parameters.cache-only-lockfile>>{{
                          checksum "/tmp/node-project-package.json"
                          }}-<</parameters.cache-only-lockfile>>{{ checksum
                          "/tmp/node-project-lockfile" }}
                        paths:
                          - << parameters.cache-path >>
              - unless:
                  condition: << parameters.cache-path >>
                  steps:
                    - save_cache:
                        key: >-
                          node-deps-{{ arch
                          }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                          .Branch
                          }}-<</parameters.include-branch-in-cache-key>><<^parameters.cache-only-lockfile>>{{
                          checksum "/tmp/node-project-package.json"
                          }}-<</parameters.cache-only-lockfile>>{{ checksum
                          "/tmp/node-project-lockfile" }}
                        paths:
                          - ~/.npm
  - when:
      condition:
        equal:
          - yarn
          - << parameters.pkg-manager >>
      steps:
        - run:
            command: >-
              #!/usr/bin/env bash


              # Run override ci command if provided, otherwise run default yarn
              install

              if [[ -n "$PARAM_OVERRIDE_COMMAND" ]]; then
                  echo "Running override package installation command:"
                  eval "$PARAM_OVERRIDE_COMMAND"
              else
                  yarn install --frozen-lockfile
              fi
            environment:
              PARAM_OVERRIDE_COMMAND: << parameters.override-ci-command >>
              YARN_CACHE_FOLDER: << parameters.cache-path >>
            name: Installing YARN packages
            working_directory: <<parameters.app-dir>>
        - when:
            condition: << parameters.with-cache >>
            steps:
              - when:
                  condition: << parameters.cache-path >>
                  steps:
                    - save_cache:
                        key: >-
                          node-deps-{{ arch
                          }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                          .Branch
                          }}-<</parameters.include-branch-in-cache-key>><<^parameters.cache-only-lockfile>>{{
                          checksum "/tmp/node-project-package.json"
                          }}-<</parameters.cache-only-lockfile>>{{ checksum
                          "/tmp/node-project-lockfile" }}
                        paths:
                          - <<parameters.cache-path>>
              - unless:
                  condition: << parameters.cache-path >>
                  steps:
                    - save_cache:
                        key: >-
                          node-deps-{{ arch
                          }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                          .Branch
                          }}-<</parameters.include-branch-in-cache-key>><<^parameters.cache-only-lockfile>>{{
                          checksum "/tmp/node-project-package.json"
                          }}-<</parameters.cache-only-lockfile>>{{ checksum
                          "/tmp/node-project-lockfile" }}
                        paths:
                          - <<parameters.app-dir>>/node_modules
  - when:
      condition:
        equal:
          - yarn-berry
          - << parameters.pkg-manager >>
      steps:
        - when:
            condition:
              equal:
                - detect
                - << parameters.check-cache >>
            steps:
              - restore_cache:
                  keys:
                    - >-
                      yarn-berry-{{ arch
                      }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                      .Branch }}-<</parameters.include-branch-in-cache-key>>{{
                      checksum "/tmp/yarn-zero-lockfile" }}
                    - >-
                      yarn-berry-{{ arch
                      }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                      .Branch }}-<</parameters.include-branch-in-cache-key>>
        - run:
            command: >-
              #!/usr/bin/env bash


              # Run override ci command if provided, otherwise run default yarn
              install

              # See: https://yarnpkg.com/configuration/yarnrc/#cacheFolder

              if [[ -n "$PARAM_CACHE_PATH" ]]; then
                  yarn config set cacheFolder "$PARAM_CACHE_PATH"
              fi


              if [[ -n "$PARAM_OVERRIDE_COMMAND" ]]; then
                  echo "Running override package installation command:"
                  eval "$PARAM_OVERRIDE_COMMAND"
              else
                  # If a cache folder is already present, then we use Yarn Zero installs
                  # See: https://yarnpkg.com/features/zero-installs
                  if [[ -e "$PARAM_CACHE_PATH" ]]; then
                      # See: https://yarnpkg.com/features/zero-installs#does-it-have-security-implications
                      YARN_LOCKFILE_PATH="/tmp/yarn-zero-lockfile"

                      if [[ "$PARAM_CHECK_CACHE" == "detect" ]]; then
                          if [[ ! -f "$YARN_LOCKFILE_PATH" ]]; then
                              echo "No yarn zero lockfile cached. Enabling check cache this run."
                              ENABLE_CHECK_CACHE="true"
                              elif [[ $(diff -q "$YARN_LOCKFILE_PATH" yarn.lock) ]]; then
                              echo "Detected changes in lockfile. Enabling check cache this run."
                              rm -f "$YARN_LOCKFILE_PATH"
                              ENABLE_CHECK_CACHE="true"
                          else
                              echo "No changes detected in lockfile. Skipping check cache this run."
                          fi
                      fi

                      if [[ "$PARAM_CHECK_CACHE" == "always" || -n "$ENABLE_CHECK_CACHE" ]]; then
                          set -- "$@" --check-cache
                      fi

                      yarn install --immutable --immutable-cache "$@"

                      if [[ "$PARAM_CHECK_CACHE" == "detect" && -n "$ENABLE_CHECK_CACHE" ]]; then
                          cp yarn.lock "$YARN_LOCKFILE_PATH"
                      fi
                  else
                      yarn install --immutable
                  fi
              fi
            environment:
              PARAM_CACHE_PATH: << parameters.cache-path >>
              PARAM_CHECK_CACHE: << parameters.check-cache >>
              PARAM_OVERRIDE_COMMAND: << parameters.override-ci-command >>
            name: Installing YARN packages
            working_directory: <<parameters.app-dir>>
        - when:
            condition:
              equal:
                - detect
                - << parameters.check-cache >>
            steps:
              - save_cache:
                  key: >-
                    yarn-berry-{{ arch
                    }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                    .Branch }}-<</parameters.include-branch-in-cache-key>>{{
                    checksum "/tmp/yarn-zero-lockfile" }}
                  paths:
                    - <<parameters.cache-path>>
        - when:
            condition: << parameters.with-cache >>
            steps:
              - when:
                  condition: << parameters.cache-path >>
                  steps:
                    - save_cache:
                        key: >-
                          node-deps-{{ arch
                          }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                          .Branch
                          }}-<</parameters.include-branch-in-cache-key>><<^parameters.cache-only-lockfile>>{{
                          checksum "/tmp/node-project-package.json"
                          }}-<</parameters.cache-only-lockfile>>{{ checksum
                          "/tmp/node-project-lockfile" }}
                        paths:
                          - <<parameters.cache-path>>
              - unless:
                  condition: << parameters.cache-path >>
                  steps:
                    - save_cache:
                        key: >-
                          node-deps-{{ arch
                          }}-<<parameters.cache-version>>-<<#parameters.include-branch-in-cache-key>>{{
                          .Branch
                          }}-<</parameters.include-branch-in-cache-key>><<^parameters.cache-only-lockfile>>{{
                          checksum "/tmp/node-project-package.json"
                          }}-<</parameters.cache-only-lockfile>>{{ checksum
                          "/tmp/node-project-lockfile" }}
                        paths:
                          - ~/.yarn/berry/cache
                          - <<parameters.app-dir>>/.yarn/cache
  - when:
      condition: << parameters.with-cache >>
      steps:
        - run:
            command: >-
              rm -f /tmp/node-project-lockfile /tmp/node-project-package.json
              /tmp/yarn-zero-lockfile
            name: Remove temporary links
