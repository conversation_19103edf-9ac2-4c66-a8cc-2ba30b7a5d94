campaigns = EmailCampaigns::Campaign.select do |campaign|
  campaign.class.try(:consentable_for?, user)
end

# Frontend::UrlService#unsubscribe_url
user = User.find_by(email: '<EMAIL>')
configuration = AppConfiguration.instance
campaign = campaigns.find { |campaign| !campaign.manual? }
campaign_id = campaign.id
user_id = user.id
url = Frontend::UrlService.new.unsubscribe_url(configuration, campaign_id, user_id)

user_id = user.id
urls = campaigns.map do |campaign|
  Frontend::UrlService.new.unsubscribe_url(configuration, campaign.id, user_id)
end