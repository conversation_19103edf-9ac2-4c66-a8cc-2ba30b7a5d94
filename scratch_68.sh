#
# Just a few instructions to run cl2-nlp locally.

# If you are running into issues when trying to set up cl2-nlp locally (and make it work with your local deployment of citizenlab), the best is probably to reset your whole environment first. That's certainly an overkill, but starting from a blank slate will maximise your chances of success ;) The script `reset_dev_env.sh` (citizenlab-ee repo) can help with that, but here I'll do things manually. Maybe, this will give you a better idea of what's going on. In the following example, I will assume that you want to run the master branch, but it shouldn't be too difficult to transpose it to other branch combinations. I'm using zsh by the way.


# First, make sure that no containers from citizenlab and cl2-nlp stacks are running.
# You can check with...
docker ps

# Now, let's try to go back to a clean environment.
cd citizenlab-ee
git switch master
git pull
sh ./scripts/disable_ee.sh && sh ./scripts/enable_ee.sh
# Also make sure that you don't have any local changes. Otherwise, revert or stash them, or whatever.

cd ../citizenlab
git switch master
git pull
# Again, make sure that you don't have any local changes.

docker compose build

# The next step resets the whole database.
# So you gonna loose any changes you have made on your local platform(s).
docker compose run --rm web bin/rails db:reset

# Now, it's pretty much a clean environment. We didn't scratch the volumes, but that should be fine.

# Let's check out the cl2-nlp branch of your liking.
cd ../cl2-nlp
git switch master
git pull
# Again, yada yada yada.
docker compose build

# Let's now spin up the services...
# You need to start citizenlab first!
cd ../citizenlab
docker compose up -d
# (Wait a few seconds to let the services start up.)
# (...background music...)

cd ../cl2-nlp
docker compose up -d

# Since it's a fresh install the backend db and the nlp db are out of sync.
# To fix this, we need to sync them manually.
# (I usually do this directly in a Rails console, but the rake task should work just fine.)
cd ../citizenlab
docker-compose run --rm web bin/rake nlp:dump_all_tenants_to_nlp

# Everything should be ready for you to start playing. You just need a front-end.
cd ../citizenlab/front
npm i && npm start

# Now, go to your browser

