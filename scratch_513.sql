WITH
    "session_locales" AS (
        SELECT DISTINCT "impact_tracking_pageviews"."session_id", split_part(path, '/', 2) AS locale
        FROM "impact_tracking_pageviews"
                 INNER JOIN "impact_tracking_sessions"
                            ON "impact_tracking_sessions"."id" = "impact_tracking_pageviews"."session_id"
        WHERE "impact_tracking_sessions"."highest_role" = 'user'
          AND split_part(path, '/', 2) IN
              ('en', 'ar-MA', 'ar-SA', 'ca-ES', 'cy-GB', 'da-DK', 'de-DE', 'el-GR', 'en-CA', 'en-GB', 'en-IE', 'es-CL',
               'es-ES', 'fi-FI', 'fr-BE', 'fr-FR', 'hr-HR', 'hu-HU', 'it-IT', 'kl-GL', 'lb-LU', 'lt-LT', 'lv-LV', 'mi',
               'nb-NO', 'nl-BE', 'nl-NL', 'pa-IN', 'pl-PL', 'pt-BR', 'ro-RO', 'sr-Latn', 'sr-SP', 'sv-SE', 'tr-TR',
               'ur-PK'))
SELECT "impact_tracking_sessions".*
FROM session_locales
GROUP BY "locales"
