# run rspec in bisect mode to find the first failing spec
bin/rspec --bisect engines/commercial/multi_tenancy/spec/services/multi_tenancy/templates/apply_service_spec.rb engines/free/email_campaigns/spec/services/email_campaigns/delivery_service_spec.rb spec/services/participation_context_service_spec.rb spec/acceptance/admin_publications_spec.rb spec/policies/comment_reaction_policy_spec.rb spec/policies/idea_comment_policy_spec.rb spec/acceptance/mentions_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/user_digest_spec.rb spec/policies/initiative_comment_policy_spec.rb engines/commercial/multi_tenancy/spec/models/app_configuration_spec.rb engines/commercial/insights/spec/finders/insights/inputs_finder_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/moderator_digest_spec.rb spec/services/additional_seats_incrementer_spec.rb engines/commercial/smart_groups/spec/rules/role_spec.rb engines/commercial/idea_custom_fields/spec/acceptance/idea_custom_fields/project_context/update_all_ideation_spec.rb engines/commercial/public_api/spec/acceptance/v1/ideas_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/admin_digest_spec.rb engines/commercial/user_custom_fields/spec/acceptance/user_custom_fields_spec.rb engines/commercial/analytics/spec/services/analytics/query_runner_service_spec.rb spec/acceptance/user_token_spec.rb spec/services/multiloc_service_spec.rb engines/commercial/idea_custom_fields/spec/policies/idea_custom_field_policy_spec.rb engines/commercial/verification/spec/services/verification_service_spec.rb engines/free/seo/spec/acceptance/application_spec.rb spec/acceptance/phase_files_spec.rb engines/commercial/insights/spec/acceptance/category_suggestions_spec.rb engines/free/volunteering/spec/services/xlsx_service_spec.rb spec/interactors/reset_user_email_spec.rb spec/models/phase_spec.rb engines/commercial/insights/spec/acceptance/processed_flag_spec.rb spec/models/notifications/project_phase_upcoming_spec.rb spec/jobs/update_member_count_job_spec.rb spec/interactors/confirm_user_spec.rb engines/commercial/admin_api/spec/graphql/ideas_spec.rb spec/acceptance/user_comments_spec.rb spec/lib/participation_method/volunteering_spec.rb engines/commercial/smart_groups/spec/models/user_spec.rb spec/services/side_fx_phase_spec.rb spec/services/content_image_service_spec.rb engines/commercial/user_custom_fields/spec/models/user_custom_fields/representativeness/binned_distribution_spec.rb engines/commercial/geographic_dashboard/spec/acceptance/geotag_ideas_spec.rb engines/commercial/public_api/spec/acceptance/v1/phases_spec.rb spec/models/internal_comment_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/new_comment_on_reacted_idea_spec.rb engines/commercial/smart_groups/spec/rules/custom_field_checkbox_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/internal_comment_on_idea_you_moderate_spec.rb spec/models/notifications/threshold_reached_for_admin_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/mention_in_internal_comment_spec.rb engines/commercial/multi_tenancy/spec/services/track_segment_service_spec.rb spec/services/ui_schema_generator_service_spec.rb spec/models/custom_field_option_spec.rb engines/commercial/analytics/spec/models/analytics/fact_post_spec.rb spec/lib/participation_method/none_spec.rb spec/serializers/web_api/v1/internal_comment_serializer_spec.rb engines/commercial/insights/spec/serializers/insights/web_api/v1/input_serializer_spec.rb spec/models/pin_spec.rb engines/commercial/multi_tenancy/spec/jobs/publish_activity_to_rabbit_job_spec.rb engines/commercial/analytics/spec/models/analytics/dimension_user_spec.rb engines/free/polls/spec/models/poll_participation_context_spec.rb spec/services/phone_service_spec.rb engines/commercial/granular_permissions/spec/policies/response_policy_spec.rb spec/models/nav_bar_item_spec.rb engines/commercial/smart_groups/spec/services/side_fx_area_spec.rb engines/free/email_campaigns/spec/mailers/voting_basket_not_submitted_mailer_spec.rb engines/free/email_campaigns/spec/models/email_campaigns/campaigns/voting_basket_submitted_spec.rb spec/lib/voting_method/single_voting_spec.rb spec/services/sort_by_params_service_spec.rb



rspec './spec/models/idea_file_spec.rb[1:2:1]' './spec/services/timeline_service_spec.rb[1:1:4,1:3:2,1:5:5]' --seed 1234
