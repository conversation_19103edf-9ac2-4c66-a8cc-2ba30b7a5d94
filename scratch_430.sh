#find the hosted zone for a given domain
aws route53 list-hosted-zones-by-name --dns-name example.com
aws-vault exec prd-admin -- aws route53 list-hostefd-zones-by-name --dns-name example.com
watermael-boitsfort.demo.citizenlab.co

# cat with line number
cat -n file.txt

# get docker daemon logs (journalctl)
journalctl -u docker.service

# only the last 100 lines
journalctl -u docker.service -n 100

# between 10am and 11am (18 oct 2024)
journalctl -u docker.service --since "2024-10-18 10:40:00" --until "2024-10-18 11:00:00"  -p err

# follow the logs
journalctl -u docker.service -f

