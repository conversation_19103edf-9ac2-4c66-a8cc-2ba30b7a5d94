# SELECT
#     project_id,
#     min(start_at) as start_at,
#     max(start_at) as last_phase_start_at,
#     CASE WHEN count(end_at) = count(*) THEN max(end_at) ELSE NULL END as end_at
# FROM phases
# WHERE project_id IN (
#     SELECT project_id
#     FROM phases
#     WHERE (start_at, end_at) OVERLAPS ('2024-01-01', '2024-06-01')
# )
# GROUP BY project_id;

start_date = '2024-01-01'.to_date
end_date = '2024-06-01'.to_date

query = ActiveRecord::Base.connection.execute(<<-SQL)
  SELECT
      project_id,
      min(start_at) as start_at, 
      max(start_at) as last_phase_start_at,
      CASE WHEN count(end_at) = count(*) THEN max(end_at) ELSE NULL END as end_at
  FROM phases
  WHERE project_id IN (
      SELECT project_id
      FROM phases
      WHERE (start_at, end_at) OVERLAPS (
          #{ActiveRecord::Base.connection.quote(start_date)},
          #{ActiveRecord::Base.connection.quote(end_date)}
      )
  )
  GROUP BY project_id;
SQL

result = ActiveRecord::Base.connection.execute(query, start_date, end_date)

# OR

overlapping_project_ids = Phase
  .select(:project_id)
  .where("(start_at, end_at) OVERLAPS (?, ?)", start_date, end_date)

periods = Phase
  .select(
    :project_id,
    "min(start_at) as start_at",
    "max(start_at) as last_phase_start_at",
    "CASE WHEN count(end_at) = count(*) THEN max(end_at) ELSE NULL END as end_at")
  .where(project_id: overlapping_project_ids)
  .group(:project_id)

